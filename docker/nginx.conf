user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
	use epoll;
    worker_connections  10240;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    types {
        # here are extending default mime types
        application/javascript mjs;
    }

    # for Matrix to Kibana
    log_format json_mtlog '{ "@timestamp": "$time_iso8601", '
                            '"remote_addr": "$remote_addr", '
                            '"server_addr": "$server_addr", '
                            '"host": "$host", '
                            '"request": "$request", '
                            '"status": "$status", '
                            '"request_time": "$request_time", '
                            '"http_x_real_ip": "$http_x_real_ip", '
                            '"http_x_forwarded_for": "$http_x_forwarded_for", '
                            '"content_length": "$content_length", '
                            '"request_length": "$request_length", '
                            '"sent_http_content_length": "$sent_http_content_length", '
                            '"body_bytes_sent": "$body_bytes_sent", '
                            '"http_cdn": "$http_cdn", '
                            '"http_referer": "$http_referer", '
                            '"http_user_agent": "$http_user_agent", '
                            '"upstream_addr": "$upstream_addr", '
                            '"upstream_status": "$upstream_status", '
                            '"upstream_response_time": "$upstream_response_time", '
                            '"sent_http_request_id": "$sent_http_request_id" }';

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile       on;

    keepalive_timeout  60;
	keepalive_requests 10000;

    tcp_nopush  on;
	tcp_nodelay off;

    gzip  on;
    gzip_types text/plain text/xml text/css text/markdown
               text/comma-separated-values
               text/javascript application/javascript application/x-javascript
               image/gif image/jpeg image/png image/svg+xml image/webp image/x-icon
               audio/mp4 audio/ogg audio/mpeg audio/wav
               video/mp4 video/ogg video/webm
               font/otf font/woff font/woff2 application/x-font-ttf application/x-font-opentype application/x-font-truetype
               application/pdf
               application/json;

    server {
        listen 80;
        server_name pre-aigc.meitu.com beta-aigc.meitu.com aigc.meitu.com;
        index index.html;

        root /usr/share/nginx/html/;

        # 用于根据不同域名显示不同首页
        set $index_file index.html;

        # 用于美图设计室域名下，如果是移动端设备，是否重定向到美图设计室首页
        set $design_domain_redirect_to_mobile "0";

        # 根据用户代理字符串判断是否为移动端设备
        if ($http_user_agent ~* "android|ip(ad|hone|od)|phone|mobile|kindle") {
            set $design_domain_redirect_to_mobile "1";
        }

        # 如果是在美图设计室域名下
        if ($http_host ~* "(pre|beta-)?design\.meitu\.com$") {
            set $index_file index.designer.html;
            set $design_domain_redirect_to_mobile "${design_domain_redirect_to_mobile}1";
        }

        # 如果是在大模型域名下
        if ($http_host ~* "(pre|beta-)?miraclevision\.com$") {
            set $index_file index.miraclevision.html;
        }

        # 如果是在美图设计室新域名 x-design 下
        if ($http_host ~* "(pre|beta|www)?\.x-design\.com$") {
            set $index_file index.designer.html;
            set $design_domain_redirect_to_mobile "${design_domain_redirect_to_mobile}1";
        }

        # add_header X-Index-File "${index_file}"; # 仅用于本地调试输出

        # add_header X-Design-Domain-Redirect-To-Mobile "${design_domain_redirect_to_mobile}"; # 仅用于本地调试输出

        # 美图设计室域名下，如果是移动端设备，重定向到美图设计室首页
        if ($design_domain_redirect_to_mobile = "11") {
            rewrite ^/aigc(.*)?$ https://$host/?type=ai_wst redirect;
            break;
        }

        # for /aigc/static/*
        location /aigc/ {
            alias /usr/share/nginx/html/;
            try_files $uri $uri/ /$index_file;
            gzip_static on;
        }

        # for /editor
        # for /text-to-image
        # for /*
        location / {
            try_files $uri $uri/ /$index_file;
        }

        location ~ (.*\.sh?$|/\.) {
            return 403;
        }

        location ~ /\.ht {
            deny all;
        }

        access_log /dev/stdout json_mtlog;
        error_log  /dev/stderr;
    }
}
