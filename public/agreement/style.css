* {
  margin: 0;
  padding: 0;
}

.container {
  width: 100%;
  min-height: 100vh;
  background-color: #ffffff;
}

.agreement {
  padding: 2rem 0;
  width: 90%;
  margin: 0 auto;
  line-height: 1.7;
  max-width: 800px;
  font-size: 15px;
}

.sub-title {
  color: #7b7d80;
  font-size: 14px;
  text-align: center;
  font-weight: normal;
}

h1 {
  font-size: 20px;
  text-align: center;
  margin-bottom: 8px;
  font-weight: bold;
}

h3 {
  font-size: 18px;
  margin-bottom: 10px;
}

h4 {
  font-size: 16px;
  margin-bottom: 10px;
}

p {
  margin: 15px 0 10px;
  line-height: 1.8;
}

a {
  color: #4f60dd;
}

.underline {
  text-decoration: underline;
}

ul {
  list-style: none;
  margin: 0;
  padding: 0;
  padding-left: 20px;
}

ul > li {
  margin-top: 4px;
}

/* 适配表格 */
table {
  border: 1px solid #000;
  border-collapse: collapse;
  border-spacing: 0;
}

table p {
  text-indent: 0;
  margin: 2px;
  word-break: break-all;
}

table a {
  word-break: break-all;
}

th {
  border: 1px solid #000;
}

td {
  border: 1px solid #000;
}
