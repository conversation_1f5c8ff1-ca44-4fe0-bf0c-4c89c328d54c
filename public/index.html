<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />

    <!-- 百度渠道投放验证需求 -->
    <meta name="baidu-site-verification" content="codeva-YJOmkAAlqn" />

    <!-- `.env` 中 `REACT_APP_DESCRIPTION` 字段 -->
    <meta
      name="description"
      content="<%= htmlWebpackPlugin.options.description %>"
    />

    <!-- `.env` 中 `REACT_APP_KEYWORDS` 字段 -->
    <meta name="keywords" content="<%= htmlWebpackPlugin.options.keywords %>" />

    <!-- `.env` 中 `REACT_APP_TITLE` 字段 -->
    <!-- prettier-ignore -->
    <title data-title="<%= htmlWebpackPlugin.options.appTitle %>"<% if (htmlWebpackPlugin.options.appEnv) { %> data-title-suffix="<%= htmlWebpackPlugin.options.appEnv %>"<% } %>>
      <%= htmlWebpackPlugin.options.appTitle %>
    </title>

    <script src="https://public.static.meitudata.com/meitu/mtstat-sdk/mtstat-sdk.min.js"></script>
  </head>

  <body>
    <!-- 挂载 React 应用的节点 -->
    <div id="root"></div>

    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
    <noscript>本页面需要浏览器支持（启用）JavaScript。</noscript>
    <!-- 微信/企业微信分享链接时的图片 -->
    <img
      id="wx-cover"
      style="position: absolute; z-index: -9999; top: -9999em; left: -9999em"
      src="%PUBLIC_URL%/wx.cover.png"
      alt="<%= htmlWebpackPlugin.options.appTitle %>"
      width="0"
      height="0"
    />
  </body>
</html>
