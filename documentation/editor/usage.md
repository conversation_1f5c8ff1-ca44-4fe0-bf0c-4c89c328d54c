

# WHEE Editor

- [设计](#设计)
- [定义](#定义)
	- [EditorComponetsProps](#editorcomponetsprops)
	- [GlobalContextType](#globalcontexttype)
	- [ShapesProvider](#shapesprovider)
- [使用](#使用)
	- [如何新建画布](#如何新建画布)
	- [如何增删改shapes](#如何增删改shapes)
	- [如何将指定元素位于屏幕正中间](#如何将指定元素位于屏幕正中间)
	- [如何变成画板模式](#如何变成画板模式)
	- [如何指定一个画画区域](#如何指定一个画画区域)
	- [如何实现undo redo功能](#如何实现undo-redo功能)







## 设计

![设计](./design.png)

上图就是该编辑器组件的设计， 可以看到<code>globalProvider</code>提供了一些基础的全局功能， <code>shapesProvider</code>作为数据仓驱动konva的图形组件。

为了优化性能，我们分了三个图层：
- common-layer: <code>shapesProvider</code>中的shapes都会渲染在这个分层里面，拥有内置的一些功能比如undo/redo，transform
- draw-layer: 编辑器模式为画板模式时，绘制的线条(包含橡皮擦线条)将绘制在这个分层上
- performance-layer: 通常渲染选中的图形，也就是有transform能力的图形。原生创建的一些业务图形也可以放这


## 定义

### EditorComponetsProps

| 属性          | 类型                    | 描述                                               | 默认值             |
| ------------- | ----------------------- | -------------------------------------------------- | ------------------ |
| zoomAlgorithm | 'mouse' \| 'center'     | 缩放算法 mouse: 鼠标位置不变，center: 画布中心不变 | "mouse"            |
| maxZoom       | number                  | 最大缩放系数                                       | 2                  |
| minZoom       | number                  | 最小缩放系数                                       | 0.2                |
| responsive    | boolean                 | 画布是否为响应式                                   | true               |
| width         | number                  | 画布宽度                                           | window.innerwidth  |
| height        | number                  | 画布高度                                           | window.innerheight |
| containerRef  | RefObject\<HTMLElemet\> | 容器ref                                            | undefined          |


### GlobalContextType

| 属性                                     | 类型                                          | 描述                                                                                       | 默认值          |
| ---------------------------------------- | --------------------------------------------- | ------------------------------------------------------------------------------------------ | --------------- |
| stageRef                                 | RefObject<Konva.Stage>                        | 舞台的引用                                                                                 | {current: null} |
| layerRef                                 | RefObject<Konva.Layer>                        | 图层的引用                                                                                 | {current: null} |
| changeCursor                             | (cursor: string) => void                      | 更改鼠标样式                                                                               |                 |
| setIsOpenMouseZoom                       | (isOpen: boolean) => void                     | 是否开启鼠标缩放画布                                                                       |                 |
| canvasSize                               | {width: number, height: number}               | 画布尺寸                                                                                   |                 |
| setIsAllShapesSelectable                 | (can: boolean) => void                        | 所有图形是否可以选中                                                                       |                 |
| setIsAllShapesDraggable                  | （can: boolean) => void                       | 所有图形是否可以拖拽                                                                       |                 |
| isAllShapesDraggable                     | boolean                                       | 当前全局图形是否可以拖拽                                                                   | true            |
| canvasMode                               | "pen" \| "origin" \| "eraser"                 | 当前画布模式                                                                               |                 |
| changeCanvasMode                         | (mode: "pen" \| "origin" \| "eraser") => void | 设置画布模式                                                                               |                 |
| setLineProperty                          | (lineConfig: Konva.LineConfig)                | 绘线的样式配置等                                                                           |                 |
| setCanDynamicChangeCanvasModeByDrawScope | （can: boolean) => void                       | 是否开启根据锁定的绘画区域来改变画板模式，比如说当鼠标超出绘画区域，画板模式会变成"origin" |                 |


### ShapesProvider

| 属性                       | 类型                                                                   | 描述             | 默认值 |     |
| -------------------------- | ---------------------------------------------------------------------- | ---------------- | ------ | --- |
| shapes                     | Konva.ShapeConfig[]                                                    | 图形数据仓       | []     |     |
| addShapes                  | (config: ShapeConfig \| ShapeConfig[], options?:OptionsType)=> string; | 增加图形         |        |     |
| updateShapes               | (config: ShapeConfig, options?:{saveHistory: boolean})=> string;                  | 更新图形         |        |     |
| removeShape                | (id: string, options?:{saveHistory: boolean})=> string;                | 删除图形         |        |     |
| removeAllShapes            | (options?: {saveHistory: boolean})                                     | 删除所有图形     |        |     |
| canUndo                    | boolean                                                                | 是否可以undo     |        |     |
| canRedo                    | boolean                                                                | 是否可以redo     |        |     |
| undo                       | () => void                                                             | undo             |        |     |
| redo                       | () => void                                                             | redo             |        |     |
| resetHistory               | (shapes?: ShapeConfig[]) =>   void                                     | 重置历史记录堆栈 |        |     |
| setCanUseUndoRedoKeyboards | (can: boolean) => void                                                 | 是否启用undoredo键盘事件                 |        |     |

#### optionsType

```ts
{
	groupId?: string; // 将元素添加到哪一个Konva.Group中
	saveHistory?: boolean; // 是否保存到历史记录栈中
	isInitial?: boolean; // 是不是初始化， resetHistory会复原成该时刻的快照
}
```




## 工具 （待增加用法）

- adaptiveImageByContainer 图片适配容器
- centerObjectPosition 目标元素在容器元素中心的x,y
- fitBouds: 目标元素位于容器元素中心时容器的x,y，scale
- viewportToCanvasBox 视口转画布
- canvasToViewportBox  画布转视口
- getCenterPosition 获取中心点xy
- getStore 获取上下文变量的一个转换函数
- isBBoxContainsByBox ，isBBoxContains 容器元素是否包含目标元素
- isContainPoint， 点是否在容器内
- useHtmlCover 自定义div覆盖canvas元素
- useGlobalStoreRefSubscription，useShapesRefSubscription  订阅provider中变量的change

## 使用

###  如何新建画布

```tsx
import { type EditorRefType, Editor } from '@/components';

const EditorUsage = () => {
	// 容器ref - resize
	const ref = useRef(null);
	const editorRef = useRef<EditorRefType>(null);

	return (
		<div ref={ref} className={styles.canvas}>
			<Editor
				containerRef={ref}
				ref={editorRef}
			>
				{/* 放置konva图形(自定义的，所以不拥有undo/redo等功能) */}
			</Editor>
		</div>)
}
```


### 如何增删改shapes

```tsx
import { type EditorRefType, Editor,getStore } from '@/components';
...
const editorRef = useRef<EditorRefType>(null);
const [id, setId] = useState("")

const addRect = () => {
	// getStore是暴露的一个utils 方便根据ref获取整个provider
	// 也可以直接通过ref获取， 比如 editorRef.current?.shapesRef.current?.addShapes
	const { addShapes } = getStore(editorRef) ?? {};

	const id = addShapes?.({
		type: 'rect',
		name: "customname"
		width: 100,
		height: 100,
		fill: 'red'
	});
	setId(id)
}

const removeRect = () => {
	const { removeShape } = getStore(editorRef) ?? {};
	removeShape?.(id)
}

const removeRectByName = () => {
	const { removeShape,stageRef } = getStore(editorRef) ?? {};
	const node = stageRef.findOne(".customname")

	removeShape?.(node?.id)
}

const updateRect = () => {
	const { updateShape } = getStore(editorRef) ?? {};
	updateShape?.({id,x:10})
}
...
```

### 如何将指定元素位于屏幕正中间

```tsx
import { useGlobalStoreRefSubscription, fitBoundsByStage } from '@/components';

...

const [canvasSize, setCanvasSize] = useState<GlobalCtxType['canvasSize']>(null);

// 监听
useGlobalStoreRefSubscription(editorRef, (v) => {
	setCanvasSize(v.canvasSize);
});

useEffect(() => {
	fitBoundsByStage({
		editorRef,
		targetBBoxOrName: {x: 0, y: 0, width: 100, height: 100},
		padding: { left: 25, right: 25, top: 25, bottom: 25 }
	})
}, [canvasSize]);
...

```


### 如何变成画板模式

```tsx
...

useMount(() => {
	const { changeCanvasMode, setLineProperty } = getStore() ?? {}

	changeCanvasMode?.('pen');
	// 设置线的参数
	setLineProperty?.((prev) => ({
		...prev,
		stroke: '#00E0FF',
		strokeWidth: 40
	}));
})



...
```

### 如何指定一个画画区域

```tsx
import { EditorRefType, getStore, DRAW_SCOPE_ID } from '@/components';
..
const initShape = () => {
	const { addShapes,setCanDynamicChangeCanvasModeByDrawScope } = getStore() ?? {}
	// 必须打开动态监听
	setCanDynamicChangeCanvasModeByDrawScope(true)

	const addShapes?.(
		{
		id: DRAW_SCOPE_ID, // 这是标识位，只有在该区域中才能绘制，并且挂载的元素不能将listening 置为false
		type: 'image',
		name: imageContainerName,
		src,
		fill: 'hsl(0, 0%, 90%)',
		isSelected: false,
		draggable: false,
		perfectDrawEnabled: false,
		...shapesBBox
		}
	);

}


...

```

### 如何实现undo redo功能

```tsx

...
// 默认 addshapes, updateShape，removeShape，removeAllShapes 都会保存到历史记录栈中

const [canUndo, setCanUndo] = useState(false);

const [canRedo, setCanRedo] = useState(false);

useShapesRefSubscription(editorRef, (v) => {
	setCanUndo(v.canUndo);
	setCanRedo(v.canRedo);
});
const undo = () => {
	const {undo} = getStore ?? {}
	undo?.()
}

const redo = () => {
	const {redo} = getStore ?? {}
	redo?.()
}

...

```


...