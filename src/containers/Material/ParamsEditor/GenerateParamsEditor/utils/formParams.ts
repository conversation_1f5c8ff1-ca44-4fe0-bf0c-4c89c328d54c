import { CreateMaterialGenerateTaskParams } from '@/api/types/aiMaterial';
import { InputMode, PromptInputValue } from '../components/PromptInput';
import { EditorParams } from '../types';
import { deserializeValueList, fillTemplate } from './templateFill';
import { StyleModelConfigType } from '@/api/types';

export function toRequestParams(
  values: EditorParams
): Omit<CreateMaterialGenerateTaskParams, 'width' | 'height'> {
  const { prompt, styleModel, quantity } = values;

  function getPrompt(): { prompt: string; userPrompt?: string } {
    switch (prompt.mode) {
      case InputMode.Free: {
        return { prompt: prompt.value };
      }
      case InputMode.Fill: {
        const { result, valueListStr } = fillTemplate({
          template: prompt.template,
          edge: ['(', ')'],
          values: prompt.valueList,
          ignoreList: styleModel?.blackPrompt?.split(',') ?? []
        });

        return {
          prompt: result,
          userPrompt: valueListStr
        };
      }
    }
  }

  return {
    ...getPrompt(),
    styleModelConfig: [
      {
        styleModelId: styleModel?.id,
        styleModelCategories: styleModel?.categoryIds,
        styleModelWeight: styleModel?.styleModelWeight
      }
    ],
    batchSize: quantity
  };
}

export function toFormValues(
  params: CreateMaterialGenerateTaskParams
): EditorParams {
  const { prompt, userPrompt, styleModelConfig, batchSize } = params;
  const styleModel = styleModelConfig[0] as StyleModelConfigType & {
    blackPrompt: string;
    defaultPrompt: string;
  };

  function getPrompt(): PromptInputValue {
    if (!userPrompt) {
      return {
        mode: InputMode.Free,
        value: prompt
      };
    }

    return {
      mode: InputMode.Fill,
      template: styleModel.defaultPrompt,
      valueList: deserializeValueList(userPrompt)
    };
  }

  return {
    prompt: getPrompt(),
    styleModel: {
      id: styleModel.styleModelId,
      categoryIds: styleModel.styleModelCategories,
      styleModelWeight: styleModel.styleModelWeight,
      defaultPrompt: styleModel.defaultPrompt,
      blackPrompt: styleModel.blackPrompt
    },
    quantity: batchSize
  };
}
