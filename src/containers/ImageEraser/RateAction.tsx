import CompRateAction from '@/components/RateAction';
import { useImageEraserOperator } from './OperatorProvider';
import { ImageStatus, RateType } from '@/api/types';
import { motion } from 'framer-motion';
import styles from './index.module.less';

export interface RateActionProps {}

export const RateActions = (props: RateActionProps) => {
  const { currentSatisfactionScore, updateCurrentSatisfactionScore } =
    useImageEraserOperator();

  const isShowRate =
    !currentSatisfactionScore?.hadSatisfied &&
    currentSatisfactionScore?.imageStatus === ImageStatus.NORMAL;

  if (!currentSatisfactionScore) return null;

  return (
    <motion.div
      key="rate-action"
      initial={{ bottom: -150 }}
      animate={{ bottom: 24 }}
      exit={{ bottom: -150 }}
      className={styles.rateContainer}
    >
      <CompRateAction
        activeGraphSrc={currentSatisfactionScore?.url}
        taskId={currentSatisfactionScore.taskId}
        onSuccess={() => {
          updateCurrentSatisfactionScore();
        }}
        rateType={RateType.Eraser}
        showRate={isShowRate}
      />
    </motion.div>
  );
};
