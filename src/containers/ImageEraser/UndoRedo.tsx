import { App, Flex } from 'antd';
import styles from './index.module.less';
import {
  CompareBold,
  RedoBold,
  RefreshBold,
  UndoBold
} from '@meitu/candy-icons';
import { Button, DRAW_SCOPE_ID } from '@/components';
import { useImageEraserOperator } from './OperatorProvider';
import classNames from 'classnames';
import { useCallback } from 'react';
import { useImageEraser } from '@/hooks/useImageEraser';
import Konva from 'konva';
import { useLongPress } from 'react-use';

export interface UndoRedoProps {
  disabled: boolean;
}

export const UndoRedo = ({ disabled }: UndoRedoProps) => {
  const {
    canRedo,
    canUndo,
    undo,
    redo,
    resetHistory,
    resetSatisfactionScoreList,
    showCompareCover
  } = useImageEraserOperator();

  const { setImageEraserStore } = useImageEraser();

  const { modal } = App.useApp();

  const getIconClass = useCallback((disabled: boolean) => {
    return classNames(styles.font, { [styles.fontDisabled]: disabled });
  }, []);

  const changeTaskId = (res: ReturnType<typeof undo>) => {
    if (!res) return;

    const nodeConfig = res.find(
      (shape) => shape.id === DRAW_SCOPE_ID
    ) as Konva.ImageConfig;

    setImageEraserStore({
      taskId: nodeConfig?.taskId
    });
  };

  const {
    onMouseUp: _onMouseUp,
    onTouchEnd: _onTouchEnd,
    ...longPressEvent
  } = useLongPress(
    () => {
      showCompareCover(true);
    },
    {
      isPreventDefault: true,
      delay: 150
    }
  );

  const onMouseUp = () => {
    showCompareCover(false);
    _onMouseUp();
  };

  const onTouchEnd = () => {
    showCompareCover(false);
    _onTouchEnd();
  };

  const fixturesEvent = { ...longPressEvent, onMouseUp, onTouchEnd };

  return (
    <Flex align="center" className={styles.undoRedo} gap={4}>
      <Button
        type="text"
        disabled={!canUndo || disabled}
        icon={<UndoBold className={getIconClass(!canUndo || disabled)} />}
        onClick={() => {
          const res = undo();
          changeTaskId(res);
        }}
      />
      <Button
        type="text"
        disabled={!canRedo || disabled}
        icon={<RedoBold className={getIconClass(!canRedo || disabled)} />}
        onClick={() => {
          const res = redo();
          changeTaskId(res);
        }}
      />
      <Button
        disabled={(!canRedo && !canUndo) || disabled}
        type="text"
        icon={
          <RefreshBold
            className={getIconClass((!canRedo && !canUndo) || disabled)}
          />
        }
        onClick={() => {
          modal.confirm({
            title: '提示',
            content: '重置会清空当前图片操作，回到初始状态，是否重置？',
            okText: '重置',
            cancelText: '取消',
            onOk: () => {
              resetHistory();
              resetSatisfactionScoreList();
              setImageEraserStore({ taskId: undefined });
            }
          });
        }}
      />

      <Button
        disabled={(!canRedo && !canUndo) || disabled}
        type="text"
        icon={
          <CompareBold
            className={getIconClass((!canRedo && !canUndo) || disabled)}
          />
        }
        {...fixturesEvent}
      />
    </Flex>
  );
};
