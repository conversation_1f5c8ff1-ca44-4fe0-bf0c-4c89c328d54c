import {
  DRAW_SCOPE_ID,
  EditorRefType,
  getStore,
  isContainPoint,
  useGlobalStoreRefSubscription
} from '@/components';
import { CSSProperties, useEffect, useState } from 'react';

export interface CustomCursorProps {
  editorRef: React.RefObject<EditorRefType | null>;
}

export const CustomCursor = ({ editorRef }: CustomCursorProps) => {
  const [style, setStyle] = useState<CSSProperties>({
    top: 0,
    left: 0,
    opacity: 0,
    width: '24px',
    height: '24px'
  });

  useGlobalStoreRefSubscription(editorRef, (store) => {
    if (store.canvasMode === 'origin') {
      showCursor(false);
    }

    setStyle((prev) => ({
      ...prev,
      width: store.lineProperty.strokeWidth,
      height: store.lineProperty.strokeWidth
    }));
  });

  const showCursor = (visible: boolean) =>
    setStyle((prev) => ({ ...prev, opacity: Number(visible) }));

  useEffect(() => {
    const store = getStore(editorRef);

    if (!store) return;

    const { stageRef, changeCursor, isDrawingRef } = store;

    if (!stageRef?.current) return;

    stageRef.current.off('mousemove.cursor');

    stageRef.current?.on('mousemove.cursor', (e) => {
      requestAnimationFrame(() => {
        const stage = e.target.getStage();
        const node = stage?.findOne(`#${DRAW_SCOPE_ID}`);

        const absPoint = stage?.getPointerPosition();
        if (!absPoint || !node) return;
        setStyle((prev) => ({ ...prev, top: absPoint.y, left: absPoint.x }));

        const isInsideDrawBox = isContainPoint(
          e.target.getRelativePointerPosition(),
          node?.getAttrs()
        );
        if (isInsideDrawBox) {
          changeCursor('none');
          showCursor(true);
        } else if (!isDrawingRef.current) {
          changeCursor('grab');
          showCursor(false);
        }
      });
    });

    return () => {
      stageRef.current?.off('mousemove.cursor');
    };
  }, [editorRef]);

  return (
    <div
      className="customCursor"
      style={{
        ...style,
        position: 'absolute',
        pointerEvents: 'none',
        zIndex: 1000,
        borderRadius: '50%',
        backgroundColor: 'rgba(103, 118, 255, 0.5)',
        border: '2.5px solid #fff',
        transform: 'translate3d(-50%, -50%, 0)',
        willChange: 'transform',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}
    >
      <div
        style={{
          width: '3px',
          height: '3px',
          borderRadius: '50%',
          backgroundColor: '#fff'
        }}
      ></div>
    </div>
  );
};
