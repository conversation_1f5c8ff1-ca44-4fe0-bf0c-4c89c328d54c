import { fetchImageEraserConfig } from '@/api/editor';
import { UserAwardModal, UserAwardModalRef } from '@/components/UserAwardTip';
import { useEffect, useRef } from 'react';

export interface AwardProps {}

export const Award = (props: AwardProps) => {
  const ref = useRef<UserAwardModalRef>(null);

  useEffect(() => {
    fetchImageEraserConfig().then((res) => {
      if (res.tip.show) {
        ref.current?.open({ title: res.tip.title });
      }
    });
  }, []);

  return <UserAwardModal ref={ref} />;
};
