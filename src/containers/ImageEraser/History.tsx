import { HistoryModal, HistoryModalRef } from '@/components/HistoryModal';
import { Actions } from '@/components/ImagesContainer/Actions';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import classNames from 'classnames';
import { useState, useRef, useEffect } from 'react';
import styles from './index.module.less';
import { fetchImageEraserHistory } from '@/api/imageEraser';
import { ImageEraserQueryResponse } from '@/api/types/imageEraser';
import { useImageEraser } from '@/hooks/useImageEraser';
import { TaskStatus } from '@/api/types';
import { StepTypeV2 } from '@/types/imageEditor';
import { trackEvent } from '@/services';
import { AppModuleParam } from '@/types';

export interface HistoryProps {
  onIconClick?: () => void;
}

export const History = ({ onIconClick }: HistoryProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [cursor, setCursor] = useState('');
  const [historyList, setHistoryList] = useState<ImageEraserQueryResponse[]>(
    []
  );
  const modalRef = useRef<HistoryModalRef>(null);

  const { taskId, goEditorStepBySrc, step } = useImageEraser();

  useEffect(() => {
    if (step === StepTypeV2.Upload) {
      getHistoryList(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [step]);

  const getHistoryList = async (isReset?: boolean) => {
    try {
      const res = await fetchImageEraserHistory({
        cursor: isReset ? '' : cursor,
        count: '30'
      });
      if (isReset) {
        setHistoryList(res.list);
      } else {
        setHistoryList((prev) => [...prev, ...res.list]);
      }
      setCursor(res.cursor);
    } catch (err) {
      defaultErrorHandler(err);
    }
  };

  return (
    <>
      <Actions.HistoryButton
        onClick={() => {
          trackEvent('whee_edit_page_click', {
            clickType: 'history',
            function: AppModuleParam.ImageEraser
          });
          onIconClick?.();
          if (isOpen) {
            setIsOpen(false);
            return;
          }

          setIsOpen(true);
          if (!historyList.length) {
            setHistoryList([]);
            setCursor('');
            getHistoryList(true);
          }
        }}
        className={classNames({
          [styles.activeIcon]: isOpen
        })}
      />

      <HistoryModal
        ref={modalRef}
        openModal={isOpen}
        historyList={historyList}
        onCancel={() => {
          setIsOpen(false);
        }}
        hasMore={cursor !== ''}
        getHistoryList={getHistoryList}
        historyItemClicked={(res) => {
          goEditorStepBySrc({
            src: res.resultImages[0].url,
            taskId: res.id,
            taskStatus: TaskStatus.SUCCESS
          });
        }}
        taskId={taskId}
      />
    </>
  );
};
