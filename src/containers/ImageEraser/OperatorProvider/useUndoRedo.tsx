import { EditorRefType, useShapesRefSubscription } from '@/components';
import { useState } from 'react';
import { useResizeHandler } from './useResizeHandler';

export type useUndoRedoType = {
  editorRef: React.RefObject<EditorRefType | null>;
  onResize: ReturnType<typeof useResizeHandler>['onResize'];
};

export const useUndoRedo = ({ editorRef, onResize }: useUndoRedoType) => {
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [canSubmit, setCanSubmit] = useState(false);

  useShapesRefSubscription(editorRef, (v) => {
    setCanUndo(v.canUndo);
    setCanRedo(v.canRedo);

    setCanSubmit(v.shapes.some((shape) => shape.type === 'line'));
  });

  const resetHistory = () => {
    onResize();

    editorRef.current?.shapesRef?.current?.resetHistory?.();
  };

  return {
    canUndo,
    canRedo,
    undo: () => editorRef.current?.shapesRef?.current?.undo?.(),
    redo: () => editorRef.current?.shapesRef?.current?.redo?.(),
    resetHistory,
    canSubmit
  };
};
