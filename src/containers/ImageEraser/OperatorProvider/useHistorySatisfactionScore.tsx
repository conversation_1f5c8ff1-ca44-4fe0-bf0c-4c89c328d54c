import { ImageEraserQueryResponse } from '@/api/types/imageEraser';
import { useImageEraser } from '@/hooks/useImageEraser';
import { useList } from 'react-use';

/**
 * 每当轮询到结果的时候会 pushSatisfactionScoreList 将当前结果塞进list里面
 * taskId 是主键
 * taskId 变化 就会引起  currentSatisfactionScore 的变化
 * taskId变化的场景有 1.点击立即生成的时候 2.undo redo的时候
 */
export const useHistorySatisfactionScore = () => {
  const [
    satisfactionScoreList,
    {
      push: pushSatisfactionScoreList,
      updateFirst,

      reset: resetSatisfactionScoreList
    }
  ] = useList<
    ImageEraserQueryResponse['resultImages'][number] & { taskId: string }
  >([]);

  const { taskId } = useImageEraser();

  const currentSatisfactionScore = satisfactionScoreList.find(
    (item) => item.taskId === taskId
  );

  const updateCurrentSatisfactionScore = () => {
    if (!currentSatisfactionScore) return;

    updateFirst((item) => item.taskId === taskId, {
      ...currentSatisfactionScore,
      hadSatisfied: true
    });
  };

  return {
    pushSatisfactionScoreList,
    currentSatisfactionScore,
    updateCurrentSatisfactionScore,
    resetSatisfactionScoreList
  };
};
