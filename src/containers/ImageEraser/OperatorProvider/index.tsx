import { initialContextMethod } from '@/utils/initialContextWarning';
import { createContext, useContext, PropsWithChildren } from 'react';
import { useResizeHandler } from './useResizeHandler';
import { useOperateCanvas } from './useOperateCanvas';
import { useUndoRedo } from './useUndoRedo';
import { EditorRefType } from '@/components';
import { useLoading } from './useLoading';
import { useHistorySatisfactionScore } from './useHistorySatisfactionScore';

const initialCtxWarning = initialContextMethod.bind(
  null,
  'imageExtendsOperatorCtx'
);

export type ImageEraserOperatorType = ReturnType<typeof useResizeHandler> &
  ReturnType<typeof useOperateCanvas> &
  ReturnType<typeof useUndoRedo> &
  ReturnType<typeof useLoading> &
  ReturnType<typeof useHistorySatisfactionScore>;

export const ImageEraserOperatorCtx = createContext<ImageEraserOperatorType>({
  onResize: () => undefined,
  initShapes: async () => {},
  canUndo: false,
  canRedo: false,
  undo: initialCtxWarning,
  redo: initialCtxWarning,
  resetHistory: initialCtxWarning,
  getMaskInfo: async () => undefined,
  canvasSize: null,
  canSubmit: false,
  startAnimation: () => {},
  stopAnimation: () => {},
  updateImage: () => {},
  pushSatisfactionScoreList: () => {},
  currentSatisfactionScore: undefined,
  updateCurrentSatisfactionScore: () => {},
  resetSatisfactionScoreList: () => {},
  showCompareCover: () => {},
  updatePenSize: () => {}
});

export type ImageEraserOperatorProviderType = {
  editorRef: React.RefObject<EditorRefType | null>;
};

export const ImageEraserOperator = ({
  editorRef,
  children,
  ...restProps
}: PropsWithChildren<ImageEraserOperatorProviderType>) => {
  const resizeHandlerStore = useResizeHandler(editorRef);

  const loadingStore = useLoading({ editorRef });

  const undoRedoStore = useUndoRedo({
    editorRef,
    onResize: resizeHandlerStore.onResize
  });

  const operateCanvasStore = useOperateCanvas({
    editorRef,
    onResize: resizeHandlerStore.onResize
  });

  const historySatisfactionScoreStore = useHistorySatisfactionScore();

  return (
    <ImageEraserOperatorCtx.Provider
      value={{
        ...resizeHandlerStore,
        ...operateCanvasStore,
        ...undoRedoStore,
        ...loadingStore,
        ...historySatisfactionScoreStore
      }}
      {...restProps}
    >
      {!!resizeHandlerStore.canvasSize && children}
    </ImageEraserOperatorCtx.Provider>
  );
};

export const useImageEraserOperator = () => useContext(ImageEraserOperatorCtx);
