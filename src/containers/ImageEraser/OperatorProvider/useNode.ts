import { EditorRefType } from '@/components';
import { RefObject } from 'react';
import { IMAGE_NODE_NAME } from './constants';
import Konva from 'konva';

export const useNode = (editorRef: RefObject<EditorRefType | null>) => {
  const findImage = () => {
    return editorRef.current?.globalRef.current?.stageRef.current?.findOne(
      `.${IMAGE_NODE_NAME}`
    ) as Konva.Image | undefined;
  };

  const findStage = () => {
    return editorRef.current?.globalRef.current?.stageRef.current;
  };

  const findDrawLayer = () => {
    return editorRef.current?.globalRef.current?.stageRef.current?.findOne(
      '.draw-layer'
    ) as Konva.Layer | undefined;
  };

  const findDrawGroup = () => {
    return editorRef.current?.globalRef.current?.stageRef.current?.findOne(
      '.draw-group'
    ) as Konva.Group | undefined;
  };

  const findCommonLayer = () => {
    return editorRef.current?.globalRef.current?.stageRef.current?.findOne(
      '.common-layer'
    ) as Konva.Layer | undefined;
  };

  return {
    findImage,
    findStage,
    findDrawLayer,
    findDrawGroup,
    findCommonLayer
  };
};
