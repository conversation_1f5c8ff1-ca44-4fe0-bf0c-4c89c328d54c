import { EditorRefType, getStore } from '@/components';
import { useRef } from 'react';
import Konva from 'konva';
import { useNode } from './useNode';

export type UseLoadingType = {
  editorRef: React.RefObject<EditorRefType | null>;
};

const START_COLOR = 'white';
const END_COLOR = 'rgb(103, 118, 255)';

export const useLoading = ({ editorRef }: UseLoadingType) => {
  // 动画实例
  const ref = useRef<Konva.Animation>();
  // loading 区域实例
  const loadingRef = useRef<Konva.Rect>();
  // mask 区域实例
  const maskRef = useRef<Konva.Image>();
  // cloneDrawGroup
  const cloneDrawGroupRef = useRef<Konva.Group>();

  const { findImage, findDrawGroup, findCommonLayer, findDrawLayer } =
    useNode(editorRef);

  const lock = (isLock: boolean) => {
    const store = getStore(editorRef);
    if (!store) return;

    const {
      stageRef,
      setCanDynamicChangeCanvasModeByDrawScope,
      setCanUseUndoRedoKeyboards,
      changeCanvasMode,
      setIsOpenMouseZoom
    } = store;

    if (isLock) {
      changeCanvasMode('origin');
      setCanUseUndoRedoKeyboards(false);
      setCanDynamicChangeCanvasModeByDrawScope(false);
      setIsOpenMouseZoom(false);
      stageRef.current?.draggable(false);
      stageRef.current?.content.children[1]?.classList.remove('drawing-layer');
    } else {
      changeCanvasMode('pen');
      setCanUseUndoRedoKeyboards(true);
      setCanDynamicChangeCanvasModeByDrawScope(true);
      setIsOpenMouseZoom(true);
      stageRef.current?.content.children[1]?.classList.add('drawing-layer');
    }
  };

  const startAnimation = () => {
    const store = getStore(editorRef);

    if (!store || ref.current?.isRunning()) return;

    lock(true);

    const drawGroupNode = findDrawGroup();
    const drawLayer = findDrawLayer();
    const imageNode = findImage();

    if (!drawGroupNode || !imageNode) return;

    cloneDrawGroupRef.current = drawGroupNode.clone();
    cloneDrawGroupRef.current.addName('copy');
    drawLayer?.add(cloneDrawGroupRef.current);
    const lines = cloneDrawGroupRef.current.getChildren(
      (node) => node.getAttrs()?.type === 'line'
    );

    lines.forEach((l) => l.globalCompositeOperation('destination-out'));

    drawGroupNode.visible(false);

    // 涂抹路径图层
    maskRef.current = new Konva.Image({ ...imageNode.attrs, name: 'mask' });
    // 将遮罩图片置于涂抹区域下方
    cloneDrawGroupRef.current.add(maskRef.current);
    maskRef.current.moveToBottom();

    // 原始图像图层
    const commonLayerNode = findCommonLayer();

    if (!commonLayerNode) return;

    if (!loadingRef.current) {
      // loading 的背景 Rect
      loadingRef.current = new Konva.Rect({
        width: imageNode.width(),
        height: imageNode.height(),
        x: imageNode.x(),
        y: imageNode.y(),
        opacity: 0.5,
        fillLinearGradientStartPoint: { x: 0, y: 0 },
        fillLinearGradientEndPoint: {
          x: imageNode.width(),
          y: 0
        },
        fillLinearGradientColorStops: [0, START_COLOR, 1, END_COLOR]
      });

      // 将 loading 背景置于图片上方
      commonLayerNode.add(loadingRef.current);
    } else {
      loadingRef.current.visible(true);
    }

    // 添加动画效果
    ref.current = new Konva.Animation((frame) => {
      if (!frame?.time) return false;

      const x = (Math.sin(frame.time / 300) + 1) / 2;
      loadingRef.current?.fillLinearGradientColorStops([
        x > 0.5 ? x - 0.5 : 0,
        END_COLOR,
        x,
        START_COLOR,
        x > 0.5 ? 1 : x + 0.5,
        END_COLOR
      ]);
    }, findCommonLayer);

    // 启动动画
    ref.current?.start();
  };

  const stopAnimation = (cb: () => void) => {
    const drawGroupNode = findDrawGroup();
    const store = getStore(editorRef);

    if (!drawGroupNode || !store) return;

    // drawGroupNode.visible(false);
    // 停止动画
    ref.current?.stop();
    // 隐藏 loading 背景
    loadingRef.current?.destroy();
    loadingRef.current = undefined;

    // 隐藏 mask 图片
    maskRef.current?.destroy();
    maskRef.current = undefined;
    cloneDrawGroupRef.current?.destroy();
    cloneDrawGroupRef.current = undefined;

    cb();
    drawGroupNode.visible(true);

    lock(false);
  };

  return {
    startAnimation,
    stopAnimation
  };
};
