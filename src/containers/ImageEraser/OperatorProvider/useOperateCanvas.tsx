import { useResize<PERSON><PERSON><PERSON> } from './useResizeHandler';
import { EditorRefType, getStore, DRAW_SCOPE_ID } from '@/components';
import Konva from 'konva';
import { useUploader } from '@/hooks';
import { defaultUploadParam } from '@/constants/uploadParams';
import { useImageEraser } from '@/hooks/useImageEraser';
import { getImageDimensions } from '@/utils/blob';
import { adaptiveImageByContainer } from '../../../components/Editor/utils/adaptiveImage';
import { IMAGE_NODE_NAME, ORIGIN_IMAGE_NODE_NAME } from './constants';
import { getSamplePoints, normalizationPoints } from '@/utils/normalization';
import { ImageEraserDoRequest } from '@/api/types/imageEraser';

export type useOperateCanvasType = {
  editorRef: React.RefObject<EditorRefType | null>;
  onResize: ReturnType<typeof useResizeHandler>['onResize'];
};

export const useOperateCanvas = ({
  editorRef,
  onResize
}: useOperateCanvasType) => {
  const uploader = useUploader();

  const { originImageDimensions, src, setImageEraserStore, taskId } =
    useImageEraser();

  const initShapes = async () => {
    Konva.pixelRatio = 2;
    if (!src) return;
    try {
      const store = getStore(editorRef);
      if (!store) return;

      const {
        stageRef,
        removeAllShapes,
        setIsOpenMouseZoom,
        changeCanvasMode,
        addShapes,
        setCanDynamicChangeCanvasModeByDrawScope,
        setLineProperty
      } = store;

      if (!stageRef.current) return;
      // 设置笔刷样式
      setLineProperty((prev) => ({
        ...prev,
        type: 'line',
        strokeWidth: 24,
        stroke: 'rgba(103, 118, 255)'
      }));
      setIsOpenMouseZoom(true);
      setCanDynamicChangeCanvasModeByDrawScope(true);
      changeCanvasMode('pen');
      removeAllShapes({ saveHistory: false });

      // HACK 避免有交点的线 重合区域颜色加深
      stageRef.current.content.children[1].className = 'drawing-layer';

      const dimensions = await getImageDimensions(src);
      setImageEraserStore({ originImageDimensions: dimensions });

      const containerWidth = stageRef?.current?.width();
      const containerHeight = stageRef?.current?.height();
      if (!containerWidth || !containerHeight) return;

      const fixturesDimensions = adaptiveImageByContainer(
        { width: containerWidth, height: containerHeight },
        dimensions.ratio,
        containerWidth / containerHeight
      );

      const shapesBBox = { x: 0, y: 0, ...fixturesDimensions };

      onResize(shapesBBox);

      addShapes?.(
        [
          {
            id: DRAW_SCOPE_ID,
            type: 'image',
            taskId,
            canCache: false,
            name: IMAGE_NODE_NAME,
            src,
            fill: 'hsl(0, 0%, 90%)',
            isSelected: false,
            draggable: false,
            ...shapesBBox
          },
          {
            name: ORIGIN_IMAGE_NODE_NAME,
            type: 'image',
            canCache: false,
            src,
            fill: 'hsl(0, 0%, 90%)',
            isSelected: false,
            draggable: false,
            visible: false,
            ...shapesBBox
          }
        ],
        { saveHistory: false, isInitial: true }
      );
    } catch (error) {
      console.log(error);
    }
  };

  const getMaskInfo = async (): Promise<ImageEraserDoRequest | undefined> => {
    const store = getStore(editorRef);
    if (!store) return;

    const { stageRef } = store;

    const clonedStage = stageRef.current?.clone() as Konva.Stage;

    if (!clonedStage) return;

    // 重置stage
    clonedStage.scale({ x: 1, y: 1 });
    clonedStage.x(0);
    clonedStage.y(0);

    const imageNode = clonedStage.findOne(`.${IMAGE_NODE_NAME}`);
    const drawGroup = clonedStage.findOne(`.draw-group`);
    const tempDrawGroup = clonedStage.findOne(`.copy`) as Konva.Group;
    // 执行当前函数的时候 已经开始闪烁动画了， 会有比较多的干扰元素。 清空元素
    tempDrawGroup.destroy();
    drawGroup.visible(true);

    const layer = imageNode.getLayer();
    const imageAttrs = imageNode.getAttrs();

    const scale = originImageDimensions!.width / imageAttrs.width;

    const bgNode: Konva.Node = new Konva.Rect({ ...imageAttrs, fill: 'black' });

    layer?.add(bgNode as Konva.Rect);
    imageNode.destroy();

    const lineNodes = clonedStage.find((n: any) => n.attrs.type === 'line');

    const samplePoints: number[][] = [];

    lineNodes.forEach((node: any) => {
      if (node.visible()) {
        // 采样点
        const fixturesPoints = getSamplePoints(
          node.points(),
          node.points().length / 8 + 2
        );

        samplePoints.push(
          // 归一化
          ...normalizationPoints(
            fixturesPoints,
            imageNode.height(),
            imageNode.width()
          )
        );
      }
      node.stroke('#fff');
      node.opacity(1);
      node.moveToTop();
    });

    try {
      const blob = (await clonedStage!.toBlob({
        ...bgNode.getClientRect(),
        pixelRatio: scale
      })) as Blob;

      const { name } = blob;
      const suffix = name?.slice(name?.lastIndexOf('.')) ?? '.png';
      const res = await uploader.upload(blob, {
        ...defaultUploadParam,
        suffix
      });

      return {
        initImage: imageAttrs.src,
        maskImage: res?.previewUrl ?? '',
        clickList: samplePoints as [number, number, number][]
      };
    } catch (error) {
      console.log(error);
    } finally {
      clonedStage.destroy();
    }
  };

  const updateImage = (src: string, taskId: string) => {
    const store = getStore(editorRef);

    if (!store) return;

    store.removeShapes(
      store.shapes
        .filter((c) => c.type === 'line')
        .map((line) => line.id) as any,
      { saveHistory: false }
    );
    // 多个步骤合为一个步骤，当回退的时候就能返回上一个快照
    store.updateShape({ id: DRAW_SCOPE_ID, src, taskId });
  };

  const showCompareCover = (isShow: boolean) => {
    const { stageRef } = getStore(editorRef) ?? {};

    if (!stageRef) return;

    const originImageNode = stageRef.current?.findOne(
      `.${ORIGIN_IMAGE_NODE_NAME}`
    );
    const ImageNode = stageRef.current?.findOne(`.${IMAGE_NODE_NAME}`);
    const drawGroupNode = stageRef.current?.findOne(`.draw-group`);

    if (isShow) {
      ImageNode?.visible(false);
      drawGroupNode?.visible(false);
      originImageNode?.visible(true);
    } else {
      ImageNode?.visible(true);
      drawGroupNode?.visible(true);
      originImageNode?.visible(false);
    }
  };

  const updatePenSize = (size: number) => {
    const store = getStore(editorRef);

    store?.setLineProperty((prev) => ({ ...prev, strokeWidth: size }));
  };

  return {
    initShapes,
    getMaskInfo,
    updateImage,
    showCompareCover,
    updatePenSize
  };
};
