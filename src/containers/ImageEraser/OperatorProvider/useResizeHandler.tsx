import {
  GlobalCtxType,
  useGlobalStoreRefSubscription,
  fitBoundsByStage,
  EditorRefType,
  BBoxType
} from '@/components';
import { useEffect, useState } from 'react';
import { IMAGE_NODE_NAME } from './constants';

export const useResizeHandler = (
  editorRef: React.RefObject<EditorRefType | null>
) => {
  const [canvasSize, setCanvasSize] =
    useState<GlobalCtxType['canvasSize']>(null);

  useGlobalStoreRefSubscription(editorRef, (v) => {
    setCanvasSize(v.canvasSize);
  });

  const onResize = (shapeBBox?: BBoxType) =>
    fitBoundsByStage({
      editorRef,
      targetBBoxOrName: shapeBBox ?? IMAGE_NODE_NAME,
      padding: {
        bottom: 120,
        top: 20,
        left: 25,
        right: 25
      }
    });

  /**
   * resize canvas when canvasSize and step changed
   */
  useEffect(() => {
    onResize();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [canvasSize]);

  return {
    onResize,
    canvasSize
  };
};
