import { ErrorBoundary } from '@/components';
import { Layout } from '@/layouts';
import { StepTypeV2 } from '@/types/imageEditor';
import { useEffect, useRef, useState } from 'react';
import styles from './index.module.less';
import './index.less';
import classNames from 'classnames';
import { ImageEraserOperator } from './OperatorProvider';
import { CustomCursor } from './CustomCursor';
import { type EditorRefType, Editor } from '@/components';
import Tools from './Tools';
import { AnimatePresence, motion } from 'framer-motion';
import { useUnmount } from 'react-use';
import { Spin } from 'antd';
import { useImageEraser } from '@/hooks/useImageEraser';
import { UndoRedo } from './UndoRedo';
import { RateActions } from './RateAction';
import { History } from './History';
import { UploadBootSplash } from '@/components/UploadBootSplash';
import { Actions } from '@/components/ImagesContainer/Actions';
import { Header } from './Header';
import { Award } from './Award';
import { useAuthoringPageEnter } from '@/hooks';
import { AppModuleParam } from '@/types';
import { trackEvent } from '@/services';
import { isMobileV2 } from '@/utils/isMobile';

import { DraftType } from '@/api/types';
export interface ImagePartialRepaintProps {}

const ImageEraser = (props: ImagePartialRepaintProps) => {
  const [globalLoading, setGlobalLoading] = useState(false);
  // 在loading的时候禁止操作 undo redo 禁止操作
  const [canvasAnimateLoading, setCanvasAnimateLoading] = useState(false);

  const ref = useRef(null);
  const editorRef = useRef<EditorRefType>(null);

  const { step, initImageEraser, goEditorStepBySrc } = useImageEraser();

  const isUploadStep = step === StepTypeV2.Upload;
  const isEditStep = step === StepTypeV2.Edit;

  useAuthoringPageEnter(AppModuleParam.ImageEraser);

  useEffect(() => {
    if (step !== StepTypeV2.Edit) return;
    setCanvasAnimateLoading(false);
    setGlobalLoading(false);
  }, [step]);

  useUnmount(initImageEraser);

  return (
    <ErrorBoundary>
      <Spin spinning={globalLoading}>
        <Layout
          theme="app"
          className={classNames({
            [styles.uploaderBg]: isUploadStep,
            [styles.canvasBg]: isEditStep,
            [styles.mobile]: isMobileV2()
          })}
          header={
            <Header loading={canvasAnimateLoading} editorRef={editorRef} />
          }
        >
          <Award />

          {isUploadStep && (
            <UploadBootSplash
              title="AI无痕消除"
              description="AI生成式消除，效果无痕更自然！"
              videoSource={require('@/assets/videos/ai-eraser.mov')}
              onUploadClick={() => {
                trackEvent('whee_upload_image_click', {
                  function: AppModuleParam.ImageEraser
                });
              }}
              onUploaded={(src) => {
                trackEvent('whee_upload_image_success', {
                  function: AppModuleParam.ImageEraser
                });
                goEditorStepBySrc({ src });
              }}
              taskCategory={DraftType.AI_ERASER}
            />
          )}

          {isEditStep && (
            <>
              <div ref={ref} className={classNames(styles.canvas)}>
                <Editor
                  containerRef={ref}
                  ref={editorRef}
                  maxZoom={4}
                  minZoom={0.1}
                ></Editor>
              </div>

              <CustomCursor editorRef={editorRef} />

              <ImageEraserOperator editorRef={editorRef}>
                <AnimatePresence mode="sync">
                  <motion.div
                    key="undo"
                    initial={{ top: -250, transform: 'translateX(-50%)' }}
                    animate={{ top: -40, transform: 'translateX(-50%)' }}
                    exit={{ top: -250, transform: 'translateX(-50%)' }}
                    className={styles.toolContainer}
                  >
                    <UndoRedo disabled={canvasAnimateLoading} />
                  </motion.div>
                  <motion.div
                    key="tool"
                    initial={{ bottom: -150, transform: 'translateX(-50%)' }}
                    animate={{ bottom: 24, transform: 'translateX(-50%)' }}
                    exit={{ bottom: -150, transform: 'translateX(-50%)' }}
                    className={styles.toolContainer}
                  >
                    <Tools
                      onLoadingChange={setGlobalLoading}
                      onAnimateLoading={setCanvasAnimateLoading}
                    />
                  </motion.div>

                  <RateActions />
                </AnimatePresence>
              </ImageEraserOperator>
            </>
          )}

          <Actions type="default" className={styles.actions}>
            {isUploadStep && <History />}
          </Actions>
        </Layout>
      </Spin>
    </ErrorBoundary>
  );
};

export { ImageEraser as Component };
