import { useInterval, useMount } from 'react-use';
import { TaskStatus } from '@/api/types';
import { useImageEraser } from '@/hooks/useImageEraser';
import { queryImageEraser } from '@/api/imageEraser';
import { ImageEraserQueryResponse } from '@/api/types/imageEraser';
import { useRef } from 'react';

const stopLooperStatus = [TaskStatus.FAILURE, TaskStatus.SUCCESS];

export interface useLooperProps {
  onStartLooper?: () => void;
  onFinishLooper?: (
    isSuccess: boolean,
    img: ImageEraserQueryResponse['resultImages'][number]
  ) => void;
}

export const useLooper = ({
  onFinishLooper,
  onStartLooper
}: useLooperProps) => {
  const { taskId, taskStatus, setImageEraserStore } = useImageEraser();
  const isStartedLoopRef = useRef(false);

  const canStartPooling = taskId && !stopLooperStatus.includes(taskStatus);

  const delay = canStartPooling ? 1000 * 2 : null;

  useMount(() => {
    // 处理从历史记录入口进来时数据初始化
    if (taskId && stopLooperStatus.includes(taskStatus)) {
      polling();
    }
  });

  const polling = async () => {
    if (!taskId) return;

    if (isStartedLoopRef.current === false) {
      isStartedLoopRef.current = true;
      onStartLooper?.();
    }

    try {
      const res = await queryImageEraser({ ids: taskId });

      if (stopLooperStatus.includes(res[0]?.status)) {
        isStartedLoopRef.current = false;
        const task = res[0];
        const img = task.resultImages?.[0];
        setImageEraserStore({ taskStatus: task?.status });

        onFinishLooper?.(task?.status === TaskStatus.SUCCESS, img);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useInterval(polling, delay);

  return { loading: !!delay };
};
