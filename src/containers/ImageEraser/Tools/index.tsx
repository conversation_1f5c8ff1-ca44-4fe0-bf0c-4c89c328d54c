import { AppModuleParam, type CanvasSize } from '@/types';
import styles from './index.module.less';
import { useEffect, useRef, useState } from 'react';
import { App, Dropdown, Flex, Spin } from 'antd';

import { MeiDouPrice, MeiDouPriceRef } from '@/components/MeiDouPrice';
import { FunctionCode } from '@/api/types/meidou';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useSyncMemberDescErrorHandler } from '@/hooks/useMember';
import { useImageEraserOperator } from '../OperatorProvider';
import { MediaType, MtccFuncCode, TaskStatus } from '@/api/types';
import { LoadingCircleOutlined, PaintBold } from '@meitu/candy-icons';
import classNames from 'classnames';
import { startImageEraser } from '@/api/imageEraser';
import { useImageEraser } from '@/hooks/useImageEraser';
import { useLooper } from './useLooper';
import { MeiDouButtonV2 } from '@/components/MeiDouPrice/MeiDouButtonV2';
import { useMount } from 'react-use';
import { SliderInput } from '@/components';
import { taskCompleteTrack } from '@/utils';
import { trackEvent } from '@/services';

export interface ToolsProps {
  onLoadingChange(isLoading: boolean): void;
  onAnimateLoading(isLoading: boolean): void;
}

export interface ToolsRef {
  setCanvasSize: (size: CanvasSize) => void;
}

const Tools = ({ onLoadingChange, onAnimateLoading }: ToolsProps) => {
  const [buttonLoading, setButtonLoading] = useState(false);

  const { message } = App.useApp();

  const {
    initShapes,
    getMaskInfo,
    canSubmit,
    startAnimation,
    stopAnimation,
    updateImage,
    pushSatisfactionScoreList
  } = useImageEraserOperator();

  const meidouPriceRef = useRef<MeiDouPriceRef>(null);

  const { updateMeiDouBalance } = useMeiDouBalance({ meidouPriceRef });
  const handleError = useSyncMemberDescErrorHandler();

  const { setImageEraserStore, taskId, msgId } = useImageEraser();

  const taskIdRef = useRef(taskId);

  const isMountedRef = useRef(false);

  useEffect(() => {
    taskIdRef.current = taskId;
  });

  // 初始化
  useMount(() => {
    // taskId存在 = 从历史记录入口进来
    // 不走外面的mount初始化， 走useLooper的初始化
    if (taskId) return;

    isMountedRef.current = true;

    onLoadingChange(true);

    initShapes().finally(() => {
      onLoadingChange(false);
    });
  });

  const { loading: loopLoading } = useLooper({
    onStartLooper() {
      if (!isMountedRef.current) {
        onLoadingChange(true);
      }
    },
    onFinishLooper: (isSuccess, img) => {
      // 满意度队列

      // 历史记录入口进来 初始化
      if (!isMountedRef.current) {
        pushSatisfactionScoreList({
          ...img,
          taskId
        });

        isMountedRef.current = true;
        onLoadingChange(true);
        initShapes().finally(() => {
          onLoadingChange(false);
        });
        return;
      }

      taskCompleteTrack(isSuccess ? TaskStatus.SUCCESS : TaskStatus.FAILURE, {
        batchSize: 1
      });

      updateMeiDouBalance();

      stopAnimation(() => {
        onAnimateLoading(false);

        if (isSuccess) {
          pushSatisfactionScoreList({
            ...img,
            taskId
          });

          // 将src，taskId塞进shapesConfig 里面
          // taskId 主要处理undo redo 时候满意度评分与当前照片的对齐状态
          updateImage(img.url, taskId);
        } else {
          message.error('生成失败，美豆已退回');
          console.log('error');
        }
      });
    }
  });

  return (
    <Flex className={styles.tools} gap={10} align="center">
      <Dropdown
        placement="top"
        trigger={['click']}
        dropdownRender={() => {
          return <PenSizeControl />;
        }}
      >
        <div
          onClick={() => {
            trackEvent('whee_edit_page_click', {
              clickType: 'smear',
              function: AppModuleParam.ImageEraser
            });
          }}
          className={classNames(styles.button, styles.active)}
        >
          <PaintBold className={styles.icon} />
          涂抹
        </div>
      </Dropdown>

      <MeiDouPrice
        ref={meidouPriceRef}
        functionCode={FunctionCode.aiEraser}
        getFunctionBody={() => ({})}
      >
        {(price, fetchLoading) => (
          <MeiDouButtonV2
            price={price}
            size="large"
            disabled={
              buttonLoading || fetchLoading || loopLoading || !canSubmit
            }
            onClick={async () => {
              trackEvent('create_btn_click', {
                function: AppModuleParam.ImageEraser,
                batchSize: 1,
                freeBatchSize: price?.useFreeNum
              });

              try {
                if (buttonLoading) return;

                message.warning({
                  content: '生成中请稍后',
                  icon: <TipsLoading />
                });

                setButtonLoading(true);
                onAnimateLoading(true);

                startAnimation();
                const mask = await getMaskInfo();

                if (!mask) return;

                const res = await startImageEraser({
                  ...mask,
                  msgId: String(msgId),
                  functionName: MtccFuncCode.FuncCodeImageEraser,
                  mediaType: MediaType.Photo,
                  resMediaType: MediaType.Photo
                });

                setImageEraserStore({
                  taskId: res.id,
                  taskStatus: TaskStatus.GENERATING
                });
              } catch (error) {
                handleError(error);
                stopAnimation(() => {
                  onAnimateLoading(false);
                });
              } finally {
                updateMeiDouBalance();
                setButtonLoading(false);
              }
            }}
          />
        )}
      </MeiDouPrice>
    </Flex>
  );
};

const PenSizeControl = () => {
  const { updatePenSize } = useImageEraserOperator();
  const [size, setSize] = useState(30);
  return (
    <Flex className={styles.penSizeControl} align="center" gap={20}>
      <span>大小</span>
      <SliderInput
        className={styles.slider}
        direction="horizontal"
        min={1}
        max={100}
        step={1}
        value={size}
        onChange={(v) => {
          updatePenSize(v);
          setSize(v);
        }}
      />
    </Flex>
  );
};

const TipsLoading = () => {
  return (
    <Spin
      size="small"
      indicator={<LoadingCircleOutlined className={styles.tipsLoading} spin />}
    />
  );
};

export default Tools;
