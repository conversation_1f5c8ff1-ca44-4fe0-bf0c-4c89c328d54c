@import '~@/styles/variables.less';

.tools {
  padding: 10px;
  padding-right: 16px;
  border-radius: 12px;
  box-shadow: @level-2;
  background: @background-system-frame-floatpanel;

  .icon {
    font-size: 22px;
  }

  .button {
    cursor: pointer;
    display: flex;
    width: 60px;
    height: 60px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: 8px;

    color: @content-system-primary;
    font-size: @text-12;
    font-weight: 400;
    line-height: 16px;

    &:hover,
    &.active {
      background: @background-btn-hover;
    }
  }
}

.meidou-button {
  :global(.@{ant-prefix}-btn) {
    height: 48px !important;
  }
  position: relative;

  .icon {
    font-size: 8px;
    margin-top: -1px;
  }

  .meidou {
    z-index: 2;
    position: absolute;
    right: -4px;
    top: -4px;
    height: 14px;
    padding: 0px 4px 0px 5px;
    border-radius: 100px;
    background: #f5f5fa;
    color: @content-system-primary;
    font-size: 9px;
    font-weight: 500;
  }
}

.pen-size-control {
  min-width: 280px;
  height: 42px;
  padding: 8px 8px 8px 12px;
  border-radius: 12px;
  box-shadow: @level-2;
  background-color: @background-system-frame-floatpanel;

  color: @content-system-primary;
  font-size: @text-12;
  font-weight: 400;
  line-height: 16px;

  .slider {
    flex: 1;
  }
}

.tips-loading {
  font-size: 16;
  margin-right: 10px;
}
