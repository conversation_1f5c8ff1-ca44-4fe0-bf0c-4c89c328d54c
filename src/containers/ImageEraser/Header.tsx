import {
  Can<PERSON><PERSON>eader,
  DRAW_SCOPE_ID,
  EditorRefType,
  useShapesRefSubscription
} from '@/components';
import { KVImageConfig } from '@/components/Editor/type';
import { useGoBack } from '@/hooks';
import { useImageEraser } from '@/hooks/useImageEraser';
import { AppModule, generateRouteTo, trackEvent } from '@/services';
import { AppModuleParam } from '@/types';
import { StepTypeV2 } from '@/types/imageEditor';
import { assertUnreachable } from '@/utils/assertUnreachable';
import moment from 'moment';
import { useState } from 'react';

export interface HeaderProps {
  editorRef: React.RefObject<EditorRefType | null>;
  loading?: boolean;
}

export const Header = ({ editorRef, loading }: HeaderProps) => {
  const [currentSrc, setCurrentSrc] = useState('');

  const goBack = useGoBack(generateRouteTo(AppModule.Overview));
  const { step, goUploadStep, src, taskId } = useImageEraser();

  const onBackClick = () => {
    switch (step) {
      case StepTypeV2.Upload:
        goBack();
        break;
      case StepTypeV2.Edit:
      case StepTypeV2.Result:
      case StepTypeV2.HistoryResult:
        goUploadStep();
        break;
      default:
        assertUnreachable(step, 'unreachable step');
    }
  };

  useShapesRefSubscription(editorRef, (v) => {
    const nodeConfig = v.shapes.find(
      (shape) => shape.id === DRAW_SCOPE_ID
    ) as KVImageConfig;
    if (!nodeConfig) return;

    setCurrentSrc(nodeConfig.src ?? '');
  });

  const fixturesSrc = step === StepTypeV2.Upload ? '' : currentSrc;

  return (
    <CanvasHeader
      downloadFileName={() =>
        `whee_remover_${moment().format('YYYYMMDDHHmmss')}`
      }
      disabled={loading}
      src={fixturesSrc}
      taskId={taskId}
      onBackClick={onBackClick}
      onDownloadClick={() => {
        if (src === currentSrc) return;

        trackEvent('whee_success_results_page_click', {
          clickType: 'download',
          function: AppModuleParam.ImageEraser
        });
      }}
      onClickMore={() => {
        trackEvent('whee_edit_page_click', {
          clickType: 'more_edit',
          function: AppModuleParam.ImageEraser
        });
      }}
      onClickImgExtension={() => {
        trackEvent('whee_edit_page_click', {
          clickType: 'image_extension',
          function: AppModuleParam.ImageEraser
        });
      }}
      onClickImgGenImg={() => {
        trackEvent('whee_edit_page_click', {
          clickType: 'image_to_image',
          function: AppModuleParam.ImageEraser
        });
      }}
      onClickImgPartialRepaint={() => {
        trackEvent('whee_edit_page_click', {
          clickType: 'ai_modification',
          function: AppModuleParam.ImageEraser
        });
      }}
    ></CanvasHeader>
  );
};
