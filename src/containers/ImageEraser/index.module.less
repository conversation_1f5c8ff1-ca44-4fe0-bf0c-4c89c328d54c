@import '~@/styles/variables.less';

@primary-color: #1890ff;

.canvas-bg:global(.ant-layout) {
  background-image: radial-gradient(rgba(0, 0, 0, 0.05) 2px, transparent 0);
  background-size: 16px 16px;

  :global(.ant-layout-header) {
    background-color: transparent;
  }

  &.mobile {
    :global(.designer-layout-main) {
      margin-bottom: calc(constant(safe-area-inset-bottom) + 50px);
      margin-bottom: calc(env(safe-area-inset-bottom) + 50px);
    }
  }

  :global(.designer-layout-main) {
    overflow: inherit !important;
    position: relative;
    z-index: 3;
  }
}

.uploader-bg:global(.ant-layout) {
  background-color: @background-web-page;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 355px;
    background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0) 39.44%,
        #fff 100%
      ),
      linear-gradient(92deg, #ffeded 0.75%, #f3eaff 50.01%, #e0e3ff 99.28%);
  }

  :global(.ant-layout-header) {
    background-color: transparent;
  }

  :global(.designer-layout-main) {
    overflow: hidden !important;
    position: relative;
  }
}

:global(.designer-layout-main) {
  overflow: hidden !important;
  position: relative;
}

.canvas {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  overflow: hidden;
}

.rate-action-box {
  position: fixed;
  right: 16px;
  bottom: 16px;
  z-index: 9999;
  border-radius: 8px;
  background: @background-btn-secondary;
}

.undo-redo {
  padding: 0 4px;
  height: 40px;
  border-radius: 12px;
  background-color: @background-btn-secondary;
  box-shadow: @level-1;
  z-index: 999;
  cursor: pointer;

  .font {
    pointer-events: none;
    font-size: @text-18 !important;
    font-weight: 600;
    color: @content-system-primary;
    transform: unset !important;
  }
  .font-disabled {
    color: @content-btn-transparency-disable;
  }
}

.tool-container {
  position: absolute;
  left: 50%;
}

.rate-container {
  position: absolute;
  right: 20px;
  z-index: 10;
  background: #fff;
  border-radius: 10px;
}

.actions {
  z-index: 9 !important;

  :global(.@{ant-prefix}-btn.@{ant-prefix}-btn-icon-only) {
    width: calc(@size-xl + @size-xxs) !important;
    height: calc(@size-xl + @size-xxs) !important;
    font-size: @font-size-xl;
  }

  &.pos {
    top: 118px;
    position: fixed;
    z-index: 11;
  }

  .active-icon {
    border: 1px solid @stroke-icon-btn-hover !important;

    path {
      fill: @content-icon-btn-hover !important;
    }
  }
}
