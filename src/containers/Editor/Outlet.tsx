import { Outlet, useLocation } from 'react-router-dom';
import { Authorized } from '@/components';

export function EditorOutlet() {
  const location = useLocation();
  if (
    location.pathname.startsWith('/ai/text-to-image') ||
    location.pathname.startsWith('/ai/image-to-image') ||
    location.pathname.startsWith('/ai/image-upscale')
  ) {
    return <Outlet />;
  }

  return (
    <Authorized>
      <Outlet />
    </Authorized>
  );
}

// `<Route />` 组件的 `lazy` 要求
// https://reactrouter.com/en/main/route/route#lazy
export { EditorOutlet as Component };
