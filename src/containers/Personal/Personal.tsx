import { Layout } from '@/layouts';
import styles from './styles.module.less';
import { ReactElement, useEffect, useRef, useState } from 'react';
import { OptionsType, fetchUserInfo } from '@/api/personal';
import { CreateLocationType, UserInfo } from '@/types';
import { Tabs } from '@/components';
import { Empty, TabsProps, Typography } from 'antd';
import StickyBox from 'react-sticky-box';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import classNames from 'classnames';
import { PersonalWorks } from './PersonalWorks';
import { CollectionWorks } from './CollectionWorks';
import { PersonalModels } from './PersonalModels';
import { CollectionModels } from './CollectionModels';
import { CollectionActiveTab } from './hooks';
import { useRecoilState } from 'recoil';
import { PersonalContext } from './context';
import { useMount } from 'react-use';
import { trackEvent } from '@/services';
import empty from '@/assets/images/empty.jpg';
import {
  DetailModalContext,
  DetailModalProvider
} from '@/components/DetailModalProvider';
import { DraftType } from '@/api/types';
import { ModelDetailModal } from '@/components/GalleryModel/ModelDetailModal';
import { GalleryImageDetailModal, GalleryProvider } from '@/components/Gallery';

import {
  applyModelTrack,
  galleryCollectTracking,
  galleryCompareTracking,
  galleryCopyPromptTracking,
  galleryCreateSameTracking,
  galleryCreateWithModelTracking,
  galleryStyleModelTrack,
  gallerySwitchTracking
} from '@/utils/galleryTracking';
import { trackClickEvent } from './fixturesTrack';
import { TabItemsType } from '@/types';
import { personalTabTrack } from './tracer';
import { PersonalProjects } from './PersonalProjects';
import { useSearchParams as useSearchParamsInRouter } from 'react-router-dom';
import { useSearchParams } from '@/hooks';
import { PersonalPosters } from './PersonalPosters';

export interface CollectionRef {
  refetch: () => void;
}

export interface PersonalList {
  empty: ReactElement;
  type?: OptionsType;
}

// 收藏下的tab
const collectionTab = [
  {
    name: '创作',
    key: 'collectionWorks'
  },
  {
    name: '模型',
    key: 'collectionModels'
  }
];

// 下拉框枚举
export const ratioOptions = [
  {
    name: '全部',
    key: OptionsType.All
  },
  {
    name: '已发布',
    key: OptionsType.Published
  },
  {
    name: '未发布',
    key: OptionsType.UnPublish
  }
];

export function Personal() {
  const [userInfo, setUserInfo] = useState<UserInfo>();
  const [feedCount, setFeedCount] = useState<number>(0);
  const [modelCount, setModelCount] = useState<number>(0);
  const [collectionCount, setCollectionCount] = useState<number>(0);
  const [selectActiveTab, setSelectActiveTab] = useState<OptionsType>(
    OptionsType.All
  );
  const [projectCount, setProjectCount] = useState(0);
  const [posterCount, setPosterCount] = useState(0);

  const searchParams = useSearchParams();
  const [, setSearchParams] = useSearchParamsInRouter();

  const { tab: personalActiveTab = TabItemsType.Works } = searchParams;
  function setPersonalActiveTab(tab: string) {
    setSearchParams(Object.assign({}, searchParams, { tab }));
  }

  const [collectionActiveTab, setCollectionActiveTab] =
    useRecoilState(CollectionActiveTab);

  const CollectionModelsRef = useRef<CollectionRef>(null);
  const CollectionWorksRef = useRef<CollectionRef>(null);

  useMount(() => {
    trackEvent('personal_page_enter', { tab: personalActiveTab });
  });

  useEffect(() => {
    getUserInfo();
  }, []);

  // 获取用户信息和相关数量
  const getUserInfo = () => {
    fetchUserInfo()
      .then((res) => {
        // console.log(res);
        setUserInfo(res);
        setFeedCount(res.feedCount);
        setModelCount(res.modelCount);
        setCollectionCount(res.favoriteFeedCount + res.favoriteModelCount);
        setProjectCount(res.projectCount);
        setPosterCount(res.aiPosterCount);
      })
      .catch((err) => {
        defaultErrorHandler(err);
      });
  };

  function EmptyNode() {
    return (
      <Empty
        className={styles.empty}
        image={empty}
        description={
          <Typography.Text type="secondary">暂无数据</Typography.Text>
        }
      />
    );
  }

  const renderTabBar: TabsProps['renderTabBar'] = (props, DefaultTabBar) => (
    <StickyBox offsetTop={56} offsetBottom={0} className={styles.tabBar}>
      <DefaultTabBar {...props} />
      {personalActiveTab === TabItemsType.Collections && (
        <div className={styles.collectBox}>
          {collectionTab.map((item) => {
            return (
              <div
                key={item.key}
                className={classNames(styles.collectBtn, {
                  [styles.active]: collectionActiveTab === item.key
                })}
                onClick={() => {
                  setCollectionActiveTab(item.key);
                  personalTabTrack(personalActiveTab, item.key as TabItemsType);
                }}
              >
                {item.name}
              </div>
            );
          })}
        </div>
      )}
      {personalActiveTab === TabItemsType.Models && (
        <div className={styles.collectBox}>
          {ratioOptions.map((item) => {
            return (
              <div
                key={item.key}
                className={classNames(styles.collectBtn, {
                  [styles.active]: selectActiveTab === item.key
                })}
                onClick={() => {
                  setSelectActiveTab(item.key);
                  personalTabTrack(personalActiveTab, item.key as OptionsType);
                }}
              >
                {item.name}
              </div>
            );
          })}
        </div>
      )}
    </StickyBox>
  );

  const tabItems = [
    {
      label: (
        <div>
          创作 <span className={styles.count}>{feedCount}</span>
        </div>
      ),
      key: TabItemsType.Works,
      children: <PersonalWorks empty={<EmptyNode />} type={selectActiveTab} />
    },
    {
      label: (
        <div>
          模型 <span className={styles.count}>{modelCount}</span>
        </div>
      ),
      key: TabItemsType.Models,
      children: <PersonalModels empty={<EmptyNode />} type={selectActiveTab} />
    },
    {
      label: (
        <div>
          收藏 <span className={styles.count}>{collectionCount}</span>
        </div>
      ),
      key: TabItemsType.Collections,
      children: (
        <div>
          {collectionActiveTab === 'collectionWorks' ? (
            <CollectionWorks
              ref={CollectionWorksRef}
              empty={<EmptyNode />}
              getUserInfo={getUserInfo}
            />
          ) : (
            <CollectionModels
              ref={CollectionModelsRef}
              empty={<EmptyNode />}
              getUserInfo={getUserInfo}
            />
          )}
        </div>
      )
    },
    {
      label: (
        <div>
          项目 <span className={styles.count}>{projectCount}</span>
        </div>
      ),
      key: TabItemsType.Projects,
      children: <PersonalProjects empty={<EmptyNode />} />
    },
    {
      label: (
        <div>
          画布 <span className={styles.count}>{posterCount}</span>
        </div>
      ),
      key: TabItemsType.PosterProjects,
      children: <PersonalPosters empty={<EmptyNode />} />
    }
  ];

  return (
    <GalleryProvider>
      <PersonalContext.Provider
        value={{
          setFeedCount,
          feedCount,
          modelCount,
          setModelCount,
          collectionCount,
          setCollectionCount,
          setProjectCount,
          setPosterCount
        }}
      >
        <DetailModalProvider>
          <Layout theme="overview">
            <div className={styles.personalBox}>
              <section className={styles.topBox}>
                <img
                  src={userInfo?.avatarUrl}
                  alt=""
                  className={styles.personalImg}
                />
                <div className={styles.infoBox}>{userInfo?.screenName}</div>
              </section>
              <section className={styles.listBox} id="scrollBox">
                <Tabs
                  className={styles.personalModelsTabs}
                  destroyInactiveTabPane
                  items={tabItems}
                  activeKey={personalActiveTab}
                  renderTabBar={renderTabBar}
                  onTabClick={(key: string) => {
                    trackEvent('personal_page_tab_click', {
                      tab: key
                    });
                    setPersonalActiveTab(key);
                    setSelectActiveTab(OptionsType.All);
                    personalTabTrack(key as TabItemsType, OptionsType.All);
                    getUserInfo();
                  }}
                />
              </section>
            </div>
          </Layout>
          <DetailModalContext.Consumer>
            {({ modals, sameModelId }) => {
              return modals.map((item) => (
                <>
                  {item.taskCategory === DraftType.MODEL ? (
                    <ModelDetailModal
                      key={item.id}
                      id={item.id}
                      taskCategory={item.taskCategory}
                      tab={personalActiveTab}
                      mode={
                        personalActiveTab === TabItemsType.Collections
                          ? 'visitor'
                          : 'host'
                      }
                      afterClose={() => {
                        if (personalActiveTab === TabItemsType.Collections) {
                          CollectionModelsRef.current?.refetch();
                          getUserInfo();
                        }
                      }}
                      onApply={(appModule, detail) => {
                        applyModelTrack(
                          CreateLocationType.Detail,
                          detail,
                          appModule
                        );
                      }}
                    />
                  ) : (
                    <GalleryImageDetailModal
                      key={item.id}
                      id={item.id}
                      sameModelId={String(sameModelId)}
                      taskCategory={item.taskCategory}
                      mode={
                        personalActiveTab === TabItemsType.Collections
                          ? 'visitor'
                          : 'host'
                      }
                      afterClose={() => {
                        if (personalActiveTab === TabItemsType.Collections) {
                          CollectionWorksRef.current?.refetch();
                          getUserInfo();
                        }
                      }}
                      /** 作品图手动切换上报 */
                      onGraphChange={(...params) => {
                        gallerySwitchTracking(...params, 'personal_page');
                      }}
                      /** 作品比对上报 */
                      onCompare={(detail) => {
                        galleryCompareTracking(detail, 'personal_page');
                      }}
                      /** 收藏上报 */
                      onCollectedChange={(detail) => {
                        galleryCollectTracking(
                          CreateLocationType.Detail,
                          detail
                        );
                      }}
                      /** 作品描述拷贝上报 */
                      onCopy={(...params) => {
                        galleryCopyPromptTracking(...params, 'personal_page');
                      }}
                      /** 作品模型跳转上报 */
                      onModelClick={(modelId, detail) => {
                        galleryCreateWithModelTracking(
                          modelId,
                          detail,
                          'personal_page'
                        );
                      }}
                      onStyleModelClick={(modelId, detail) => {
                        galleryStyleModelTrack(modelId, detail);
                      }}
                      /** 创作同款点击上报 */
                      onCreateClick={(detail) => {
                        trackClickEvent('create_same', {
                          ...detail,
                          location: 'personal_page',
                          tab: personalActiveTab
                        });
                        galleryCreateSameTracking(
                          CreateLocationType.Detail,
                          detail
                        );
                      }}
                    />
                  )}
                </>
              ));
            }}
          </DetailModalContext.Consumer>
        </DetailModalProvider>
      </PersonalContext.Provider>
    </GalleryProvider>
  );
}

export { Personal as Component };
