import { fetchPosterProjectsList } from '@/api/personal';
import { PosterProjectListItem } from '@/api/types';
import { CanvasSize } from '@/types';

import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import _ from 'lodash';

import { useRef, useState } from 'react';

const POSTER_ITEM_CONTAINER_SIZE = [269, 253] as CanvasSize;

export type PosterProjectItemType = {
  id: string;
  size: typeof POSTER_ITEM_CONTAINER_SIZE;
} & PosterProjectListItem;

function transformProjectList(
  listItem: PosterProjectListItem
): PosterProjectItemType {
  return {
    id: listItem.projectId + '',
    size: POSTER_ITEM_CONTAINER_SIZE,
    ...listItem
  };
}

function timer(times: number) {
  if (times <= 0) {
    return;
  }

  return new Promise<void>((resolve) => {
    setTimeout(resolve, times);
  });
}

export const usePosterProjectList = () => {
  const [list, setList] = useState<PosterProjectItemType[]>([]);
  const [cursor, setCursor] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [noMore, setNoMore] = useState(false);

  const preFetchTimer = useRef(Date.now());

  const getProjectList = async (replace?: boolean) => {
    preFetchTimer.current = Date.now();
    setIsLoading(true);
    try {
      const res = await fetchPosterProjectsList({
        // count: PAGE_SIZE,
        cursor: replace ? '' : cursor ?? ''
      });

      if (!res.cursor) setNoMore(true);
      setCursor(res.cursor);

      const projectList = res.list.map(transformProjectList);

      /**
       * 分页组件有1024毫秒的节流时间 要绕开节流机制
       */
      const currentFetchTimer = Date.now();
      const delta = currentFetchTimer - preFetchTimer.current;
      preFetchTimer.current = currentFetchTimer;
      await timer(1025 - delta);

      setList(replace ? projectList : _.concat(list, projectList));
    } catch (error) {
      defaultErrorHandler(error);
    } finally {
      setIsLoading(false);
    }
  };

  const refetch = () => {
    setList([]);
    setCursor('');
    setIsLoading(false);
    setNoMore(false);

    getProjectList(true);
  };

  return {
    isLoading,
    list,
    refetch,
    getProjectList,
    noMore,
    setList
  };
};
