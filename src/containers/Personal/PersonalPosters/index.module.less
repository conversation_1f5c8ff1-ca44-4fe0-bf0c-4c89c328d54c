@import '~@/styles/variables.less';
@bg-color: #f3f5f8;
@delete-btn-bg-color: #0000007f;
@delete-btn-color: #fff;
@cover-error-placeholder-color: #b1b6bf;

.project-item {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-radius: 12px;
  border: 1px solid var(--system-stroke-input, #e2e8f0);
  background: @bg-color;
  overflow: hidden;
  cursor: pointer;
  position: relative;

  .projectItemContainer {
    width: 100%;
    height: 100%;
    padding: 8px;

    :global {
      .@{ant-prefix}-image {
        // position: absolute;
        // left: 50%;
        // top: 50%;
        // transform: translate(-50%, -50%);
        // border-radius: 2px;
        // overflow: hidden;
        border-radius: var(--radius-8, 8px);
        border: 1px solid var(--stroke-systemBorderOverlay, rgba(0, 0, 0, 0.08));
        width: 100%;
        height: 80%;
        overflow: hidden;

        .@{ant-prefix}-image-img {
          height: 100%;
          border: none;
          border-radius: var(--radius-8, 8px);
          object-fit: cover;
          transition: all 0.3s ease-in-out;
        }
      }
    }

    .projectItemPlaceholder {
      width: 100%;
      height: 80%;
      border-radius: var(--radius-8, 8px);
      border: 1px solid var(--stroke-systemBorderOverlay, rgba(0, 0, 0, 0.08));
    }

    .projectItemName {
      color: var(--system-content-secondary, #293545);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px;
      margin: 8px 0 4px;
    }

    .projectItemTime {
      overflow: hidden;
      color: var(--system-content-thirdary, #6a7b94);
      text-overflow: ellipsis;
      white-space: nowrap;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
    }
  }

  :global {
    .project-item-mask {
      position: absolute;
      left: 0;
      bottom: 0;
      padding: var(--size-xs);
      width: 100%;
      height: 100%;
      // background: linear-gradient(180deg,
      //     rgba(0, 0, 0, 0) 80%,
      //     rgba(0, 0, 0, 0.2) 93%);
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      opacity: 0;
      transition: opacity 0.5s ease-in-out;
      cursor: pointer;

      &-router {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
      }

      &-delete {
        font-size: 16px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        background-color: @delete-btn-bg-color;
        color: @delete-btn-color;

        position: absolute;
        right: 16px;
        top: 16px;
      }
    }
  }

  &:hover {
    :global(.project-item-mask) {
      opacity: 1;
    }

    .projectItemContainer {
      :global {
        .@{ant-prefix}-image-img {
          transform: scale(1.1);
        }
      }
    }
  }
}

.delete-modal {
  text-align: center;

  // font-size: 54px;
  // color: @content-system-info-icon-regular;
  :global {
    .ant-modal-body > .ant-modal-confirm-body-wrapper {
      text-align: center;

      .ant-modal-confirm-title {
        font-size: 54px;
        line-height: 54px;
        color: @content-system-info-icon-regular;
      }

      .ant-modal-confirm-paragraph {
        max-width: 100%;
      }

      .ant-modal-confirm-content {
        max-width: unset !important;
        color: @content-system-primary;
        font-size: @text-16;
        font-weight: 600;
        line-height: 22px;
      }

      .ant-modal-confirm-btns {
        margin-top: 28px;
        display: flex;
        justify-content: space-between;
      }
    }
  }
}

.error-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: @cover-error-placeholder-color;

  :global {
    .error-placeholder-icon {
      font-size: 36px;
      line-height: 1;
      margin-bottom: 12px;
    }

    .error-placeholder-label {
      font-size: 14px;
    }
  }
}
