import { useEffect, useLayoutEffect, useState } from 'react';
import { PersonalList } from '../Personal';
import { Waterfall } from '@/components';
import { PosterProjectItemType, usePosterProjectList } from './usePosterList';
import { useLocationUpdated } from '@/hooks';
import { PosterItem } from './PosterItem';
import { App } from 'antd';
import { CrossBlack, InfoCircleFill } from '@meitu/candy-icons';
import styles from './index.module.less';
import { deleteProject as deletePersonalProject } from '@/api/aiPoster/project';
import { usePersonalContext } from '../context';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';

export function PersonalPosters(props: Omit<PersonalList, 'type'>) {
  const { empty } = props;

  const { isLoading, list, refetch, noMore, getProjectList, setList } =
    usePosterProjectList();

  const [scrollTop, setScrollTop] = useState(0);
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = document.documentElement.scrollTop;
      setScrollTop(scrollTop);
    };
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useLocationUpdated(() => {
    refetch();
  });

  useLayoutEffect(() => {
    refetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { modal, message } = App.useApp();
  const { setPosterCount } = usePersonalContext();

  const handleDeleteProject = (projectId: number) => {
    modal.confirm({
      zIndex: 2000,
      width: 300,
      closable: true,
      closeIcon: <CrossBlack />,
      icon: null,
      centered: true,
      wrapClassName: styles.deleteModal,
      title: <InfoCircleFill />,
      content: '是否确认删除？',
      getContainer: '#root',
      onOk: async () => {
        try {
          await deletePersonalProject({ projectId });

          message.success('删除成功');
          setList((projects) =>
            projects.filter((p) => p.projectId !== projectId)
          );
          setPosterCount((count) => (count > 0 ? count - 1 : count));
        } catch (error: any) {
          const responseData = error.response?.data;

          if (responseData?.code === 11002) {
            message.error('项目不存在，请刷新后重试。');
          } else {
            defaultErrorHandler(error);
          }
        }
      },
      onCancel: () => {},
      cancelButtonProps: { block: true },
      okButtonProps: { block: true }
    });
  };

  return (
    <Waterfall<PosterProjectItemType>
      empty={empty}
      gutter={[12, 14]}
      expectColumnWidth={300}
      hasMore={!noMore}
      loading={isLoading}
      batches={list}
      getMore={getProjectList}
      isAtBottom={scrollTop > 0}
      paginationSentinelInColumn
      renderItem={(item) => (
        <PosterItem
          projectItem={item}
          padding={8}
          onDelete={() => {
            handleDeleteProject(item.projectId);
          }}
        />
      )}
    />
  );
}
