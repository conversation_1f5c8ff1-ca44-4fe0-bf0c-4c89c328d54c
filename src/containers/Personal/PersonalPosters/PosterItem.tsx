import { Image } from 'antd';
import { PosterProjectItemType } from './usePosterList';
import styles from './index.module.less';
import AutoSizer from 'react-virtualized-auto-sizer';
import { useImageSize } from '@/hooks';
import { toAtlasImageView2URL } from '@meitu/util';
import { Loading } from '@/components';
import { TrashCanBold, ExclamationmarkTriangle } from '@meitu/candy-icons';
import { Link } from 'react-router-dom';
import { AppModule, generateRouteTo } from '@/services';
import { useState } from 'react';

type PosterItemProps = {
  projectItem: PosterProjectItemType;

  // 当图片比例和容器比例不一致时 需要有一个内边距
  padding: number;

  onDelete?: () => void;
};

export function PosterItem({
  projectItem,
  padding,
  onDelete
}: PosterItemProps) {
  const cover =
    projectItem.picUrl &&
    toAtlasImageView2URL(projectItem.picUrl, {
      mode: 2,
      width: 192 * 2
    });
  const [imgWidth, imgHeight] = useImageSize(cover);

  const [imageLoaded, setImageLoaded] = useState(false);
  const [isError, setIsError] = useState(false);

  /**
   * 仅在图片有宽高 并且加载成功后 才可以展示
   */
  const imageCanShow = !!(imgWidth && imgHeight && imageLoaded && !isError);

  return (
    <div className={styles.projectItem}>
      {/* <AutoSizer>
        {({ width, height }) => {
          if (!width) {
            width = 0;
          }

          if (!height) {
            height = 0;
          }

          const imgAspect = imgHeight ? imgWidth / imgHeight : 0;
          const containerAspect = height ? width / height : 0;

          // 如果图片和容器的宽高比一致 则不需要内边距
          if (Math.abs(imgAspect - containerAspect) < 1e-3) {
            padding = 0;
          }

          // 减去两边的内边距
          width -= padding * 2;
          height -= padding * 2;

          const adaptWidth =
            imgAspect > containerAspect ? width : height * imgAspect;

          const adaptHeight =
            imgAspect > containerAspect
              ? imgAspect
                ? width / imgAspect
                : 0
              : height;

          return (
           
          );
        }}
      </AutoSizer> */}
      <div className={styles.projectItemContainer}>
        <Image
          src={cover}
          // width={adaptWidth}
          // height={adaptHeight}
          preview={false}
          hidden={!imageCanShow}
          onLoad={() => {
            setImageLoaded(true);
          }}
          onError={() => {
            setIsError(true);
          }}
        />

        {!imageCanShow && (
          <div className={styles.projectItemPlaceholder}>
            {isError ? <ErrorPlaceholder /> : <Loading />}
          </div>
        )}

        <div className={styles.projectItemName}>{projectItem.projectName}</div>
        <div className={styles.projectItemTime}>
          {projectItem.lastEditedTime}
        </div>
      </div>

      <div className="project-item-mask">
        <Link
          className="project-item-mask-router"
          to={`/editor/${projectItem.projectId}`}
          target="_blank"
        ></Link>

        <div
          className="project-item-mask-delete"
          onClick={(e) => {
            e.stopPropagation();
            onDelete?.();
          }}
        >
          <TrashCanBold />
        </div>
        {/* <div className="project-item-mask-name">{projectItem.projectName}</div> */}
      </div>
    </div>
  );
}

function ErrorPlaceholder() {
  return (
    <div className={styles.errorPlaceholder}>
      <span className="error-placeholder-icon">
        <ExclamationmarkTriangle />
      </span>
      <span className="error-placeholder-title">无图片</span>
    </div>
  );
}
