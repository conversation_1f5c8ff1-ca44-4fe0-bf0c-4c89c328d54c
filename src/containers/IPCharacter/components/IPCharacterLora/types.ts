import { TrainingStatus } from '@/api/types';

export type SuccessLora = {
  status: TrainingStatus.Completed;
};

export type GeneratingLora = {
  status: TrainingStatus.Training;
  remainingTime: string;
};

export type GenerationFailedLora = {
  status: TrainingStatus.Failed;
};

export type IPCharacterLora = {
  /**
   * 生图使用的styleModelId
   */
  id: number;
  /**
   * 对于预设的lora 这里的indexId就是id
   * 对于自己训练的lora 这里的indexId是用来【重试】和【删除】的
   */
  indexId: number;
  /**
   * 预览图
   */
  preview: string;
  /**
   * 关联的提示词 当lora被选中时 自动填入该提示词
   *
   * 注：这里不是预设的提示词 预设的提示词在前端和用户无感知
   */
  relatedPrompt?: string;
  /**
   * 是否为预设的lora
   */
  isPreset?: boolean;
} & (SuccessLora | GeneratingLora | GenerationFailedLora);
