import { Form, Image, message, Space, Spin, Typography } from 'antd';
import { Upload } from '@/components/Upload';
import styles from './style.module.less';
import { SVIPIcon } from '@/components/SVIPIcon';
import { MeiDouButton, MeiDouPrice } from '@/components/MeiDouPrice';
import { useEffect, useRef } from 'react';
import { FunctionCode } from '@/api/types/meidou';
import { TrashCanBold, UploadOutlined } from '@meitu/candy-icons';
import { IMAGE_MAX_SIZE } from '@/components/Upload/Upload';
import { Button } from '@/components/Button';
import { VipButton } from '@/components/VipButton';
import { toAtlasImageView2URL } from '@meitu/util';
import { trackEvent } from '@/services';
import { AppModuleParam, MemberGroupCategory } from '@/types';
import { useOpenSubscribePopup } from '@/hooks/useSubscribe';
import { MtccFuncCode } from '@/api/types/common';

type DraggableUploadProps = {
  value?: string;
  onChange?: (value: string) => void;
  triggerSource?: string;
};

const uploadPreviewSize = [302, 204] as const;

function DraggableUpload({
  value,
  onChange,
  triggerSource = AppModuleParam.IPCharacterCustomization
}: DraggableUploadProps) {
  const track = () => {
    trackEvent('portrait_design_popup_click', {
      location: triggerSource,
      click_type: 'upload'
    });
  };

  const [width, height] = uploadPreviewSize.map(
    (measure) => measure * window.devicePixelRatio
  );
  const previewUrl =
    value && toAtlasImageView2URL(value, { mode: 2, width, height });

  if (value) {
    return (
      <div className="ip-character-preview">
        <Image
          src={previewUrl}
          preview={false}
          placeholder={<Spin spinning />}
        />

        <div className="ip-character-preview-mask">
          <div
            role="button"
            className="remove-btn"
            onClick={(event) => {
              event.stopPropagation();
              onChange?.('');
            }}
          >
            <TrashCanBold />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div onClick={track}>
      <Upload.Dragger
        className="ip-character-uploader"
        value={value}
        onDrop={track}
        onChange={(shortUrl, url) => {
          const img = url || shortUrl;
          if (!img) {
            return;
          }

          trackEvent('whee_upload_image_success', {
            function: AppModuleParam.IPCharacterCustomization
          });
          onChange?.(img);
        }}
        renderChildren={(loading) => {
          if (loading) {
            return <Spin spinning />;
          }

          return (
            <Space direction="vertical">
              <Button icon={<UploadOutlined />}>拖拽或点击</Button>
              <Typography.Text type="secondary">
                {`支持JPG/PNG/ ${IMAGE_MAX_SIZE}MB`}
              </Typography.Text>
            </Space>
          );
        }}
      />
    </div>
  );
}

export type CreateTaskParams = {
  image: string;
};

export type RetryTaskParams = {
  id: number;
};

type IPCharacterCreateProps = {
  loading?: boolean;
  initialImage?: string;
  triggerSource?: string;

  beforeCreate?: () => boolean;
  onCreateLoraTask?: (params: CreateTaskParams) => Promise<boolean>;

  /**
   * 重试按钮也可能吊起该弹窗
   * 如果用户不重传图片 执行重试的操作
   */
  retryId?: number;
  onRetry?: (params: RetryTaskParams) => Promise<boolean>;

  /**
   * 是否需要订阅VIP
   */
  needsSubscribeVIP?: boolean;
  /**
   * 用户订充值成后执行的回调
   */
  onTradeFinish?: () => any;
};

export function IPCharacterCreate({
  initialImage,
  onCreateLoraTask,
  loading,
  triggerSource = AppModuleParam.IPCharacterCustomization,
  needsSubscribeVIP,
  onTradeFinish,
  retryId,
  onRetry,
  beforeCreate
}: IPCharacterCreateProps) {
  const [form] = Form.useForm();

  useEffect(() => {
    trackEvent('portrait_design_popup_expo', {
      location: triggerSource || AppModuleParam.IPCharacterCustomization
    });
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  //#region 会员订阅
  const openSubscribePopup = useOpenSubscribePopup({
    onTradeFinish
  });
  function renderSubscribeButton() {
    return (
      <VipButton
        className={styles.subscribeBtn}
        onClick={() =>
          openSubscribePopup(
            MemberGroupCategory.Member,
            undefined,
            MtccFuncCode.FuncCodeIpImageTraining
          )
        }
      >
        立即开通
      </VipButton>
    );
  }
  //#endregion

  //#region 创建训练任务/失败任务重试
  /**
   * 当重新上传时 请空这个id
   */
  const retryIdRef = useRef(retryId || null);

  function renderCreateLoraButton() {
    return (
      <MeiDouPrice
        functionCode={FunctionCode.ipimagetraining}
        fields={['image']}
        getFunctionBody={() => {
          return {
            params: {
              image: form.getFieldValue('image')
            }
          };
        }}
      >
        {(price, fetchLoading) => (
          <MeiDouButton
            className={styles.meidouBtn}
            hasBorder={false}
            price={price}
            block
            htmlType="submit"
            loading={loading}
            fetchPriceLoading={fetchLoading}
            functionId={MtccFuncCode.FuncCodeIpImageTraining}
            onClick={(e) => {
              const editorComponentParams = form.getFieldsValue();

              if (beforeCreate && !beforeCreate()) {
                e.preventDefault();
                return;
              }

              if (!editorComponentParams.image) {
                e.preventDefault();
                message.error('请上传图片');
                return;
              }

              trackEvent('portrait_design_popup_click', {
                location: triggerSource,
                click_type: 'create_now'
              });

              const p = retryIdRef.current
                ? onRetry?.({ id: retryIdRef.current })
                : onCreateLoraTask?.({ image: editorComponentParams.image });

              Promise.resolve(p).then(() => {
                message.success('IP形象努力生成中～去逛逛其他的吧');
              });
            }}
          />
        )}
      </MeiDouPrice>
    );
  }
  //#endregion

  return (
    <Form
      form={form}
      initialValues={{
        image: initialImage
      }}
    >
      <div className={styles.container}>
        <header>
          <h1>
            <span className="title-content">创建IP形象</span>
          </h1>
          <div className="subtitle">
            <SVIPIcon className="svip-icon" />
            <span className="subtitle-content">当前功能为VIP专属功能</span>
          </div>
        </header>
        <main>
          <div className="video-container">
            <video
              autoPlay
              loop
              muted
              src="https://titan-h5.meitu.com/whee/assets/ipCharacter/create-ip-lora-tips.mov"
            />
          </div>
          <Form.Item className="upload-container" name="image">
            <DraggableUpload
              triggerSource={triggerSource}
              onChange={() => {
                retryIdRef.current = null;
              }}
            />
          </Form.Item>
        </main>
        <footer>
          {needsSubscribeVIP
            ? renderSubscribeButton()
            : renderCreateLoraButton()}
        </footer>
      </div>
    </Form>
  );
}
