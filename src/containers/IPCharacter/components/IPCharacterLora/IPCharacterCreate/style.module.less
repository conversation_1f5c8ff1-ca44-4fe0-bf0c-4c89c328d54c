@import '~@/styles/variables.less';

.container {
  & > header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;

    & > h1 {
      display: flex;
      align-items: center;
      position: relative;
      margin-bottom: 0;

      :global {
        .title-content {
          font-size: 24px;
        }
      }
    }

    :global {
      .subtitle {
        height: 34px;
        display: flex;
        justify-content: center;
        align-items: center;

        &-content {
          margin-left: 8px;
          color: @content-system-primary;
          font-size: 14px;
        }
      }
    }
  }

  & > main {
    display: flex;
    justify-content: space-between;
    :global {
      .upload-container,
      .video-container {
        box-sizing: border-box;
        width: 318px;
        height: 220px;
        flex: 0 0 auto;
        border-radius: 10px;
        overflow: hidden;
        border-radius: var(--radius-10, 10px);
        background: @background-system-frame-floatpanel;
        box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08),
          0px 6px 24px 0px rgba(0, 0, 0, 0.06);
        margin-bottom: 0;
      }

      .video-container {
        overflow: hidden;
        & > video {
          width: 100%;
          height: 100%;
        }
      }

      .upload-container {
        display: flex;
        align-items: center;
        justify-content: center;

        .ip-character-preview,
        .ip-character-uploader {
          position: relative;
          width: 302px;
          height: 204px;
        }

        .ip-character-uploader {
          &.ant-upload-wrapper > .ant-upload-drag {
            background: @background-system-frame-floatpanel;
          }
        }

        .ip-character-preview {
          display: flex;
          justify-content: center;
          align-items: center;

          .ant-image {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .ant-image-placeholder {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            img.ant-image-img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }

          &-mask {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;

            .remove-btn {
              position: absolute;
              top: 0;
              right: 0;
              width: 28px;
              height: 28px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 4px;
              color: #fff;
              background: rgba(0, 0, 0, 0.5);
              cursor: pointer;

              svg {
                width: 16px;
                height: 16px;
              }
            }
          }
        }
      }
    }
  }

  & > footer {
    display: flex;
    justify-content: center;
    margin-top: 40px;
    position: relative;

    & > .meidou-btn {
      position: static;
      box-shadow: none;
      padding: 0;
      width: 288px;

      & > button[type='submit'] {
        height: 44px;
      }
    }

    & > .subscribe-btn {
      margin-top: 32px;
      margin-bottom: 30px;
      width: 288px;
      height: 44px;
      border-radius: 8px;
      font-size: 16px;
    }
  }
}
