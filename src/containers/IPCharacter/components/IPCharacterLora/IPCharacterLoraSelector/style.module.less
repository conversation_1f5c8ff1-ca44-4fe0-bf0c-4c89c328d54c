@import '~@/styles/variables.less';

.selector {
  user-select: none;
  :global {
    .create-btn {
      display: flex;
      width: 100%;
      height: 32px;
      justify-content: center;
      align-items: center;
      color: @content-btn-primary;
      font-size: 14px;
      font-weight: 600;
      border-radius: 8px;
      background: @background-btn-ai;
      margin-bottom: 16px;
    }

    .preset-lora {
      & > .preset-lora-panel:last-child {
        box-shadow: none;
        border-radius: 0;
        & > .ant-collapse-header {
          padding: 0;

          & > .ant-collapse-expand-icon {
            .preset-lora-expand-icon {
              justify-content: center;
              align-items: center;
              border-radius: 50%;
              width: 16px;
              height: 16px;
              border: var(--stroke-1, 1px) solid
                var(--stroke-border_overlay, rgba(0, 0, 0, 0.1));

              svg {
                width: 10px;
                height: 10px;
              }

              &.active {
                transform: rotateZ(180deg);
              }
            }
          }

          & > .ant-collapse-header-text {
            font-weight: 400;
            font-size: 12px;
            color: @content-system-tertiary;
          }
        }

        & > .ant-collapse-content {
          border-radius: 0;
          & > .ant-collapse-content-box {
            padding: 0;
            padding-top: 16px;
          }
        }
      }
    }
  }
}

.preset-list {
  display: grid;
  grid-template-columns: repeat(5, 48px);
  grid-auto-rows: 64px;
  row-gap: 12px;
  column-gap: 12px;

  :global {
    .card-item {
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;
      position: relative;
      z-index: 0;

      .ant-image {
        position: relative;
        width: 100%;
        height: 100%;
        box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        border-radius: 4px;
        background: transparent;
        img {
          width: 100%;
          height: 100%;
          position: relative;
          z-index: -1;
          object-fit: cover;
        }
      }

      &.active {
        .ant-image {
          box-shadow: inset 0 0 0 2px @content-list-selected;
        }
      }
    }
  }
}

.custom-list {
  width: calc(100% + 4px);
  margin-top: -2px;
  margin-left: -2px;
  margin-bottom: 16px;

  padding: 2px;
  max-height: 280px;
  overflow-x: visible;
  overflow-y: auto;

  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }

  :global {
    .custom-list-content {
      display: grid;
      grid-template-columns: repeat(3, 88px);
      grid-auto-rows: 117px;
      row-gap: 12px;
      column-gap: 12px;

      .card-item {
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        z-index: 0;

        .ant-image {
          position: relative;
          width: 100%;
          height: 100%;
          box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          border-radius: 4px;
          background: transparent;
          img {
            width: 100%;
            height: 100%;
            position: relative;
            z-index: -1;
            object-fit: cover;
          }
        }

        &.card-item-success {
          cursor: pointer;

          &.active {
            .ant-image {
              box-shadow: inset 0 0 0 2px @content-list-selected;
            }
          }

          & > .remove-btn {
            opacity: 0;
            transition: opacity 0.3s;
          }

          &:hover > .remove-btn {
            opacity: 1;
          }
        }

        &.card-item-generating {
          .loading-mask {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);

            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding-top: 30.5px;

            color: #fff;
            text-align: center;

            font-size: 14px;
            line-height: 20px;

            &-icon {
              width: 28px;
              height: 28px;
              flex: 0 0 auto;
              margin-bottom: 8px;
            }
          }
        }

        &.card-item-generation_failed {
          .retry-mask {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);

            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            padding: 25px 0 24px 0;
            text-align: center;

            font-size: 14px;
            line-height: 20px;

            & > .remove-btn {
              opacity: 0;
              transition: opacity 0.3s;
            }

            &:hover > .remove-btn {
              opacity: 1;
            }

            &-icon {
              color: #fff;
              svg {
                width: 28px;
                height: 28px;
              }
            }

            .ant-spin-container {
              border-radius: 8px;
              overflow: hidden;
              .retry-mask-btn {
                display: flex;
                height: var(--spacing-xl, 32px);
                padding: var(--spacing-0, 0px) var(--spacing-10, 10px);
                justify-content: center;
                align-items: center;

                border-radius: var(--radius-8, 8px);
                border: var(--stroke-1, 1px) solid var(---, #d0d2d6);
                background: var(--_base-white_opacity_100, #fff);
                color: @content-btn-secondary;
                cursor: pointer;

                &:hover {
                  background: darken(#fff, 5%);
                }

                &:active {
                  background: #fff;
                }
              }
            }
          }
        }
      }
    }
  }
}

.remove-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  position: absolute;
  right: 3px;
  top: 3px;
  width: 22px;
  height: 22px;
  border-radius: 4px;
  border: 1px solid var(--_base-white_opacity_25, rgba(255, 255, 255, 0.25));
  background: var(--_base-black_opacity_50, rgba(0, 0, 0, 0.5));
  cursor: pointer;
  &:hover {
    background-color: var(--_base--black_opacity_35, rgba(0, 0, 0, 0.35));
  }

  svg {
    width: 11.5px;
    height: 11.5px;
    color: white;
  }
}
