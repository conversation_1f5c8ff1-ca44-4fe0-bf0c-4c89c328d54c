import { IPCharacterLora } from '../types';
import { <PERSON><PERSON>, Collapse } from '@/components';
import {
  ChevronDownBold,
  ExclamationmarkTriangleBold,
  TrashCanBold
} from '@meitu/candy-icons';
import classNames from 'classnames';
import { Image, Spin } from 'antd';
import { toAtlasImageView2URL } from '@meitu/util';
import styles from './style.module.less';
import { TrainingStatus } from '@/api/types';
import { trackEvent } from '@/services';
import { AppModuleParam } from '@/types';
import { CircleLoading } from '@/components/CircleLoading';

export type IPCharacterLoraSelectorValueType = {
  id: number;
  isPreset?: boolean;
};

type IPCharacterLoraSelectorProps = {
  /**
   * 已经创建好的预设lora
   */
  presetLoraList?: Array<IPCharacterLora>;

  /**
   * 用户自定义的lora
   */
  customLotaList?: Array<IPCharacterLora>;

  /**
   * 点击创建形象按钮时的回调
   */
  onCreateLora?: () => void;

  /**
   * 正在重试的id
   */
  retryingIds?: Array<number>;

  /**
   * 是否正在创建
   */
  isCreating?: boolean;

  value?: IPCharacterLoraSelectorValueType;
  onChange?: (
    value: IPCharacterLoraSelectorValueType,
    prevValue?: IPCharacterLoraSelectorValueType
  ) => void;

  onRetry?: (id: number, image: string) => void;

  onRemove?: (id: number) => void;
};

const presetCardSize = [48, 64] as const;
const customCardSize = [88, 117] as const;

export function IPCharacterLoraSelector({
  presetLoraList = [],
  customLotaList = [],
  onCreateLora,
  value,
  onChange,
  onRetry,
  retryingIds = [],
  isCreating,
  onRemove
}: IPCharacterLoraSelectorProps) {
  function renderPresetLoraCards() {
    const width = presetCardSize[0] * window.devicePixelRatio;
    const height = presetCardSize[1] * window.devicePixelRatio;

    const cards = presetLoraList.map(({ id, preview }) => {
      return (
        <div
          key={id}
          className={classNames(
            'card-item',
            value?.isPreset && id === value?.id && 'active'
          )}
          onClick={() => {
            onChange?.({ id, isPreset: true }, value);
          }}
        >
          <Image
            src={toAtlasImageView2URL(preview, {
              mode: 2,
              width,
              height
            })}
            preview={false}
          />
        </div>
      );
    });

    return <div className={styles.presetList}>{cards}</div>;
  }

  function renderCustomLoraCards() {
    const cards = customLotaList.map((item) => {
      const { id, preview } = item;
      const width = customCardSize[0] * window.devicePixelRatio;
      const height = customCardSize[1] * window.devicePixelRatio;

      switch (item.status) {
        case TrainingStatus.Completed: {
          return (
            <div
              key={id}
              className={classNames(
                'card-item card-item-success',
                !value?.isPreset && value?.id === id && 'active'
              )}
              onClick={() => {
                onChange?.({ id, isPreset: false }, value);
              }}
            >
              <Image
                src={toAtlasImageView2URL(preview, {
                  mode: 2,
                  width,
                  height
                })}
                preview={false}
              />

              <RemoveBtn
                className="remove-btn"
                onClick={() => item.indexId && onRemove?.(item.indexId)}
              />
            </div>
          );
        }
        case TrainingStatus.Training: {
          return (
            <div key={id} className="card-item card-item-generating">
              <Image
                src={toAtlasImageView2URL(preview, {
                  mode: 2,
                  width,
                  height
                })}
                preview={false}
              />
              <div className="loading-mask">
                <CircleLoading className="loading-mask-icon" />
                <span className="loading-mask-remaining">
                  剩余{item.remainingTime}
                </span>
              </div>
            </div>
          );
        }
        case TrainingStatus.Failed: {
          return (
            <div key={id} className="card-item card-item-generation_failed">
              <Image
                src={toAtlasImageView2URL(preview, {
                  mode: 2,
                  width,
                  height
                })}
                preview={false}
              />
              <div className="retry-mask">
                <ExclamationmarkTriangleBold className="retry-mask-icon" />
                <Spin
                  spinning={
                    !!item.indexId && retryingIds.includes(item.indexId)
                  }
                >
                  <div
                    className="retry-mask-btn"
                    role="button"
                    onClick={async (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if (item.indexId) {
                        onRetry?.(item.indexId, item.preview);
                      }
                    }}
                  >
                    重试
                  </div>
                </Spin>

                <RemoveBtn
                  className="remove-btn"
                  onClick={() => item.indexId && onRemove?.(item.indexId)}
                />
              </div>
            </div>
          );
        }
      }

      return null;
    });

    return (
      <div className={styles.customList}>
        <div className="custom-list-content">{cards}</div>
      </div>
    );
  }

  return (
    <section className={styles.selector}>
      {renderCustomLoraCards()}
      <Button
        className="create-btn"
        disabled={!!retryingIds.length}
        loading={isCreating}
        onClick={() => {
          trackEvent('whee_edit_page_click', {
            function: AppModuleParam.IPCharacterCustomization,
            clickType: 'create_ip'
          });
          onCreateLora?.();
        }}
      >
        创建形象
      </Button>
      <Collapse
        className="preset-lora"
        defaultActiveKey={['preset-lora']}
        expandIcon={({ isActive }) => {
          return (
            <span
              className={classNames(
                'preset-lora-expand-icon',
                isActive && 'active'
              )}
            >
              <ChevronDownBold />
            </span>
          );
        }}
      >
        <Collapse.Panel
          className="preset-lora-panel"
          key="preset-lora"
          header="没有想法，试试下面这些形象"
          forceRender
        >
          <Collapse.Panel.Section>
            {renderPresetLoraCards()}
          </Collapse.Panel.Section>
        </Collapse.Panel>
      </Collapse>
    </section>
  );
}

type RemoveBtnProps = {
  onClick?(): void;
  className?: string;
};
export function RemoveBtn({ onClick, className }: RemoveBtnProps) {
  return (
    <div
      className={classNames(styles.removeBtn, className)}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        onClick?.();
      }}
    >
      <TrashCanBold />
    </div>
  );
}
