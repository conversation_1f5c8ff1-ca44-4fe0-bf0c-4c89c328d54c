@import '~@/styles/variables.less';

.content {
  display: flex;
  flex-direction: column;
  align-items: center;

  :global {
    .content-icon {
      color: @content-system-info-icon-regular;

      svg {
        width: 54px;
        height: 54px;
      }
    }

    h1 {
      margin-top: 12px;
      font-size: 16px;
    }

    p {
      font-size: 14px;
      color: @content-system-tertiary;
      text-align: center;
    }

    .content-btns {
      width: 100%;
      display: flex;
      justify-content: space-between;

      & > button {
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 0 0 auto;
        width: 128px;
        height: 36px;
        border-radius: 8px;
      }
    }
  }
}
