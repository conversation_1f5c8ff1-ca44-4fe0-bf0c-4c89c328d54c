import { Button } from '@/components';
import { InfoCircleBoldFill } from '@meitu/candy-icons';
import styles from './style.module.less';
import { useEffect } from 'react';
import { trackEvent } from '@/services';

type IPCharacterLoraRemoveProps = {
  isRemoving?: boolean;
  onCancel?: () => void;
  onOk?: () => void;
};

export function IPCharacterLoraRemove({
  isRemoving,
  onOk,
  onCancel
}: IPCharacterLoraRemoveProps) {
  useEffect(() => {
    trackEvent('portrait_delete_popup_expo');
  }, []);

  return (
    <section className={styles.content}>
      <InfoCircleBoldFill className="content-icon" />
      <h1>确认删除？</h1>
      <p>删除该形象后将无法恢复，并且该形象的历史生图记录中不再支持重新编辑</p>
      <div className="content-btns">
        <Button
          type="default"
          onClick={() => {
            trackEvent('portrait_delete_popup_click', {
              click_type: 'cancel'
            });

            onCancel?.();
          }}
        >
          取消
        </Button>
        <Button
          type="primary"
          loading={isRemoving}
          onClick={() => {
            trackEvent('portrait_delete_popup_click', {
              click_type: 'delete'
            });

            onOk?.();
          }}
        >
          删除
        </Button>
      </div>
    </section>
  );
}
