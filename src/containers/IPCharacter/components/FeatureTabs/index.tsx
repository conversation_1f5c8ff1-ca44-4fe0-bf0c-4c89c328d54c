import { Segmented } from '@/components';
import { ConfigProvider } from 'antd';

import { IPCharacterFeature } from '../../constants';

const featureTabs = [
  { label: '概念图设计', value: IPCharacterFeature.Concept },
  { label: '形象定制', value: IPCharacterFeature.Customization }
];

type FeatureTabsProps = {
  value?: IPCharacterFeature;
  onChange?: (newValue: IPCharacterFeature) => void;
};

export function FeatureTabs({ value, onChange }: FeatureTabsProps) {
  if (!value) {
    return null;
  }

  return (
    <ConfigProvider
      theme={{
        token: { controlHeight: 34 }
      }}
    >
      <Segmented
        options={featureTabs}
        value={value}
        large
        onChange={(feat) => onChange?.(feat as IPCharacterFeature)}
      />
    </ConfigProvider>
  );
}
