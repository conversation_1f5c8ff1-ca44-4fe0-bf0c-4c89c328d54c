import type {
  TaskQuery,
  DraftQuery,
  TaskParams,
  DraftRemoveBody,
  CommonPostResponse
} from '@/api/types';
import { GraphBatch, Draft, SortFilterType, Graph } from '@/types/draft';

import { PropsWithChildren, createContext } from 'react';

import { EditorMode } from '@/constants';
import { FilterType } from '@/types';
import { useImagesContainerProvider } from './Hooks';
import { Optional } from 'utility-types';

type Id = GraphBatch['id'];

interface ImagesContainerContextValue {
  /** 分页游标 */
  cursor?: string | null;
  setCursor: (cursor?: string | null) => void;

  /** 图片 id - 详情 映射 */
  graphBatch: Record<Id, GraphBatch>;
  setGraphBatch: (
    graphBatches: ImagesContainerContextValue['graphBatch']
  ) => void;
  updateGraphBatch: (graphBatch: GraphBatch) => void;

  /**是否应用水印图片 */
  imageWatermark: boolean;
  setImageWatermark: (imageWatermark: boolean) => void;

  /** 图片源字段区分 */
  imageSourceKey: 'src' | 'urlWatermark';

  /** 图片 id 列表 */
  ids: Id[];
  addIds: (ids: Id[]) => void;
  insertIds: (ids: Id[]) => void;
  removeId: (id: Id) => void;
  fetchImages?: (params: DraftQuery) => Promise<Draft>;
  removeImage?: (data: DraftRemoveBody) => Promise<CommonPostResponse>;

  /** 加载中任务 id 列表 */
  loadingIds: Set<Id>;
  addLoadingIds: (ids: Id[]) => void;
  setLoadingIds: (ids: Id[]) => void;
  fetchTasks?: (params: TaskQuery) => Promise<GraphBatch[]>;

  /** 图片预览 id */
  previewId?: Id;

  /** 图片容器滚动高度 */
  scrollTop: number;
  setScrollTop: (scrollTop: number) => void;

  /** 路由返回 */
  goBack: () => void;

  /** 生成任务 */
  tasks: (Pick<GraphBatch, 'id' | 'size' | 'editorMode'> &
    Optional<Pick<GraphBatch, 'params'>>)[];
  clearTasks: () => void;

  /** 回填表单 */
  setFormValue?: (id: Id, params: Partial<TaskParams>) => Promise<void>;
  /** 设置表单编辑模式 */
  setEditorMode?: (editorMode?: EditorMode) => void;

  toPreview?: (id: Id) => void;

  /** 筛选类型 */
  filterType: FilterType;
  /** 设置筛选类型 */
  setFilterType: (type: FilterType) => void;

  /** 排序筛选类型 */
  sortFilterType: SortFilterType;
  /** 设置排序筛选类型 */
  setSortFilterType: (type: SortFilterType) => void;

  /** 搜索关键字 */
  keyword?: string;
  /** 设置搜索关键字 */
  setKeyword: (keyword: string) => void;

  clearGraphBatch: () => void;

  onImageCreated?: (images: GraphBatch[]) => void;
  batchOrigin: Graph[];
  setBatchOrigin: (batchOrigin: Graph[]) => void;

  historyActiveIndex: number;
  setHistoryActiveIndex: (index: number) => void;
}

export const ImagesContainerContext =
  createContext<ImagesContainerContextValue>({
    cursor: null,
    setCursor: () => {},

    imageWatermark: false,
    setImageWatermark: () => {},

    imageSourceKey: 'src',

    graphBatch: {},
    setGraphBatch: () => {},
    updateGraphBatch: () => {},

    ids: [],
    addIds: () => {},
    insertIds: () => {},
    removeId: () => {},

    loadingIds: new Set(),
    addLoadingIds: () => {},
    setLoadingIds: () => {},

    scrollTop: 0,
    setScrollTop: () => {},

    goBack: () => {},

    tasks: [],
    clearTasks: () => {},

    filterType: FilterType.ALL,
    setFilterType: () => {},

    sortFilterType: SortFilterType.DESC,
    setSortFilterType: () => {},
    keyword: '',
    setKeyword: () => {},

    clearGraphBatch: () => {},

    onImageCreated: () => {},
    batchOrigin: [],
    setBatchOrigin: () => {},
    historyActiveIndex: 0,
    setHistoryActiveIndex: () => {}
  });

type ImagesContainerContextProviderType = Pick<
  ImagesContainerContextValue,
  | 'goBack'
  | 'clearTasks'
  | 'tasks'
  | 'setFormValue'
  | 'fetchImages'
  | 'removeImage'
  | 'fetchTasks'
  | 'onImageCreated'
>;

export const ImagesContainerContextProvider = ({
  children,
  ...rest
}: PropsWithChildren<ImagesContainerContextProviderType>) => {
  const contextValue = useImagesContainerProvider({
    ids: [],
    loadingIds: new Set(),
    graphBatch: {},
    imageWatermark: false
  });

  return (
    <ImagesContainerContext.Provider
      value={{
        ...contextValue,
        ...rest
      }}
    >
      {children}
    </ImagesContainerContext.Provider>
  );
};
