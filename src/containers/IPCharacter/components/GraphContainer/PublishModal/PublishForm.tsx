import type { FormProps } from 'antd';

import { Form, Tag, Space, Switch, Tooltip } from 'antd';
import { TextArea } from '@/components/TextArea';
import { QuestionMarkCircle } from '@meitu/candy-icons';
import { useRecoilValue } from 'recoil';
import { EditorConfigState } from '@/hooks/useGetEditorConfig';
import { Suspense } from 'react';
import _ from 'lodash';

import styles from './styles.module.less';
import { Button } from '@/components';

export interface PublishFormValue {
  /** 创意名 */
  name: string;
  /**简介 */
  desc: string;
  /** 标签 */
  tags?: string[];
  /** 允许使用同款 */
  canUseSame?: boolean;

  /** 是否允许将作平同步至画廊 （如果没有设置 `false` 默认是会同步） */
  canPostToGallery?: boolean;

  /** 允许使用画面控制器图片 */
  canUseControlnetImage?: boolean;
}

interface PublishFormTagsProps {
  value?: string[];
  onChange?: (value: string[]) => void;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function Tags({ value, onChange }: PublishFormTagsProps) {
  const config = useRecoilValue(EditorConfigState);
  const valueMap = new Set(value);

  return (
    <Suspense fallback={null}>
      <Space size={[0, 'small']} wrap>
        {config?.publishTags.map(({ tagName }, index) => (
          <Tag.CheckableTag
            key={`tag-${index}`}
            checked={valueMap.has(tagName)}
            onChange={(checked) => {
              checked ? valueMap.add(tagName) : valueMap.delete(tagName);

              onChange?.(Array.from(valueMap));
            }}
          >
            {tagName}
          </Tag.CheckableTag>
        ))}
      </Space>
    </Suspense>
  );
}

interface PublishFormProps
  extends Pick<FormProps<PublishFormValue>, 'onValuesChange' | 'onFinish'> {
  /** 是否有同步至画廊操作按钮的权限 */
  hasPublishGalleryControlAccess?: boolean;

  /** 是否有允许使用画面控制图片操作按钮的权限 */
  hasControlnetImageControlAccess?: boolean;

  onCancel?: () => void;
  loading: boolean;
  initialValues?: Partial<PublishFormValue>;

  /** 自定义校验字段 */
  titleVerify: string;
  descriptionVerify: string;
}

export default function PublishForm({
  onValuesChange,
  onFinish,
  loading,
  initialValues,
  hasPublishGalleryControlAccess,
  hasControlnetImageControlAccess,
  titleVerify,
  descriptionVerify
}: PublishFormProps) {
  const [form] = Form.useForm();

  return (
    <Form
      layout="vertical"
      initialValues={initialValues}
      form={form}
      className={styles.publishContainerForm}
      onValuesChange={onValuesChange}
      onFinish={onFinish}
    >
      <Form.Item
        label="创意名"
        name="name"
        validateStatus={titleVerify ? 'error' : ''}
        help={titleVerify}
        rules={[{ required: true, message: '请输入创意名' }]}
      >
        <TextArea
          placeholder="请输入内容"
          allowClear
          maxLength={20}
          showCount
          className={styles.nameTextArea}
        />
      </Form.Item>
      <Form.Item
        label="简介"
        name="desc"
        validateStatus={descriptionVerify ? 'error' : ''}
        help={descriptionVerify}
      >
        <TextArea
          placeholder="请输入内容"
          allowClear
          maxLength={100}
          showCount
          className={styles.descriptionTextArea}
        />
      </Form.Item>

      {_.isBoolean(initialValues?.canUseSame) && (
        <Form.Item
          label={
            <>
              允许使用同款
              <Tooltip overlay="开启后允许其他人使用当前AI参数，创作同款内容">
                <QuestionMarkCircle />
              </Tooltip>
            </>
          }
          name="canUseSame"
          valuePropName="checked"
          className={styles.horizontalFormItem}
        >
          <Switch />
        </Form.Item>
      )}

      <Form.Item
        noStyle
        shouldUpdate={(prevValue, curValue) =>
          prevValue.canUseSame !== curValue.canUseSame
        }
      >
        {({ getFieldValue }) => {
          const canUseSame = getFieldValue('canUseSame');
          /** 允许使用画面控制和同步至画廊 后台返回的是同一个 `hasPublishGalleryControlAccess` 权限控制,但前端还是区分两个字端 方便后期扩展*/
          return canUseSame && hasControlnetImageControlAccess ? (
            <Form.Item
              className={styles.horizontalFormItem}
              name="canUseControlnetImage"
              valuePropName="checked"
              label={
                <>
                  允许使用画面参考
                  <Tooltip overlay="开启后允许其他人使用画面参考中原图，创作同款内容">
                    <QuestionMarkCircle />
                  </Tooltip>
                </>
              }
            >
              <Switch />
            </Form.Item>
          ) : null;
        }}
      </Form.Item>

      {hasPublishGalleryControlAccess && (
        <Form.Item
          label="同步至画廊"
          name="canPostToGallery"
          valuePropName="checked"
          className={styles.horizontalFormItem}
        >
          <Switch />
        </Form.Item>
      )}

      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.name !== currentValues.name
        }
      >
        {({ getFieldValue }) => (
          <div className={styles.publishContainerFormActions}>
            <Button
              htmlType="submit"
              loading={loading}
              disabled={!getFieldValue('name')}
              className={styles.confirmBtn}
            >
              确认发布
            </Button>
          </div>
        )}
      </Form.Item>
    </Form>
  );
}
