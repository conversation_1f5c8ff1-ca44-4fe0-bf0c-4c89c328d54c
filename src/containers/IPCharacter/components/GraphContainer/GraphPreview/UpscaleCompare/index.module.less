@import '~@/styles/variables.less';

.comparing-container {
  position: relative;
}

.comparing-action:global(.@{ant-prefix}-btn.@{ant-prefix}-btn-icon-only) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(@size + @size-sm);
  height: calc(@size + @size-sm);
  background: rgba(0, 0, 0, 0.5);
  color: @content-btn-primary;
  border: 1px solid @base-white-opacity-25;
  transition: opacity 0.5s ease-in-out;
  z-index: 2;

  :global(path) {
    fill: @content-btn-primary;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.65);
    border-color: @base-white-opacity-25;

    :global(path) {
      fill: @base-white-opacity-50;
    }
  }
}

.comparing-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  border-radius: @size-xxs;
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  z-index: 2;

  &.active {
    opacity: 1;
  }
}

.upscale-success-corner {
  position: absolute;
  bottom: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  background: @background-tag-amount;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  z-index: 2;
  cursor: pointer;

  .line {
    width: 1px;
    height: 13px;
    background: rgba(255, 255, 255, 0.15);
    margin: 0 6px;
  }

  .icons-box {
    display: flex;
    justify-content: center;

    .icons {
      margin-right: 4px;
      margin-top: -1px;

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  .active-corner {
    color: #fff;
    font-weight: 500;
  }
}
