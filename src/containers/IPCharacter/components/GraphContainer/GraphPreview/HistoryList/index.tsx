// 预览页面右侧历史记录页面

import React, { useRef, useContext, useEffect } from 'react';
import styles from './index.module.less';
import classNames from 'classnames';
import { ImagesContainerContext } from '../../context';
import { useImageListState } from '../../Hooks';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Loading } from '@/components';
import { Empty, Image } from 'antd';
import empty from '@/assets/images/empty.jpg';

import { Graph, LoadingStatus, GraphStatus } from '@/types';
import { LoadFailed } from '../../GraphList/GraphBatch';
import { useParams, useNavigate } from 'react-router-dom';
import { toAtlasImageView2URL } from '@meitu/util';
import { DraftType } from '@/api/types';
import { SlashCircle } from '@meitu/candy-icons';

interface HistoryListProps {
  taskCategory: DraftType;
}

const HistoryList: React.FC<HistoryListProps> = (props) => {
  const { id } = useParams();
  const { list, hasMore, getMore } = useImageListState();
  const navigate = useNavigate();
  const itemRefs = useRef<Array<HTMLDivElement | null>>([]);
  // const scrollRefs = useRef<Boolean>(false);
  const {
    setScrollTop,
    setBatchOrigin,
    historyActiveIndex,
    setHistoryActiveIndex
  } = useContext(ImagesContainerContext);
  // 获取展示的缩略图
  useEffect(() => {
    const index = list.findIndex((item) => item.id === id);
    setHistoryActiveIndex(index);

    // 让页面滚动到元素所在位置
    const desiredElement = itemRefs.current?.[index];
    if (desiredElement) {
      scrollTo(desiredElement?.offsetTop);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const item = list.find((item) => item.id === id);
    const index = list.findIndex((item) => item.id === id);
    if (
      item?.batch.length === 0 &&
      item?.loadingStatus !== LoadingStatus.LOADING
    ) {
      let item = list[index + 1] || list[index - 1];

      // 如果列表还有数据，选中临近的一条；没有则跳转列表页
      if (item) {
        navigate(
          `/ai/ip-character/${
            props.taskCategory === DraftType.IP_CONCEPT
              ? 'concept'
              : 'customization'
          }/preview/${item?.id}`,
          {
            replace: true
          }
        );
      } else {
        navigate(
          `/ai/ip-character/${
            props.taskCategory === DraftType.IP_CONCEPT
              ? 'concept'
              : 'customization'
          }`,
          {
            replace: true
          }
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [list]);
  const getDisplaySrc = (src: string) => {
    // 如果src为空，返回空字符串
    if (!src) {
      return '';
    }
    return toAtlasImageView2URL(src ?? '', {
      mode: 2,
      width: 128
    });
  };
  const changePreview = (id: string, index: number) => {
    let item = list.find((item) => item.id === id);

    navigate(
      `/ai/ip-character/${
        props.taskCategory === DraftType.IP_CONCEPT
          ? 'concept'
          : 'customization'
      }/preview/${item?.id}`,
      {
        replace: true
      }
    );
    setHistoryActiveIndex(index);
    setBatchOrigin([
      {
        src: '',
        status: GraphStatus.AUDITING
      }
    ]);
    sessionStorage.setItem('activeId', id);
    const desiredElement = itemRefs.current?.[index];
    if (desiredElement) {
      // scrollTo(desiredElement?.offsetTop);
      // let columnNumber = sessionStorage.getItem('columnNumber') ?? 4;
      // console.log(columnNumber, 'columnNumber', desiredElement?.offsetTop);
      setScrollTop(desiredElement?.offsetTop);
    }
  };
  const scrollTo = (num: number) => {
    const scrollableBox = document.getElementById('scrollableBox');
    scrollableBox?.scrollTo?.({ top: num, behavior: 'smooth' });
  };

  return (
    <div className={styles.historyList}>
      <div className={styles.historyTitle}>历史记录</div>
      <div className={styles.listBox} id="scrollableBox">
        {list.length > 0 ? (
          <InfiniteScroll
            scrollableTarget="scrollableBox"
            dataLength={list.length}
            next={getMore}
            hasMore={hasMore}
            loader={<Loading />}
            endMessage={
              <p style={{ textAlign: 'center', paddingBottom: '16px' }}>
                没有更多了～
              </p>
            }
          >
            {list.map((item, index) => (
              <div
                className={classNames(
                  styles.listItem,

                  historyActiveIndex === index && styles.active,
                  item.batch.length === 1
                    ? styles.historyItemOnly
                    : styles.historyItemMore,
                  item.batch.length === 0 && styles.noImage
                )}
                key={item.id}
                onClick={() => {
                  // 非成功的，不可点击预览
                  // if (item.loadingStatus !== LoadingStatus.SUCCESS) return;
                  if (id === item.id) return;
                  changePreview(item.id, index);

                  // itemClicked(index, item.id);
                }}
                ref={(el) => (itemRefs.current[index] = el)}
              >
                {item.loadingStatus === LoadingStatus.SUCCESS ? (
                  <>
                    {item.batch.map((image: Graph, index) => {
                      if (image?.status === 'success') {
                        return (
                          <Image
                            key={image?.src}
                            src={getDisplaySrc(image?.src ?? '')}
                            // placeholder={<Loading />}
                            className={styles.itemImg}
                            preview={false}
                          />
                        );
                      } else {
                        return (
                          <div className={styles.itemImgErr} key={index}>
                            <SlashCircle className={styles.itemImgErrIcon} />
                          </div>
                        );
                      }
                    })}
                  </>
                ) : item.loadingStatus === LoadingStatus.LOADING ? (
                  <div className={styles.error}>
                    <Loading />
                  </div>
                ) : (
                  <div className={styles.historyItem}>
                    <LoadFailed type={item.loadingStatus} showText={false} />
                  </div>
                )}
              </div>
            ))}
          </InfiniteScroll>
        ) : (
          <Empty
            className={styles.empty}
            image={empty}
            description={'暂无数据'}
          />
        )}
      </div>
    </div>
  );
};
export default HistoryList;
