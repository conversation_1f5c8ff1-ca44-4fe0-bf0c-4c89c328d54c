@import '~@/styles/variables.less';

.actions {
  position: absolute;
  top: @size;
  right: @size;
  z-index: 2;

  &:global(.@{ant-prefix}-space-gap-col-small) {
    row-gap: 0;
    column-gap: 0;

    :global(.@{ant-prefix}-space-item) {
      &:not(:first-child) {
        margin-top: 8px;
      }
    }
  }

  :global .@{ant-prefix}-btn.@{ant-prefix}-btn-icon-only {
    display: flex;
    align-items: center;
    justify-content: center;
    width: calc(@size + @size-sm);
    height: calc(@size + @size-sm);
  }

  &.primary {
    :global .@{ant-prefix}-btn.@{ant-prefix}-btn-icon-only {
      background-color: @background-hover-tips;
      color: @content-btn-primary;
      border: none;

      path {
        fill: @content-btn-primary;
      }
    }
  }

  &.default {
    :global .@{ant-prefix}-btn.@{ant-prefix}-btn-icon-only {
      background-color: @background-btn-secondary;
      color: @content-btn-secondary;
      border: none;
      box-shadow: 0px 2px 8px 0px #0000000f;

      path {
        fill: @content-btn-secondary;
      }
    }
  }

  &.history {
    right: 192px;
  }
}

.repaint-box {
  position: relative;

  .inside-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 28px;
    height: 16px;
    border-radius: 4px;
    background: linear-gradient(91deg, #d4fcca 1.27%, #c7ecff 98.73%);
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      color: var(--content-navBarTag, #1c1d1f);
      font-size: 12px;
      transform: scale(0.84);
      font-style: normal;
      font-weight: 600;
    }
  }
}
