import type { ButtonProps } from 'antd';
import {
  createContext,
  useContext,
  type PropsWithChildren,
  useRef,
  useEffect
} from 'react';

import { Button, Space, Tooltip } from 'antd';
import {
  DownloadBold,
  LosslessAmplificationBold,
  PublishBold,
  RedrawBold,
  TrashCanBold,
  ResolutionBold,
  ClockBold,
  CompareBold,
  RegionVaryBold,
  ImageToImageBold,
  AiDeepenBold,
  EliminateBold
} from '@meitu/candy-icons';
import classNames from 'classnames';

import styles from './index.module.less';
import { Link } from 'react-router-dom';
import { AppModule, generateRouteTo } from '@/services';

/**
 * 仅展示icon的按钮
 * 文字通过tooltip展示
 * @returns
 */
export function TooltipButton({ children, ...buttonProps }: ButtonProps) {
  return (
    <Tooltip overlay={children}>
      <Button {...buttonProps} />
    </Tooltip>
  );
}

/**
 * 与Actions中的buttonRender属性关联
 *
 * 如果Actions设置了buttonRender属性，则使用buttonRender渲染
 * 如果Actions没有设置buttonRender属性，则使用TooltipButton渲染
 */
export function CommonButton(props: ButtonProps) {
  const { buttonRender } = useContext(ActionsContext);
  if (buttonRender) {
    return buttonRender(props);
  }

  return <TooltipButton {...props} />;
}

function PublishButton({ children, ...props }: ButtonProps) {
  return (
    <CommonButton icon={<PublishBold />} {...props}>
      {children ?? '发布'}
    </CommonButton>
  );
}

function RefreshButton(props: ButtonProps) {
  return (
    <CommonButton icon={<RedrawBold />} {...props}>
      重新编辑
    </CommonButton>
  );
}

function DownloadButton(props: ButtonProps) {
  return (
    <CommonButton icon={<DownloadBold />} {...props}>
      下载
    </CommonButton>
  );
}

function ExtensionButton({ children, ...props }: ButtonProps) {
  return (
    <CommonButton icon={<LosslessAmplificationBold />} {...props}>
      {children ?? 'AI扩图'}
    </CommonButton>
  );
}

function RemoveButton(props: ButtonProps) {
  return (
    <CommonButton icon={<TrashCanBold />} {...props}>
      删除
    </CommonButton>
  );
}

function UpscalerButton(props: ButtonProps) {
  return (
    <CommonButton icon={<ResolutionBold />} {...props}>
      AI超清
    </CommonButton>
  );
}

function HistoryButton(props: ButtonProps) {
  return (
    <CommonButton icon={<ClockBold />} {...props}>
      历史记录
    </CommonButton>
  );
}

function RepaintButton(
  props: ButtonProps & { editorImageUrl: string; className?: string }
) {
  return (
    <Link
      to={generateRouteTo(AppModule.ImageEditor, {
        editorImageUrl: props.editorImageUrl
      })}
      target="_blank"
    >
      <div className={styles.repaintBox}>
        <CommonButton icon={<RegionVaryBold />} onClick={props.onClick}>
          AI改图
        </CommonButton>
        <div className={classNames(styles.insideBadge, props.className)}>
          <span>内测</span>
        </div>
      </div>
    </Link>
  );
}

function CompareButton(props: ButtonProps) {
  return (
    <CommonButton icon={<CompareBold />} {...props}>
      长按对比
    </CommonButton>
  );
}

function ImgToImgButton(props: ButtonProps) {
  return (
    <CommonButton icon={<ImageToImageBold />} {...props}>
      图生图
    </CommonButton>
  );
}

function DeeperDesignButton(props: ButtonProps) {
  return (
    <CommonButton icon={<AiDeepenBold />} {...props}>
      延伸创作
    </CommonButton>
  );
}

function EraserButton(props: ButtonProps) {
  return (
    <CommonButton icon={<EliminateBold />} {...props}>
      AI无痕消除
    </CommonButton>
  );
}

interface ActionsProps {
  type?: 'default' | 'primary';
  className?: string;
  /**
   * 自定义渲染按钮
   */
  buttonRender?: (props: ButtonProps) => React.ReactElement;
  /**
   * 通过scale的方式限制高度
   */
  limitHeightWithScale?: LimitHeightWithScaleType;
  showHistoryList?: boolean;
}

const ActionsContext = createContext<Pick<ActionsProps, 'buttonRender'>>({});

export function Actions({
  children,
  type = 'primary',
  className,
  buttonRender,
  limitHeightWithScale,
  showHistoryList = false
}: PropsWithChildren<ActionsProps>) {
  const { elementRef } = useLimitHeightWithScale(limitHeightWithScale);

  return (
    <ActionsContext.Provider value={{ buttonRender }}>
      <Space
        direction="vertical"
        className={classNames(
          styles.actions,
          styles[type],
          showHistoryList ? styles.history : '',
          className
        )}
        ref={elementRef}
      >
        {children}
      </Space>
    </ActionsContext.Provider>
  );
}

type LimitHeightWithScaleType = {
  /**
   * 超过这个值 则应用scale缩小到该值
   */
  maxHeight: number;
  transformOrigin: React.CSSProperties['transformOrigin'];
};
function useLimitHeightWithScale(option?: LimitHeightWithScaleType) {
  const element = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const dom = element.current!;
    // 在计算原始大小之前将缩放比例重置为1
    dom.style.transform = `scale(1)`;
    const domHeight = dom.clientHeight;

    if (!option || option.maxHeight <= 0 || !domHeight) {
      return;
    }

    const { maxHeight, transformOrigin } = option;

    const scale =
      maxHeight > domHeight
        ? // 如果元素高度没有大于限制高度 不用缩小
          1
        : // 如果元素高度大于限制高度 通过scale缩小到maxHeight
          maxHeight / domHeight;
    dom.style.transform = `scale(${scale})`;
    dom.style.transformOrigin = `${transformOrigin}`;

    return () => {
      dom.style.transform = `scale(1)`;
    };
  }, [option]);

  return {
    elementRef: element
  };
}

Actions.PublishButton = PublishButton;
Actions.RefreshButton = RefreshButton;
Actions.DownloadButton = DownloadButton;
Actions.ExtensionButton = ExtensionButton;
Actions.RemoveButton = RemoveButton;
Actions.UpscalerButton = UpscalerButton;
Actions.HistoryButton = HistoryButton;
Actions.RepaintButton = RepaintButton;
Actions.CompareButton = CompareButton;
Actions.ImgToImg = ImgToImgButton;
Actions.DeeperDesignButton = DeeperDesignButton;
Actions.Eraser = EraserButton;
