import { Button, Modal } from 'antd';
import { CrossBoldOutlined } from '@meitu/candy-icons';
import styles from './styles.module.less';
import { Meido<PERSON> } from '@/icons';
import { TextArea } from '@/components/TextArea/TextArea';
import { useEffect, useState } from 'react';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useOpenSubscribePopup } from '@/hooks/useSubscribe';
import { MemberGroupCategory } from '@/types/meidou';
import { FunctionCode } from '@/api/types/meidou';
import { toSnakeCase } from '@meitu/util';
import { fetchPriceDesc } from '@/api/meidou';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';

export default function DeeperDesignModal({
  open,
  onCommit,
  onCancel,
  onChange,
  value
}: {
  open: boolean;
  onCommit: () => void;
  onCancel: () => void;
  onChange: (value: string) => void;
  value?: string;
}) {
  const [loading, setLoading] = useState(false);
  const { availableAmount } = useMeiDouBalance();
  const openSubscribePopup = useOpenSubscribePopup();
  const [price, setPrice] = useState<number>();
  const [deficit, setDeficit] = useState(true);

  useEffect(() => {
    setPriceAsync();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const setPriceAsync = async () => {
    setLoading(true);
    const fixtureParams = {
      functionCode: FunctionCode.text2img,
      functionBody: JSON.stringify(
        toSnakeCase({
          params: {
            batchSize: 4
          }
        })
      )
    };
    try {
      const res = await fetchPriceDesc(fixtureParams);
      setPrice(res?.amount);
      setDeficit(
        Boolean(availableAmount && res && availableAmount < res.amount)
      );
      setLoading(false);
    } catch (e) {
      defaultErrorHandler(e);
    }
  };

  const onClick = () => {
    if (loading) {
      return;
    }
    if (deficit) {
      // 余额不足时，调用美豆充值弹窗
      openSubscribePopup(MemberGroupCategory.Meidou);
      return;
    }

    onCommitClickAsync();
  };

  const handleCancel = (): void => {
    onCancel();
  };

  const onCommitClickAsync = async () => {
    setLoading(true);
    try {
      await onCommit();
    } finally {
      setLoading(false);
    }
  };

  const onTextAreaChange = (e: any) => {
    onChange(e.target.value);
  };

  return (
    <Modal
      width={420}
      centered
      open={open}
      closeIcon={false}
      onCancel={handleCancel}
      footer={null}
    >
      <CrossBoldOutlined onClick={handleCancel} className={styles.closeIcon} />
      <div className={styles.modalHeader}>
        <div className={styles.title}>延伸创作</div>
        <div className={styles.content}>
          <TextArea
            className={styles.textArea}
            value={value}
            showCount
            allowClear
            maxLength={800}
            placeholder="请输入关键词。"
            onChange={onTextAreaChange}
          />
          <div className={styles.remark}>
            <span>修改原提示词可在保留风格的同时再次生成更多创意。</span>
          </div>
          <div className={styles.buttonContainer}>
            <Button
              loading={loading}
              type="primary"
              className={styles.commitButton}
              onClick={onClick}
            >
              <span>延伸创作</span>
              <Meidou />
              <span className={styles.price}>
                {deficit ? '余额不足' : `${price}美豆`}
              </span>
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
