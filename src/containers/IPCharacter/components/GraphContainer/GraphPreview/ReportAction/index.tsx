import { useApp } from '@/App';
import { AppOrigin, appOrigin } from '@/constants';
import { Report as ReportSvg } from '@/icons';
import { CommonButton } from '../Actions';

interface ReportActionProps {
  /** 作品的任务id */
  id: string;
}
/**
 * 临时需求 举报按钮
 */
export function ReportAction(props: ReportActionProps) {
  const { id } = props;
  const { openReportModal } = useApp();
  const onReport = () => {
    openReportModal(id);
  };

  return appOrigin === AppOrigin.MiracleVision ? (
    <CommonButton onClick={onReport} icon={<ReportSvg />}>
      举报
    </CommonButton>
  ) : null;
}
