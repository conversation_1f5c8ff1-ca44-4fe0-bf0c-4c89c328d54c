import { ThreeDBoldFill } from '@meitu/candy-icons';
import classNames from 'classnames';
import styles from './index.module.less';

interface To3DCompareProps {
  /** 原始图片路径 */
  originGraph: string;
  /** 缩放模式 */
  zoomMode?: number;
  /** 对比时触发 */
  onCompare?: (originGraph: string) => void;
  /** 是否原图 */
  isOrigin: boolean;
  setIsOrigin: (val: boolean) => void;
}

function getBackgroundSize(zoomMode?: number) {
  switch (zoomMode) {
    case 1:
      return 'cover';
    case 0:
      return '100% 100%';
    default:
      return 'contain';
  }
}
export function To3DCompare(props: To3DCompareProps) {
  const { originGraph, zoomMode, isOrigin, setIsOrigin } = props;

  return (
    <>
      <div
        className={classNames(styles.comparingMask, isOrigin && styles.active)}
        style={{
          backgroundImage: `url(${originGraph})`,
          backgroundSize: getBackgroundSize(zoomMode)
        }}
      />

      <div className={styles.successCorner}>
        <div
          className={classNames(
            !isOrigin && styles.activeCorner,
            styles.iconsBox
          )}
          onClick={(e) => {
            e.stopPropagation();
            setIsOrigin(false);
          }}
        >
          <ThreeDBoldFill className={styles.icons} />
          平面转3D
        </div>
        <div className={styles.line}></div>
        <div
          className={classNames(
            isOrigin && styles.activeCorner,
            styles.iconsBox
          )}
          onClick={(e) => {
            e.stopPropagation();
            setIsOrigin(true);
          }}
        >
          原始
        </div>
      </div>
    </>
  );
}
