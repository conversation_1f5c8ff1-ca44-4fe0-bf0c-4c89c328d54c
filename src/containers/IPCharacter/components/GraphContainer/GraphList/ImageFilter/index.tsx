import { type RefSelectProps, Select } from 'antd';
import {
  FunctionsGroupBold,
  PublishBold,
  FivePointedStarBold,
  ChevronDownBlack
} from '@meitu/candy-icons';
import { useRef, useState } from 'react';

import { FilterType } from '@/types';
import styles from './index.module.less';

export interface ImageFilterProps {
  value: FilterType;
  onChange: (value: FilterType) => void;
  fixturesOptions?: (option: OptionsType) => OptionsType;
}

type OptionsType = typeof options;

const options = [
  {
    value: FilterType.ALL,
    label: (
      <span>
        <FunctionsGroupBold />
        全部
      </span>
    )
  },
  {
    value: FilterType.PUBLISHED,
    label: (
      <span>
        <PublishBold />
        已发布
      </span>
    )
  },
  {
    value: FilterType.COLLECTED,
    label: (
      <span>
        <FivePointedStarBold />
        收藏
      </span>
    )
  }
];

export function ImageFilter({
  fixturesOptions,
  value,
  onChange
}: ImageFilterProps) {
  const selectRef = useRef<RefSelectProps>(null);
  const [open, setOpen] = useState(false);

  return (
    <Select
      ref={selectRef}
      value={value}
      options={fixturesOptions?.(options) ?? options}
      suffixIcon={
        <ChevronDownBlack
          onClick={() => {
            setOpen(!open);
          }}
        />
      }
      open={open}
      className={styles.imageFilter}
      popupClassName={styles.imageFilterPopup}
      onDropdownVisibleChange={setOpen}
      onChange={(value) => {
        onChange(value);
        selectRef.current?.blur();
      }}
    />
  );
}
