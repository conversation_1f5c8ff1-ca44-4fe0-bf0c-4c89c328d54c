@import '~@/styles/variables.less';

.image-filter {
  width: 112px;

  :global {
    .@{ant-prefix}-select-selector {
      display: flex;
      align-items: center;
      height: 26px !important;
      border-radius: @size-xs;

      .@{ant-prefix}-select-selection-item {
        font-size: @font-size-sm;

        .@{ant-prefix}icon {
          font-size: @font-size;
          margin-right: calc(@size-sm / 2);
        }
      }
    }

    .@{ant-prefix}-select-arrow {
      font-size: 10px !important;
      padding-left: calc(@size-sm / 2);
      border-left: 1px solid @stroke-system-separator;
      margin-top: -9px !important;
    }
  }

  &-popup:global(.ant-select-dropdown) {
    padding-left: 0;
    padding-right: 0;

    :global {
      .@{ant-prefix}-select-item-option {
        font-size: @font-size-sm;

        .@{ant-prefix}icon {
          font-size: @font-size;
          margin-right: calc(@size-sm / 2);
        }

        &-content {
          display: flex;
          align-items: center;

          & > span {
            display: flex;
            align-items: center;
          }
        }
      }

      .@{ant-prefix}-select-item-option-selected:not(
          .@{ant-prefix}-select-item-option-disabled
        ) {
        color: @content-list-selected;
        background-color: transparent;
      }
    }
  }
}
