@import '~@/styles/variables.less';

.graph-batch {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: @color-bg-base;
  border: 1px solid @stroke-system-border-overlay;
  border-radius: @border-radius-lg;
  overflow: hidden;

  &-content {
    height: 100%;
    cursor: pointer;
  }

  :global .@{ant-prefix}-image {
    width: 100%;
    height: 100%;

    img {
      max-width: 100%;
      max-height: 100%;
      min-width: 100%;
      min-height: 100%;
      object-fit: cover;
    }

    &-mask {
      opacity: 1 !important;
      background: transparent !important;

      .loading {
        background: transparent;
      }

      .upscaler-mask {
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.4);

        .upscaler-text {
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.7);
          margin-top: 16px;
        }
      }
    }
  }

  &-multiple-icon {
    position: absolute;
    left: @size-xs;
    top: @size-xs;
    font-size: @size-sm;
    line-height: @size;

    :global path {
      fill: @color-bg-base;
    }

    .tag {
      display: inline-flex;
      align-items: center;
      height: 24px;
      padding: 0 6px;
      vertical-align: middle;

      .icon {
        margin-left: 4px;

        svg {
          width: 18px;
          height: 18px;
        }
      }

      .three-icon {
        margin-right: 3px;

        svg {
          width: 14px;
          height: 14px;
        }
      }
    }
  }

  &-content:hover {
    color: @color-text-base;
  }

  &:hover &-mask {
    opacity: 1;
  }

  &-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;

    .actions {
      position: absolute;
      bottom: @size-xs;
      right: @size-xs;
      display: grid;
      grid-auto-flow: column;
      gap: 6px;
    }

    .mask-btn:global(.@{ant-prefix}-btn-text),
    .mask-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: @base-black-opacity-50;
      color: @color-bg-base;
      border-radius: @size-xxs;
      padding: 6px;
      font-size: @size;
      width: 28px;
      height: 28px;

      > span {
        transform: scale(1) !important;
      }

      &:not(:disabled):not(.@{ant-prefix}-btn-disabled):hover {
        background-color: @base-black-opacity-35;
        color: @color-bg-base;
      }

      &:active {
        background-color: @base-black-opacity-75;
      }

      &.collection-switch {
        position: absolute;
        right: @size-xs;
        top: @size-xs;

        & > div {
          font-size: @size;
        }
      }
    }
  }
}

.invisible {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-image: url('~@/assets/images/invisible-graph.jpg');
  background-size: cover;
  background-position: center center;
  color: @color-white;
  line-height: @line-height-lg;

  :global .@{ant-prefix}-image {
    margin-bottom: @size;
  }

  &-icon {
    font-size: calc(@size-xxl + @size-sm);
    margin-bottom: @size;

    :global path {
      fill: @color-white;
    }
  }
}

.irregularity {
  background-image: url('~@/assets/images/irregularity.png');
}

.timeout {
  background-image: url('~@/assets/images/timeout.png');
}

.count-up {
  position: absolute;
  left: @size-xs;
  bottom: @size-xs;
}
