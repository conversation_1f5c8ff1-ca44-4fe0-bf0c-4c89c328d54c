import type { <PERSON>raphBatch } from '@/types/draft';
import { GraphStatus } from '@/types/draft';
import { SearchBold } from '@meitu/candy-icons';
import { Waterfall } from '@/components';
import GraphBatchContainer from './GraphBatch';
import type { GraphBatchProps } from './GraphBatch';
import PublishModal, { PublishModalRef } from '../PublishModal';
import { useNavigate } from 'react-router-dom';
import { useRef, useEffect, useState, useContext } from 'react';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { App, Modal, Empty, Input } from 'antd';
import { ImageFilter, ImageFilterProps } from './ImageFilter';
import { downloadFile } from '@/utils/blob';
import styles from '../styles.module.less';
import { AppOrigin, EditorMode, appOrigin } from '@/constants';
import produce from 'immer';
import { FilterType } from '@/types';
import empty from '@/assets/images/empty.jpg';
import classNames from 'classnames';
import { AppModule } from '@/services';
import {
  useCollectionChange,
  useCreateLoadingTask,
  useFetchTaskLooper,
  useFetchTo3DTaskLooper,
  useFetchUpscalerTaskLooper,
  useFilterType,
  useImageListState,
  useKeywords,
  useRemoveImageBatch
} from '../Hooks';
import { ImagesContainerContext } from '../context';
import { downloadImage } from '@/api';

type SearchTrigger = 'enter' | 'blur' | 'input';
interface ImageListProps
  extends Pick<GraphBatchProps, 'hiddenActionKeys'>,
    Pick<ImageFilterProps, 'fixturesOptions'> {
  onImageCreated?: (images: GraphBatch[]) => void;

  /** 重新编辑事件 */
  onReEditParams?: (id: string) => void;

  /** 图片下载 */
  onDownload?: (id: string) => void;

  /** 是否有同步至画廊操作按钮的权限 */
  hasPublishGalleryControlAccess?: boolean;

  /** 是否有允许使用画面控制图片操作按钮的权限 */
  hasControlnetImageControlAccess?: boolean;
  /**  自定义空列表的icon */
  emptyIcon?: React.ReactNode;
  /** 空列表文案 */
  emptyDesc?: (type: FilterType) => string;
  /** 页面类型 */
  appModule?: AppModule;
  searchTrigger?: SearchTrigger[];
}

const emptyDescribtion = {
  [FilterType.ALL]: '你还没有生成过创意喔～',
  [FilterType.COLLECTED]: '您还没有收藏过作品',
  [FilterType.PUBLISHED]: '您还没有发布过作品'
};

export function GraphList(props: ImageListProps) {
  const { list, loading, hasMore, getMore } = useImageListState();

  const navigate = useNavigate();
  const { message } = App.useApp();
  const scrollRef = useRef<HTMLDivElement>(null);
  const {
    scrollTop,
    setScrollTop,
    toPreview,
    graphBatch,
    updateGraphBatch,
    imageSourceKey,
    onImageCreated: onImageCreatedByCtx
  } = useContext(ImagesContainerContext);
  const [getNeedConfirm, removeGraphBatch] = useRemoveImageBatch();
  const [modal, contextHolder] = Modal.useModal();
  const [needScroll, setNeedScroll] = useState(false);
  const publishModalRef = useRef<PublishModalRef>(null);
  const inputSearchRef = useRef<any>(null);

  const completedTask = useFetchTaskLooper();

  useCreateLoadingTask(scrollRef.current);
  useFetchUpscalerTaskLooper();
  useFetchTo3DTaskLooper();

  useEffect(
    () => {
      scrollTop && setNeedScroll(true);
      const container = scrollRef.current;

      function handleScroll() {
        setScrollTop(container?.scrollTop ?? 0);
      }

      // HACK 兼容处理 Safari
      // Safari 下 useLayoutEffect可能会在更新之前执行
      // 使用 useEffect 代替，或者在setTimeout中添加一个延迟时间
      setTimeout(() => {
        if (!container) {
          setNeedScroll(false);
          return;
        }

        container.addEventListener('scroll', handleScroll);

        if (scrollTop) {
          container.scrollTop = scrollTop;
        }

        setNeedScroll(false);
      }, 256);

      setTimeout(() => {
        // 从sessionStorage中获取当前的id 如果存在就查找对应的dom 并滚动到对应的位置
        const id = sessionStorage.getItem('activeId');
        // console.log(id)
        if (id) {
          const dom = document.getElementById(id);
          if (dom) {
            let top = dom.offsetTop;
            setScrollTop(top ?? 0);
            if (scrollRef.current) {
              scrollRef.current.scrollTop = top;
            }
            sessionStorage.removeItem('activeId');
          }
        }
      }, 2000);

      return () => {
        container?.removeEventListener('scroll', handleScroll);
      };
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  useEffect(
    () => {
      if (completedTask?.length) {
        props.onImageCreated?.(completedTask);
        onImageCreatedByCtx?.(completedTask);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [completedTask]
  );

  const onGraphClick: GraphBatchProps['onClick'] = (id) => {
    setScrollTop(scrollRef.current?.scrollTop ?? 0);

    const navigateToPreview = toPreview
      ? () => {
          toPreview(id.toString());
        }
      : () => {
          navigate(`preview/${id}${window.location.search}`, { replace: true });
        };

    navigateToPreview();
  };

  const onGraphRemove: GraphBatchProps['onRemove'] = (id) => {
    // 点击删除可不提示直接删除即可
    if (getNeedConfirm(id)) {
      removeGraphBatch(id);
      return;
    }

    modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '是否删除此草稿内所有图片',
      okButtonProps: {
        danger: true
      },
      okText: '删除',
      cancelText: '取消',
      onOk: () => removeGraphBatch(id)
    });
  };

  /** 发布作品 */
  const onPublish: GraphBatchProps['onPublish'] = (id) => {
    publishModalRef.current?.open({
      id,
      graph: graphBatch[id]?.batch,
      canUseSame: true, // 默认是开启允许使用同款
      canPostToGallery: props.hasPublishGalleryControlAccess,
      hasPublishGalleryControlAccess: props.hasPublishGalleryControlAccess,
      // 高级模式生成的作品才有 Controlnet 相关权限
      hasControlnetImageControlAccess:
        props.hasControlnetImageControlAccess &&
        graphBatch[id]?.editorMode === EditorMode.Advanced
    });
  };

  const onPublishSuccess: GraphBatchProps['onPublishSuccess'] = (value) => {
    const { id } = value;
    let nextGraphBatch = produce(graphBatch, (draft) => {
      draft[id].isPublish = true;
    });
    updateGraphBatch(nextGraphBatch[id]);
  };

  const onDownload: GraphBatchProps['onDownload'] = async (
    id,
    setDownloading
  ) => {
    const currentGraph = graphBatch[id];
    if (!currentGraph?.batch?.length) {
      return;
    }
    const imageUrls = currentGraph.batch.reduce((acc, curr) => {
      return curr.status === GraphStatus.SUCCESS &&
        (curr?.[imageSourceKey] || curr?.src)
        ? acc.concat(curr?.[imageSourceKey] || curr?.src || '')
        : acc;
    }, [] as string[]);

    if (!imageUrls?.length) {
      return;
    }

    setDownloading(true);
    try {
      await downloadFile(imageUrls);
      message.success('下载成功');
      props?.onDownload?.(id);
      downloadImage({ msgId: id, imageUrl: imageUrls.join(',') });
    } catch (error) {
      message.error('下载失败');
    }
    setDownloading(false);
  };

  const onCollect = useCollectionChange();
  const [filterType, setFilterType] = useFilterType();
  const [keyword, onKeywordsChange, onKeywordsSearch] = useKeywords();
  const handleSearch = () => {
    onKeywordsSearch();
  };
  return (
    <div className={styles.draftContainer}>
      <div ref={scrollRef} className={styles.scroll}>
        {appOrigin === AppOrigin.Whee && (
          <div className={styles.draftContainerHeader}>
            <ImageFilter
              fixturesOptions={props.fixturesOptions}
              value={filterType}
              onChange={setFilterType}
            />

            <Input
              className={styles.search}
              ref={inputSearchRef}
              value={keyword}
              allowClear
              onChange={({ target: { value }, type }) => {
                onKeywordsChange(value);
                if (type === 'click') {
                  handleSearch();
                }
              }}
              onPressEnter={({ target }) => {
                handleSearch();
              }}
              onBlur={({ target, type }) => {
                handleSearch();
              }}
              placeholder={
                props.appModule === AppModule.IPCharacterConcept
                  ? '可输入“形象关键词”搜索'
                  : '可输入“场景关键词”搜索'
              }
              prefix={
                <SearchBold
                  onClick={(e) => {
                    // 阻止冒泡
                    e.stopPropagation();
                    inputSearchRef?.current?.blur();
                    if (keyword !== '') {
                      handleSearch();
                    }
                  }}
                />
              }
            />
          </div>
        )}
        <div className={styles.draftContainerWaterfall}>
          <Waterfall<GraphBatchProps>
            batches={list}
            hasMore={hasMore}
            getMore={getMore}
            loading={loading || needScroll}
            isAtBottom={scrollTop > 0}
            empty={
              <Empty
                image={props.emptyIcon ?? empty}
                description={
                  keyword
                    ? '没有相关作品'
                    : props.emptyDesc?.(filterType) ||
                      emptyDescribtion[filterType]
                }
                className={classNames(styles.draftContainerPlaceholder)}
              />
            }
            renderItem={(itemProps: GraphBatchProps) => {
              const hiddenKeys = props.hiddenActionKeys?.slice(0) ?? [];
              if (itemProps.disableEdit) {
                hiddenKeys.push('reEdit');
              }

              return (
                <GraphBatchContainer
                  {...itemProps}
                  id={itemProps.id}
                  hiddenActionKeys={hiddenKeys}
                  onClick={onGraphClick}
                  onRemove={onGraphRemove}
                  onReEdit={(id) => {
                    props.onReEditParams?.(id);
                  }}
                  onPublish={onPublish}
                  onPublishSuccess={onPublishSuccess}
                  onDownload={onDownload}
                  onCollect={onCollect}
                />
              );
            }}
          />
          {contextHolder}
        </div>
        <div className={styles.filter} />
      </div>
      <PublishModal ref={publishModalRef} />
    </div>
  );
}
