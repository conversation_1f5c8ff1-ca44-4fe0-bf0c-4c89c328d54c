import { useSyncMemberDescErrorHand<PERSON> } from '@/hooks/useMember';
import { atom, useRecoilState, useResetRecoilState } from 'recoil';
import {
  createTo3DImageTask as createTo3DImageTaskApi,
  fetchTaskByIds
} from '@/api';
import { CreateTo3DImageTaskBody, MtccFuncCode } from '@/api/types';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useContext, useState } from 'react';
import { useLooper } from '@/hooks';
import { GraphBatch, LoadingStatus } from '@/types';
import { ImagesContainerContext } from '../context';
import produce from 'immer';

/**
 * 超分任务的 id
 */
const imageTo3DTaskIds = atom<string[]>({
  key: 'imageTo3DTaskIds',
  default: []
});

export function useImageTo3DState() {
  const [to3DLoadingIds, setTo3DLoadingIds] = useRecoilState(imageTo3DTaskIds);
  const resetTo3DLoadingIds = useResetRecoilState(imageTo3DTaskIds);
  const handleError = useSyncMemberDescErrorHandler(
    undefined,
    MtccFuncCode.FuncCodePlaneTo3d
  );
  const { updateMeiDouBalance } = useMeiDouBalance();

  /** 创建超分任务 */
  const createTo3DImageTask = async (createParams: CreateTo3DImageTaskBody) => {
    try {
      const { id } = await createTo3DImageTaskApi({
        ...createParams,
        functionName: MtccFuncCode.FuncCodePlaneTo3d
      });
      const nextIds = produce(to3DLoadingIds, (draft) => {
        draft.push(id);
      });
      setTo3DLoadingIds(nextIds);

      return { id };
    } catch (error) {
      handleError(error);
    } finally {
      updateMeiDouBalance();
    }
  };

  return {
    to3DLoadingIds,
    setTo3DLoadingIds,
    resetTo3DLoadingIds,
    createTo3DImageTask
  };
}

/**
 * 轮询获取超分任务
 */
export function useFetchTo3DTaskLooper() {
  const { graphBatch, setGraphBatch } = useContext(ImagesContainerContext);
  const [to3DLoadingIds, setTo3DLoadingIds] = useRecoilState(imageTo3DTaskIds);
  const [completedTask, setCompletedTask] =
    useState<Record<string, GraphBatch>>();
  const { updateMeiDouBalance } = useMeiDouBalance();

  useLooper({
    callback: async () => {
      try {
        const result = await fetchTaskByIds({
          ids: to3DLoadingIds.join(',')
        });

        // 已经处理完的任务，替换原数据
        const doneTask = result.filter(
          ({ loadingStatus }) => loadingStatus !== LoadingStatus.LOADING
        );

        // 如果有失败的，更新美豆
        if (
          doneTask.some(
            ({ loadingStatus }) => loadingStatus !== LoadingStatus.SUCCESS
          )
        ) {
          updateMeiDouBalance();
        }

        const nextGraphBatchData = produce(graphBatch, (draft) => {
          doneTask.forEach((item) => {
            const graphBatchId = item.wheeMsgId ?? 0;

            // 通过超分id匹配 要更新的batch
            let doneIndex = draft[graphBatchId]?.batch.findIndex(
              (batchItem) => {
                return batchItem.planeto3DInfo?.id === item.id;
              }
            );

            if (item.loadingStatus === LoadingStatus.SUCCESS) {
              draft[graphBatchId].batch[doneIndex] = item.batch?.[0];
            } else {
              draft[graphBatchId].batch[doneIndex].planeto3DInfo =
                item.batch?.[0].planeto3DInfo;
            }
          });
        });

        setGraphBatch(nextGraphBatchData);
        setCompletedTask(nextGraphBatchData);

        // 未完成的 同步更新LoadingIds
        const loadingTask = result.filter(
          ({ loadingStatus }) => loadingStatus === LoadingStatus.LOADING
        );

        const loadingIds = loadingTask.map(({ id }) => id);

        setTo3DLoadingIds(loadingIds);
      } catch (e) {
        console.log(e);
      }
    },
    trigger: () => to3DLoadingIds.length > 0,
    duration: 4096
  });

  return completedTask;
}
