import { type TaskParams, UpscalerTaskStatus } from '@/api/types';
import type { Graph, GraphBatch, SortFilterType } from '@/types/draft';
import { GraphStatus, LoadingStatus, To3DTaskStatus } from '@/types/draft';

import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { collectDraft } from '@/api/draft';
import { useState, useEffect, useContext, useRef } from 'react';
import { useLooper } from '@/hooks';
import { useNavigate } from 'react-router-dom';
import produce from 'immer';
import _ from 'lodash';

import { FilterType } from '@/types';
import { useImageTo3DState, useImageUpscalerState } from '.';
import { AppOrigin, appOrigin } from '@/constants';
import { replaceAlpha } from '@/containers/ImageEditor/utils/filterImage';
import { createImage } from '@/utils/cropImage';
import { uploaderFunc } from '@/containers/ImageEditor/utils/upload';
import { replaceUpscaleImage } from '@/api';
import { ImagesContainerContext } from '../context';

/**
 * 获取草稿图片列表
 */
export function useImageListState() {
  const [loading, setLoading] = useState(false);
  const {
    cursor,
    setCursor,
    ids,
    addIds,
    graphBatch,
    setGraphBatch,
    loadingIds,
    addLoadingIds,
    fetchImages,
    filterType,
    sortFilterType,
    keyword
  } = useContext(ImagesContainerContext);
  const { setUpscalerLoadingIds, upscalerLoadingIds } = useImageUpscalerState();
  const { to3DLoadingIds, setTo3DLoadingIds } = useImageTo3DState();

  const getMore = async () => {
    if (!fetchImages) {
      return;
    }

    setLoading(true);
    try {
      const draft = await fetchImages({
        cursor: cursor ?? '',
        status: filterType,
        orderAsc: sortFilterType,
        keyword: keyword ?? ''
      });

      //TODO 待优化
      const [
        currentIds,
        currentGraphBatch,
        currentLoadingIds,
        currentloadingUpscalerIds,
        currentTo3DLoadingIds
      ] = draft.list.reduce<
        [string[], Record<string, GraphBatch>, string[], string[], string[]]
      >(
        (acc, graphBatch) => {
          const { id, loadingStatus, batch } = graphBatch;

          acc[0].push(id);
          acc[1][id] = graphBatch;

          // 将 loading 的批次塞入等待集合中
          if (!loadingIds.has(id) && loadingStatus === LoadingStatus.LOADING) {
            acc[2].push(id);
          }

          // 将loading的超分任务塞入等待集合中
          batch.forEach((item) => {
            if (
              item.upscalerInfo?.status === UpscalerTaskStatus.GENERATING &&
              upscalerLoadingIds.indexOf(item.upscalerInfo.id) === -1
            ) {
              acc[3].push(item.upscalerInfo.id);
            }
          });

          // 将loading的3d任务塞入等待集合中
          batch.forEach((item) => {
            if (
              item.planeto3DInfo?.status === To3DTaskStatus.GENERATING &&
              to3DLoadingIds.indexOf(item.planeto3DInfo.id) === -1
            ) {
              acc[4].push(item.planeto3DInfo.id);
            }
          });

          return acc;
        },
        [[], {}, [], [], []]
      );

      const _replaceAlpha = async () => {
        for (const batchId of Object.getOwnPropertyNames(currentGraphBatch)) {
          const item = currentGraphBatch[batchId];
          if (item.batch[0].alphaStatus === 1) {
            const hdImg = await createImage(item.batch[0].src ?? '');
            const originImg = await createImage(item.hdOriginUrl ?? '');
            const clip = await replaceAlpha(hdImg, originImg);

            if (!clip) return;

            await new Promise((resolve, reject) => {
              clip.toBlob(async (blob) => {
                if (blob) {
                  let res = await uploaderFunc(blob, 'png');

                  item.batch[0].src = res.previewUrl;

                  await replaceUpscaleImage({
                    id: item.id,
                    resultImageUrl: res.url
                  });
                  resolve(true);
                }
                reject(false);
              }, 'image/png');
            });
          }
        }
      };

      await _replaceAlpha();

      setCursor(draft.cursor);
      setGraphBatch(currentGraphBatch);
      addLoadingIds(currentLoadingIds);
      addIds(currentIds);
      setUpscalerLoadingIds(
        upscalerLoadingIds.concat(currentloadingUpscalerIds)
      );
      setTo3DLoadingIds(to3DLoadingIds.concat(currentTo3DLoadingIds));
    } catch (err) {
      defaultErrorHandler(err);
    }

    setLoading(false);
  };

  useEffect(
    () => {
      if (cursor === null) {
        getMore();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [cursor]
  );

  return {
    loading,
    list: ids.map((id) => graphBatch[id]).filter(Boolean),
    hasMore: cursor !== '',
    getMore
  };
}

/**
 * 轮询处理 loading 中批次
 */
export function useFetchTaskLooper() {
  const { tasks, setGraphBatch, loadingIds, setLoadingIds, fetchTasks } =
    useContext(ImagesContainerContext);
  const [completedTask, setCompletedTask] = useState<GraphBatch[]>();

  useLooper({
    callback: async () => {
      if (!fetchTasks) {
        return;
      }
      const currentGraphBatch = await fetchTasks({
        ids: Array.from(loadingIds).join(',')
      });
      const currentGraphBatchMap = currentGraphBatch.reduce<
        Record<string, GraphBatch>
      >(
        (batches, batch) =>
          Object.assign(batches, {
            [batch.id]: batch
          }),
        {}
      );

      const loadingGraphBatch = currentGraphBatch.filter(
        ({ loadingStatus }) => loadingStatus === LoadingStatus.LOADING
      );
      const loadingGraphBatchIds = loadingGraphBatch.map(({ id }) => id);

      const temp = _.difference([...loadingIds], loadingGraphBatchIds)
        ?.map((id) => currentGraphBatchMap[id])
        // HACK 处理异常数据
        ?.filter((batch) => !_.isNil(batch));

      if (temp[0].batch[0].alphaStatus === 1) {
        // 需要替换alpha通道
        const hdImg = await createImage(temp[0].batch[0].src ?? '');
        const originImg = await createImage(temp[0].hdOriginUrl ?? '');
        const clip = await replaceAlpha(hdImg, originImg);

        if (!clip) return;

        clip.toBlob(async (blob) => {
          if (blob) {
            let res = await uploaderFunc(blob, 'png');

            const tempData = produce(temp, (draft) => {
              draft[0].batch[0].src = res.previewUrl;
            });

            const tempCurrentGraphBatchMap = produce(
              currentGraphBatchMap,
              (draft) => {
                draft[tempData[0].id].batch[0].src = res.previewUrl;
              }
            );

            setCompletedTask(tempData);
            setLoadingIds(
              loadingGraphBatchIds.concat(tasks.map(({ id }) => id))
            );
            setGraphBatch(tempCurrentGraphBatchMap);

            await replaceUpscaleImage({
              id: temp[0].id,
              resultImageUrl: res.url
            });
          }
        }, 'image/png');
      } else {
        setCompletedTask(temp);
        setLoadingIds(loadingGraphBatchIds.concat(tasks.map(({ id }) => id)));
        setGraphBatch(currentGraphBatchMap);
      }
    },
    trigger: () => loadingIds.size > 0,
    duration: 4096
  });

  return completedTask;
}

/**
 * 生成任务后 轮询执行任务状态
 * @returns
 */
export function useCreateLoadingTask(scrollContainer?: HTMLElement | null) {
  const {
    previewId: id,
    insertIds,
    setGraphBatch,
    setScrollTop,
    addLoadingIds,
    goBack,
    tasks,
    clearTasks,
    setHistoryActiveIndex
  } = useContext(ImagesContainerContext);
  const navigate = useNavigate();

  useEffect(
    () => {
      if (tasks.length === 0) {
        return;
      }

      const [taskIds, taskGraphBatch] = tasks.reduce<
        [string[], Record<string, GraphBatch>]
      >(
        (acc, { id, size, editorMode, params }) => {
          acc[0].push(id);
          acc[1][id] = {
            id,
            batch: [],
            size,
            time: +new Date() / 1000,
            loadingProgress: 0,
            loadingStatus: LoadingStatus.LOADING,
            editorMode,
            params
          };
          return acc;
        },
        [[], {}]
      );

      addLoadingIds(taskIds);

      setGraphBatch(taskGraphBatch);
      insertIds(taskIds);

      clearTasks();
      // 生成任务后 setScrollTop 为 0
      setScrollTop(0);
      if (scrollContainer) {
        scrollContainer.scrollTop = 0;
      }
      // 如果是在文生图或者图生图中的详情页不进行跳转
      // 如果是在详情中要进行跳转
      let path = window.location.pathname;

      if (id) {
        if (appOrigin === AppOrigin.Whee) {
          setHistoryActiveIndex(0);
          navigate(
            `/ai/ip-character/${
              path.includes('concept/preview') ? 'concept' : 'customization'
            }/preview/${taskIds[0]}`,
            {
              replace: true
            }
          );
          return;
        } else {
          goBack();
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [tasks]
  );
}

/**
 * 根据 id 获取批次图片信息
 * @returns
 */
export function useGetImagePreview() {
  const {
    previewId: id,
    updateGraphBatch,
    graphBatch,
    goBack,
    fetchTasks
  } = useContext(ImagesContainerContext);
  const { setUpscalerLoadingIds } = useImageUpscalerState();
  const { setTo3DLoadingIds } = useImageTo3DState();

  async function fetchPreview(id: string) {
    try {
      if (!id || !fetchTasks) {
        return;
      }

      const fetchedGraph = await fetchTasks({ ids: id });
      if (!fetchedGraph[0]) {
        goBack();
        return;
      }

      // 未完成的 同步更新超分LoadingIds
      const loadingTask = fetchedGraph[0].batch.filter(
        (item) => item.upscalerInfo?.status === UpscalerTaskStatus.GENERATING
      );
      const loadingIds = loadingTask.map((item) => item.upscalerInfo!.id);
      setUpscalerLoadingIds(loadingIds);

      // 未完成的 同步更新3DLoadingIds
      const to3DLoadingTask = fetchedGraph[0].batch.filter(
        (item) => item.planeto3DInfo?.status === To3DTaskStatus.GENERATING
      );
      const to3DLoadingIds = to3DLoadingTask.map(
        (item) => item.planeto3DInfo!.id
      );
      setTo3DLoadingIds(to3DLoadingIds);

      updateGraphBatch(fetchedGraph[0]);
    } catch (error) {
      defaultErrorHandler(error);
    }
  }

  useEffect(
    () => {
      if (!id || !!graphBatch[id]) {
        return;
      }

      fetchPreview(id);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [id]
  );

  /**
   * 为了避免任务创建完成时 graphBatch[id]为undefined导致页面闪烁
   * 记录上一次不为undefined的batch 在状态未同步期间 这里返回上一次不为undefined的batch
   */
  const existingBatch = useRef<GraphBatch | null>(null);
  if (id && graphBatch[id]) {
    existingBatch.current = graphBatch[id];
  }

  return (id && graphBatch[id]) || existingBatch.current;
}

/**
 * 删除图片
 * @return {(id: string, src: string) => void}
 */
export function useRemoveImage() {
  const {
    removeId,
    graphBatch,
    setGraphBatch,
    goBack,
    removeImage,
    setHistoryActiveIndex,
    setBatchOrigin
  } = useContext(ImagesContainerContext);
  const navigate = useNavigate();
  const { list } = useImageListState();

  // HACK 增加index, 审核失败的图片路径一致
  return async (id: string, src: string, index?: number) => {
    if (!removeImage) {
      return;
    }

    try {
      const { result } = await removeImage({
        id,
        // 如果图片只剩一张则全部删除
        imageUrl: graphBatch[id].batch.length === 1 ? undefined : src
      });

      if (result) {
        const rejector =
          index === undefined
            ? (graph: Graph) => graph.src === src
            : (graph: Graph, i: number) => i === index;
        const nextBatch = _.reject(graphBatch[id].batch, rejector);

        setGraphBatch(
          Object.assign({}, graphBatch, {
            [id]: Object.assign({}, graphBatch[id], {
              batch: nextBatch
            })
          })
        );

        // 图片删除后剩余图片为0则返回草稿页
        if (nextBatch.length < 1) {
          if (appOrigin === AppOrigin.Whee) {
            let path = window.location.pathname;

            const item = list.find((item) => item.id === id);
            const index = list.findIndex((item) => item.id === id);

            if (item?.batch.length === 0) {
              setHistoryActiveIndex(index + 1);
              let itemNext = list[index + 1];
              setBatchOrigin([
                {
                  src: '',
                  status: GraphStatus.AUDITING
                }
              ]);
              navigate(
                `/ai/ip-character/${
                  path.includes('concept/preview') ? 'concept' : 'customization'
                }/preview/${itemNext?.id}`,
                {
                  replace: true
                }
              );
            }

            setTimeout(() => {
              removeId(id);
            }, 200);

            return;
          } else {
            goBack();
            removeId(id);
          }
        }

        return { nextBatch };
      }
    } catch (err) {
      defaultErrorHandler(err);
    }
  };
}

/** 删除整个批次 */
export function useRemoveImageBatch() {
  const { removeId, graphBatch, removeImage } = useContext(
    ImagesContainerContext
  );

  const getNeedConfirm = (id: string | number) =>
    graphBatch[id].loadingStatus === LoadingStatus.FAILED ||
    !graphBatch[id].batch.some(({ status }) => status === GraphStatus.SUCCESS);

  const removeImageBatch = async (id: string | number) => {
    if (!removeImage) {
      return;
    }

    try {
      const { result } = await removeImage({
        id: id.toString()
      });

      if (result) {
        removeId(id.toString());
      }
    } catch (err) {
      defaultErrorHandler(err);
    }
  };

  return [getNeedConfirm, removeImageBatch];
}

/** 重新编辑参数 */
export function useReEditParams() {
  const [loading, setLoading] = useState(false);
  const { graphBatch, setFormValue, setEditorMode } = useContext(
    ImagesContainerContext
  );

  const reEdit = (id: string | number, params: Partial<TaskParams>) => {
    setEditorMode?.(graphBatch[id].editorMode);
    // EditorMode 异步更新后再更新表单
    setTimeout(async () => {
      try {
        setLoading(true);
        await setFormValue?.(id.toString(), params);
      } finally {
        setLoading(false);
      }
    });
  };

  return { loading, reEdit };
}

/** 收藏/取消收藏 */
export function useCollectionChange() {
  const { filterType, graphBatch, updateGraphBatch, removeId } = useContext(
    ImagesContainerContext
  );

  return async (id: GraphBatch['id'], collected: boolean) => {
    const updateGraphBatchCollected = (collected: boolean) => {
      updateGraphBatch(
        produce(graphBatch[id], (draft) => {
          draft.isCollect = collected;
        })
      );
    };
    updateGraphBatchCollected(collected);

    try {
      const result = await collectDraft({
        id,
        actionType: collected ? 1 : 2
      });

      if (!result) {
        updateGraphBatchCollected(!collected);
      }

      if (filterType === FilterType.COLLECTED && !collected) {
        removeId(id);
      }
    } catch (err) {
      defaultErrorHandler(err);
      updateGraphBatchCollected(!collected);
    }
  };
}

/** 筛选类型 */
export function useFilterType() {
  const {
    filterType,
    setFilterType,
    setCursor,
    clearGraphBatch,
    setScrollTop
  } = useContext(ImagesContainerContext);

  const onFilterTypeChange = (filterType: FilterType) => {
    clearGraphBatch();
    setCursor(null);
    setFilterType(filterType);
    setScrollTop(0);
  };

  return [filterType, onFilterTypeChange] as const;
}

/** 排序筛选类型 */
export function useSortFilterType() {
  const {
    sortFilterType,
    setSortFilterType,
    setCursor,
    clearGraphBatch,
    setScrollTop
  } = useContext(ImagesContainerContext);

  const onSortFilterTypeChange = (sortFilterType: SortFilterType) => {
    clearGraphBatch();
    setCursor(null);
    setSortFilterType(sortFilterType);
    setScrollTop(0);
  };

  return [sortFilterType, onSortFilterTypeChange] as const;
}
/**搜索 */

export function useKeywords() {
  const { keyword, setKeyword, setCursor, clearGraphBatch, setScrollTop } =
    useContext(ImagesContainerContext);

  const onKeywordsChange = (keyword: string) => {
    if (setKeyword) {
      setKeyword(keyword);
    }
  };
  const onKeywordsSearch = () => {
    clearGraphBatch();
    setCursor(null);

    setScrollTop(0);
  };

  return [keyword, onKeywordsChange, onKeywordsSearch] as const;
}
