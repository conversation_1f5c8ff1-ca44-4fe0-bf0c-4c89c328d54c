import { Button } from '@/components';
import { AppModule, generateRouteTo } from '@/services';
import { CheckCircleBoldFilled, CrossBoldOutlined } from '@meitu/candy-icons';
import { Modal, Space, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { matchPath, useNavigate } from 'react-router-dom';
import { useRootStore } from '../../store';
import styles from './style.module.less';
import { useAccount } from '@/hooks';
import { useMeiDouBalance } from '@/hooks/useMeiDou';

export function IPLoraTrainingNotify() {
  const [isOpen, setIsOpen] = useState(false);
  const [needsNavigate, setNeedsNavigate] = useState(false);
  const { isLogin } = useAccount();

  function openModal(needsNavigate = true) {
    setIsOpen(true);
    setNeedsNavigate(needsNavigate);
  }
  function closeModal() {
    setIsOpen(false);
  }

  const navigate = useNavigate();

  const { IPCharacterLoraTrainingStore } = useRootStore();
  useEffect(() => {
    if (!isLogin) {
      return;
    }

    const inCustomizationPage = () => {
      return (
        !!matchPath(
          '/ai/ip-character/customization',
          window.location.pathname
        ) ||
        !!matchPath(
          '/ai/ip-character/customization/preview',
          window.location.pathname
        ) ||
        !!matchPath(
          '/ai/ip-character/customization/preview/:id',
          window.location.pathname
        )
      );
    };

    // 在ip创作的形象定制时 页面内启动轮训
    if (!inCustomizationPage()) {
      IPCharacterLoraTrainingStore.startLoop();
    }

    function handleSuccess() {
      // 如果在pi创作形象定制 不需要跳转
      openModal(!inCustomizationPage());
    }

    IPCharacterLoraTrainingStore.observeSuccess(handleSuccess);

    return () => {
      IPCharacterLoraTrainingStore.stopLoop();
      IPCharacterLoraTrainingStore.stopObserveSuccess(handleSuccess);
    };
  }, [IPCharacterLoraTrainingStore, isLogin]);

  // 生成失败时 美豆有退款 需要更新美豆余额
  const { updateMeiDouBalance } = useMeiDouBalance();
  useEffect(() => {
    if (!isLogin) {
      return;
    }

    const handleGenerateFailure = () => {
      updateMeiDouBalance();
    };

    IPCharacterLoraTrainingStore.observeFailure(handleGenerateFailure);
    return () => {
      IPCharacterLoraTrainingStore.stopObserveFailure(handleGenerateFailure);
    };
  }, [IPCharacterLoraTrainingStore, isLogin, updateMeiDouBalance]);

  return (
    <Modal
      centered
      open={isOpen}
      onCancel={closeModal}
      width={360}
      className={styles.notifyModal}
      footer={
        <Space>
          <Button
            type="primary"
            onClick={() => {
              if (needsNavigate) {
                navigate(generateRouteTo(AppModule.IPCharacterCustomization));
              }
              closeModal();
            }}
          >
            去查看
          </Button>
        </Space>
      }
      closeIcon={<CrossBoldOutlined />}
    >
      <Space>
        <CheckCircleBoldFilled style={{ fontSize: '25px', color: '#52c41a' }} />
        <Typography.Text>
          您的专属IP形象已经制作完成！快去试试吧～
        </Typography.Text>
      </Space>
    </Modal>
  );
}
