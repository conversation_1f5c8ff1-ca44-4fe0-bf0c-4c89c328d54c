import { Image, Spin } from 'antd';
import { Upload } from '@/components/Upload';
import { PlusBold, TrashCanBold } from '@meitu/candy-icons';
import { toAtlasImageView2URL } from '@meitu/util';
import classNames from 'classnames';
import {
  Posture,
  PostureControlParams
} from '@/api/types/ipCharacter/customization';
import styles from './style.module.less';

const imageSize = [63, 84] as const;

type IPCharacterPostureProps = {
  presetPostureList?: Array<Posture>;

  value?: PostureControlParams;
  onChange?: (value: PostureControlParams | null) => void;

  uploadImageValue?: string;
  onUploadImageChange?: (img: string) => void;
};

function isSamePosture(p1: PostureControlParams, p2: PostureControlParams) {
  if (p1 === p2) {
    return true;
  }

  const isSameId = (
    id1: number | null | undefined,
    id2: number | null | undefined
  ) => {
    if (!id1 === !id2 && !id1) {
      return true;
    }

    return id1 === id2;
  };

  return isSameId(p1.id, p2.id) && p1.image === p2.image;
}

/**
 * 姿势的选中和取消选中逻辑
 * @param posture
 * @param prevPosture
 * @returns
 */
function nextPosture(
  posture: PostureControlParams,
  prevPosture?: PostureControlParams
) {
  if (prevPosture && isSamePosture(posture, prevPosture)) {
    return null;
  }

  return posture;
}

export function IPCharacterPosture({
  presetPostureList = [],
  value,
  onChange,

  uploadImageValue,
  onUploadImageChange
}: IPCharacterPostureProps) {
  function selectPosture(posture: PostureControlParams) {
    onChange?.(nextPosture(posture, value));
  }

  function renderPresetList() {
    return presetPostureList.map(({ id, coverPic, bonesPic }) => {
      return (
        <PostureItem
          key={bonesPic}
          img={coverPic}
          isActive={id === value?.id}
          onClickRoot={() => {
            selectPosture({ id, image: bonesPic });
          }}
        />
      );
    });
  }

  return (
    <section className={styles.posture}>
      {renderPresetList()}
      <ImageUploader
        value={uploadImageValue}
        onChange={onUploadImageChange}
        isActive={!value?.id && value?.image === uploadImageValue}
        // 图片上传完成后 自动选择上传后的图片
        onSelect={(image) => selectPosture({ image })}
      />
    </section>
  );
}

type PostureItemProps = React.PropsWithChildren<{
  img: string;
  onClickRoot?: () => void;
  isActive?: boolean;
}>;

function PostureItem({
  img,
  isActive,
  children,
  onClickRoot
}: PostureItemProps) {
  const [width, height] = imageSize.map(
    (measure) => measure * window.devicePixelRatio
  );
  const previewSrc = toAtlasImageView2URL(img, { mode: 2, width, height });

  return (
    <div
      className={classNames(styles.postureItem, isActive && 'active')}
      onClick={onClickRoot}
    >
      <Image src={previewSrc} preview={false} />
      {children}
    </div>
  );
}

type ImageUploaderProps = {
  value?: string;
  onChange?: (img: string) => void;
  onSelect?: (img: string) => void;
  isActive?: boolean;
};

function ImageUploader({
  value,
  onChange,
  isActive,
  onSelect
}: ImageUploaderProps) {
  function handleUploaderChange(shortUrl: string, url?: string) {
    const img = url || shortUrl;
    // 上传过程中 会存在img为空的情况 尽在img不为空的时候表示上传完成
    if (!img) {
      return;
    }

    onChange?.(img);
    // 上传完成后 自动选择上传后的图片
    onSelect?.(img);
  }

  if (!value) {
    return (
      <Upload.Dragger
        className={styles.uploader}
        value={value}
        onChange={handleUploaderChange}
        renderChildren={(uploading) => {
          if (uploading) {
            return <Spin spinning />;
          }

          return (
            <div className="upload-btn">
              <PlusBold className="btn-icon" />
              <span className="btn-display">
                上传姿势
                <br />
                参考图
              </span>
            </div>
          );
        }}
      />
    );
  }

  return (
    <PostureItem
      key={value}
      img={value}
      onClickRoot={() => {
        onSelect?.(value);
      }}
      isActive={isActive}
    >
      <div className="uploader-mask">
        <div
          role="button"
          className="remove-btn"
          onClick={(event) => {
            event.stopPropagation();

            onChange?.('');
            // 如果当前上传的图片被选中 图片删除时 清空选中状态
            if (isActive) {
              onSelect?.('');
            }
          }}
        >
          <TrashCanBold />
        </div>
      </div>
    </PostureItem>
  );
}
