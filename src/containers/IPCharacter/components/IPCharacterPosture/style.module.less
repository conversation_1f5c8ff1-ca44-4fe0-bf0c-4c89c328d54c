@import '~@/styles/variables.less';

.posture {
  display: grid;
  grid-template-columns: repeat(4, 63px);
  grid-auto-rows: 84px;
  row-gap: 12px;
  column-gap: 12px;
}

.posture-item {
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  z-index: 0;

  &:global(.active) {
    :global(.ant-image) {
      box-shadow: inset 0 0 0 2px @content-list-selected;
    }
  }

  :global {
    .ant-image {
      position: relative;
      width: 100%;
      height: 100%;
      box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      border-radius: 4px;
      background: transparent;
      img.ant-image-img {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: -1;
        object-fit: cover;
      }
    }

    .uploader-mask {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;

      .remove-btn {
        display: flex;
        width: 14px;
        height: 14px;
        position: absolute;
        right: 4px;
        top: 4px;

        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.5);

        svg {
          width: 8px;
          height: 8px;
          color: #fff;
        }
      }
    }
  }
}

.uploader:global(.ant-upload-wrapper) {
  min-width: 0;
  width: 63px;
  height: 84px;
  color: @content-system-quaternary;
  border-radius: 4px;
  overflow: hidden;

  :global {
    .ant-upload.ant-upload-drag {
      border: none;

      .ant-upload.ant-upload-btn {
        padding: 0 0;

        .ant-upload-drag-container {
          .upload-btn,
          .ant-spin {
            display: flex;
            flex-direction: column;
            align-items: center;
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            background: @background-system-space-holder;
          }

          .upload-btn {
            padding: 15px 0;
            &:hover {
              background: rgba(28, 29, 31, 0.06);
            }
            &:active {
              background: @background-system-space-holder;
            }

            .btn-icon {
              svg {
                width: 16px;
                height: 16px;
              }
            }

            .btn-display {
              font-size: 12px;
              line-height: 16px;
              margin-top: 6px;
              text-align: center;
            }
          }

          .ant-spin {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
    }
  }
}
