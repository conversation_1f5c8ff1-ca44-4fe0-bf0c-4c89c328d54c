import { makeAutoObservable, runInAction } from 'mobx';
import { IPCharacterCustomizationConfig } from '@/api/types/ipCharacter/customization';
import { fetchIPFigureConfig } from '@/api/editor';
import { ImageTasks } from '@/api/types';
import { FunctionCode, MeiDouFetchPriceDescResponse } from '@/api/types/meidou';
import { fetchPriceDesc } from '@/api/meidou';
/**
 * 注：
 *  如果要是使用用户自定义的lora列表
 *  不要在该store中的config中读取 这个config不会更新训练状态
 *  应该去IPCharacterLoraTrainingStore中读取
 */
class CustomizationStore {
  //#region config相关
  public config = null as null | IPCharacterCustomizationConfig;
  public get presetPostureList() {
    return this.config?.postureCtl?.picList ?? [];
  }
  public get presetLoraList() {
    const category = this.config?.loraList?.[0];
    return category?.list ?? [];
  }

  ipLoraPriceDesc = null as null | MeiDouFetchPriceDescResponse;
  public async fetchCreateLoraPriceDesc() {
    try {
      const desc = await fetchPriceDesc({
        functionCode: FunctionCode.ipimagetraining,
        functionBody: JSON.stringify({ image: '' })
      });

      runInAction(() => {
        this.ipLoraPriceDesc = desc;
      });
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
        console.log(e);
      }
    }
  }

  /**
   * 当姿势回填时 如果在预设列表中没有找到对应姿势 则认为这个姿势是用户上传的
   * 这时 需要修改到姿势上传组件的状态
   * 因为在参数form和draft都需要使用到这个状态 因此放到store中
   */
  postureUploadValue = '';
  public setPostureUploadValue(img: string) {
    this.postureUploadValue = img;
  }

  loadConfigStatus = 'init' as 'init' | 'loading' | 'loaded' | 'loadFailure';
  public async initConfig() {
    this.loadConfigStatus = 'loading';
    try {
      const config = await fetchIPFigureConfig();
      runInAction(() => {
        this.config = config;
        this.loadConfigStatus = 'loaded';
      });
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
        console.log(e);
      }
      runInAction(() => {
        this.loadConfigStatus = 'loadFailure';
      });
    }
  }
  //#endregion

  tasks: ImageTasks = [];
  setTasks(val: ImageTasks) {
    this.tasks = val;
  }

  public reset() {
    this.postureUploadValue = '';
  }

  constructor() {
    makeAutoObservable(this, {
      eventEmitter: false,
      observe: false,
      unobserve: false
    } as any);
  }
}

export default CustomizationStore;
