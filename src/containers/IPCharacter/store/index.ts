import { useContext } from 'react';
import ConceptStore from './conceptStore';
import CustomizationStore from './customization';
import { MobXProviderContext } from 'mobx-react';
import IPCharacterLoraTrainingStore from './ipCharacterLoraTraining';

const RootStore = {
  ConceptStore: new ConceptStore(),
  CustomizationStore: new CustomizationStore()
  // IPCharacterLoraTraining 在使用时注入
};

export function useRootStore() {
  return useContext(MobXProviderContext) as typeof RootStore & {
    IPCharacterLoraTrainingStore: IPCharacterLoraTrainingStore;
  };
}

export default RootStore;
