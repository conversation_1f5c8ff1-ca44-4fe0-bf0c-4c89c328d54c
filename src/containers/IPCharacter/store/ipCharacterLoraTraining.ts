import { makeAutoObservable, runInAction, toJS } from 'mobx';
import EventEmitter from 'events';
import { CustomLoraItem } from '@/api/types/ipCharacter/customization';
import { fetchIPCharacterTrainingProgress } from '@/api/training';
import { TrainingProgressResponse, TrainingStatus } from '@/api/types';
import { fetchIPFigureConfig } from '@/api';
import { trackEvent } from '@/services';

type IPCharacterLoraTrainingEvent = 'success' | 'failure' | 'remove';

type IPCharacterLoraTrainingSuccessEventHandler = (
  successList: TrainingProgressResponse[]
) => void;
type IPCharacterLoraTrainingFailureEventHandler = (
  failureList: TrainingProgressResponse[]
) => void;
type IPCharacterLoraRemoveEventHandler = (
  removedList: CustomLoraItem[]
) => void;

type IPCharacterLoraTrainingEventHandler<
  Event extends IPCharacterLoraTrainingEvent
> = Event extends 'success'
  ? IPCharacterLoraTrainingSuccessEventHandler
  : Event extends 'failure'
  ? IPCharacterLoraTrainingFailureEventHandler
  : Event extends 'remove'
  ? IPCharacterLoraRemoveEventHandler
  : never;

export type CustomLoraItemWithProgress = CustomLoraItem & {
  estimatedTime?: string;
};

class IPCharacterLoraTrainingStore {
  /**
   * 轮训间隔
   */
  public static LoopInterval = 10 * 1000;

  public customLoraList = [] as Array<CustomLoraItemWithProgress>;

  /**
   * 当前正在轮训的训练任务 不需要响应式
   */
  private loopingIds = [] as Array<number>;
  private get looping() {
    return !!this.loopingIds.length;
  }
  public get loopingCounts() {
    return this.loopingIds.length;
  }

  /**
   * 用于取消轮训的key 不需要响应式
   */
  private timeoutKey = null as NodeJS.Timeout | null;
  /**
   * 为了避免tick并发 对tick加锁
   */
  private tickLock = 0;
  private async tick() {
    const privateLock = Date.now();
    this.tickLock = privateLock;
    try {
      const { list } = await fetchIPCharacterTrainingProgress({
        ids: this.loopingIds.join(',')
      });
      /**
       * 如果锁不一致 表示已经有新的tick 放弃后续操作
       */
      if (this.tickLock !== privateLock) {
        return;
      }
      /**
       * 如果轮训已经被手动停止 放弃后续操作
       */
      if (!this.loopingIds.length) {
        return;
      }

      const [finishMap, loadingMap, successList, failureList] = list.reduce(
        (res, lora) => {
          if (lora.status === TrainingStatus.Training) {
            res[1].set(lora.id, lora);
            return res;
          }

          res[0].set(lora.id, lora);
          if (lora.status === TrainingStatus.Completed) {
            res[2].push(lora);
            return res;
          }

          if (lora.status === TrainingStatus.Failed) {
            res[3].push(lora);
            return res;
          }

          return res;
        },
        [
          new Map<number, TrainingProgressResponse>(),
          new Map<number, TrainingProgressResponse>(),
          [] as TrainingProgressResponse[],
          [] as TrainingProgressResponse[]
        ]
      );
      this.loopingIds = Array.from(loadingMap.keys());

      //1. 更新lora列表中的训练状态
      runInAction(() => {
        this.customLoraList = this.customLoraList.map((lora) => {
          const finishItem = finishMap.get(lora.id);
          if (finishItem) {
            const updated = {
              ...lora,
              status: finishItem.status
            };

            if (finishItem.trainingModelId) {
              updated.trainingModelId = finishItem.trainingModelId;
            }
            return updated;
          }

          const loadingItem = loadingMap.get(lora.id);
          if (loadingItem) {
            return {
              ...lora,
              estimatedTime: loadingItem.estimatedTime
            };
          }

          return lora;
        });
      });

      // 2. 如果有生成成功的lora 进行通知
      if (successList.length) {
        this.eventEmitter.emit('success', successList);
      }

      // 3. 如果有生成失败的lora 进行通知
      if (failureList.length) {
        this.eventEmitter.emit('failure', failureList);
      }
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
        console.error(e);
      }
    }

    if (this.loopingIds.length) {
      this.timeoutKey && clearTimeout(this.timeoutKey);
      this.timeoutKey = setTimeout(
        () => this.tick(),
        IPCharacterLoraTrainingStore.LoopInterval
      );
    }
  }

  private async fetchCustomLoraList() {
    const config = await fetchIPFigureConfig();
    const userLoraList = Array.isArray(config.userLoraList)
      ? config.userLoraList
      : [];
    runInAction(() => {
      this.customLoraList = userLoraList.filter(
        (lora) =>
          lora.status === TrainingStatus.Completed ||
          lora.status === TrainingStatus.Training ||
          lora.status === TrainingStatus.Failed
      );
    });
  }
  /**
   * 从服务端拉取config 同步状态
   * @returns
   */
  public async update() {
    if (this.looping) {
      await this.stopLoop();
    }

    await this.startLoop();
  }

  public async startLoop(loraList?: Array<CustomLoraItem>) {
    if (this.looping) {
      return;
    }

    if (!loraList) {
      await this.fetchCustomLoraList();
    } else {
      this.customLoraList = loraList;
    }

    this.loopingIds = this.customLoraList
      .filter((lora) => lora.status === TrainingStatus.Training)
      .map(({ id }) => id);

    if (!this.loopingIds.length) {
      return;
    }

    return this.tick();
  }

  public stopLoop() {
    this.loopingIds = [];
    this.tickLock = Date.now();
    this.timeoutKey && clearTimeout(this.timeoutKey);
    this.timeoutKey = null;
  }

  /**
   * 从store中删除lora 这里不做服务端同步
   * @param ids
   */
  public removeLoraById(...ids: number[]) {
    const deletedLoraIdSet = new Set(ids);
    const [removedLora, leftLora] = toJS(this.customLoraList).reduce(
      ([deleted, left], lora) => {
        if (deletedLoraIdSet.has(lora.id)) {
          deleted.push(lora);
        } else {
          left.push(lora);
        }

        return [deleted, left];
      },
      [[] as CustomLoraItem[], [] as CustomLoraItem[]]
    );

    runInAction(() => {
      const nextLoopingIds = this.loopingIds.filter(
        (id) => !deletedLoraIdSet.has(id)
      );
      if (!nextLoopingIds.length) {
        this.stopLoop();
      }
      this.loopingIds = nextLoopingIds;
      this.customLoraList = leftLora;
    });

    this.eventEmitter.emit('remove', removedLora);

    return {
      removedLora,
      leftLora
    };
  }

  /**
   * 事件通知器 不需要响应式
   */
  private eventEmitter = new EventEmitter();
  /**
   * 当有lora训练成功 通过事件进行通知
   * @param handler
   */
  public observeSuccess(
    handler: IPCharacterLoraTrainingEventHandler<'success'>
  ) {
    this.eventEmitter.addListener('success', handler);
  }
  public stopObserveSuccess(
    handler: IPCharacterLoraTrainingEventHandler<'success'>
  ) {
    this.eventEmitter.removeListener('success', handler);
  }
  /**
   * 当有lora训练失败 通过事件进行通知
   * @param handler
   */
  public observeFailure(
    handler: IPCharacterLoraTrainingEventHandler<'failure'>
  ) {
    this.eventEmitter.addListener('failure', handler);
  }
  public stopObserveFailure(
    handler: IPCharacterLoraTrainingEventHandler<'failure'>
  ) {
    this.eventEmitter.removeListener('failure', handler);
  }
  /**
   * 当有lora训练失败 通过事件进行通知
   * @param handler
   */
  public observeRemove(handler: IPCharacterLoraTrainingEventHandler<'remove'>) {
    this.eventEmitter.addListener('remove', handler);
  }
  public stopObserveRemove(
    handler: IPCharacterLoraTrainingEventHandler<'remove'>
  ) {
    this.eventEmitter.removeListener('remove', handler);
  }

  constructor() {
    makeAutoObservable(this, {
      looping: false,
      loopingIds: false,
      timeoutKey: false,
      tickLock: false,
      eventEmitter: false,
      observeSuccess: false,
      stopObserveSuccess: false,
      observeFailure: false,
      stopObserveFailure: false
    } as any);

    // 生成成功上报
    this.observeSuccess((successList) => {
      successList.forEach(() => {
        trackEvent('portrait_design_popup_create_success');
      });
    });
  }
}

export default IPCharacterLoraTrainingStore;
