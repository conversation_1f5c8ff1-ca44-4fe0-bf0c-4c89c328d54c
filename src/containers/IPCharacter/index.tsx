import { Authorized, Collapse, ErrorBoundary, Loading } from '@/components';
import { MeidouBalanceWithMissionCenterEntry } from '@/components/MeiDouPrice';
import { Layout } from '@/layouts';
import { AppHeader } from '@/layouts/AppHeader';
import { AccountAvatar } from '@/layouts/Header/AccountAvatar';
import { Spin } from 'antd';
import { Suspense, lazy, useEffect, useState } from 'react';
import { IPCharacterFeature } from './constants';
import { useNavigate, useParams } from 'react-router-dom';
import { AppModule, generateRouteTo } from '@/services';
import { FeatureTabs } from './components/FeatureTabs';
import { FormContextProvider } from './context/FormContext';
import styles from './style.module.less';
import { Provider as MobxProvider } from 'mobx-react';
import rootStore, { useRootStore } from './store';
import useStore from '@/hooks/useStore';
import { useAuthoringPageEnter } from '@/hooks';
import { AppModuleParam } from '@/types';

// prettier-ignore
// IP形象 - 概念图 - 参数
const IPCharacterConceptParams = lazy(() => import(
  /* webpackChunkName: "ip-character-concept-params" */
  '@/containers/IPCharacter/Concept/ParamsEditor'
));

// prettier-ignore
// IP形象 - 概念图 - 列表/详情
const IPCharacterConceptDraft = lazy(() => import(
  /* webpackChunkName: "ip-character-concept-draft" */
  '@/containers/IPCharacter/Concept/Draft'
));

// prettier-ignore
// IP形象 - 形象定制 - 参数
const IPCharacterCustomizationParams = lazy(() => import(
  /* webpackChunkName: "ip-character-customization-params" */
  '@/containers/IPCharacter/Customization/ParamsEditor'
));

// prettier-ignore
// IP形象 - 形象定制 - 列表/详情
const IPCharacterCustomizationDraft = lazy(() => import(
  /* webpackChunkName: "ip-character-customization-draft" */
  '@/containers/IPCharacter/Customization/Draft'
));

const featureMap = new Map([
  [
    IPCharacterFeature.Concept as string,
    {
      ParamsEditor: IPCharacterConceptParams,
      Draft: IPCharacterConceptDraft
    }
  ],
  [
    IPCharacterFeature.Customization as string,
    {
      ParamsEditor: IPCharacterCustomizationParams,
      Draft: IPCharacterCustomizationDraft
    }
  ]
]);

function IPCharacter() {
  const [loading, setLoading] = useState(true);
  const { CustomizationStore, IPCharacterLoraTrainingStore } = useRootStore();
  useEffect(() => {
    CustomizationStore.fetchCreateLoraPriceDesc().finally(() => {
      setLoading(false);
    });
  }, [CustomizationStore]);

  // 生成失败时 限免次数可能会退回 更新限免次数
  // 生成失败时 也需要退回美豆 美豆余额也需要更新 这部分逻辑放在IPLoraTrainingNotify中
  useEffect(() => {
    const handleTrainingFailure = () => {
      CustomizationStore.fetchCreateLoraPriceDesc();
    };
    IPCharacterLoraTrainingStore.observeFailure(handleTrainingFailure);
    return () => {
      IPCharacterLoraTrainingStore.stopObserveFailure(handleTrainingFailure);
    };
  }, [CustomizationStore, IPCharacterLoraTrainingStore]);

  // 路由匹配 ip-character/:feature/preview?/:id?
  // feature是当前所在的功能tab
  // 如果有preview且有id 则为预览详情
  const { feature, id } = useParams();

  // 页面进入埋点
  useAuthoringPageEnter(
    feature === IPCharacterFeature.Concept
      ? AppModuleParam.IPCharacterConcept
      : AppModuleParam.IPCharacterCustomization
  );

  const navigate = useNavigate();

  // -------hook分隔 后续代码存在条件返回逻辑

  // 路由中的feature如果匹配不到【概念图】或者【形象定制】
  // 重定向到【概念图】
  const featureComponents = feature && featureMap.get(feature);
  if (!featureComponents) {
    navigate(generateRouteTo(AppModule.IPCharacterConcept), { replace: true });
    return <></>;
  }

  // feature是通过path取到的 切换tab通过修改path实现
  function handleFeatureChange(feature: IPCharacterFeature) {
    switch (feature) {
      case IPCharacterFeature.Concept: {
        navigate(generateRouteTo(AppModule.IPCharacterConcept), {
          replace: true
        });
        break;
      }

      case IPCharacterFeature.Customization: {
        navigate(generateRouteTo(AppModule.IPCharacterCustomization), {
          replace: true
        });
        break;
      }
    }
  }

  const { ParamsEditor, Draft } = featureComponents;

  return (
    <Spin
      className={styles.loadingSpin}
      indicator={<Loading />}
      spinning={loading}
    >
      <Layout
        theme="app"
        header={
          <AppHeader
            actionsGutter={20}
            actions={
              <>
                <MeidouBalanceWithMissionCenterEntry />
                <AccountAvatar />
              </>
            }
          />
        }
      >
        <Layout.Sider width={320} noStyle>
          <Collapse className={styles.sider} defaultActiveKey={['feature-tab']}>
            <Collapse.Panel
              className={styles.featureTabsContainer}
              header={null}
              key="feature-tab"
              showArrow={false}
              collapsible="disabled"
            >
              <Collapse.Panel.Section>
                <FeatureTabs
                  value={feature as IPCharacterFeature}
                  onChange={handleFeatureChange}
                />
              </Collapse.Panel.Section>
            </Collapse.Panel>

            <Suspense>
              <ParamsEditor key={feature} />
            </Suspense>
          </Collapse>
        </Layout.Sider>

        <Layout.Content noStyle>
          {!loading && (
            <Suspense>
              <Draft key={feature} previewId={id} />
            </Suspense>
          )}
        </Layout.Content>
      </Layout>
    </Spin>
  );
}

export default function Index() {
  const IPCharacterLoraTrainingStore = useStore('IPCharacterLoraTrainingStore');

  return (
    <ErrorBoundary>
      <Authorized>
        <MobxProvider
          IPCharacterLoraTrainingStore={IPCharacterLoraTrainingStore}
          {...rootStore}
        >
          <FormContextProvider>
            <IPCharacter />
          </FormContextProvider>
        </MobxProvider>
      </Authorized>
    </ErrorBoundary>
  );
}

export { Index as Component };
