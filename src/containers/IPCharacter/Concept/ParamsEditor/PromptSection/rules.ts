import type { FormItemRules } from '@/types';
import { AppOrigin, appOrigin } from '@/constants';

const basicRules = [{ required: true, message: '' }];

// 大模型官网 提示词限制输入【数字、字母、汉字、符号】主要限制小语种输入  --zxl
const basicRulesForMiracleVision = [
  {
    required: true,
    message: ''
  },
  {
    required: true,
    message: '仅可支持输入字母、汉字、数字和符号哦~',
    validator: (_: FormItemRules, value: string) => {
      let reg: RegExp = /^[\u4e00-\u9fa5a-zA-Z0-9|\p{P}|\p{S}]+$/gu;
      return reg.test(value.replace(/\s/g, ''))
        ? Promise.resolve()
        : Promise.reject(new Error('仅可支持输入字母、汉字、数字和符号哦~'));
    }
  }
];

/** Prompt */
export const promptRules: FormItemRules =
  appOrigin === AppOrigin.MiracleVision
    ? basicRulesForMiracleVision
    : basicRules;

/** 模型 */
export const modelIdRules: FormItemRules = basicRules;

/** 风格模型 */
export const stylesRules: FormItemRules = basicRules;
