import { useCachedIpCharacterFields } from '@/hooks/useCachedPageFields';
import { Form } from 'antd';
import { useUpdateEffect } from 'react-use';

export const useCachePromptAndSmartComplementValue = () => {
  const prompt = Form.useWatch('prompt');

  const {
    setSmartComplementValue,
    setParamsEditorCached,
    cachedFields: { smartComplementValue }
  } = useCachedIpCharacterFields();

  useUpdateEffect(() => {
    setParamsEditorCached({ prompt });
  }, [prompt]);

  return { setSmartComplementValue, smartComplementValue };
};
