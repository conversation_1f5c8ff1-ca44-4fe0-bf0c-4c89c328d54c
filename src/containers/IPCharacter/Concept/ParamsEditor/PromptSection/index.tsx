import { Collapse, TextArea } from '@/components';
import { Form } from 'antd';
import { useCachePromptAndSmartComplementValue } from './useCache';
import { trackEvent } from '@/services';
import { AppOrigin, appOrigin } from '@/constants';
import { CorpusFilter } from '@/types';
import { getSource } from '@/utils';
import { transformLanguage } from '@/utils/draftTracking';

export function PromptSection() {
  const { smartComplementValue, setSmartComplementValue } =
    useCachePromptAndSmartComplementValue();
  const form = Form.useFormInstance();
  const source = getSource();
  const promptPlaceholder =
    '请输入你创作 IP 形象的关键词，括号可加权重，如（可爱）\nIP 形象词库内有推荐提示词哦～';
  return (
    <Collapse.Panel.Section title={<strong>形象关键词</strong>}>
      {/* rules={promptRules} */}
      <Form.Item name="prompt" noStyle>
        <TextArea.SmartTextarea
          showInitRandomPrompt={false}
          hasComplementAction={true}
          style={{ height: '160px', resize: 'none' }}
          allowClear
          complementInitValue={smartComplementValue}
          onComplementChange={(v) => {
            setSmartComplementValue(v);
          }}
          maxLength={800}
          showCount
          placeholder={promptPlaceholder}
          corpusAction={{
            visible: appOrigin === AppOrigin.Whee,
            corpusFilter: CorpusFilter.IP_CHARACTER,
            btnLabel: 'IP形象词库'
          }}
          onOpen={() => {
            trackEvent('complete_prompt_btn_click', {
              text: form.getFieldValue('prompt')
            });
          }}
          onStartComplement={(text) => {
            trackEvent('complete_prompt_start_click', { text });
          }}
          onSuccess={(text, resultPrompt) => {
            trackEvent('complete_prompt_success', { text, resultPrompt });
          }}
          onApply={(resultPrompt) => {
            trackEvent('complete_prompt_apply_btn_click', { resultPrompt });
            // 更新userSubmitValue
            // setUserSubmitValue(form.getFieldValue('prompt'));
          }}
          onReGenerate={(resultPrompt) => {
            trackEvent('complete_prompt_regenerate_btn_click', {
              resultPrompt
            });
          }}
          onCorpusLanguageChange={(language) => {
            // 词库页面_语言切换点击
            trackEvent('language_switch_click', {
              pageName: source,
              language: transformLanguage(language),
              location: 'cue_cards'
            });
          }}
          onCorpusVisibleChange={(visible) => {
            // 词库点击
            visible &&
              trackEvent('lexicon_click', {
                pageName: source,
                location: 'cue_cards'
              });
          }}
        />
      </Form.Item>
    </Collapse.Panel.Section>
  );
}
