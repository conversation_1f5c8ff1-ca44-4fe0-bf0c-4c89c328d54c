import {
  Collapse,
  SliderInput,
  SizeSelector,
  PresetSizeMode
} from '@/components';
import { Form, Tooltip } from 'antd';
import { QuestionMarkCircleBold } from '@meitu/candy-icons';
import { SelectZoomMode } from '@/components';
import { Proportion } from '@/components/SizeSelector/constants';
import './index.module.less';

export function ParamsSection() {
  return (
    <div className="no-select">
      <Collapse.Panel.Section>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.image?.key !== currentValues.image?.key
          }
        >
          {({ getFieldValue }) => (
            <Form.Item name="canvas" noStyle>
              <SizeSelector
                imageUrl={
                  getFieldValue('image')?.preview ?? getFieldValue('image')?.key
                }
                type="adaption"
                presetSizeMode={PresetSizeMode.AUTO_MAX_PRESET_SIZE}
              />
            </Form.Item>
          )}
        </Form.Item>
      </Collapse.Panel.Section>

      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.canvas?.proportion !== currentValues.canvas?.proportion
        }
      >
        {({ getFieldValue }) =>
          getFieldValue(['canvas', 'proportion']) !== Proportion.ADAPTION && (
            <Collapse.Panel.Section
              title={
                <>
                  画面缩放
                  <Tooltip title="当上传图片的比例与设定比例不一致时按照选择的模式进行缩放">
                    <QuestionMarkCircleBold />
                  </Tooltip>
                </>
              }
            >
              <Form.Item name={['image', 'zoomMode']} noStyle>
                <SelectZoomMode />
              </Form.Item>
            </Collapse.Panel.Section>
          )
        }
      </Form.Item>

      <Collapse.Panel.Section>
        <Form.Item name="quantity" noStyle>
          <SliderInput
            title="生成张数"
            tooltip="生成张数越多，耗时越久。"
            min={1}
            max={4}
          />
        </Form.Item>
      </Collapse.Panel.Section>
    </div>
  );
}
