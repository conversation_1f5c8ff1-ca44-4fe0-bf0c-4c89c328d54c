import type { BasicParams } from '@/types';
import { ControlNetParams } from '@/types';

/** 额外的功能 */
export enum ExtraFunction {
  /** 面部修复 */
  FacialRestoration,

  /** 高清修复 */
  HDRestoration
}

/** 风格模型 */
export interface StyleModel {
  /** 风格编号 */
  id: number;

  /** 强度 */
  strength: number;
}

/** 画面缩放 */
export enum ControlNetZoomMode {
  /** 拉伸 */
  Stretching,

  /** 裁剪 */
  Cropping,

  /** 自动缩放 */
  Auto
}

/**
 * ControlNet 参数
 */

/**
 * 参数设定
 */
export interface ImageToImageParams extends BasicParams {
  /** 人脸相似度 */
  faceSimilarity: number;

  /** 参考图片 */
  image?: {
    /** 图片存储在 OBS 的键 */
    key: string;

    /** 强度 */
    strength: number;

    /** 画面缩放 */
    zoomMode: ControlNetZoomMode;
  };

  /** ControlNet */
  controlNet: ControlNetParams[];

  /** 图片识别 */
  facialDetection: boolean;
}
