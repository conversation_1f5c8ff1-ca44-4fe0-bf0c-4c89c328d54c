import { Model<PERSON>ard, ModalRef, ModelModalV2 } from '@/components';
import { Form } from 'antd';
import { ModelCardProps } from '@/components';
import { ImageToImageParams } from '../types';
import { useRef } from 'react';
import { useRecommendParams } from '@/hooks/useGetEditorConfig/useRecommendParams';

import { EditorConfigModelResponse } from '@/api/types/editorConfig';
import { useGetAllModel } from '@/containers/ImageToImage/hooks';

import styles from './index.module.less';
import { initialEditorParams } from '../utils';
import { ChevronRightBlack } from '@meitu/candy-icons';

export interface BaseModelProps {
  baseModel: ModelCardProps<number>['list'];
  baseModelResponse: EditorConfigModelResponse[];
  refetch(): Promise<any>;
}

export const BaseModel = (props: BaseModelProps) => {
  const modelModalRef = useRef<ModalRef>(null);
  const { baseModel, baseModelResponse } = props;
  const form = Form.useFormInstance<ImageToImageParams>();

  const setModalVisible = (visible: boolean) => {
    modelModalRef.current?.setVisible(visible);
  };
  const { baseModelMap } = useGetAllModel();

  useRecommendParams(baseModelMap, initialEditorParams);

  function handleCardClick() {
    baseModel.length > 1 && setModalVisible(true);
  }

  return (
    <>
      <Form.Item noStyle name="baseModelId">
        <ModelCard
          className={styles.modelCard}
          list={baseModel}
          onClick={handleCardClick}
          onExtraClick={handleCardClick}
          showCornerLabelUrl={false}
          extra={
            <div className={styles.extraIcon}>
              <ChevronRightBlack />
            </div>
          }
        />
      </Form.Item>
      {/* <Form.Item noStyle name="recommendParams" valuePropName="checked">
        <Checkbox className={styles.recommendParams}>使用推荐参数</Checkbox>
      </Form.Item> */}
      <ModelModalV2
        title="生图模型"
        list={baseModelResponse}
        ref={modelModalRef}
        onModelClick={async (baseModel) => {
          form.setFieldsValue({ baseModelId: baseModel.id });
          setModalVisible(false);
        }}
        onCollectMutation={props.refetch}
      />
    </>
  );
};
