@import '~@/styles/variables.less';

.recommend-params:global(.@{ant-prefix}-checkbox-wrapper) {
  height: 20px;
  line-height: 20px;
  font-weight: 400;
  margin-top: 8px;

  :global(.@{ant-prefix}-checkbox) + span {
    padding: 0 4px;
  }
}

.model-card {
  :global(.model-card-extra) {
    background-color: transparent;

    .extra-icon {
      position: absolute;
      top: @size;
      right: @size-sm;
    }
  }
}
