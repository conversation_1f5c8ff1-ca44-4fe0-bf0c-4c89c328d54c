import { SliderInput, ModelCard, ModelCardProps } from '@/components';
import { TrashCanBold } from '@/icons';
import { formListNamesWrapper } from '@/utils/formUtils';
import { Form } from 'antd';
import styles from './styles.module.less';
import { useStyleModelTooltipOnSlider } from '@/hooks';

export interface FormListItemProps {
  styleModel: ModelCardProps<number>['list'];

  namePath: Parameters<typeof formListNamesWrapper>;

  onModelCardClick?: () => void;

  remove(): void;
}

export const FormListItem = ({
  styleModel,
  namePath,
  onModelCardClick,
  remove
}: FormListItemProps) => {
  const openModal = () => {
    onModelCardClick?.();
  };

  const { tooltipOnSlider, onPointerEnter, onPointerLeave } =
    useStyleModelTooltipOnSlider({ autoHideDelay: 5000 });

  return (
    <Form.Item noStyle name={namePath}>
      <ModelCard
        childrenFormName="strength"
        list={styleModel}
        onClick={openModal}
        onExtraClick={remove}
        extra={<TrashCanBold />}
      >
        <SliderInput
          controls={false}
          suffix="%"
          label="强度"
          direction="horizontal"
          min={0}
          max={100}
          step={1}
          markNum={3}
          className={styles.sliderInput}
          tooltipOnSlider={tooltipOnSlider}
          onPointerEnterSlider={onPointerEnter}
          onPointerLeaveSlider={onPointerLeave}
        />
      </ModelCard>
    </Form.Item>
  );
};
