import { Form } from 'antd';
import { Collapse, Upload } from '@/components';
import { trackEvent } from '@/services';
import { AppModuleParam } from '@/types';
import { DraftType } from '@/api/types';

interface DraggableUploadProps {
  value?: string;
  onChange?: (value: string) => void;
}

function DraggableUpload({ value, onChange }: DraggableUploadProps) {
  const form = Form.useFormInstance();
  const onValueChange = (url: string, previewUrl?: string) => {
    onChange?.(previewUrl ?? '');

    if (!!previewUrl) {
      trackEvent('whee_upload_image_success', {
        function: AppModuleParam.IPCharacterConcept
      });
    }

    form.setFieldValue(['image', 'preview'], previewUrl);

    if (!previewUrl) {
      return;
    }

    const getNamePath = (index: number) => [
      'controlNet',
      index,
      'imageProcessParams'
    ];
    const controlNetNumbers = form.getFieldValue('controlNet')?.length ?? 0;

    for (let i = 0; i < controlNetNumbers; ++i) {
      const imageProcessParamsPath = getNamePath(i);
      // 重新上传时 默认选中新上传的原图
      const imageProcessParams = form.getFieldValue(imageProcessParamsPath);
      // 如果controlnet没有开启 不需要替换为原图
      if (imageProcessParams && imageProcessParams.model) {
        form.setFieldValue([...imageProcessParamsPath, 'image'], previewUrl);
      }
    }
  };

  const track = () => {
    trackEvent('whee_upload_image_click', {
      function: AppModuleParam.ImageToImage
    });
  };

  return (
    <div onClick={track}>
      <Upload.Dragger
        value={value}
        onDrop={track}
        onChange={onValueChange}
        taskCategory={DraftType.IMAGE_TO_IMAGE}
      />
    </div>
  );
}

/**
 * 参考图片模块
 */
export function ImageSection() {
  return (
    <>
      <Collapse.Panel.Section title={<strong>形象参考图</strong>}>
        <Form.Item name={['image', 'key']} noStyle>
          <DraggableUpload />
        </Form.Item>
      </Collapse.Panel.Section>
    </>
  );
}
