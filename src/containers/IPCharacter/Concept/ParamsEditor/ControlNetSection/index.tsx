import type {
  ControlNetSectionTabPaneProps,
  ControlNetSectionHandle
} from '@/components/ControlNetSection';
import type { ImageToImageParams } from '../types';

import { forwardRef, useEffect } from 'react';
import { Form } from 'antd';
import { ControlNetSection as OriginControlNetSection } from '@/components/ControlNetSection';

import { SliderInput } from '@/components';
import { EditorConfigControlNetModel, EditorConfigResponse } from '@/api/types';
import { ControlNetModelImage } from '@/components/ControlNet/ControlNetModelImage';
import { RangeSliderInput } from '@/components/RangeSliderInput';
import { ControlNetParams } from '@/types';
import { DraftType } from '@/api/types';
import { usePrevious } from 'react-use';

interface ControlNetSectionProps {
  moduleList: EditorConfigResponse['moduleList'];
}

function ControlNetSectionTabPane({
  tab,
  index,
  moduleList
}: ControlNetSectionTabPaneProps) {
  const getNamePath = (key: string) => ['controlNet', index, key];
  const form = Form.useFormInstance<ImageToImageParams>();

  const imageProcessParamsPath = getNamePath('imageProcessParams');
  const initImageUrl = Form.useWatch(['image', 'key'], form);
  // const [originImage, setOriginImage] = useState<null | string>(null);

  // HACK 获取上传原图加签后的路径；回填没有image.preview，但image.key会带有加签参数
  const originImage = form.getFieldValue(['image', 'preview']) ?? initImageUrl;
  const preOriginImage = usePrevious(originImage);

  const imageNamePath = [...imageProcessParamsPath, 'image'];
  useEffect(
    () => {
      if (!initImageUrl) {
        const currentUrl = form.getFieldValue(imageNamePath);
        if (currentUrl === preOriginImage) {
          // 如果当前选中的图片是原图 原图删除时 取消选择
          form.setFieldValue([...imageProcessParamsPath, 'image'], '');
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [initImageUrl]
  );

  const value = Form.useWatch(imageProcessParamsPath, form);
  const selectedModel = value?.modelConfig as
    | EditorConfigControlNetModel
    | undefined;

  const choosablePicBeforePreset = originImage ? [originImage] : [];

  const weightNamePath = getNamePath('weight');
  const timingNamePath = getNamePath('interventionTiming');

  // 选择一个模型后 填入对应的默认值
  function handleSelectModel(model: EditorConfigControlNetModel | undefined) {
    form.setFieldValue(timingNamePath, [
      model?.guidanceStart,
      model?.guidanceEnd
    ]);
    form.setFieldValue(weightNamePath, model?.weight);
  }

  return (
    <div key={tab.value}>
      <Form.Item name={imageProcessParamsPath}>
        <ControlNetModelImage
          moduleList={moduleList}
          choosablePicBeforePreset={choosablePicBeforePreset}
          onSelectModel={handleSelectModel}
          taskCategory={DraftType.IMAGE_TO_IMAGE}
        />
      </Form.Item>
      {selectedModel && (
        <>
          <Form.Item name={getNamePath('weight')}>
            <SliderInput
              title="参考程度"
              min={0}
              max={selectedModel?.weightMax ?? 2}
              step={selectedModel?.weightStep ?? 0.01}
            />
          </Form.Item>
          <Form.Item name={timingNamePath}>
            <RangeSliderInput
              title="参考时段"
              min={0}
              max={selectedModel?.guidanceEndMax ?? 1}
              step={selectedModel?.guidanceStep ?? 0.01}
              minInterval={selectedModel?.guidanceInterval ?? 0.1}
            />
          </Form.Item>
        </>
      )}
    </div>
  );
}

export const ControlNetSection = forwardRef<
  ControlNetSectionHandle,
  ControlNetSectionProps
>((props, ref) => {
  const controlNet = Form.useWatch('controlNet');

  function tabIsEnable(index: number) {
    const params = controlNet?.[index]
      ?.imageProcessParams as ControlNetParams['imageProcessParams'];
    if (!params || !params.modelId) {
      return false;
    }

    /**
     * 作品之前使用的controlent模型可能会下线
     * 导致当前controlent列表中找不到
     */
    const hasId =
      props.moduleList
        .flatMap(({ list }) => list)
        .findIndex((item) => item.id === params.modelId) !== -1;

    return !!(hasId && params.image && params.model && params.module);
  }

  return (
    <OriginControlNetSection
      {...props}
      renderTabPane={(props) => <ControlNetSectionTabPane {...props} />}
      tabIsEnable={tabIsEnable}
      ref={ref}
    />
  );
});
