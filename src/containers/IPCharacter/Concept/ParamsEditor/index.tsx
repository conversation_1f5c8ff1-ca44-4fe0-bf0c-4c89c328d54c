import { Form, Image, message } from 'antd';
import { EditorConfigProvider } from '@/components';
import { Collapse } from '@/components/Collapse';
import { PromptSection } from './PromptSection';
import { ImageSection } from './ImageSection';
import { useRef, useState } from 'react';
import { BaseModel } from './BaseModel';
import { toAtlasImageView2URL } from '@meitu/util';
import styles from './index.module.less';
import { StyleModel } from './StyleModel';
import { ControlNetSection } from './ControlNetSection';
import { ControlNetSectionHandle } from '@/components/ControlNetSection';
import { ParamsSection } from './ParamsSection';
import {
  MeiDouButton,
  MeiDouPrice,
  MeiDouPriceRef
} from '@/components/MeiDouPrice';
import { FunctionCode } from '@/api/types/meidou';
import { toEditorQueryParams } from './utils';
import { useParamsForm } from '../../context/FormContext';
import { observer } from 'mobx-react';
import { useGetAllModel, useConceptConfig, useInitConceptForm } from './hooks';
import { AppModuleParam, CreateImageTaskParams, OnFormFinish } from '@/types';
import { ImageToImageParams } from './types';
import { createConceptTask as createConceptTaskApi } from '@/api';
import { useSyncMemberDescErrorHandler } from '@/hooks/useMember';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useRootStore } from '../../store';
import { ImageTasks, MtccFuncCode } from '@/api/types';
import { trackEvent } from '@/services';
import { useSearchParams } from '@/hooks';

function ParamsEditor() {
  const { form } = useParamsForm();
  const { editorConfig, fetchConceptConfig } = useConceptConfig();
  const baseModelResponse = editorConfig?.baseModel ?? [];
  const { baseModel } = useGetAllModel();
  const moduleList = editorConfig?.moduleList ?? [];
  const controlNetSectionRef = useRef<ControlNetSectionHandle>(null);
  const handleError = useSyncMemberDescErrorHandler();
  const [loading, setLoading] = useState(false);
  const meidouPriceRef = useRef<MeiDouPriceRef>(null);
  const { updateMeiDouBalance } = useMeiDouBalance({
    meidouPriceRef
  });
  const { styleModelId } = useSearchParams();

  const { ConceptStore } = useRootStore();

  useInitConceptForm(form, {
    onInitialized: () => {}
  });

  const onFinish: OnFormFinish<ImageToImageParams> = async (values) => {
    const params = toEditorQueryParams(values);

    if (!values.prompt) {
      message.error('请输入形象参考词，否则无法生图哦');
      return;
    }

    createConceptTask({ params });
  };
  /** 创建任务 */
  const createConceptTask = async (createParams: CreateImageTaskParams) => {
    try {
      setLoading(true);

      const { width, height } = createParams.params;
      let result = await createConceptTaskApi({
        ...createParams,
        functionName: MtccFuncCode.FuncCodeConceptMapDesign
      });
      // 设置图片任务id和宽高 供任务轮训那使用
      const tasks = result.map(({ id }) => {
        return {
          id,
          size: [width, height]
        };
      });

      ConceptStore.setTasks(tasks as ImageTasks);
      // setUserSubmitValue('');
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
      updateMeiDouBalance();
      // setUserSubmitValue('');
    }
  };

  return (
    <EditorConfigProvider>
      <Form
        className={styles.paramsEditor}
        form={form}
        onFinish={onFinish}
        scrollToFirstError={{
          behavior: 'smooth',
          block: 'nearest'
        }}
      >
        <Collapse
          defaultActiveKey={[
            'prompt',
            'image',
            'model',
            'styles',
            'control-net',
            'generate-params'
          ]}
        >
          <Collapse.Panel
            key="image"
            header="形象参考图"
            showArrow={false}
            forceRender
          >
            <ImageSection />
          </Collapse.Panel>

          <Collapse.Panel
            header={null}
            key="prompt"
            showArrow={false}
            forceRender
          >
            <PromptSection />
          </Collapse.Panel>

          <Collapse.Panel
            key="model"
            header={null}
            collapsible="icon"
            showArrow={false}
          >
            <Collapse.Panel.Section
              title={
                <div>
                  <strong>生图模型</strong>
                  {editorConfig?.baseTagUrl && (
                    <Image
                      className={styles.icon}
                      preview={false}
                      src={toAtlasImageView2URL(
                        editorConfig?.baseTagUrl ?? '',
                        {
                          mode: 2,
                          width: 50
                        }
                      )}
                    ></Image>
                  )}
                </div>
              }
            >
              <BaseModel
                refetch={fetchConceptConfig}
                baseModel={baseModel}
                baseModelResponse={baseModelResponse}
              />
            </Collapse.Panel.Section>
          </Collapse.Panel>

          <Collapse.Panel key="styles" header="风格模型" forceRender>
            <StyleModel />
          </Collapse.Panel>

          <Collapse.Panel key="control-net" header="画面参考" forceRender>
            <ControlNetSection
              moduleList={moduleList}
              ref={controlNetSectionRef}
            />
          </Collapse.Panel>

          <Collapse.Panel key="generate-params" header="参数设定" forceRender>
            <ParamsSection />
          </Collapse.Panel>
        </Collapse>

        <MeiDouPrice
          ref={meidouPriceRef}
          functionCode={FunctionCode.concept}
          fields={['quantity', 'batches']}
          getFunctionBody={() => {
            return {
              params: toEditorQueryParams(form.getFieldsValue())
            };
          }}
        >
          {(price, fetchLoading) => (
            <MeiDouButton
              price={price}
              block
              htmlType="submit"
              loading={loading}
              functionId={MtccFuncCode.FuncCodeConceptMapDesign}
              fetchPriceLoading={fetchLoading}
              onClick={() => {
                const editorComponentParams = form.getFieldsValue();
                const editorQueryParams = toEditorQueryParams(
                  editorComponentParams
                );

                if (!price) return;
                /** 立即生成按钮点击 */
                trackEvent('create_btn_click', {
                  function: AppModuleParam.IPCharacterConcept,
                  ...editorQueryParams,
                  modelId: styleModelId,
                  portraitImage: editorComponentParams?.image?.key
                });
              }}
            />
          )}
        </MeiDouPrice>
      </Form>
    </EditorConfigProvider>
  );
}

export default observer(ParamsEditor);
