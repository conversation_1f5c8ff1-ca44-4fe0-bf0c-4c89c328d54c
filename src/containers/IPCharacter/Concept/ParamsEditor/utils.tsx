import { ImageToImageParams, ControlNetZoomMode } from './types';
import { TaskParams } from '@/api/types';
import { RANDOM_SEED_VALUE } from '@/constants';
import { Proportion } from '@/components/SizeSelector/constants';
import { toSnakeCase } from '@meitu/util';
import { getControlNet, formatControlNet } from '@/utils/getControlNet';

/**
 * 编辑器初始值
 * @description TODO: 后期可能做成云控配置
 */
export const initialEditorParams: ImageToImageParams = {
  prompt: '',
  userPrompt: '',
  negativePrompt: '',
  promptWeight: 5,
  quantity: 4,
  sampler: {
    type: '',
    steps: 30
  },
  seed: RANDOM_SEED_VALUE,
  batches: 1,
  moreDetailWeight: 0,
  canvas: {
    size: [768, 1024],
    // proportion: Proportion.ADAPTION,
    proportion: Proportion.THREE_TO_FOUR
  },
  faceSimilarity: 30, // 0-100 默认 0 人脸相似度
  /** 参考图片 */
  image: {
    key: '',
    strength: 80,
    zoomMode: ControlNetZoomMode.Cropping
  },
  baseModelId: -1,
  styleModel: [],
  controlNet: Array(3).fill({
    enable: false,
    weight: 0.6,
    interventionTimingStart: 0,
    interventionTimingEnd: 1,
    zoomMode: ControlNetZoomMode.Stretching
  }),

  facialDetection: true,
  /** 默认开启面部修复 AD 插件 */
  adetailerFace: true,

  /** 默认开启使用推荐参数 */
  recommendParams: true
};

/**
 * 将编辑器 参数设定的数据格式转成查询参数 （即后端需要的格式）
 * @param {ImageToImageParams} editorParams  编辑器参数设定
 * @returns {TaskParams}
 */
export function toEditorQueryParams(
  editorParams: ImageToImageParams
): TaskParams {
  const {
    prompt,
    userPrompt,
    negativePrompt,
    promptWeight,
    baseModelId,
    styleModel = [],
    canvas,
    faceSimilarity,
    quantity,
    sampler,
    seed,
    batches,
    moreDetailWeight,
    image,
    controlNet = [],
    facialDetection,
    superResolution,
    adetailerFace
  } = editorParams;

  const [width, height] = canvas.size;

  const params = {
    /** 提示词 */
    prompt,

    /** 用户填写的原始值  未经过智能联想*/
    userPrompt,

    /** 不希望呈现的内容 */
    negativePrompt,

    /** 模型 */
    baseModelId,

    /** 风格模型 */
    styleModelConfig: styleModel.map(({ id, strength, categoryIds }) => {
      return {
        styleModelId: id,
        styleModelCategories: categoryIds,
        styleModelWeight: strength
      };
    }),

    picRatio: canvas?.proportion,

    /** 生成图片的宽 */
    width,

    /** 生成图片的高 */
    height,

    /** prompt相关性权重 */
    cfgScale: promptWeight,

    /** 人脸相似度 */
    faceGenerationScale: faceSimilarity,

    /** 生成张数 */
    batchSize: quantity,

    /** 随机种子 */
    seed,

    /** 采样器 */
    samplerIndex: sampler?.type,

    /** 采样步骤 */
    steps: sampler?.steps,

    /** 生成批次 */
    nIter: batches,

    /**
     * 更多细节
     * 编辑器中的范围: [-2, 2]
     * 服务端需要:    [-200, 200]
     */
    moreDetailWeight: moreDetailWeight * 100,

    /** 原图 key */
    initImages: image?.key ? [image?.key] : [],

    /** 原图的强度 （降噪强度） */
    denoisingStrength: image?.strength,

    /** 原图的缩放模式 */
    // 图生图选择自适应的时候，缩放模式默认裁剪
    resizeMode:
      canvas?.proportion === Proportion.ADAPTION
        ? ControlNetZoomMode.Cropping
        : image?.zoomMode,

    /** 控制参数 */
    controlnetUnits: formatControlNet(controlNet),

    /** 图片识别 */
    facialDetection,

    /** 图像超分 */
    mdUpscaler: superResolution,

    adetailerFace
  };

  return toSnakeCase(params);
}

/**
 * 将编辑器 后端返回的数据格式转成 前端组件需要的格式
 * @param {TaskParams} editorQueryParams  编辑器参数设定
 * @returns {ImageToImageParams}
 */
export function toEditorComponentParams(
  editorQueryParams: TaskParams
): ImageToImageParams {
  const {
    prompt = '',
    userPrompt = '',
    negativePrompt = '',
    baseModelId,
    styleModelConfig = [],
    cfgScale,
    nIter,
    samplerIndex,
    steps,
    seed,
    batchSize,
    moreDetailWeight,
    initImages = '',
    faceGenerationScale = initialEditorParams.faceSimilarity,
    denoisingStrength,
    resizeMode,
    width,
    height,
    picRatio,
    controlnetUnits = [],
    facialDetection = true,
    mdUpscaler = false,
    adetailerFace = true
  } = editorQueryParams;

  return {
    /** 提示词 */
    prompt,

    /** 用户填写的原始值  未经过智能联想*/
    userPrompt,

    /** 不希望呈现的内容 */
    negativePrompt,

    /** 模型 */
    baseModelId,

    /** 风格模型 */
    styleModel: styleModelConfig.map(
      ({
        styleModelId,
        styleModelWeight,
        styleModelCategories,
        styleModelType,
        styleModelImage,
        styleModelName
      }) => {
        return {
          /** 风格模型id */
          id: styleModelId,

          /** 强度 */
          strength: styleModelWeight,

          /** 分类ids */
          categoryIds: styleModelCategories,

          src: styleModelImage,

          title: styleModelName,

          tag: styleModelType
        };
      }
    ),

    /** 画布 */
    canvas: {
      /** 比例 */
      proportion: picRatio,

      /** 生成图片的宽高 */
      size: [width, height]
    },

    /** prompt相关性权重 */
    promptWeight: cfgScale,

    /** 人脸相似度 */
    faceSimilarity: faceGenerationScale,

    /** 生成张数 */
    quantity: batchSize,

    /** 随机种子 */
    seed,

    /** 采样器 */
    sampler: {
      type: samplerIndex,
      steps
    },

    /** 生成批次 */
    batches: nIter,

    /** 更多细节 */
    moreDetailWeight: moreDetailWeight / 100,

    /** 原图  */
    image: {
      key: initImages?.[0] ?? '',
      strength: denoisingStrength,
      zoomMode: resizeMode
    },

    /** 控制参数 */
    controlNet: getControlNet(
      controlnetUnits,
      initialEditorParams?.controlNet?.[0]
    ),

    /** 图片识别 */
    facialDetection,

    /** 图像超分 */
    superResolution: mdUpscaler,

    adetailerFace
  };
}
