import type { FormInstance } from 'antd';
import produce from 'immer';
import { useCachedIpCharacterFields } from '@/hooks/useCachedPageFields';
import { useSearchParams } from 'react-router-dom';
import { useLocationSearchParamsChanged } from '@/hooks';
import { fetchSameStyleGallery as fetchSameStyleGalleryApi } from '@/api';
import { EditorConfigResponse } from '@/api/types';
import { isExistInBaseModelConfig } from '@/utils/isModelConfigExists';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { toEditorComponentDefaultParams } from '@/utils/editor';
import _ from 'lodash';
import { useConceptConfig } from './useConceptConfig';
import { ImageToImageParams } from '../types';
import { initialEditorParams, toEditorComponentParams } from '../utils';
import { useEffect } from 'react';

/**
 * 初始化表单
 */
export const useInitConceptForm = (
  form: FormInstance<ImageToImageParams>,
  { onInitialized }: { onInitialized?: (params: ImageToImageParams) => void }
) => {
  const [searchParams] = useSearchParams();
  const baseModelId = searchParams.get('baseModelId');
  const id = searchParams.get('id');

  const { fetchConceptConfig } = useConceptConfig();
  const {
    cachedFields: { paramsEditor },
    applyFlushParams
  } = useCachedIpCharacterFields();

  // useLocationSearchParamsChanged(() => {
  //   (async () => {
  //     try {
  //       // 保持重构前的逻辑， 只有id存在的时候才会有整个组件的loading

  //       const res = await fetchConceptConfig();
  //       await initForm(res);
  //     } catch (error) {
  //       console.log(error);
  //     } finally {
  //     }
  //   })();
  // });

  const initForm = async (config: EditorConfigResponse | undefined) => {
    if (!config) return;

    const initBaseModel = config.baseModel[0]?.list[0];
    const initRecommendParams = toEditorComponentDefaultParams(
      config.baseModel[0]?.list[0]?.defaultParams
    );

    let fixturesConfig = produce(
      {
        ...initialEditorParams,
        ...initRecommendParams,
        ...(paramsEditor ?? {}),
        ...(applyFlushParams() ?? {})
      },
      (draft) => {
        draft.baseModelId = initBaseModel?.id; // 默认取第二个分类(第一个后端排序为收藏)的第一个模型(如果是通过url传过来的 优先)
        if (!draft.sampler.type) {
          // 优先取基础模型绑定的采样器，如果没有值再默认取的第一个采样器
          draft.sampler.type = config?.samplerIndex[0].value;
        }
      }
    );

    if (!!id) {
      // TODO is need retry when fetchStyleGalleryConfig fail?
      try {
        const { params } = await fetchSameStyleGalleryApi(id);
        fixturesConfig = {
          // HACK 创作同款时不使用推荐参数
          ..._.omit(fixturesConfig, ['recommendParams']),
          ...(toEditorComponentParams(params) ?? {})
        };
      } catch (error) {
        defaultErrorHandler(error);
      }
    }

    fixturesConfig = produce(fixturesConfig, (draft) => {
      if (!!baseModelId) {
        const isExists = isExistInBaseModelConfig(
          config.baseModel,
          Number(baseModelId)
        );

        if (isExists) {
          draft.baseModelId = Number(baseModelId);
        }
      }
    });

    form.resetFields();
    form.setFieldsValue(fixturesConfig);
    onInitialized?.(fixturesConfig);
  };

  useEffect(() => {
    fetchConceptConfig().then((res) => {
      initForm(res);
    });
  }, []);
};
