import type { EditorConfig } from '@/hooks/useGetEditorConfig/useGetStyleModel';
import { fetchConceptConfig as fetchConceptConfigApi } from '@/api';
import { useCallback } from 'react';
import { atom, useRecoilState, useResetRecoilState } from 'recoil';
import { getAllModelByConfig } from '@/hooks/useGetEditorConfig/getAllModelByConfig';
import { generateEditorConfig } from '@/hooks/useGetEditorConfig/useGetStyleModel';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';

export const ConceptConfigState = atom<EditorConfig | undefined>({
  key: 'conceptConfigState',
  default: undefined
});

/**
 * 获取配置
 * @returns {EditorConfig|undefined}
 */
export const useConceptConfig = () => {
  const resetEditorConfig = useResetRecoilState(ConceptConfigState);
  const [editorConfig, setEditorConfig] = useRecoilState(ConceptConfigState);

  const fetchConceptConfig = useCallback(async () => {
    try {
      const res = await fetchConceptConfigApi();
      setEditorConfig(generateEditorConfig(res));
      return res;
    } catch (error) {
      defaultErrorHandler(error);
    }
  }, [setEditorConfig]);

  return {
    editorConfig,
    setEditorConfig,
    resetEditorConfig,
    fetchConceptConfig
  };
};

export const useGetAllModel = () => {
  const [editorConfig] = useRecoilState(ConceptConfigState);
  const baseModel = getAllModelByConfig(editorConfig?.baseModel ?? []);
  const styleModel = getAllModelByConfig(editorConfig?.styleModel ?? []);
  const baseModelMap = new Map(baseModel.map((model) => [model.id, model]));

  return { baseModel, styleModel, baseModelMap };
};
