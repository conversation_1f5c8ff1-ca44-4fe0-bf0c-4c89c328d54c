import { Suspense, lazy } from 'react';
import { DraftProps } from '../../types';
import { observer } from 'mobx-react';
import DraftProvider from './DraftProvider';

// prettier-ignore
// IP形象 - 概念图 - 列表
const IPCharacterConceptDraftList = lazy(() => import(
  /* webpackChunkName: "ip-character-concept-list" */
  '@/containers/IPCharacter/Concept/Draft/DraftList'
));

// prettier-ignore
// IP形象 - 概念图 - 详情
const IPCharacterConceptDraftPreview = lazy(() => import(
  /* webpackChunkName: "ip-character-concept-preview" */
  '@/containers/IPCharacter/Concept/Draft/DraftPreview'
));

function Draft({ previewId }: DraftProps) {
  return (
    <DraftProvider>
      <Suspense>
        {previewId ? (
          <IPCharacterConceptDraftPreview id={previewId} />
        ) : (
          <IPCharacterConceptDraftList />
        )}
      </Suspense>
    </DraftProvider>
  );
}

export default observer(Draft);
