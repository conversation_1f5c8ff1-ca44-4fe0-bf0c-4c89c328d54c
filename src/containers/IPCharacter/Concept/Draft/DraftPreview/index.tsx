import { AppModuleParam, GraphBatch, LoadingStatus } from '@/types';
import { GraphPreviewContainer } from '../../../components/GraphContainer/GraphPreview/ImagePreviewContainer';
import styles from './index.module.less';
import { taskCompleteTracking } from '@/utils/draftTracking';
import { trackEvent } from '@/services';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useEditorModuleAccessibility, useSearchParams } from '@/hooks';
import { ZcoolButton } from '@/components/ZcoolButton';

type IPCharacterPreviewProps = {
  id: string;
};

export default function IPCharacterPreview({ id }: IPCharacterPreviewProps) {
  const { updateMeiDouBalance } = useMeiDouBalance();
  const { styleModelId } = useSearchParams();
  const editorModuleAccessibility = useEditorModuleAccessibility();
  const hasPublishGalleryControlAccess =
    editorModuleAccessibility?.hasPublishGalleryControlAccess;

  const onImageCreated = (images: GraphBatch[]) => {
    updateMeiDouBalance();
    images.forEach((image) => {
      image.loadingStatus !== LoadingStatus.LOADING &&
        /** 上报任务完成事件 */
        taskCompleteTracking(image, {
          modelId: styleModelId,
          portraitImage: image.params?.initImages
        })(AppModuleParam.IPCharacterConcept);
    });
  };

  function trackFunctionClick(clickType: string) {
    trackEvent('whee_success_results_page_click', {
      click_type: clickType,
      function: AppModuleParam.IPCharacterConcept
    });
  }

  return (
    <GraphPreviewContainer
      extra={<ZcoolButton />}
      hiddenActionKeys={['highDefinition']}
      hasPublishGalleryControlAccess={hasPublishGalleryControlAccess}
      onOpenPublishModal={() => {
        /** 发布按钮点击上报 */
        trackFunctionClick('publish');
      }}
      onPublish={(values) => {
        /** 发布按钮点击上报 */
        trackEvent('post_to_gallery_confirm');
      }}
      onPublishSuccess={(values) => {
        /** 发布按钮点击上报 */
        trackEvent('post_to_gallery_success');
      }}
      onDownload={() => {
        trackFunctionClick('download');
      }}
      onReEditParams={() => {
        trackFunctionClick('re_edit');
      }}
      onRemove={() => {
        trackFunctionClick('delete');
      }}
      onUpscaler={(type) => {
        trackFunctionClick(type);
      }}
      onImagePartialRepaintClick={() => {
        trackFunctionClick('ai_modification');
      }}
      onImageExtensionClick={() => {
        trackFunctionClick('image_extension');
      }}
      onImageEraserClick={() => {
        trackFunctionClick('ai_clear');
      }}
      onConvertTo3D={() => {
        trackFunctionClick('2d_to_3d');
      }}
      onNavigateToIPCharacterCustomization={() => {
        trackFunctionClick('ip_design');
      }}
      onImageCreated={onImageCreated}
      graphPreviewClassName={styles.previewWhee}
    />
  );
}

export { IPCharacterPreview as Component };
