import type { GraphBatch } from '@/types';
import type { ReactNode } from 'react';
import { AppModule, generateRouteTo } from '@/services';
import { DraftType } from '@/api/types';
import {
  fetchDraft,
  fetchTaskByIds,
  removeDraft,
  fetchDraftParamsById
} from '@/api';
import { toEditorComponentParams } from '../ParamsEditor/utils';
import { useRef } from 'react';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { getValidSeed } from '@/utils/editor';
import { ImagesContainerContext } from '../../components/GraphContainer/context';
import { useImagesContainerProvider } from '../../components/GraphContainer/Hooks';
import { useRootStore } from '../../store';
import { useGetParamsForm } from '../../context/FormContext';
import { observer } from 'mobx-react';
import { useNavigate } from 'react-router-dom';

function DraftProvider({ children }: { children: ReactNode }) {
  const contextValue = useImagesContainerProvider({
    ids: [],
    loadingIds: new Set(),
    graphBatch: {},
    imageWatermark: true
  });
  const { ids, graphBatch } = contextValue;

  const navigate = useNavigate();
  const goBack = () => {
    navigate(generateRouteTo(AppModule.IPCharacterConcept), { replace: true });
  };

  const { ConceptStore } = useRootStore();

  const getForm = useGetParamsForm();

  const listRef = useRef<GraphBatch[]>([]);

  listRef.current = ids.map((id) => graphBatch[id]).filter(Boolean);

  return (
    <ImagesContainerContext.Provider
      value={{
        ...contextValue,
        goBack,
        tasks: ConceptStore.tasks,
        clearTasks() {
          // 更新参数栏状态
          ConceptStore.setTasks([]);
        },
        async setFormValue(id, coverParmas) {
          try {
            const { params } = await fetchDraftParamsById(
              id,
              DraftType.IP_CONCEPT
            );

            getForm()?.resetFields();
            getForm()?.setFieldsValue(
              toEditorComponentParams(
                Object.assign(params, coverParmas, {
                  // HACK coverParmas中的seed若为-1则取params中的seed
                  seed: getValidSeed(coverParmas.seed, params.seed)
                })
              )
            );
          } catch (error) {
            defaultErrorHandler(error);
          }
        },
        fetchImages: fetchDraft.bind(null, DraftType.IP_CONCEPT),
        removeImage: removeDraft,
        fetchTasks: fetchTaskByIds
      }}
    >
      {children}
    </ImagesContainerContext.Provider>
  );
}

export default observer(DraftProvider);
