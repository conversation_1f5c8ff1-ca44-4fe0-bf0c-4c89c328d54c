import { GraphList } from '@/containers/IPCharacter/components/GraphContainer/GraphList';
import { useEditorModuleAccessibility, useSearchParams } from '@/hooks';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { AppModule, trackEvent } from '@/services';
import { AppModuleParam, GraphBatch, LoadingStatus } from '@/types';
import { taskCompleteTracking } from '@/utils/draftTracking';
import { observer } from 'mobx-react';

function DraftList() {
  const { updateMeiDouBalance } = useMeiDouBalance();
  const { styleModelId } = useSearchParams();
  const editorModuleAccessibility = useEditorModuleAccessibility();
  const hasPublishGalleryControlAccess =
    editorModuleAccessibility?.hasPublishGalleryControlAccess;

  const onImageCreated = (images: GraphBatch[]) => {
    updateMeiDouBalance();
    images.forEach((image) => {
      image.loadingStatus !== LoadingStatus.LOADING &&
        /** 上报任务完成事件 */
        taskCompleteTracking(image, {
          modelId: styleModelId,
          portraitImage: image.params?.initImages
        })(AppModuleParam.IPCharacterConcept);
    });
  };

  return (
    <GraphList
      appModule={AppModule.IPCharacterConcept}
      onImageCreated={onImageCreated}
      hasPublishGalleryControlAccess={hasPublishGalleryControlAccess}
      onDownload={(id) => {
        trackEvent('ai_image_save', {
          function: AppModuleParam.IPCharacterConcept,
          taskId: id
        });
      }}
    />
  );
}

export default observer(DraftList);
