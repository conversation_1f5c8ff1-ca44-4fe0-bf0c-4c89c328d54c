.sider:global(.ant-collapse) {
  & > .feature-tabs-container:global(:not(:last-child)) {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    margin-bottom: 0;
  }

  & > :global(.ant-form) {
    & > :global(.ant-collapse:first-child) {
      & > :global(.ant-collapse-item:first-child) {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        box-shadow: 0 -9px 0 0 white,
          var(
            --level_2,
            0 0 2px rgba(0, 0, 0, 0.08),
            0 6px 24px rgba(0, 0, 0, 0.06)
          );

        & > :global(.ant-collapse-content) {
          & > :global(.ant-collapse-content-box) {
            padding-top: 0;
          }
        }
      }
    }
  }
}

.loading-spin {
  max-height: none !important;
}
