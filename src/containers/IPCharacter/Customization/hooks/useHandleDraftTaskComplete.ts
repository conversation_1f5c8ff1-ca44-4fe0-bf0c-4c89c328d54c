import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useRootStore } from '../../store';
import { AppModuleParam, GraphBatch, LoadingStatus } from '@/types';
import { toEditorComponentParams } from '../utils';
import { trackEvent } from '@/services';

export function useHandleDraftTaskComplete() {
  const { updateMeiDouBalance } = useMeiDouBalance();
  const { CustomizationStore } = useRootStore();

  function trackTaskComplete(batch: GraphBatch) {
    const eventId =
      batch.loadingStatus === LoadingStatus.SUCCESS
        ? 'ai_image_create_success'
        : 'ai_image_create_fail';
    const values = toEditorComponentParams(batch.params, {
      presetLoraList: CustomizationStore.presetLoraList
    });
    trackEvent(eventId, {
      function: AppModuleParam.IPCharacterCustomization,
      batch_size: batch.params?.batchSize,
      prompt: batch.params?.prompt,
      base_model_id: batch.params?.baseModelId,
      style_model_config: batch.params?.styleModelConfig,
      controlnet_units: batch.params?.controlnetUnits ?? '',
      pic_ratio: batch.params?.picRatio,
      controlnet_image: values?.posture?.image ?? '',
      portrait_id: values?.ipLora?.isPreset ? values.ipLora.id : 'customized'
    });
  }

  const handleImageCreated = (images: GraphBatch[]) => {
    updateMeiDouBalance();
    images.forEach((image) => {
      image.loadingStatus !== LoadingStatus.LOADING &&
        /** 上报任务完成事件 */
        trackTaskComplete(image);
    });
  };

  return handleImageCreated;
}
