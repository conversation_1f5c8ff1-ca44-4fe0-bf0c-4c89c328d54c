import { default<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/utils/defaultErrorHandler';
import { runInAction } from 'mobx';
import { useState } from 'react';
import { useRootStore } from '../../store';
import { useGetParamsForm } from '../../context/FormContext';
import { ParamsForm } from '../ParamsEditor/types';
import { useAutoSelectLora } from './useSelectLora';
import { deleteTrainingModel } from '@/api/training';

export function useRemoveLora() {
  const getForm = useGetParamsForm();
  const [isRemoving, setIsRemoving] = useState(false);
  const { IPCharacterLoraTrainingStore } = useRootStore();
  const autoSelectLora = useAutoSelectLora();

  async function removeLoraById(...ids: number[]) {
    if (isRemoving) {
      return false;
    }

    try {
      setIsRemoving(true);
      for (const id of ids) {
        await deleteTrainingModel({ id });
      }

      const form = getForm();
      const formValues = form?.getFieldsValue() as ParamsForm | undefined;
      const currentSelectedIpLora = formValues?.ipLora;
      runInAction(() => {
        const { removedLora } = IPCharacterLoraTrainingStore.removeLoraById(
          ...ids
        );
        const removedLoraIds = new Set(
          removedLora.map((lora) => lora.trainingModelId)
        );

        if (
          currentSelectedIpLora &&
          removedLoraIds.has(currentSelectedIpLora.id)
        ) {
          autoSelectLora();
        }
      });
      return true;
    } catch (e: any) {
      if (process.env.NODE_ENV === 'development') {
        console.error(e);
      }

      defaultErrorHandler(e);
      return false;
    } finally {
      setIsRemoving(false);
    }
  }

  return {
    isRemoving,
    removeLoraById
  };
}
