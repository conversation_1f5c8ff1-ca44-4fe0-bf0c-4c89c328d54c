import { LoraItem } from '@/api/types/ipCharacter/customization';
import { IPCharacterLoraSelectorValueType } from '../../components/IPCharacterLora';
import { useGetParamsForm } from '../../context/FormContext';
import { useRootStore } from '../../store';
import { TrainingStatus } from '@/api/types';

export function useSelectLora() {
  const { CustomizationStore } = useRootStore();
  const getForm = useGetParamsForm();

  /**
   * 当lora改变时 填入默认的提示词
   *
   * 1. 用户选择自己的形象时：保持输入框内容（如果输入框有值，不清空；如果输入框没有值，也不会自动填入）
   * 2. 用户选择试用形象时：
   *  2.1 如果输入框为空，自动填入预设的提示词
   *  2.2 如果输入框不为空
   * 	 2.2.1 如果输入框中的提示词是试用的lora的提示词，切换到另一个试用lora时，自动填入另一个试用lora的提示词
   *	 2.2.2 如果输入框中的提示词不是试用lora的提示词，切换到另一个试用lora时，输入框的内容不变
   *
   * @param nextValue
   * @param curValue
   * @returns
   */
  function onLoraChange(
    nextValue: IPCharacterLoraSelectorValueType,
    curValue?: IPCharacterLoraSelectorValueType
  ) {
    const form = getForm();

    // 用户选择自己的形象时：保持输入框内容（如果输入框有值，不清空；如果输入框没有值，也不会自动填入）
    if (!nextValue.isPreset || !form) {
      return;
    }
    const curPrompt = form.getFieldValue('prompt');
    // 当前选中的是自定义的形象 且提示词不为空 保持当前提示词
    if (curValue && !curValue.isPreset && curPrompt) {
      return;
    }

    const [nextLora, curLora] = CustomizationStore.presetLoraList.reduce(
      (res, lora) => {
        if (lora.id === nextValue.id) {
          res[0] = lora;
        }

        if (lora.id === curValue?.id) {
          res[1] = lora;
        }

        return res;
      },
      [null, null] as [null | LoraItem, null | LoraItem]
    );

    if (!nextLora || nextLora === curLora) {
      return;
    }

    // 当前提示词为空 || 第一次选中lora || 提示词内容未发生变化
    if (!curPrompt || !curLora || curLora.defaultPrompt === curPrompt) {
      form?.setFieldValue('prompt', nextLora?.defaultPrompt || '');
    }
  }

  function selectLora(value: IPCharacterLoraSelectorValueType) {
    const form = getForm();
    const prevValue = form?.getFieldValue('ipLora') as
      | IPCharacterLoraSelectorValueType
      | undefined;
    form?.setFieldValue('ipLora', value);
    onLoraChange(value, prevValue);
  }

  return {
    selectLora,
    onLoraChange
  };
}

export function useAutoSelectLora() {
  const { selectLora } = useSelectLora();
  const { CustomizationStore, IPCharacterLoraTrainingStore } = useRootStore();

  function autoSelectLora() {
    // 优先选择最新训练的lora
    const newestLora = IPCharacterLoraTrainingStore.customLoraList.find(
      ({ status }) => status === TrainingStatus.Completed
    );
    if (newestLora) {
      return selectLora({
        id: newestLora.trainingModelId,
        isPreset: false
      });
    }

    // 如果没有自己训练的lora 选择第一个试用lora
    const firstPresetLora = CustomizationStore.presetLoraList[0];
    if (firstPresetLora) {
      selectLora({
        id: firstPresetLora.id,
        isPreset: true
      });
    }
  }

  return autoSelectLora;
}
