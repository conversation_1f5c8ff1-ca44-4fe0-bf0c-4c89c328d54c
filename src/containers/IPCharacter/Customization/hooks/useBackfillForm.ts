import { toJS } from 'mobx';
import { useGetParamsForm } from '../../context/FormContext';
import { useRootStore } from '../../store';
import { ParamsForm } from '../ParamsEditor/types';

export function useBackfillForm() {
  const getForm = useGetParamsForm();
  const { CustomizationStore } = useRootStore();

  return function backFill(values: ParamsForm) {
    const form = getForm();
    if (!form) {
      return;
    }
    form?.resetFields();
    form?.setFieldsValue(values);

    const { posture } = values;
    const presetPostureList = toJS(CustomizationStore.presetPostureList);
    const existing =
      posture &&
      posture.id &&
      presetPostureList.find((preset) => preset.id === posture.id);
    if (!existing && posture?.image) {
      CustomizationStore.setPostureUploadValue(posture.image);
    } else {
      CustomizationStore.setPostureUploadValue('');
    }
  };
}
