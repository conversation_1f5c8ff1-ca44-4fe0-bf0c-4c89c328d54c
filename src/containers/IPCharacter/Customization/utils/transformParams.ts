import {
  CustomLoraItem,
  LoraItem,
  PostureControlParams
} from '@/api/types/ipCharacter/customization';
import { ParamsForm } from '../ParamsEditor/types';
import { Proportion } from '@/components/SizeSelector/constants';
import { DraftType, TrainingStatus } from '@/api/types';
import { fetchStyleModelList } from '@/api';

type DataSource = {
  presetLoraList: LoraItem[];
  customLoraList: CustomLoraItem[];
};

export function toEditorComponentParams(
  params: any,
  { presetLoraList }: Pick<DataSource, 'presetLoraList'>
): ParamsForm {
  const canvas = {
    size: [params.width, params.height] as [number, number],
    proportion: params.picRatio as Proportion
  };

  const loraId =
    params.styleModelConfig?.[0]?.styleModelId ?? presetLoraList[0]?.id;
  const isPreset = !!presetLoraList.find((lora) => lora.id === loraId);

  return {
    canvas,
    prompt: params.prompt || ('' as string),
    posture: params.postureCtl as PostureControlParams,
    quantity: params.batchSize || (4 as number),
    ipLora: {
      id: loraId,
      isPreset
    }
  };
}

export async function toRequestParams(
  formValues: ParamsForm,
  { presetLoraList, customLoraList }: DataSource
) {
  const styleModelConfig = {} as any;
  if (formValues.ipLora.isPreset) {
    const lora = presetLoraList.find(
      (lora) => lora.id === formValues.ipLora.id
    );
    if (!lora) {
      return;
    }

    styleModelConfig.styleModelId = lora.id;
    styleModelConfig.styleModelCategories = lora.categoryId + '';
    styleModelConfig.styleModelName = lora.name;
    styleModelConfig.styleModelImage = lora.images;
    styleModelConfig.styleModelWeight = lora.weight;
  } else {
    const lora = customLoraList.find(
      (lora) =>
        lora.status === TrainingStatus.Completed &&
        lora.trainingModelId === formValues.ipLora.id
    );
    if (!lora) {
      return;
    }

    const categoryList = await fetchStyleModelList({
      styleModelIds: lora.trainingModelId + '',
      from: DraftType.IP_CUSTOMIZATION
    });

    function findLora() {
      for (const c of categoryList) {
        for (const l of c.list) {
          if (l.id === lora?.trainingModelId) {
            return l;
          }
        }
      }
    }

    styleModelConfig.styleModelId = lora.trainingModelId;
    styleModelConfig.styleModelWeight = findLora()?.styleModelWeight ?? 60;
    styleModelConfig.styleModelImage = lora.coverPic;
    styleModelConfig.styleModelName = lora.name;
  }

  const {
    size: [width, height],
    proportion
  } = formValues.canvas;

  const params = {
    width,
    height,
    picRatio: proportion,
    prompt: formValues.prompt,
    batchSize: formValues.quantity,
    styleModelConfig: [styleModelConfig],
    postureCtl: undefined as undefined | PostureControlParams
  };

  if (formValues.posture?.image) {
    Object.assign(params, {
      postureCtl: formValues.posture
    });
  }

  return params;
}
