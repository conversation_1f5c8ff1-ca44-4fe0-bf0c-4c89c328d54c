import { Collapse } from '@/components';
import {
  Create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  IPCharacterCreate,
  IPCharacterLora,
  IPCharacterLoraSelector
} from '../../../components/IPCharacterLora';
import { Form, message, Modal } from 'antd';
import { useEffect, useState } from 'react';
import styles from './style.module.less';
import { useLocation, useNavigate } from 'react-router-dom';
import { observer } from 'mobx-react';
import { useRootStore } from '@/containers/IPCharacter/store';
import { LoraItem } from '@/api/types/ipCharacter/customization';
import { toJS } from 'mobx';
import {
  MtccFuncCode,
  TrainingProgressResponse,
  TrainingStatus
} from '@/api/types';
import { CustomLoraItemWithProgress } from '@/containers/IPCharacter/store/ipCharacterLoraTraining';
import { useSelectLora } from '../../hooks/useSelectLora';
import { useGetParamsForm } from '@/containers/IPCharacter/context/FormContext';
import {
  createIpCharacter<PERSON>ora,
  ipCharacterLoraTrainingRetry
} from '@/api/training';
import { AppModuleParam, MemberGroupCategory } from '@/types';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useOpenSubscribePopup } from '@/hooks/useSubscribe';
import { useRemoveLora } from '../../hooks/useRemoveLora';
import { IPCharacterLoraRemove } from '@/containers/IPCharacter/components/IPCharacterLora/IPCharacterLoraRemove';
import { trackEvent } from '@/services';

/**
 * 可以通过location.state的方式向组件传递打开弹窗的数据
 */
export type OpenModalFromLocationState = {
  initialImage: string;
  source: string;
};

type ModalOptions = {
  isOpen: boolean;
  retryId?: number;
  initialImage?: string;
  triggerSource?: string;
};

type RemoveModalOptions = {
  isOpen: boolean;
  removeId?: number;
};

function IPCharacterLoraSection() {
  const location = useLocation();
  const navigate = useNavigate();

  const [modalOptions, setModalOptions] = useState<ModalOptions>({
    isOpen: !!location.state?.openCreateLoraModal,
    initialImage: location.state?.openCreateLoraModal?.initialImage || '',
    triggerSource: location.state?.openCreateLoraModal?.source || ''
  });

  const openModal = (modalOptions?: Omit<ModalOptions, 'isOpen'>) => {
    setModalOptions({
      ...modalOptions,
      isOpen: true
    });
  };
  const closeModal = () => {
    setModalOptions({
      isOpen: false
    });
  };

  useEffect(() => {
    if (location.state) {
      const state = { ...location.state };

      // 从state取值完毕后 清理state中的参数 防止刷新时弹窗再次弹出
      if (location.state.openCreateLoraModal) {
        delete state.openCreateLoraModal;
      }

      navigate(`${location.pathname}${location.search}${location.hash}`, {
        state,
        replace: true
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { CustomizationStore, IPCharacterLoraTrainingStore } = useRootStore();
  const presetLoraList = CustomizationStore.presetLoraList;
  const customLoraList = IPCharacterLoraTrainingStore.customLoraList;

  const { onLoraChange } = useSelectLora();

  //#region 训练成功后自动选中
  const getForm = useGetParamsForm();
  useEffect(() => {
    function handleTrainingSuccess(successList: TrainingProgressResponse[]) {
      if (!successList.length || !successList[0].trainingModelId) {
        return;
      }

      const form = getForm();
      if (!form) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('表单对象获取失败');
        }
        return;
      }

      form.setFieldValue('ipLora', {
        id: successList[0].trainingModelId,
        isPreset: false
      });
    }

    IPCharacterLoraTrainingStore.observeSuccess(handleTrainingSuccess);
    return () => {
      IPCharacterLoraTrainingStore.stopObserveSuccess(handleTrainingSuccess);
    };
  }, [IPCharacterLoraTrainingStore, getForm]);
  //#endregion

  const { availableAmount, updateMeiDouBalance } = useMeiDouBalance();

  const [retryingIds, setRetryingIds] = useState<number[]>([]);
  function beforeCreateLora() {
    if (IPCharacterLoraTrainingStore.loopingCounts + retryingIds.length < 3) {
      return true;
    }

    message.error('最多同时制作3个ip形象，请耐心等待～');
    return false;
  }

  //#region 创建lora训练的任务
  const [isCreating, setIsCreating] = useState(false);
  async function handleCreateLora(params: CreateTaskParams) {
    if (isCreating) {
      return false;
    }

    setIsCreating(true);
    try {
      const image = params.image;
      await createIpCharacterLora({
        image,
        functionName: MtccFuncCode.FuncCodeIpImageTraining
      });
      await updateMeiDouBalance();
      await IPCharacterLoraTrainingStore.update();
      await CustomizationStore.fetchCreateLoraPriceDesc();
      closeModal();
      return true;
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
        console.error(e);
      }
      return false;
    } finally {
      setIsCreating(false);
    }
  }
  //#endregion

  //#region 会员订阅相关逻辑
  const ipLoraPrice = CustomizationStore.ipLoraPriceDesc;
  // 需要订阅vip --> 既没有免费次数 也没有vip
  const needsSubscribeVIP = !ipLoraPrice?.totalFreeNum && !ipLoraPrice?.isVip;
  function onSubscribeTradeFinish() {
    return CustomizationStore.fetchCreateLoraPriceDesc();
  }
  //#endregion

  //#region 失败lora重试
  const openSubscribePopup = useOpenSubscribePopup();
  async function handleRetry(id: number) {
    if (!beforeCreateLora()) {
      return false;
    }

    try {
      setRetryingIds((ids) => ids.concat(id));
      await ipCharacterLoraTrainingRetry({ id });
      await IPCharacterLoraTrainingStore.update();
      await updateMeiDouBalance();
      await CustomizationStore.fetchCreateLoraPriceDesc();
      return true;
    } catch (e: any) {
      if (process.env.NODE_ENV === 'development') {
        console.error(e);
      }
      if (e.message) {
        message.error(e.message);
      }
      return false;
    } finally {
      setIsCreating(false);
      setRetryingIds((ids) => ids.filter((i) => i !== id));
    }
  }

  function handleRetryFromModal(id: number) {
    setIsCreating(true);
    return handleRetry(id).finally(closeModal);
  }

  async function handleClickRetry(id: number, image: string) {
    const priceDesc = CustomizationStore.ipLoraPriceDesc;
    if (!priceDesc) {
      return;
    }

    /**
     * 可以直接执行重试
     * 1. 有免费次数
     * 2. 用户是vip并且豆子够用
     */
    if (
      priceDesc.totalFreeNum ||
      (priceDesc.isVip &&
        availableAmount &&
        availableAmount >= priceDesc.amount)
    ) {
      await handleRetry(id);
      return;
    }

    /**
     * 如果是vip 则说明用户豆子不够用 打开订阅弹窗充值美豆
     */
    if (priceDesc.isVip) {
      openSubscribePopup(MemberGroupCategory.Meidou);
      return;
    }

    /**
     * 如果不是vip 打开引导弹窗
     */
    openModal({
      retryId: id,
      initialImage: image
    });
  }
  //#endregion

  //#region 删除lora
  const [removeModalOptions, setRemoveModalOptions] =
    useState<RemoveModalOptions>({
      isOpen: false
    });
  function openRemoveModal(options: Omit<RemoveModalOptions, 'isOpen'>) {
    console.log(options.removeId);
    setRemoveModalOptions({
      ...options,
      isOpen: true
    });
  }
  function closeRemoveModal() {
    setRemoveModalOptions({
      isOpen: false
    });
  }
  const { isRemoving, removeLoraById } = useRemoveLora();
  function handleRemoveLora() {
    if (isRemoving || !removeModalOptions.removeId) {
      return;
    }

    removeLoraById(removeModalOptions.removeId).then((success) => {
      if (!success) {
        return;
      }

      message.success('删除成功');
      closeRemoveModal();
    });
  }

  //#endregion
  return (
    <>
      <Collapse.Panel.Section title={<strong>我的定制</strong>}>
        <Form.Item name="ipLora" className={styles.ipLoraItem}>
          <IPCharacterLoraSelector
            onCreateLora={openModal}
            presetLoraList={toJS(presetLoraList.map(transformPresetLoraData))}
            customLotaList={toJS(customLoraList.map(transformUserLoraData))}
            onChange={onLoraChange}
            onRetry={handleClickRetry}
            onRemove={(id) => {
              openRemoveModal({ removeId: id });

              trackEvent('whee_edit_page_click', {
                function: 'portrait_design',
                click_type: 'delete_portrait'
              });
            }}
            retryingIds={retryingIds}
            isCreating={isCreating}
          />
        </Form.Item>
      </Collapse.Panel.Section>

      <Modal
        centered
        width={740}
        className={styles.createModal}
        open={modalOptions.isOpen}
        footer={false}
        destroyOnClose
        maskClosable={!isCreating}
        onCancel={() => {
          closeModal();
        }}
      >
        <IPCharacterCreate
          initialImage={modalOptions?.initialImage || ''}
          retryId={modalOptions?.retryId}
          triggerSource={
            modalOptions?.triggerSource ||
            AppModuleParam.IPCharacterCustomization
          }
          beforeCreate={beforeCreateLora}
          onCreateLoraTask={handleCreateLora}
          loading={isCreating}
          onRetry={({ id }) => handleRetryFromModal(id)}
          needsSubscribeVIP={needsSubscribeVIP}
          onTradeFinish={onSubscribeTradeFinish}
        />
      </Modal>

      <Modal
        centered
        width={300}
        className={styles.removeModal}
        open={removeModalOptions.isOpen}
        footer={false}
        destroyOnClose={false}
        maskClosable={false}
        closeIcon={null}
        onCancel={() => {
          closeRemoveModal();
        }}
      >
        <IPCharacterLoraRemove
          isRemoving={isRemoving}
          onOk={handleRemoveLora}
          onCancel={closeRemoveModal}
        />
      </Modal>
    </>
  );
}

function transformPresetLoraData(lora: LoraItem): IPCharacterLora {
  return {
    id: lora.id,
    indexId: lora.id,
    preview: lora.images,
    status: TrainingStatus.Completed,
    isPreset: true
  };
}

function transformUserLoraData(
  lora: CustomLoraItemWithProgress
): IPCharacterLora {
  if (lora.status === TrainingStatus.Completed) {
    return {
      id: lora.trainingModelId,
      indexId: lora.id,
      preview: lora.coverPic,
      status: TrainingStatus.Completed,
      isPreset: false
    };
  }

  if (lora.status === TrainingStatus.Failed) {
    return {
      id: lora.id,
      indexId: lora.id,
      preview: lora.coverPic,
      status: TrainingStatus.Failed,
      isPreset: false
    };
  }

  return {
    id: lora.trainingModelId || lora.id,
    indexId: lora.id,
    preview: lora.coverPic,
    status: TrainingStatus.Training,
    remainingTime: lora.estimatedTime ?? '',
    isPreset: false
  };
}

export default observer(IPCharacterLoraSection);
