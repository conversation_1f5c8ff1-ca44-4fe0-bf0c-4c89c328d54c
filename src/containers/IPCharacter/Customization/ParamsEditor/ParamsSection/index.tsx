import {
  Collapse,
  SliderInput,
  SizeSelector,
  PresetSizeMode
} from '@/components';
import { Form } from 'antd';
import './index.module.less';

export function ParamsSection() {
  return (
    <div className="no-select">
      <Collapse.Panel.Section>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.image?.key !== currentValues.image?.key
          }
        >
          {({ getFieldValue }) => (
            <Form.Item name="canvas" noStyle>
              <SizeSelector
                type="custom"
                presetSizeMode={PresetSizeMode.AUTO_MAX_PRESET_SIZE}
              />
            </Form.Item>
          )}
        </Form.Item>
      </Collapse.Panel.Section>

      <Collapse.Panel.Section>
        <Form.Item name="quantity" noStyle>
          <SliderInput
            title="生成张数"
            tooltip="生成张数越多，耗时越久。"
            min={1}
            max={4}
          />
        </Form.Item>
      </Collapse.Panel.Section>
    </div>
  );
}
