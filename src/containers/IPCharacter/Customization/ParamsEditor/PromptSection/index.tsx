import { Collapse, TextArea } from '@/components';
import { Form } from 'antd';

import { trackEvent } from '@/services';
import { AppOrigin, appOrigin } from '@/constants';
import { CorpusFilter } from '@/types';
import { getSource } from '@/utils';
import { transformLanguage } from '@/utils/draftTracking';

export function PromptSection() {
  const form = Form.useFormInstance();
  const source = getSource();
  const promptPlaceholder =
    '无需输入人物关键词，仅输入你的场景关键词，比如“氛围光照，在舞台上，夸张的动作”';
  return (
    <Collapse.Panel.Section title={<strong>场景延展</strong>}>
      <Form.Item name="prompt" noStyle>
        <TextArea.SmartTextarea
          hasComplementAction={false}
          style={{ height: '160px', resize: 'none' }}
          allowClear
          maxLength={800}
          showCount
          placeholder={promptPlaceholder}
          corpusAction={{
            visible: appOrigin === AppOrigin.Whee,
            corpusFilter: CorpusFilter.IP_CHARACTER_CUSTOM,
            btnLabel: 'IP形象词库',
            expand: {
              numbers: 2
            }
          }}
          onOpen={() => {
            trackEvent('complete_prompt_btn_click', {
              text: form.getFieldValue('prompt')
            });
          }}
          onStartComplement={(text) => {
            trackEvent('complete_prompt_start_click', { text });
          }}
          onSuccess={(text, resultPrompt) => {
            trackEvent('complete_prompt_success', { text, resultPrompt });
          }}
          onApply={(resultPrompt) => {
            trackEvent('complete_prompt_apply_btn_click', { resultPrompt });
            // 更新userSubmitValue
            // setUserSubmitValue(form.getFieldValue('prompt'));
          }}
          onReGenerate={(resultPrompt) => {
            trackEvent('complete_prompt_regenerate_btn_click', {
              resultPrompt
            });
          }}
          onCorpusLanguageChange={(language) => {
            // 词库页面_语言切换点击
            trackEvent('language_switch_click', {
              pageName: source,
              language: transformLanguage(language),
              location: 'cue_cards'
            });
          }}
          onCorpusVisibleChange={(visible) => {
            // 词库点击
            visible &&
              trackEvent('lexicon_click', {
                pageName: source,
                location: 'cue_cards'
              });
          }}
        />
      </Form.Item>
    </Collapse.Panel.Section>
  );
}
