import { Form } from 'antd';
import { Collapse } from '@/components';
import { IPCharacterPosture } from '@/containers/IPCharacter/components/IPCharacterPosture';
import { observer } from 'mobx-react';
import { useRootStore } from '@/containers/IPCharacter/store';
import { useEffect } from 'react';

function PostureSection() {
  const { CustomizationStore } = useRootStore();
  // 当组件卸载时 重置上传状态
  useEffect(
    () => () => {
      CustomizationStore.setPostureUploadValue('');
    },
    [CustomizationStore]
  );

  return (
    <Collapse.Panel.Section>
      <Form.Item name="posture" style={{ marginBottom: 0 }}>
        <IPCharacterPosture
          presetPostureList={CustomizationStore.presetPostureList}
          uploadImageValue={CustomizationStore.postureUploadValue}
          onUploadImageChange={(img) => {
            CustomizationStore.setPostureUploadValue(img);
          }}
        />
      </Form.Item>
    </Collapse.Panel.Section>
  );
}

export default observer(PostureSection);
