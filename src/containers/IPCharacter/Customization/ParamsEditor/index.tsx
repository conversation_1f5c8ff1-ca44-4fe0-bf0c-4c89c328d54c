import { Form, message } from 'antd';
import { EditorConfigProvider } from '@/components';
import { Collapse } from '@/components/Collapse';
import IPCharacterLoraSection from './IPCharacterLoraSection';
import { useParamsForm } from '../../context/FormContext';
import { ParamsSection } from './ParamsSection';
import {
  MeiDouButton,
  MeiDouPrice,
  MeiDouPriceRef
} from '@/components/MeiDouPrice';
import { FunctionCode } from '@/api/types/meidou';
import { toEditorQueryParams } from '@/containers/TextToImage/ParamsEditor/utils';
import { Proportion } from '@/components/SizeSelector/constants';
import PostureSection from './PostureSection';
import { PromptSection } from './PromptSection';
import { useRootStore } from '../../store';
import { useEffect, useRef, useState } from 'react';
import { ParamsForm } from './types';
import { observer } from 'mobx-react';
import { createIPCharacterImage } from '@/api';
import { toRequestParams } from '../utils';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useSyncMemberDescErrorHandler } from '@/hooks/useMember';
import { useAutoSelectLora } from '../hooks/useSelectLora';
import { trackEvent } from '@/services';
import { AppModuleParam } from '@/types';
import { MtccFuncCode } from '@/api/types/common';

function ParamsEditor() {
  const { form } = useParamsForm();
  const { CustomizationStore, IPCharacterLoraTrainingStore } = useRootStore();
  const autoSelectLora = useAutoSelectLora();
  useEffect(() => {
    CustomizationStore.initConfig()
      .then(() =>
        IPCharacterLoraTrainingStore.startLoop(
          CustomizationStore.config?.userLoraList
        )
      )
      .then(autoSelectLora);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [CustomizationStore, IPCharacterLoraTrainingStore]);

  const handleError = useSyncMemberDescErrorHandler();
  const [loading, setLoading] = useState(false);
  const meidouPriceRef = useRef<MeiDouPriceRef>(null);
  const { updateMeiDouBalance } = useMeiDouBalance({
    meidouPriceRef
  });
  async function onFinish(values: ParamsForm) {
    try {
      setLoading(true);
      const requestParams = await toRequestParams(values, {
        presetLoraList: CustomizationStore.presetLoraList,
        customLoraList: IPCharacterLoraTrainingStore.customLoraList
      });

      if (!requestParams) {
        return;
      }

      trackEvent('create_btn_click', {
        function: AppModuleParam.IPCharacterCustomization,
        batch_size: requestParams.batchSize,
        prompt: requestParams.prompt,
        pic_ratio: requestParams.picRatio,
        controlnet_image: requestParams.postureCtl?.image || '',
        style_model_config: requestParams.styleModelConfig,
        portrait_id: values.ipLora?.isPreset ? values.ipLora?.id : 'customized'
      });

      const { width, height } = requestParams;
      let result = await createIPCharacterImage(requestParams);
      // 设置图片任务id和宽高 供任务轮训那使用
      const tasks = result.map(({ id }: { id: string }) => {
        return {
          id,
          size: [width, height],
          params: requestParams
        };
      });

      CustomizationStore.setTasks(tasks);
      // setUserSubmitValue('');
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
      updateMeiDouBalance();
      // setUserSubmitValue('');
    }
  }

  return (
    <EditorConfigProvider>
      <Form
        // className={styles.paramsEditor}
        form={form}
        onFinish={onFinish}
        scrollToFirstError={{
          behavior: 'smooth',
          block: 'nearest'
        }}
        initialValues={{
          canvas: {
            size: [768, 1024],
            proportion: Proportion.THREE_TO_FOUR
          },
          quantity: 4
        }}
      >
        <Collapse defaultActiveKey={['lora', 'prompt', 'posture', 'params']}>
          <Collapse.Panel
            header={null}
            key="lora"
            showArrow={false}
            collapsible="disabled"
          >
            <IPCharacterLoraSection />
          </Collapse.Panel>

          <Collapse.Panel
            header={null}
            key="prompt"
            showArrow={false}
            collapsible="disabled"
          >
            <PromptSection />
          </Collapse.Panel>

          <Collapse.Panel header="姿势控制" key="posture" showArrow>
            <PostureSection />
          </Collapse.Panel>

          <Collapse.Panel header="参数设定" key="params">
            <ParamsSection />
          </Collapse.Panel>

          <MeiDouPrice
            ref={meidouPriceRef}
            functionCode={FunctionCode.ipimagedrawing}
            fields={['quantity', 'batches']}
            getFunctionBody={() => {
              return {
                params: toEditorQueryParams(form.getFieldsValue())
              };
            }}
          >
            {(price, fetchLoading) => (
              <MeiDouButton
                price={price}
                block
                htmlType="submit"
                loading={loading}
                functionId={MtccFuncCode.FuncCodeIpImageDrawing}
                fetchPriceLoading={fetchLoading}
                onClick={(e) => {
                  const values = form.getFieldsValue() as ParamsForm;
                  if (!values.prompt) {
                    message.error('请输入场景延展提示词，否则无法生图哦');
                    e.preventDefault();
                    return;
                  }
                }}
              />
            )}
          </MeiDouPrice>
        </Collapse>
      </Form>
    </EditorConfigProvider>
  );
}

export default observer(ParamsEditor);
