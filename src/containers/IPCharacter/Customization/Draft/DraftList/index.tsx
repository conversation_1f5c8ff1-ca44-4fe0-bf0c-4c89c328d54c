import { GraphList } from '@/containers/IPCharacter/components/GraphContainer/GraphList';
import { AppModule, trackEvent } from '@/services';
import { AppModuleParam, FilterType } from '@/types';
import { observer } from 'mobx-react';
import { useHandleDraftTaskComplete } from '../../hooks';

function DraftList() {
  const handleImageCreated = useHandleDraftTaskComplete();
  return (
    <GraphList
      fixturesOptions={(options) =>
        options.filter((option) => option.value !== FilterType.PUBLISHED)
      }
      hiddenActionKeys={['publish']}
      onImageCreated={handleImageCreated}
      // onReEditParams={onReEditParams}
      onDownload={(id) => {
        trackEvent('ai_image_save', {
          function: AppModuleParam.IPCharacterCustomization,
          taskId: id
        });
      }}
      // hasPublishGalleryControlAccess={hasPublishGalleryControlAccess}
      // hasControlnetImageControlAccess={hasPublishGalleryControlAccess}
      appModule={AppModule.IPCharacterCustomization}
    />
  );
}

export default observer(DraftList);

export { DraftList as Component };
