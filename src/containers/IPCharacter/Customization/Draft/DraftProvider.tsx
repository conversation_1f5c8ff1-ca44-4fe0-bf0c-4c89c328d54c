import type { GraphBatch } from '@/types';
import type { ReactNode } from 'react';
import { AppModule, generateRouteTo } from '@/services';
import { DraftType } from '@/api/types';
import {
  fetchDraft,
  fetchTaskByIds,
  removeDraft,
  fetchDraftParamsById
} from '@/api';
import { toEditorComponentParams } from '../utils';
import { useEffect, useRef } from 'react';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { ImagesContainerContext } from '../../components/GraphContainer/context';
import { useImagesContainerProvider } from '../../components/GraphContainer/Hooks';
import { observer } from 'mobx-react';
import { useRootStore } from '../../store';
import { useBackfillForm } from '../hooks';
import { useNavigate } from 'react-router-dom';
import { CustomLoraItem } from '@/api/types/ipCharacter/customization';

function DraftProvider({ children }: { children: ReactNode }) {
  const contextValue = useImagesContainerProvider({
    ids: [],
    loadingIds: new Set(),
    graphBatch: {},
    imageWatermark: true
  });
  const { ids, graphBatch } = contextValue;

  const navigate = useNavigate();
  const goBack = () => {
    navigate(generateRouteTo(AppModule.IPCharacterCustomization), {
      replace: true
    });
  };

  const listRef = useRef<GraphBatch[]>([]);

  listRef.current = ids.map((id) => graphBatch[id]).filter(Boolean);

  const { CustomizationStore, IPCharacterLoraTrainingStore } = useRootStore();
  const backfillForm = useBackfillForm();
  useEffect(() => {
    function handleLoraRemoved(removedList: CustomLoraItem[]) {
      const graphBatch = contextValue.graphBatch;
      const deletedModelIds = new Set(
        removedList.map((lora) => lora.trainingModelId)
      );

      const updatedBatch = Object.entries(graphBatch).reduce(
        (clone, [id, batch]) => {
          let batchClone = batch;

          const batchModelsConfig = batch.params?.styleModelConfig;
          // 将已经删除的模型过滤出去
          const leftConfig = batchModelsConfig?.filter(
            (config) => !deletedModelIds.has(config.styleModelId)
          );
          // 如果存在被删除的模型 标记为不可重新编辑
          if (leftConfig?.length !== batchModelsConfig?.length) {
            batchClone = {
              ...batch,
              disableEdit: true
            } as any;
          }

          clone[id] = batchClone;

          return clone;
        },
        {} as Record<string, GraphBatch>
      );
      contextValue.setGraphBatch(updatedBatch);
    }

    IPCharacterLoraTrainingStore.observeRemove(handleLoraRemoved);

    return () => {
      IPCharacterLoraTrainingStore.stopObserveRemove(handleLoraRemoved);
    };
  }, [contextValue, IPCharacterLoraTrainingStore]);

  return (
    <ImagesContainerContext.Provider
      value={{
        ...contextValue,
        goBack,
        tasks: CustomizationStore.tasks,
        clearTasks() {
          // 更新参数栏状态
          CustomizationStore.setTasks([]);
        },
        async setFormValue(id, coverParams) {
          try {
            const { params } = await fetchDraftParamsById(
              id,
              DraftType.IP_CUSTOMIZATION
            );
            const reeditParams = toEditorComponentParams(
              Object.assign(params, coverParams),
              { presetLoraList: CustomizationStore.presetLoraList }
            );
            backfillForm(reeditParams);
          } catch (error) {
            defaultErrorHandler(error);
          }
        },
        fetchImages: fetchDraft.bind(null, DraftType.IP_CUSTOMIZATION),
        removeImage: removeDraft,
        fetchTasks: fetchTaskByIds
      }}
    >
      {children}
    </ImagesContainerContext.Provider>
  );
}

export default observer(DraftProvider);
