import { Suspense, lazy } from 'react';
import { DraftProps } from '../../types';
import DraftProvider from './DraftProvider';

// prettier-ignore
// IP形象 - 形象定制 - 列表
const IPCharacterCustomizationDraftList = lazy(() => import(
  /* webpackChunkName: "ip-character-customization-list" */
  '@/containers/IPCharacter/Customization/Draft/DraftList'
));

// prettier-ignore
// IP形象 - 形象定制 - 详情
const IPCharacterCustomizationDraftPreview = lazy(() => import(
  /* webpackChunkName: "ip-character-customization-preview" */
  '@/containers/IPCharacter/Customization/Draft/DraftPreview'
));

export default function Draft({ previewId }: DraftProps) {
  return (
    <DraftProvider>
      <Suspense>
        {previewId ? (
          <IPCharacterCustomizationDraftPreview id={previewId} />
        ) : (
          <IPCharacterCustomizationDraftList />
        )}
      </Suspense>
    </DraftProvider>
  );
}
