import { trackEvent } from '@/services';
import { GraphPreviewContainer } from '../../../components/GraphContainer/GraphPreview/ImagePreviewContainer';
import styles from './index.module.less';
import { AppModuleParam } from '@/types';
import { useHandleDraftTaskComplete } from '../../hooks';
import { ZcoolButton } from '@/components/ZcoolButton';

type IPCharacterPreviewProps = {
  id: string;
};

export default function IPCharacterPreview({ id }: IPCharacterPreviewProps) {
  function trackFunctionClick(clickType: string) {
    trackEvent('whee_success_results_page_click', {
      click_type: clickType,
      function: AppModuleParam.IPCharacterCustomization
    });
  }

  const handleImageCreated = useHandleDraftTaskComplete();

  return (
    <GraphPreviewContainer
      extra={<ZcoolButton />}
      onImageCreated={handleImageCreated}
      hiddenActionKeys={['convertTo3D', 'publish']}
      onDownload={() => {
        trackFunctionClick('download');
      }}
      onReEditParams={() => {
        trackFunctionClick('re_edit');
      }}
      onRemove={() => {
        trackFunctionClick('delete');
      }}
      onUpscaler={(type) => {
        trackFunctionClick(type);
      }}
      onImagePartialRepaintClick={() => {
        trackFunctionClick('ai_modification');
      }}
      onImageExtensionClick={() => {
        trackFunctionClick('image_extension');
      }}
      onImageEraserClick={() => {
        trackFunctionClick('ai_clear');
      }}
      // onImageCreated={onImageCreated}
      graphPreviewClassName={styles.previewWhee}
    />
  );
}

export { IPCharacterPreview as Component };
