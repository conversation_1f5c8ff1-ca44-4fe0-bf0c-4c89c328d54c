@import '~@/styles/variables.less';

.galleryBox {
  width: 100%;
  background: url('./background.png') 0 0 no-repeat;
  background-size: 100% 220px;
  overflow-y: inherit;
  background-color: @background-web-page;

  .gallery-content {
    width: 100%;
    height: 100%;
    padding: 34px 120px 0;

    .select-box {
      width: 84px;
      height: 36px;
      border-radius: 12px;
      background: @background-tab;

      :global .@{ant-prefix}-select-selector {
        height: 100%;
        border-radius: 12px;
        background: @background-tab;
        border: none;
        padding: 0 16px;

        :global .@{ant-prefix}-select-selection {
          &-search-input {
            height: 100%;
          }

          &-item {
            display: flex;
            align-items: center;
          }
        }
      }

      :global .@{ant-prefix}-select-arrow {
        right: 16px;

        .suffix-icon {
          pointer-events: none;
        }

        svg {
          color: @content-tab;
        }
      }
    }

    .small-select-box {
      width: 72px;
      height: 30px;

      :global .@{ant-prefix}-select-dropdown {
        top: 34px !important;
      }
    }

    .top-box {
      display: flex;
      justify-content: space-between;

      .top-title {
        color: @content-system-primary;
        font-family: PingFang SC;
        font-size: 32px;
        font-style: normal;
        font-weight: 600;
      }
    }

    .type-box {
      display: flex;
      justify-content: space-between;
      margin-top: 40px;

      .segmented {
        width: 160px;
        height: 36px;

        :global {
          .@{ant-prefix}-segmented-item {
            .@{ant-prefix}-segmented-item-label {
              height: 100%;
              font-size: 14px;
              font-weight: 500;
              line-height: 32px;
              padding: 0 10px;
            }

            &-selected {
              .@{ant-prefix}-segmented-item-label {
              }
            }
          }
        }
      }
    }

    .tab-bar-box {
      // height: 36px;
      margin: 8px 0;
      padding: 8px 0;
      background: @base-white-opacity-100;
      position: sticky;
      top: 56px;
      z-index: 9;
      box-sizing: content-box;

      .bar-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .tab-bar {
          width: 100%;
          position: relative;

          .arrow-box {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: @background-category;
            box-shadow: 0px 1px 8px 0px rgba(0, 0, 0, 0.12);
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            top: 4px;
            z-index: 9;
            cursor: pointer;
          }

          .item-box {
            width: 100%;

            .item-list {
              display: flex;
              align-items: center;
              justify-content: flex-start;

              .gallery-type {
                position: relative;
                width: 100%;
                overflow-x: visible !important;

                :global .swiper-slide {
                  flex-shrink: 0;
                  width: max-content;
                }

                :global(.swiper-button-disabled) {
                  opacity: 0 !important;
                }

                :global(.swiper-button-prev),
                :global(.swiper-button-next) {
                  width: 28px;
                  height: 28px;
                  border-radius: 50%;
                  background: @background-category;
                  box-shadow: 0px 1px 8px 0px rgba(0, 0, 0, 0.12);
                  transition: opacity @motion-duration-mid;
                  top: 26px;

                  &::after {
                    font-size: 16px;
                    color: #abadb2;
                  }
                }

                :global(.swiper-button-prev) {
                  left: -14px;
                }

                :global(.swiper-button-next) {
                  right: -14px;
                }
              }

              .tab-item {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                height: 36px;
                padding: 6px 12px 6px 4px;
                color: @content-category;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                background: #fff;
                border: 1px solid @stroke-category;
                border-radius: 100px;
                cursor: pointer;

                .tab-img {
                  object-fit: cover;
                  border-radius: 50%;
                }

                .tab-name {
                  margin-left: 8px;
                }

                &:hover {
                  background: @background-tag-hover;
                }

                &.active {
                  color: @content-category-selected;
                  font-weight: 500;
                  background: @background-category-selected;
                  border: 1px solid @background-category-selected;
                }
              }
            }
          }
        }

        .tab-icon-box {
          display: flex;

          .search-icon {
            width: 30px;
            height: 30px;
            margin-right: 12px;
            border-radius: 8px;
            background: @background-tab;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
          }
        }
      }

      .search-box {
        display: flex;
        justify-content: center;
        margin: 20px 0 8px;
        transition: all 5s ease;

        .search {
          background: #f6f7fa;
          box-shadow: none;

          :global .@{ant-prefix}-input {
            background: #f6f7fa;
          }
        }
      }
    }
  }
}

.gallery-empty:global(.@{ant-prefix}-empty) {
  position: absolute;
  top: 200%;
  left: 50%;
  transform: translate(-50%, 0);

  :global .@{ant-prefix}-empty-image {
    width: 144px;
    height: 144px;
  }

  :global .@{ant-prefix}-empty-description {
    color: @content-system-tertiary;
  }
}

.spin:global(.@{ant-prefix}-spin) {
  pointer-events: auto;
}
