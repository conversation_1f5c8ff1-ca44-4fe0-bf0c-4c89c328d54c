import { Layout } from '@/layouts';
import styles from './styles.module.less';
import { Segmented } from '@/components';
import { WorkList } from './WorkList';
import { ModelList } from './ModelList';
import { FloatButton, Select, Spin, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { AppModule, generateRouteTo, trackEvent } from '@/services';

import { Empty, Image } from 'antd';
import empty from '@/assets/images/empty.jpg';
import { ChevronDownBold, SearchBold } from '@meitu/candy-icons';
import { fetchGalleryDetail, fetchGalleryTypeList } from '@/api';
import {
  DraftType,
  GalleryTabType,
  GalleryType,
  SortOptionsType
} from '@/api/types';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import classNames from 'classnames';

import { SearchInput, SearchInputRefType } from '@/components/SearchInput';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';

import 'swiper/less';
import 'swiper/less/effect-fade';
import 'swiper/less/pagination';
import 'swiper/less/navigation';
import {
  DetailModalContext,
  DetailModalProvider,
  useDetailModal
} from '../../components/DetailModalProvider';
import { ModelDetailModal } from '@/components/GalleryModel/ModelDetailModal';
import { GalleryImageDetailModal, GalleryProvider } from '@/components/Gallery';

import {
  galleryCompareTracking,
  gallerySwitchTracking,
  galleryCollectTracking,
  galleryCopyPromptTracking,
  galleryCreateSameTracking,
  galleryCreateWithModelTracking,
  GalleryProps,
  galleryOrderTypeTrack,
  galleryStyleModelTrack,
  applyModelTrack,
  galleryModelCollectTrack
} from '@/utils/galleryTracking';
import { getSource } from '@/utils';
import { useLocationChanged, useSearchParams } from '@/hooks';
import { CreateLocationType, EntryTypeEnum, GalleryTabKey } from '@/types';

import {
  useNavigate,
  useSearchParams as useSearchParamsInRouter
} from 'react-router-dom';
import mtstat from '@meitu/mtstat-sdk';
import { INVALID_TRAINING_ID_CODE } from '@/constants/errorCode';
import { isMobile } from '@/utils/isMobile';
import { toAtlasImageView2URL } from '@meitu/util';

function EmptyNode() {
  return (
    <Empty
      image={empty}
      description="暂无数据"
      className={styles.galleryEmpty}
    />
  );
}

const items = [
  {
    label: 'AI创作',
    value: GalleryTabKey.Creation
  },
  {
    label: '模型广场',
    value: GalleryTabKey.Model
  }
];
const selectOptions = [
  {
    label: '默认',
    value: SortOptionsType.Normal
  },
  {
    label: '最新',
    value: SortOptionsType.Time
  },
  {
    label: '最热',
    value: SortOptionsType.Hot
  }
];

function GalleryComponent() {
  const [selectVal, setSelectVal] = useState<SortOptionsType>(
    SortOptionsType.Normal
  );
  const [typeList, setTypeList] = useState<Array<GalleryTabType>>([]);
  const [selectTypeName, setSelectTypeName] = useState('');
  const [selectTypeId, setSelectTypeId] = useState('');
  const [tabIconVisible, setTabIconVisible] = useState(false);
  const [searchVisible, setSearchVisible] = useState(false);

  const tabScrollRef = useRef<HTMLDivElement>(null);
  const SearchInputRef = useRef<SearchInputRefType>(null);

  const searchParams = useSearchParams();
  const [, setSearchParams] = useSearchParamsInRouter();

  const { closeAllModals, addModal } = useDetailModal();
  const navigate = useNavigate();

  // 将galleryActiveTab和query中的tab参数同步
  // 如果没有tab参数，默认打开【AI创作】
  const { tab: galleryActiveTab = GalleryTabKey.Creation } = searchParams;
  const [preTab, setPreTab] = useState<null | GalleryTabKey>(null);

  const [spinning, setSpinning] = useState(false);

  // 每当tab进行切换时
  if (preTab !== galleryActiveTab) {
    // 重置排序方式为【默认】
    setTypeList([]);
    setSelectTypeId('');
    setSelectVal(SortOptionsType.Normal);

    setPreTab(galleryActiveTab);
  }

  const locationKeyRef = useRef('');
  useLocationChanged((location) => {
    if (locationKeyRef.current && location.key !== locationKeyRef.current) {
      closeAllModals();
    }
    locationKeyRef.current = location.key;
  });

  useEffect(() => {
    // 画廊页面进入
    trackEvent('gallery_page_enter');
  }, []);

  useEffect(() => {
    galleryActiveTab && getTypeList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [galleryActiveTab]);

  useEffect(
    () => {
      function handleScroll() {
        const scrollTop = document.documentElement.scrollTop;
        // TODO 170
        if (scrollTop > 230) {
          setTabIconVisible(true);

          // 取消搜索框选中状态再向上滑动时，会收起搜索框
          if (!SearchInputRef.current?.isFocused) {
            setSearchVisible(false);
          }
        } else if (scrollTop <= 230) {
          setTabIconVisible(false);
          setSearchVisible(false);
        }
      }

      window.addEventListener('scroll', handleScroll);

      return () => {
        window.removeEventListener('scroll', handleScroll);
      };
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [tabIconVisible]
  );

  // 链接调起详情页
  const { id, taskCategory } = searchParams;
  useEffect(() => {
    if (id && taskCategory) {
      checkAsync(id, taskCategory);
    } else if (isMobile()) {
      onInvalid(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  /**
   * 获取信息及埋点。
   *
   * @param taskId
   * @param category
   */
  const checkAsync = (taskId: string, category: DraftType) => {
    //获取
    if (isMobile() && process.env.REACT_APP_H5_LINK) {
      //移动端跳转
      toH5(id, taskCategory);
    } else {
      setSpinning(true);
      fetchGalleryDetail(id ?? '', category ?? DraftType.TEXT_TO_IMAGE)
        .then((res) => {
          if (res) {
            const searchParams = new URLSearchParams(window.location.search);
            const entryType = searchParams.get('entryType');
            trackEvent('share_visit_expo', {
              task_id: res.id,
              feed_id: res.feedId,
              model_id: res.modelId,
              gid: mtstat.getDeviceId(),
              os_type: 'web',
              entry_type: entryType || EntryTypeEnum.Other
            });
            // 失效判断
            if (res.isExcellent) {
              addModal({ id, taskCategory });
            } else {
              onInvalid();
            }
          }
        })
        .catch((e) => {
          if (e?.code === String(INVALID_TRAINING_ID_CODE)) {
            //notfound;
            onInvalid();
          }
        })
        .finally(() => {
          setSpinning(false);
        });
    }
  };

  const onInvalid = (showMessage: boolean = true) => {
    if (showMessage) {
      message.warning('此分享链接已失效');
    }
    navigate(generateRouteTo(isMobile() ? AppModule.Overview : AppModule.Art), {
      replace: true
    });
  };

  const toH5 = (taskId: string, category: DraftType) => {
    if (category === DraftType.MODEL) {
      window.location.href = `${process.env.REACT_APP_H5_LINK}/style?id=${taskId}&task_category=${category}`;
    } else {
      window.location.href = `${process.env.REACT_APP_H5_LINK}/inspiration?id=${taskId}&task_category=${category}`;
    }
  };

  const formatWithSelectTypeName = (data: GalleryProps) => ({
    ...data,
    tab: selectTypeName
  });

  // 获取tab数据
  const getTypeList = async () => {
    try {
      const type =
        galleryActiveTab === GalleryTabKey.Creation
          ? GalleryType.Work
          : GalleryType.Model;

      const res = await fetchGalleryTypeList({ type });

      setTypeList(res.tabs);
      setSelectTypeName(res.tabs[0].tabName);
      setSelectTypeId(res.tabs[0].tabId);
    } catch (err) {
      defaultErrorHandler(err);
    }
  };
  // 渲染tab
  const renderTypeList = () => {
    return (
      typeList.length > 1 && (
        <Swiper
          spaceBetween={12}
          slidesPerView="auto"
          modules={[Navigation]}
          navigation
          className={styles.galleryType}
          cssMode={true}
        >
          {typeList?.map((item) => {
            return (
              <SwiperSlide key={item.tabId}>
                <div
                  className={`${styles.tabItem} ${
                    selectTypeId === item.tabId ? styles.active : ''
                  }`}
                  key={item.tabId}
                  onClick={() => {
                    if (item.tabId === selectTypeId) return;
                    trackEvent('gallery_work_tab_click', {
                      tab: item.tabName
                    });

                    setSelectTypeName(item.tabName);
                    setSelectTypeId(item.tabId);
                  }}
                >
                  {item.backGroundPic && (
                    <Image
                      src={toAtlasImageView2URL(item.backGroundPic, {
                        mode: 2,
                        width: 56
                      })}
                      className={styles.tabImg}
                      width={28}
                      height={28}
                      preview={false}
                    />
                  )}
                  <span className={styles.tabName}>{item.tabName}</span>
                </div>
              </SwiperSlide>
            );
          })}
        </Swiper>
      )
    );
  };

  return (
    <Layout theme="overview">
      <div className={styles.galleryBox}>
        <div className={styles.galleryContent}>
          <div>
            <div className={styles.topBox}>
              <div className={styles.topTitle}>灵感探索，激发想象</div>
              <SearchInput
                searchBtnNeedAuthor={true}
                onSearch={(keyword) =>
                  trackEvent('whee_search_click', {
                    function: getSource(),
                    keyword
                  })
                }
              />
            </div>
            <div className={styles.typeBox}>
              <Segmented
                className={styles.segmented}
                options={items}
                value={galleryActiveTab}
                onChange={(value) => {
                  setTypeList([]);
                  setSelectTypeId('');
                  // 切换tab页
                  setSearchParams(
                    Object.assign({}, searchParams, { tab: value })
                  );
                  trackEvent('whee_pagebar_function_click', {
                    click_type: value
                  });
                }}
              />
              <Select
                options={selectOptions}
                className={styles.selectBox}
                value={selectVal}
                onChange={(val) => {
                  setSelectVal(val);
                  galleryOrderTypeTrack(val, galleryActiveTab);
                }}
                suffixIcon={<ChevronDownBold className="suffix-icon" />}
                getPopupContainer={(triggerNode) => triggerNode}
              />
            </div>
          </div>
          {/* 分类tab */}
          <div className={styles.tabBarBox}>
            <div className={styles.barContent}>
              <div
                className={styles.tabBar}
                style={{ width: tabIconVisible ? '85%' : '100%' }}
              >
                <div
                  className={styles.itemBox}
                  key={galleryActiveTab}
                  ref={tabScrollRef}
                >
                  <div className={styles.itemList}>{renderTypeList()}</div>
                </div>
              </div>
              {tabIconVisible && (
                <div className={styles.tabIconBox}>
                  <div
                    className={styles.searchIcon}
                    onClick={() => {
                      setSearchVisible(!searchVisible);
                    }}
                  >
                    <SearchBold />
                  </div>
                  <Select
                    options={selectOptions}
                    className={classNames(
                      styles.selectBox,
                      styles.smallSelectBox
                    )}
                    value={selectVal}
                    onChange={(val) => {
                      setSelectVal(val);
                    }}
                    suffixIcon={<ChevronDownBold className="suffix-icon" />}
                    getPopupContainer={(triggerNode) => triggerNode}
                  />
                </div>
              )}
            </div>
            {searchVisible && (
              <div
                className={styles.searchBox}
                style={{ height: searchVisible ? 'auto' : 0 }}
              >
                <SearchInput
                  className={styles.search}
                  autoFocus={true}
                  ref={SearchInputRef}
                />
              </div>
            )}
          </div>
          {/* 列表内容 */}
          <div className={styles.contentListBox}>
            {galleryActiveTab === GalleryTabKey.Creation ? (
              <WorkList
                key={selectTypeId}
                empty={<EmptyNode />}
                selectTypeName={selectTypeName}
                selectTypeId={selectTypeId}
                sortVal={selectVal}
              />
            ) : (
              <ModelList
                key={selectTypeId}
                empty={<EmptyNode />}
                selectTypeName={selectTypeName}
                selectTypeId={selectTypeId}
                sortVal={selectVal}
                typeList={typeList}
              />
            )}
          </div>
          <DetailModalContext.Consumer>
            {({ modals, sameModelId }) => {
              return modals.map((item) => (
                <>
                  {item.taskCategory === DraftType.MODEL ? (
                    <ModelDetailModal
                      key={item.id}
                      id={item.id}
                      taskCategory={item.taskCategory}
                      onApply={(appModule, detail) => {
                        applyModelTrack(
                          CreateLocationType.Detail,
                          formatWithSelectTypeName(detail),
                          appModule
                        );
                      }}
                      onCollectedChange={(detail) => {
                        galleryModelCollectTrack(
                          CreateLocationType.Detail,
                          detail
                        );
                      }}
                    />
                  ) : (
                    <GalleryImageDetailModal
                      key={item.id}
                      id={item.id}
                      sameModelId={String(sameModelId)}
                      taskCategory={item.taskCategory}
                      onGraphChange={(switchType, src, detail) => {
                        /** 作品图手动切换上报 */
                        gallerySwitchTracking(
                          switchType,
                          src,
                          formatWithSelectTypeName(detail),
                          'gallery_page'
                        );
                      }}
                      /** 作品比对上报 */
                      onCompare={(detail) => {
                        galleryCompareTracking(
                          formatWithSelectTypeName(detail),
                          'gallery_page'
                        );
                      }}
                      onCollectedChange={(detail) => {
                        /** 收藏上报 */
                        galleryCollectTracking(
                          CreateLocationType.Detail,
                          formatWithSelectTypeName(detail)
                        );
                      }}
                      /** 作品描述拷贝上报 */
                      onCopy={(prompt, detail) => {
                        galleryCopyPromptTracking(
                          prompt,
                          formatWithSelectTypeName(detail),
                          'gallery_page'
                        );
                      }}
                      /** 作品模型跳转上报 */
                      onModelClick={(modelId, detail) => {
                        galleryCreateWithModelTracking(
                          modelId,
                          formatWithSelectTypeName(detail),
                          'gallery_page'
                        );
                      }}
                      onStyleModelClick={(modelId, detail) => {
                        galleryStyleModelTrack(modelId, detail);
                      }}
                      /** 创作同款点击上报 */
                      onCreateClick={(detail) => {
                        galleryCreateSameTracking(
                          CreateLocationType.Detail,
                          formatWithSelectTypeName(detail)
                        );
                      }}
                    />
                  )}
                </>
              ));
            }}
          </DetailModalContext.Consumer>
        </div>
      </div>
      <FloatButton.BackTop />
      <Spin className={styles.spin} spinning={spinning} fullscreen />
    </Layout>
  );
}

const Gallery = () => {
  return (
    <GalleryProvider>
      <DetailModalProvider>
        <GalleryComponent />
      </DetailModalProvider>
    </GalleryProvider>
  );
};

// `<Route />` 组件的 `lazy` 需要模块导出一个 `Component`
// https://reactrouter.com/en/main/route/route#lazy
export { Gallery as Component };
