import { GalleryProfile, GalleryTabKey } from '@/types';
import { produce } from 'immer';
import _ from 'lodash';
import { atom } from 'recoil';

// 画廊当前激活 tab
export const GalleryActiveTab = atom<string>({
  key: 'galleryActiveTab',
  default: GalleryTabKey.Creation
});

// 模型集市当前激活 tab
export const ModelActiveTab = atom<string>({
  key: 'modelActiveTab',
  default: ''
});

/**
 * 设置画廊feed收藏状态
 * @returns
 */
export function useSetGalleryFavoriteState(
  list: Array<GalleryProfile>,
  setList: (props: GalleryProfile[]) => void,
  deleteItem: boolean
) {
  return (id: string, value: boolean) => {
    const result = produce(list, (draft) => {
      const targetIndex = _.findIndex(draft, (galleryProfile) => {
        return galleryProfile.id === id;
      });

      draft[targetIndex].isFavor = value;
      value ? draft[targetIndex].favorCount++ : draft[targetIndex].favorCount--;
      // 在当前列表移除此item
      deleteItem && draft.splice(targetIndex, 1);
    });

    setList(result);
  };
}
