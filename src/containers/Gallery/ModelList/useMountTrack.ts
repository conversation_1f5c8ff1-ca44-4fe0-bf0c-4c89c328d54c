import { GalleryTabType } from '@/api/types';
import { trackEvent } from '@/services';
import { useEffect, useRef } from 'react';

export const useMountTrack = (
  activeTab: string,
  typeList: Array<GalleryTabType>
) => {
  const isTracked = useRef(false);

  // 埋点
  useEffect(() => {
    if (!typeList.length || isTracked.current) return;

    isTracked.current = true;

    trackEvent('model_mart_enter', {
      tab: typeList.find((item) => item.tabId === activeTab)?.tabName ?? ''
    });
  }, [activeTab, typeList]);
};
