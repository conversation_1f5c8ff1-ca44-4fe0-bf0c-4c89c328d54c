import type { ReactElement } from 'react';
import {
  CreateLocationType,
  type GalleryProfile,
  type OverviewBanner
} from '@/types';
import { ModelImage } from '../../../components/GalleryModel/ModelImage';
import { useRef, useMemo } from 'react';

import { Gallery, GalleryRef } from '@/components/Gallery';
import {
  DraftType,
  GalleryQuery,
  GalleryQueryFrom,
  GalleryTabType,
  SortOptionsType
} from '@/api/types';
import { fetchGallery as fetchGalleryApi } from '@/api';
import { useMountTrack } from './useMountTrack';
import AdaptContainer from '@/components/Waterfall/AdaptContainer';
import { Carousel } from '@/containers/Overview/Gallery/Carousel';
import { EventsContainer } from '@/components/TrackEvent';
import {
  applyModelTrack,
  galleryClickTracking,
  galleryExposureTracking,
  galleryModelCollectTrack,
  resourcePositionClickTrack,
  resourcePositionExposeTrack
} from '@/utils/galleryTracking';
import { AppModule } from '@/services';

interface ModelListProps {
  onClick?: (modelItem: GalleryProfile) => void;
  onApplyToPage?(page: AppModule, profileProps: GalleryProfile): void;
  onTriggerApply?(profileProps: GalleryProfile): void;

  selectTypeName: string;
  selectTypeId: string;
  sortVal: SortOptionsType;
  empty: ReactElement;
  typeList?: Array<GalleryTabType>;
}

export function ModelList(props: ModelListProps) {
  const {
    selectTypeName,
    selectTypeId,
    sortVal,
    onApplyToPage,
    onTriggerApply,
    typeList
  } = props;

  const ModelRef = useRef<GalleryRef>(null);
  const formatWithSelectTypeName = (data: GalleryProfile) => ({
    ...data,
    tab: selectTypeName
  });

  useMountTrack(selectTypeId, typeList || []);

  const fetchGallery = useMemo(
    () => async (query: GalleryQuery) => {
      if (!selectTypeId) {
        return;
      }

      return await fetchGalleryApi({
        ...query,
        tabId: selectTypeId,
        sort: sortVal,
        from: GalleryQueryFrom.Model
      });
    },
    [selectTypeId, sortVal]
  );

  return (
    <>
      <Gallery
        ref={ModelRef}
        fetchGallery={fetchGallery}
        gutter={[16, 4]}
        renderItem={(profileProps: GalleryProfile, index: number) =>
          profileProps.taskCategory === DraftType.OPERATE ? (
            <AdaptContainer size={profileProps.size} columnWidth={0}>
              <EventsContainer
                onView={() => {
                  resourcePositionExposeTrack({
                    ...profileProps,
                    index,
                    tab: '模型广场'
                  });
                }}
                onClick={(banner, index) => {
                  resourcePositionClickTrack(
                    { ...profileProps, tab: '模型广场' },
                    index
                  );
                }}
              >
                <Carousel
                  banners={profileProps.operations as OverviewBanner[]}
                />
              </EventsContainer>
            </AdaptContainer>
          ) : (
            <EventsContainer
              onView={() =>
                /** 作品曝光上报 */
                galleryExposureTracking(
                  formatWithSelectTypeName(profileProps),
                  CreateLocationType.Feed
                )
              }
              onClick={(galleryItem) => {
                /** 画廊作品点击上报 */
                galleryClickTracking(
                  formatWithSelectTypeName(galleryItem),
                  CreateLocationType.Feed
                );
              }}
            >
              <ModelImage
                {...profileProps}
                selectTypeName={selectTypeName}
                mode={'visitor'}
                feedType={'gallery'}
                onApplyToPage={(appModule) => {
                  onApplyToPage?.(appModule, profileProps);
                  applyModelTrack(
                    CreateLocationType.Feed,
                    formatWithSelectTypeName(profileProps),
                    appModule
                  );
                }}
                onTriggerApply={() => onTriggerApply?.(profileProps)}
                onCollectedChange={(isFavor) => {
                  // 模型收藏上报
                  galleryModelCollectTrack(CreateLocationType.Feed, {
                    ...profileProps,
                    isFavor
                  });
                }}
              />
            </EventsContainer>
          )
        }
      ></Gallery>
    </>
  );
}
