import { ModelProfile } from '@/types';
import { createContext, useContext } from 'react';

interface ModelsContextValue {
  modelList: Array<ModelProfile>;
  setModelList: (props: ModelProfile[]) => void;
  popupContainer?: (triggerNode: HTMLElement) => HTMLElement;
}

export const ModelsContext = createContext<ModelsContextValue>({
  modelList: [],
  setModelList: () => {},
  popupContainer: () => {
    return document.body;
  }
});

export const useModelsContext = () => useContext(ModelsContext);
