@import '~@/styles/variables.less';

.modelsBox {
  .typeBox {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 14px;

    .typeItem {
      margin-right: 10px;
      padding: 6px 12px;
      color: #4d4f52;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      background: @background-tag;
      border-radius: 6px;
      cursor: pointer;

      &:hover {
        background: @background-tag-hover;
      }

      &.active {
        color: @content-btn-primary;
        font-weight: 600;
        background: @base-gradient-ai-light;
      }
    }
  }

  .listBox {
    // width: 100.5%;
    // height: calc(100vh - 178px);
    // overflow-x: hidden;
    // overflow-y: auto;
    width: 100%;
    height: 100%;

    .scrollBox {
      position: relative;
      width: 100%;
    }
  }
}
