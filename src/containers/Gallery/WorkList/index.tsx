import {
  CreateLocationType,
  type GalleryProfile,
  type OverviewBanner
} from '@/types';
import type { GalleryProps } from '@/utils/galleryTracking';
import {
  GalleryQueryFrom,
  type GalleryQuery,
  type SortOptionsType,
  DraftType
} from '@/api/types';
import type { GalleryRef } from '@/components/Gallery';

import { Gallery, GalleryImage } from '@/components/Gallery';
import {
  galleryExposureTracking,
  galleryCollectTracking,
  galleryCreateSameTracking,
  galleryClickTracking,
  resourcePositionExposeTrack,
  resourcePositionClickTrack
} from '@/utils/galleryTracking';
import { fetchGallery as fetchGalleryApi } from '@/api';
import { useMemo, useRef, ReactElement } from 'react';
import AdaptContainer from '@/components/Waterfall/AdaptContainer';
import { Carousel } from '@/containers/Overview/Gallery/Carousel';
import { EventsContainer } from '@/components/TrackEvent';

interface WorkListProps {
  selectTypeName: string;
  selectTypeId: string;
  empty: ReactElement;
  sortVal: SortOptionsType;
}

export function WorkList(props: WorkListProps) {
  const { selectTypeName, selectTypeId, sortVal } = props;

  const fetchGallery = useMemo(
    () => async (query: GalleryQuery) => {
      if (!selectTypeId) {
        return;
      }

      return await fetchGalleryApi({
        ...query,
        tabId: selectTypeId,
        sort: sortVal,
        from: GalleryQueryFrom.Creation
      });
    },
    [selectTypeId, sortVal]
  );

  const formatWithSelectTypeName = (data: GalleryProps) => ({
    ...data,
    tab: selectTypeName
  });

  const galleryRef = useRef<GalleryRef>(null);

  return (
    <Gallery
      ref={galleryRef}
      fetchGallery={fetchGallery}
      gutter={[16, 4]}
      renderItem={(profileProps: GalleryProfile, index) =>
        profileProps.taskCategory === DraftType.OPERATE ? (
          <AdaptContainer size={profileProps.size} columnWidth={0}>
            <EventsContainer
              onView={() => {
                resourcePositionExposeTrack({
                  ...profileProps,
                  index,
                  tab: 'AI创作'
                });
              }}
              onClick={(banner, curIndex) => {
                resourcePositionClickTrack(
                  { ...profileProps, tab: 'AI创作', index: index },
                  curIndex
                );
              }}
            >
              <Carousel banners={profileProps.operations as OverviewBanner[]} />
            </EventsContainer>
          </AdaptContainer>
        ) : (
          <EventsContainer
            onView={() =>
              /** 作品曝光上报 */
              galleryExposureTracking(
                formatWithSelectTypeName(profileProps),
                CreateLocationType.Feed
              )
            }
            onClick={(galleryItem) => {
              /** 画廊作品点击上报 */
              galleryClickTracking(
                formatWithSelectTypeName(galleryItem),
                CreateLocationType.Feed
              );
            }}
          >
            <GalleryImage
              location={CreateLocationType.Feed}
              {...profileProps}
              onCollectedChange={(galleryItem) =>
                /** 收藏上报 */
                galleryCollectTracking(
                  CreateLocationType.Feed,
                  formatWithSelectTypeName(galleryItem)
                )
              }
              onCreateClick={(galleryItem) => {
                /** 创作同款点击上报 */
                galleryCreateSameTracking(
                  CreateLocationType.Feed,
                  formatWithSelectTypeName(galleryItem)
                );
              }}
            />
          </EventsContainer>
        )
      }
    ></Gallery>
  );
}
