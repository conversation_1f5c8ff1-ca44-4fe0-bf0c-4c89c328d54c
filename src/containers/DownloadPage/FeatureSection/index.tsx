import { Star } from '@/icons';
import classNames from 'classnames';
import { LayerWrapper } from '../LayerWrapper';
import { Typography } from '@/components/Typography';
import { FadeType } from '../constants';
import featureImg from '@/assets/images/feature.jpg';
import styles from './index.module.less';

export function FeatureSection() {
  return (
    <section className={classNames('download-section', styles.feature)}>
      <LayerWrapper>
        <LayerWrapper.Summarize fadeType={FadeType.left}>
          <div className={styles.featureTitle}>
            <h3>创意玩法</h3>
            <Star />
            <h3>
              持续
              <Typography.TextRainbow backgroundImage="linear-gradient(90deg, #FF7575 0%, #F7F04F 100%)">
                更新
              </Typography.TextRainbow>
            </h3>
          </div>
          <span>功能与时俱进，创意永不打烊</span>
        </LayerWrapper.Summarize>
        <LayerWrapper.Overflow backgroundImage={featureImg} />
      </LayerWrapper>
    </section>
  );
}

export { FeatureSection as Component };
