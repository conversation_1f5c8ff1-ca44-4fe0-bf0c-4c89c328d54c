@import '~@/styles/variables.less';

.feature {
  &-title {
    display: flex;
    justify-content: center;
    align-items: center;
    > span {
      margin: 0 @size;
      font-size: 16px;
    }
  }
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {
  .feature {
    &-title {
      > span {
        margin: 0 1.2vw;
        font-size: 1.2vw;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .feature {
    &-title {
      > span {
        margin: 0 @size-xs;
        font-size: 10px;
      }
    }
  }
}
