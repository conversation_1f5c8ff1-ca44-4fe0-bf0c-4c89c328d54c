import { useMemo } from 'react';
import _ from 'lodash';
import { Carousel } from 'antd';
import { useIsMobileSize, useInnerHeight } from '@/hooks';
import { PC_POSTERS, MOBILE_POSTERS } from '../constants';
import styles from './index.module.less';

export const PosterSwiper = () => {
  const isMobileSize = useIsMobileSize();
  const innerHeight = useInnerHeight();
  const INTRO_POSTERS = useMemo(() => {
    return _.shuffle(isMobileSize ? MOBILE_POSTERS : PC_POSTERS);
  }, [isMobileSize]);

  return (
    <Carousel
      autoplay
      infinite
      dots={false}
      effect="fade"
      autoplaySpeed={8000}
      className={styles.introPoster}
    >
      {INTRO_POSTERS.map((posterImg, index) => (
        <div key={index} className={styles.posterItem} data-index={index}>
          <div
            style={{
              height: innerHeight,
              backgroundImage: `url(${posterImg})`
            }}
          />
        </div>
      ))}
    </Carousel>
  );
};
