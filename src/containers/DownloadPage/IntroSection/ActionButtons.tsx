import classNames from 'classnames';
import { Android, Apple, Download } from '@/icons';
import { useIsMobileSize } from '@/hooks';
import openApp from '@meitu/open-app';
import { trackEvent } from '@/services';
import styles from './index.module.less';

/**
 * todo 待替换
 * ios 二维码
 * openApp -> other替换为封装好的appName
 * */

export const ActionButtons = () => {
  const isMobileSize = useIsMobileSize();

  const handleDownload = () => {
    // 手机端下载按钮点击
    trackEvent('whee_phone_download_click');

    openApp('wheeai');
  };

  // 二维码曝光埋点
  const handleMouseEnter = () => {
    trackEvent('whee_web_download_qr_code_expo');
  };

  if (isMobileSize) {
    return (
      <div className={styles.actionButton} onClick={handleDownload}>
        <Download />
        立即下载
      </div>
    );
  }
  return (
    <div className={styles.actionButtons}>
      <div className={styles.actionButton} onMouseEnter={handleMouseEnter}>
        <Apple />
        iOS下载
        <div className={classNames(styles.qrcode, styles.qrcodeIos)}></div>
      </div>
      <div className={styles.actionButton} onMouseEnter={handleMouseEnter}>
        <Android />
        Android下载
        <div className={classNames(styles.qrcode, styles.qrcodeAndroid)}></div>
      </div>
    </div>
  );
};
