import classNames from 'classnames';
import { PosterSwiper } from './PosterSwiper';
import { ActionButtons } from './ActionButtons';
import { AI_DRAW_ID } from '../constants';
import { useInnerHeight } from '@/hooks';
import { WheeOnlyFont, WheeWithBG, NextPageAction } from '@/icons';
import styles from './index.module.less';

export function IntroSection() {
  const innerHeight = useInnerHeight();
  const handleClick = () => {
    const anchorElement = document.getElementById(AI_DRAW_ID);
    if (anchorElement) {
      anchorElement.scrollIntoView({ behavior: 'smooth', block: 'start' }); // 让页面平滑滚动到元素所在位置
    }
  };

  return (
    <section
      style={{ height: innerHeight }}
      className={classNames('download-section', styles.intro)}
    >
      <div className={styles.introWrapper}>
        <div className={styles.introContent}>
          <div className={styles.introInfo}>
            <div className={styles.appInfo}>
              <WheeWithBG className={styles.appIcon} />
              <WheeOnlyFont className={styles.appName} />
            </div>
            <h3>
              <span>A</span>
              I视觉创作的灵感激发
              <span>器</span>
            </h3>
            <ActionButtons />
          </div>
        </div>
      </div>
      <span className={styles.introNextButton} onClick={handleClick}>
        <NextPageAction />
      </span>
      <PosterSwiper />
    </section>
  );
}

export { IntroSection as Component };
