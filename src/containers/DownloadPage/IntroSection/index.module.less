@import '~@/styles/variables.less';

@intro-btn-color: #3549ff;

.intro {
  height: 100vh;
  min-height: 482px;
  &-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: rgba(0, 0, 0, 0.5);
    .intro-content {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      .intro-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        .app-info {
          > svg {
            height: 96px;
          }
          .app-icon {
            font-size: 96px;
            margin-right: @size-md;
          }
          .app-name > svg {
            width: 270px;
            height: 66px;
          }
        }
        > h3 {
          color: #fff;
          font-size: 26px;
          font-weight: 500;
          letter-spacing: 9px;
          margin: @size-ms 0 0;
          line-height: 36px;
          > span:first-child {
            letter-spacing: 4px;
          }
          > span:last-child {
            letter-spacing: 0;
          }
        }
        .action-buttons {
          margin: 80px 0 40px 0;
          display: flex;
          cursor: pointer;
        }
        .action-button {
          width: 224px;
          height: 72px;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          border-radius: 100px;
          color: #fff;
          font-size: @font-size-xl;
          font-weight: 500;
          border: 2px solid #fff;
          background: rgba(0, 0, 0, 0.25);
          backdrop-filter: blur(35px);
          transition: background 0.5s;
          &:first-child {
            margin-right: 40px;
          }
          &:hover {
            background: @intro-btn-color;
            border: unset;
            .qrcode {
              opacity: 1;
              transform: translate(-50%, 0);
            }
          }

          > span {
            font-size: 30px;
            margin-right: 8px;
          }

          .qrcode {
            position: absolute;
            top: calc(100% + 8px);
            left: 50%;
            transform: translate(-50%, 24px);
            width: 128px;
            height: 128px;
            background-size: 100% !important;
            opacity: 0;
            justify-content: center;
            align-items: center;
            z-index: 2;
            border-radius: 12px;
            background: #fff;
            box-shadow: 0 12px 36px 0 rgba(0, 0, 0, 0.5);
            transition: transform 0.5s ease-out;
          }
          .qrcode-ios {
            background-image: url('~@/assets/images/qrcode_ios.png');
          }
          .qrcode-android {
            background-image: url('~@/assets/images/qrcode_android.png');
          }
        }
      }
    }
  }
  &-next-button {
    position: absolute;
    left: 50%;
    bottom: 36px;
    transform: translateX(-50%);
    cursor: pointer;
    color: transparent;
    > span {
      font-size: 30px;
      animation: bounce 2.5s infinite;
    }
  }
  :global(.ant-carousel) {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    .poster-item {
      opacity: 1;
      border-radius: 0 0 36px 36px;
      > div {
        background-size: cover;
        background-repeat: no-repeat;
        background-position: 50%;
      }
    }

    :global(.slick-list) {
      border-radius: 0 0 36px 36px;
    }
    :global(.slick-active) {
      animation: zoomIn 8s linear forwards;
    }

    @keyframes zoomIn {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      100% {
        transform: scale(1.1);
        opacity: 1;
      }
    }
  }

  @keyframes bounce {
    0%,
    20%,
    53%,
    to {
      animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
      transform: translateZ(0);
    }

    40%,
    43% {
      animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
      transform: translate3d(0, -8px, 0) scaleY(1.1);
    }

    70% {
      animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
      transform: translate3d(0, -4px, 0) scaleY(1.05);
    }

    80% {
      transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
      transform: translateZ(0) scaleY(0.95);
    }

    90% {
      transform: translate3d(0, -4px, 0) scaleY(1.02);
    }
  }
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {
  .intro {
    min-height: 47.2vw;
    &-wrapper {
      .intro-content {
        .intro-info {
          .app-info {
            .app-icon {
              font-size: 6.7vw;
            }

            .app-name > svg {
              width: 18.75vw;
              height: 4.58vw;
            }
          }

          > h3 {
            font-size: 1.8vw;
            line-height: 2.5vw;
          }

          .action-buttons {
            width: 33.8vw;
            height: 5vw;
            margin: 5.56vw 0 2.78vw 0;
          }

          .action-button {
            width: 15.7vw;
            height: 5vw;
            border-radius: 6.94vw;
            font-size: 1.38vw;

            &:first-child {
              margin-right: 2.78vw;
            }
            > span {
              font-size: 2vw;
              margin-right: 0.56vw;
            }

            .qrcode {
              width: 8.8vw;
              height: 8.8vw;
              padding: 0.6vw;
              border-radius: 0.8vw;
            }
          }
        }
      }
    }

    &-next-button {
      bottom: 2.5vw;
      > span {
        font-size: 2vw;
      }
    }

    :global(.ant-carousel) {
      .poster-item {
        object-fit: cover;
        object-position: center;
        border-radius: 0 0 2.5vw 2.5vw;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .intro {
    &-wrapper {
      .intro-content {
        .intro-info {
          .app-info {
            .app-icon {
              font-size: 56px;
              margin-right: 14px;
            }
            .app-name > svg {
              width: 164px;
              height: 40px;
            }
          }
          > h3 {
            font-size: 15px;
            letter-spacing: 6px;
            margin: 12px 0 0;
          }
          .action-button {
            width: 200px;
            height: 60px;
            background: @intro-btn-color;
            border-radius: 100px;
            margin-top: 200px;
            font-size: 17px;
            gap: 6px;
            border: none;
            backdrop-filter: unset;
            :global(.anticon) {
              font-size: 24px;
              margin-right: 0;
            }
            animation: heart-beat 2s linear infinite;
          }
        }
      }
    }
    &-next-button {
      bottom: 60px;
      > span {
        animation: unset;
      }
    }
  }
  @keyframes heart-beat {
    0%,
    28%,
    70% {
      transform: scale(1);
    }
    14%,
    42% {
      transform: scale(1.1);
    }
  }
}
