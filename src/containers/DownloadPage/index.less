@import '~@/styles/variables.less';

.download-wrapper {
  position: relative;
  // 原布局+头部样式覆盖
  .@{ant-prefix}-layout {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background: #08090f !important;
    .@{ant-prefix}-layout-content {
      padding: 0;
    }
  }
  .blurred {
    backdrop-filter: blur(@size-xl) !important;
  }
  .download-header.@{ant-prefix}-layout-header {
    background: transparent;
    border-bottom: none;
    backdrop-filter: unset;
    z-index: 99;
    .designer-layout-header-logo {
      svg > path {
        fill: #fff;
      }
    }
    .@{ant-prefix}-menu.@{ant-prefix}-menu-overflow {
      font-weight: 400;
      border-bottom: none;
      color: rgba(255, 255, 255, 0.7);
      .@{ant-prefix}-menu-overflow-item.@{ant-prefix}-menu-item-selected,
      .@{ant-prefix}-menu-overflow-item.@{ant-prefix}-menu-item-active,
      .@{ant-prefix}-menu-submenu-active {
        .@{ant-prefix}-menu-title-content {
          color: #fff;
          background: none;
        }
      }
      .@{ant-prefix}-menu-item:hover,
      .@{ant-prefix}-menu-submenu:hover {
        .@{ant-prefix}-menu-title-content {
          background: none !important;
        }
      }
      .@{ant-prefix}-menu-overflow-item-rest {
        .anticon-ellipsis {
          color: rgba(255, 255, 255, 0.7);
          &:hover {
            color: #fff !important;
          }
        }
      }
    }
  }
  .download-section {
    width: 100%;
    overflow: hidden;
    transition: height 0.3s;
    transform: translateZ(0);
  }
}

// 适配手机端
@media screen and (max-width: 768px) {
  .download-header {
    display: none !important;
  }
}
