import classNames from 'classnames';
import { Stars } from '@/icons';
import aiDrawImg from '@/assets/images/ai-draw.jpg';
import { AI_DRAW_ID, FadeType } from '../constants';
import { Typography } from '@/components/Typography';
import { LayerWrapper } from '../LayerWrapper';
import styles from './index.module.less';
import { useIsMobileSize } from '@/hooks';

export function AIDrawSection() {
  const isMobileSize = useIsMobileSize();
  return (
    <section
      id={AI_DRAW_ID}
      className={classNames('download-section', styles.aiDraw)}
    >
      <LayerWrapper className={styles.aiDrawWrapper}>
        <LayerWrapper.Summarize fadeType={FadeType.left}>
          <div className={styles.title}>
            <h3>
              <Typography.TextRainbow backgroundImage="radial-gradient(65.16% 80.48% at 12.19% 76.61%, #FFFF6C 0%, #FF6A00 57.81%, #F200F6 100%)">
                灵感闪现
              </Typography.TextRainbow>
            </h3>
            <Stars />
          </div>
          <p>图像即刻呈现!</p>
          <span>输入提示词或者图片，AI帮你画</span>
        </LayerWrapper.Summarize>
        <LayerWrapper.Overflow
          className={styles.overflowWrapper}
          backgroundImage={aiDrawImg}
        >
          {!isMobileSize && (
            <video
              loop
              muted
              autoPlay
              playsInline
              preload="auto"
              controls={false}
              disablePictureInPicture
              webkit-playsinline="true"
              x5-video-player-type="h5"
              poster={aiDrawImg}
            >
              <source
                src={require('@/assets/videos/ai-draw.mp4')}
                type="video/mp4"
              />
              Your browser does not support the video tag
            </video>
          )}
        </LayerWrapper.Overflow>
      </LayerWrapper>
    </section>
  );
}

export { AIDrawSection as Component };
