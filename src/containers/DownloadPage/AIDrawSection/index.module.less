@import '~@/styles/variables.less';

.ai-draw {
  &-wrapper {
    padding: 120px 0 80px;
    display: flex;
    justify-content: center;
    .title {
      position: relative;
      margin-bottom: 2px;
      > span {
        position: absolute;
        font-size: @size-xl;
        top: -12px;
        right: -32px;
      }
    }
  }
  .overflow-wrapper {
    box-shadow: unset;
  }
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {
  .ai-draw {
    &-wrapper {
      padding: 8.3vw 0 5.5vw;
    }
  }
}

@media screen and (max-width: 768px) {
  .ai-draw {
    &-wrapper {
      .title {
        > span {
          font-size: 20px;
          right: -18px;
        }
      }
    }
  }
}
