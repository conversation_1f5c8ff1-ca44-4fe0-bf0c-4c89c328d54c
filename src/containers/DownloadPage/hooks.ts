import { RefObject, useEffect } from 'react';
import { atom, useRecoilValue, useSetRecoilState } from 'recoil';
import { useIsMobileSize, useOverlayScrollbars } from '@/hooks';
import type { UseOverlayScrollbarsParams } from 'overlayscrollbars-react';
import { OverlayScrollbars } from 'overlayscrollbars';
import { isMobile } from '@meitu/util';
import { trackEvent } from '@/services';

/** 是否处于顶部，非顶部时header做毛玻璃背景 */
const isTopAtom = atom<boolean>({
  key: 'download/isTop',
  default: true
});

export function useIsTop() {
  return useRecoilValue(isTopAtom);
}

export function useDownloadPageEffect(divRef: RefObject<HTMLDivElement>) {
  const isMobileSize = useIsMobileSize();
  const setIsTop = useSetRecoilState(isTopAtom);

  const overlayScrollbarsParams: UseOverlayScrollbarsParams = {
    options: {
      scrollbars: {
        visibility: 'hidden' // 设计师不想要下载页滚动条显示
      }
    },
    events: {
      scroll(instance: OverlayScrollbars, event: Event) {
        setIsTop((event.target as HTMLDivElement).scrollTop === 0);
      }
    }
  };

  const [initialize] = useOverlayScrollbars(overlayScrollbarsParams);

  // 初始化滚动条
  useEffect(() => {
    if (divRef.current) {
      initialize(divRef.current);
    }
  }, [initialize, divRef]);

  // 手机端下载曝光埋点
  useEffect(() => {
    if (isMobile() && isMobileSize) {
      trackEvent('whee_phone_download_page_expo');
    }
  }, [isMobileSize]);
}
