import classNames from 'classnames';
import lightBub from '@/assets/images/light-bulb.png';
import { LayerWrapper } from '../LayerWrapper';
import { Typography } from '@/components/Typography';
import artworkImg from '@/assets/images/artwork.jpg';
import { FadeType } from '../constants';
import styles from './index.module.less';

export function ArtworkSection() {
  return (
    <section className={classNames('download-section', styles.artwork)}>
      <LayerWrapper>
        <LayerWrapper.Overflow backgroundImage={artworkImg} />
        <LayerWrapper.Summarize fadeType={FadeType.right}>
          <p className={styles.artworkTitle}>百个现成模型</p>
          <p className={styles.artworkTitle}>
            <img alt="" src={lightBub} />
            <Typography.TextRainbow backgroundImage="linear-gradient(90deg, #CFF69D 0%, #5EF5D1 100%)">
              灵感
            </Typography.TextRainbow>
            信手拈来
          </p>
          <span>汇聚用户创造力，激发无限灵感</span>
        </LayerWrapper.Summarize>
      </LayerWrapper>
    </section>
  );
}

export { ArtworkSection as Component };
