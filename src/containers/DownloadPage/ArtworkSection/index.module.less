.artwork {
  &-title {
    display: flex;
    align-items: center;
    > img {
      width: 36px;
      height: 36px;
      object-fit: cover;
    }
  }
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {
  .artwork {
    &-title {
      > img {
        width: 2.5vw;
        height: 2.5vw;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .artwork {
    &-title {
      > img {
        width: 22px;
        height: 22px;
      }
    }
  }
}
