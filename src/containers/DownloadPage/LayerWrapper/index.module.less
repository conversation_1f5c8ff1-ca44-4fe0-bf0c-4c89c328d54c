@import '~@/styles/variables.less';

.wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px 0;
  > div:first-child {
    margin-right: 80px;
  }
  .summarize {
    width: 480px;
    height: 480px;
    display: flex;
    justify-content: center;
    &-wrapper {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: auto;
      > p,
      h3 {
        color: #fff;
        font-size: 42px;
        font-weight: 600;
        line-height: 60px;
        margin: 0;
        > span {
          font-size: inherit;
          font-weight: inherit;
          line-height: inherit;
        }
      }
      > span {
        font-size: 20px;
        font-weight: 400;
        margin-top: @size-xl;
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }
  .overflow {
    width: 600px;
    height: 480px;
    border-radius: 24px;
    position: relative;
    background-size: 100%;
    // 使用伪元素设置box-shadow修复视频无法显示外阴影问题
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 24px;
      box-shadow: 0 0 8px 0 rgba(255, 255, 255, 0.35) inset;
    }
    &-background {
      width: 520px;
      height: 224px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 520px;
      background: linear-gradient(180deg, #1426cb 0%, #40007f 100%);
      filter: blur(120px);
      z-index: -1;
    }
    > img,
    > video {
      width: 100%;
      height: 100%;
      border-radius: 24px;
    }
    > img {
      object-fit: cover;
      object-position: center;
    }
  }
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {
  .wrapper {
    height: auto;
    > div:first-child {
      margin-right: 5.6vw;
    }
    .summarize {
      width: 33.3vw;
      height: 33.3vw;
      &-wrapper {
        > p,
        h3 {
          font-size: 2.9vw;
          line-height: 4.1vw;
        }
        > span {
          font-size: 1.3vw;
          line-height: 2.2vw;
          margin-top: 2.2vw;
        }
      }
    }
    .overflow {
      width: 41.6vw;
      height: 33.3vw;
      &-background {
        width: 36vw !important;
        height: 15.6vw !important;
        border-radius: 36vw !important;
        filter: blur(8.3vw);
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .wrapper {
    height: auto !important;
    padding: 40px 0 !important;
    flex-direction: column;
    > div:first-child {
      margin-right: 0;
    }
    .summarize {
      order: 1;
      height: auto;
      &-wrapper {
        > p,
        h3 {
          font-size: 26px;
          line-height: normal;
        }
        > span {
          font-size: 14px;
          margin-top: 12px;
        }
      }
      margin-bottom: @size-lg;
    }
    .overflow {
      order: 2;
      width: 328px;
      height: 262px;
      &-background {
        width: 284px;
        height: 120px;
        border-radius: 284px;
        filter: blur(65px);
      }
    }
  }
}
