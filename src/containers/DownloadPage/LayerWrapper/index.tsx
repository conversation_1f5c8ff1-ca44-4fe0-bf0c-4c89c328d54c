import { PropsWithChildren, useMemo } from 'react';

import classNames from 'classnames';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { FadeType } from '../constants';
import { useIsMobileSize } from '@/hooks';
import styles from './index.module.less';

const initialMotionState = { opacity: 0, x: 0, y: 0 };
interface LayerProps extends PropsWithChildren {
  className?: string;
}
export function LayerWrapper({ className, children }: LayerProps) {
  return (
    <div className={classNames(styles.wrapper, className)}>{children}</div>
  );
}

interface SummarizeProps extends LayerProps {
  fadeUnit?: number;
  fadeType: FadeType;
  triggerOnce?: boolean;
}

const Summarize = ({
  className,
  fadeType,
  children,
  triggerOnce = true,
  fadeUnit = 158
}: SummarizeProps) => {
  const isMobileSize = useIsMobileSize();

  const [ref, inView] = useInView({
    triggerOnce // 仅触发一次
  });

  const initial = useMemo(() => {
    if (isMobileSize) {
      return { ...initialMotionState, y: 24 };
    }

    switch (fadeType) {
      case FadeType.up:
        return { ...initialMotionState, y: -fadeUnit };
      case FadeType.down:
        return { ...initialMotionState, y: fadeUnit };
      case FadeType.left:
        return { ...initialMotionState, x: -fadeUnit };
      case FadeType.right:
        return { ...initialMotionState, x: fadeUnit };
    }
  }, [fadeType, isMobileSize, fadeUnit]);

  return (
    <div className={classNames(styles.summarize, className)}>
      <motion.div
        ref={ref}
        className={styles.summarizeWrapper}
        animate={inView ? { ...initialMotionState, opacity: 1 } : initial}
        transition={{ duration: 1.8 }}
      >
        {children}
      </motion.div>
    </div>
  );
};

LayerWrapper.Summarize = Summarize;
interface OverflowProps extends LayerProps {
  backgroundImage?: string;
}
const Overflow = ({ className, backgroundImage, children }: OverflowProps) => {
  return (
    <div
      className={classNames(styles.overflow, className)}
      style={{ backgroundImage: `url(${backgroundImage})` }}
    >
      <div className={styles.overflowBackground}></div>
      {children}
    </div>
  );
};

LayerWrapper.Overflow = Overflow;

export { LayerWrapper as Component };
