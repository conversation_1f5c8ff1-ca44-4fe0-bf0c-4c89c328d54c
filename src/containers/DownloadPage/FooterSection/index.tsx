import { WheeOnlyFont, Whee<PERSON>ithBG } from '@/icons';
import { CopyrightBanner } from '@/layouts/Copyright/CopyrightBanner';
import styles from './index.module.less';

export function FooterSection() {
  return (
    <section className={styles.footer}>
      <div className={styles.content}>
        <div className={styles.describe}>
          <div className={styles.appInfo}>
            <WheeWithBG className={styles.appIcon} />
            <div className={styles.appGroup}>
              <WheeOnlyFont className={styles.appName} />
              <strong className={styles.appLabel}>
                AI视觉创作的灵感激发器
              </strong>
            </div>
          </div>
          <strong className={styles.label}>举报电话：400-990-9696</strong>
        </div>
        <CopyrightBanner className={styles.copyright} />
      </div>
    </section>
  );
}

export { FooterSection as Component };
