@import '~@/styles/variables.less';

.footer {
  padding: 40px 24px;
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .describe {
      display: flex;
      align-items: center;
      justify-content: center;
      .app-info {
        display: flex;
        align-items: center;
        margin-right: @size-xxl;
        .app-icon {
          font-size: 36px;
          margin-right: 6px;
        }
        .app-group {
          display: flex;
          flex-direction: column;
          .app-name > svg {
            width: 65px;
            height: 16px;
          }
          .app-label {
            color: #fff;
            font-size: 8px;
            font-weight: 400;
            margin-top: 5px;
            line-height: 8px;
            display: none;
          }
        }
      }
      .label {
        color: #fff;
        font-size: 18px;
        font-weight: 400;
      }
    }
    .copyright {
      margin-top: @size-lg;
      color: rgba(255, 255, 255, 0.5);
      font-size: 12px;
      font-weight: 400;
      line-height: 24px;
      a {
        color: rgba(255, 255, 255, 0.5);
        &:hover {
          color: @color-primary-text-hover;
        }
        &:active {
          color: @color-primary-active;
        }
      }
    }
  }
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {
  .footer {
    .content {
      .describe {
        .app-info {
          margin-right: 3.3vw;
          .app-icon {
            font-size: 2.5vw;
          }
          .app-group {
            .app-name > svg {
              width: 4.5vw;
              height: 1.2vw;
            }
          }
        }
        .label {
          font-size: 1.25vw;
        }
      }
      .copyright {
        margin-top: 1.67vw;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .footer {
    .content {
      margin: 0 auto;
      .describe {
        align-self: flex-start;
        .app-info {
          .app-icon {
            font-size: 44px;
            margin-right: @size-xs;
          }
          .app-group {
            .app-name > svg {
              width: 88px;
              height: 21px;
            }
            .app-label {
              display: block;
            }
          }
        }
        .label {
          display: none !important;
        }
      }
      .copyright {
        margin-top: @size;
        font-size: 10px;
        line-height: 18px;
      }
    }
  }
}
