import classNames from 'classnames';
import { MiracleVision } from '@/icons';
import miracleVisionImg from '@/assets/images/miracle-vision.jpg';
import { LayerWrapper } from '../LayerWrapper';
import { Typography } from '@/components/Typography';
import { FadeType } from '../constants';
import styles from './index.module.less';

export function MiracleVisionSection() {
  return (
    <section className={classNames('download-section', styles.miracleVision)}>
      <LayerWrapper>
        <LayerWrapper.Overflow backgroundImage={miracleVisionImg} />
        <LayerWrapper.Summarize fadeType={FadeType.right}>
          <MiracleVision className={styles.miracleVisionIcon} />
          <p className={styles.miracleVisionTitle}>
            懂
            <Typography.TextRainbow backgroundImage="linear-gradient(90deg, #FFBAF4 0%, #758BFF 100%)">
              美学
            </Typography.TextRainbow>
            的视觉大模型
          </p>
          <span>生成场景多样化，效果表现卓越</span>
        </LayerWrapper.Summarize>
      </LayerWrapper>
    </section>
  );
}

export { MiracleVisionSection as Component };
