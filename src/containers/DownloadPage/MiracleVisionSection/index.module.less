@import '~@/styles/variables.less';

.miracle-vision {
  &-icon {
    margin-top: 0 !important;
    > svg {
      width: 220px;
      height: 32px;
    }
  }
  &-title {
    margin-top: @size !important;
  }
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {
  .miracle-vision {
    &-icon > svg {
      width: 15.2vw;
      height: 2.2vw;
    }
    &-title {
      margin-top: 1.2vw;
    }
  }
}

@media screen and (max-width: 768px) {
  .miracle-vision {
    &-icon {
      margin-top: 0 !important;
      > svg {
        width: 150px;
        height: 22px;
      }
    }
    &-title {
      margin-top: @size-sm !important;
    }
  }
}
