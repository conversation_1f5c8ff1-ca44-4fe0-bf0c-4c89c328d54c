@import '~@/styles/variables.less';

.ai-edit {
  &-title {
    display: flex;
    justify-content: center;
    align-items: center;
    > span {
      margin: 0 @size;
      font-size: 16px;
    }
  }
  &-sub-title {
    width: 318px;
    display: inline-block;
    text-align: center;
  }
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {
  .ai-edit {
    &-title {
      > span {
        margin: 0 1.2vw;
        font-size: 1.2vw;
      }
    }
    &-sub-title {
      width: 22vw;
    }
  }
}

@media screen and (max-width: 768px) {
  .ai-edit {
    &-title {
      > span {
        margin: 0 @size-xs;
        font-size: 10px;
      }
    }
    &-sub-title {
      width: 223px;
    }
  }
}
