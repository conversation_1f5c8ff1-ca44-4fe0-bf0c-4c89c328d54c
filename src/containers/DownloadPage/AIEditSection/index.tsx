import classNames from 'classnames';
import { Star } from '@/icons';
import { LayerWrapper } from '../LayerWrapper';
import aiEditImg from '@/assets/images/ai-edit.jpg';
import { FadeType } from '../constants';
import { Typography } from '@/components/Typography';
import styles from './index.module.less';

export function AIEditSection() {
  return (
    <section className={classNames('download-section', styles.aiEdit)}>
      <LayerWrapper>
        <LayerWrapper.Summarize fadeType={FadeType.left}>
          <div className={styles.aiEditTitle}>
            <h3>
              <Typography.TextRainbow backgroundImage="linear-gradient(90deg, #2ED4DE 0%, #407CF0 100%)">
                深化
              </Typography.TextRainbow>
              创作
            </h3>
            <Star />
            <h3>精益求精</h3>
          </div>
          <span className={styles.aiEditSubTitle}>
            支持AI修图功能，可持续精细打磨， 只为创造出完美之作
          </span>
        </LayerWrapper.Summarize>
        <LayerWrapper.Overflow backgroundImage={aiEditImg} />
      </LayerWrapper>
    </section>
  );
}

export { AIEditSection as Component };
