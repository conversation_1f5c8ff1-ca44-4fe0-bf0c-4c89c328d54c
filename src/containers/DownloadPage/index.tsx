import { useRef } from 'react';
import classNames from 'classnames';
import { Layout } from '@/layouts';
import { Navigation } from '@/layouts/Header/Navigation';
import { IntroSection } from './IntroSection';
import { AIDrawSection } from './AIDrawSection';
import { MiracleVisionSection } from './MiracleVisionSection';
import { AIEditSection } from './AIEditSection';
import { ArtworkSection } from './ArtworkSection';
import { FeatureSection } from './FeatureSection';
import { FooterSection } from './FooterSection';
import { useIsTop, useDownloadPageEffect } from './hooks';
import './index.less';

export function DownloadPage() {
  const isTop = useIsTop();

  const divRef = useRef<HTMLDivElement>(null);

  useDownloadPageEffect(divRef);

  return (
    <div ref={divRef}>
      <Layout
        theme="overview"
        header={
          <Layout.Header
            className={classNames('download-header', { blurred: !isTop })}
          >
            <Navigation />
          </Layout.Header>
        }
        className="download-wrapper"
      >
        {/** 第一屏 引导页 */}
        <IntroSection />
        {/** 第二屏 文生图灵感闪现 */}
        <AIDrawSection />
        {/** 第三屏 懂美学大模型 */}
        <MiracleVisionSection />
        {/** 第四屏 深化耕作，精益求精 */}
        <AIEditSection />
        {/** 第五屏 灵感源源而来 */}
        <ArtworkSection />
        {/** 第六屏 创意玩法，持续更新 */}
        <FeatureSection />
        {/** 页脚 */}
        <FooterSection />
      </Layout>
    </div>
  );
}

export { DownloadPage as Component };
