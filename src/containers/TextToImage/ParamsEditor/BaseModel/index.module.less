@import '~@/styles/variables.less';

.recommend-params:global(.@{ant-prefix}-checkbox-wrapper) {
  height: 20px;
  line-height: 20px;
  font-weight: 400;
  margin-top: 8px;

  :global(.@{ant-prefix}-checkbox) + span {
    padding: 0 4px;
  }
}

.model-card {
  :global(.model-card-extra) {
    background-color: transparent;

    .extra-icon {
      position: absolute;
      top: @size;
      right: @size-sm;
    }
  }
}
.pop-model-card-list-popover {
  :global(.ant-popover-inner) {
    padding: 0 !important;
  }
}
.pop-model-card-list {
  display: flex;
  // width: 280px;
  height: 244px;
  overflow-y: auto;
  padding-bottom: var(--spacing-12, 12px);
  flex-direction: column;
  padding: 12px 16px;
  border-radius: 12px;
  // align-items: center;
  background: @background-system-frame-floatpanel;
  // box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08),
  //   0px 24px 128px 0px rgba(0, 0, 0, 0.16);
  &-title {
    color: var(--system-content-secondary, #293545);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    margin-bottom: 10px;
  }
  &-item {
    display: flex;
    padding: var(--spacing-6, 6px) var(--spacing-8, 8px) var(--spacing-6, 6px)
      var(--spacing-6, 6px);
    align-items: center;
    gap: var(--spacing-8, 8px);
    align-self: stretch;
    border-radius: var(--radius-12, 12px);
    border: 1px solid var(--system-stroke-input, #e2e8f0);
    background: #fff;
    height: 52px;
    margin-bottom: 8px;
    cursor: pointer;
    :global(.@{ant-prefix}-image) {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      border: 1px solid @stroke-system-border-overlay;
      object-fit: cover;
      overflow: hidden;
      img {
        height: 40px;
        vertical-align: middle;
      }
    }
    &-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      gap: 4px;
      .title {
        color: var(--system-content-secondary, #293545);
        /* text_14_medium */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
      }
      .desc {
        overflow: hidden;
        color: var(--system-content-thirdary, #6a7b94);
        text-overflow: ellipsis;
        white-space: nowrap;
        /* text_12 */
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
      }
    }
  }
  .active {
    border: 1px solid var(--system-content-brandPrimary, #3549ff);
  }
}

.pop-box {
  position: absolute;
  top: 428px;
  left: 336px;
  width: 282px;
  height: 452px;
  border-radius: 12px;
  background: @background-system-frame-floatpanel;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08),
    0px 24px 128px 0px rgba(0, 0, 0, 0.16);
  z-index: 9;
  padding: 10px 16px 10px;

  .tabs {
    height: 100%;

    :global(.@{ant-prefix}-tabs-nav) {
      :global(.@{ant-prefix}-tabs-nav-list) {
        // 对于gap生效的浏览器 需要用上这个属性覆盖掉已经有的生效的gap
        gap: 0;

        // 接下来不论gap是否在浏览器中生效 gap都为0
        // 使用margin-left代替gap
        & > :global(.@{ant-prefix}-tabs-tab) {
          &:not(:first-child) {
            margin-left: 16px;
          }
        }
      }
    }

    :global(.@{ant-prefix}-tabs-content) {
      height: 100%;

      :global(.@{ant-prefix}-tabs-tabpane) {
        height: 100%;
      }
    }
  }

  .list-box {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .item-box {
      display: flex;
      width: 100%;
      height: 80px;
      border-radius: 8px;
      border: 1px solid @stroke-btn-secondary;
      background: @background-input;
      margin-bottom: 8px;
      padding: 4px 8px 4px 4px;
      cursor: pointer;

      .item-image {
        width: 72px;
        height: 72px;
        border-radius: 4px;
        border: 1px solid @stroke-system-border-overlay;
        background: @background-system-space-holder;
        object-fit: cover;
        margin-right: 12px;
      }

      .content-box {
        display: flex;
        flex-direction: column;
        justify-content: center;

        .name-box {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .name {
            color: @content-btn-secondary;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: 20px;
            width: 115px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .icon {
            width: 36px;
            height: 18px;
          }
        }

        .desc {
          margin-top: 4px;
          color: @content-system-tertiary;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
}
