/**
 * 模型选择弹窗 新版版本UI 2025-07-04
 */

import { ModelCard, Loading } from '@/components';
import { Checkbox, Empty, Form, Image, Typography, Popover } from 'antd';
import {
  useBaseModel,
  useBaseModelResponse,
  useIsAdvanced
} from '../useBaseModel';
import {
  startTransition,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import { useGetAllModel } from '@/hooks';
import { useRecommendParams } from '@/hooks/useGetEditorConfig/useRecommendParams';
import { TextToImageParams } from '../types';
import styles from './index.module.less';
import { initialEditorParams } from '@/containers/ImageToImage/ParamsEditor/utils';
import { TextToImageContext } from '../../context';
import { ChevronRightBlack } from '@meitu/candy-icons';
import { createPortal } from 'react-dom';
import { toAtlasImageView2URL } from '@meitu/util';
import empty from '@/assets/images/empty.jpg';
import classNames from 'classnames';

export const BaseModel = () => {
  const baseModel = useBaseModel();

  const baseModelResponse = useBaseModelResponse();
  const form = Form.useFormInstance<TextToImageParams>();

  const { baseModelMap } = useGetAllModel();
  const isAdvanced = useIsAdvanced();
  const baseModelId = Form.useWatch('baseModelId');

  const { setModelMvType, setSizeRatio, setModelDefaultParams } =
    useContext(TextToImageContext);

  const [showPop, setShowPop] = useState(false);

  const handleCardClick = () => {
    setShowPop(true);
  };

  const PopModelCardList = () => {
    const baseModel = useBaseModel();
    return (
      <div className={styles.popModelCardList}>
        <div className={styles.popModelCardListTitle}>选择模型</div>
        <div>
          {baseModel.map((item) => (
            <div
              key={item.id}
              className={classNames(
                styles.popModelCardListItem,
                baseModelId === item.id && styles.active
              )}
              onClick={() => {
                form.setFieldValue('baseModelId', item.id);
                setModelMvType(item.mvType);
                setSizeRatio(item?.baseModelDefaultParams?.sizeRatio || []);
                // 切换模型设置提示词强度
                form.setFieldValue(
                  'promptWeight',
                  item.baseModelDefaultParams.cfgScale
                );
                setModelDefaultParams(item?.baseModelDefaultParams);
                setShowPop(false);
              }}
            >
              <Image
                src={item.src}
                className={styles.popModelCardListItemImage}
                preview={false}
                placeholder={<Loading />}
              />

              <div className={styles.popModelCardListItemContent}>
                <div className={styles.title}>{item.title}</div>
                <div className={styles.desc}>{item.desc}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };
  return (
    <div>
      <Popover
        content={<PopModelCardList />}
        trigger="click"
        placement="right"
        arrow={false}
        open={showPop}
        onOpenChange={setShowPop}
        rootClassName={styles.popModelCardListPopover}
      >
        <Form.Item noStyle name="baseModelId">
          <ModelCard
            className={styles.modelCard}
            list={baseModel}
            onClick={handleCardClick}
            onExtraClick={handleCardClick}
            showCornerLabelUrl={false}
            extra={
              <div className={styles.extraIcon}>
                <ChevronRightBlack />
              </div>
            }
          />
        </Form.Item>
      </Popover>
      {isAdvanced && (
        <Form.Item noStyle valuePropName="checked" name="recommendParams">
          <Checkbox className={styles.recommendParams}>使用推荐参数</Checkbox>
        </Form.Item>
      )}
    </div>
  );
};
