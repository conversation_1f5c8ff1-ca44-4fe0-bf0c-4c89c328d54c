/**
 * 模型选择弹窗 新版版本UI 2025-07-04
 */

import { ModelCard, Loading } from '@/components';
import { Checkbox, Empty, Form, Image, Typography, Popover } from 'antd';
import {
  useBaseModel,
  useBaseModelResponse,
  useIsAdvanced
} from '../useBaseModel';
import {
  startTransition,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import { useGetAllModel } from '@/hooks';
import { useRecommendParams } from '@/hooks/useGetEditorConfig/useRecommendParams';
import { TextToImageParams } from '../types';
import styles from './index.module.less';
import { initialEditorParams } from '@/containers/ImageToImage/ParamsEditor/utils';
import { tabs as controlNetTabs } from '@/components/ControlNetSection';
import { TextToImageContext } from '../../context';
import { ChevronRightBlack } from '@meitu/candy-icons';
import { createPortal } from 'react-dom';
import { toAtlasImageView2URL } from '@meitu/util';
import empty from '@/assets/images/empty.jpg';
import classNames from 'classnames';
import { usePrevious } from 'react-use';
import { ModelMvType } from '@/constants/model';
import { StyleModelContext } from '@/components/StyleModel/Provider';
import { useStyleModelList } from '@/components/StyleModel';
import { ControlNetUtils } from '@/utils/controlNetUtils';

export const BaseModel = () => {
  const baseModel = useBaseModel();

  const baseModelResponse = useBaseModelResponse();
  const form = Form.useFormInstance<TextToImageParams>();

  const { baseModelMap } = useGetAllModel();
  const isAdvanced = useIsAdvanced();
  const baseModelId = Form.useWatch('baseModelId');
  const prevBaseModelId = usePrevious(baseModelId);
  const styleModelContext = useContext(StyleModelContext);

  const { initStyleModelList } = styleModelContext;
  const { fetchStyleModels } = useStyleModelList();

  useRecommendParams(baseModelMap, initialEditorParams);
  const {
    setModelMvType,
    setSizeRatio,
    setModelDefaultParams,
    reeditFlag,
    modelMvType
  } = useContext(TextToImageContext);

  const [showPop, setShowPop] = useState(false);

  const handleCardClick = () => {
    setShowPop(true);
  };

  const handleBaseModelChange = () => {
    const prevModel = baseModel?.find((baseModel) => {
      return baseModel.id === prevBaseModelId;
    });

    const nextModel = baseModel?.find((baseModel) => {
      return baseModel.id === baseModelId;
    });

    setModelMvType(nextModel?.mvType ?? 0);
    if (reeditFlag.current) {
      return;
    }

    if (nextModel?.mvType === ModelMvType.SpecialBeta) {
      form.setFieldValue(['styleModel'], []);
      try {
        // 直接调用 initStyleModelList，确保它被执行
        initStyleModelList(() => {
          // 在回调中获取风格模型
          fetchStyleModels({
            baseModelType: 2,
            reset: true // 确保重置数据
          });
        });
      } catch (error) {}
    } else {
      form.setFieldValue(['styleModel'], []);
    }

    // 当基础模型的类型发生变化时 清空controlnet配置
    if (prevModel?.mvType !== nextModel?.mvType) {
      controlNetTabs.forEach((_, index) => {
        const namePath = ControlNetUtils.getImageProcessParamsPath(index);
        form.setFieldValue(namePath, {});
      });
    }
  };
  useEffect(() => {
    handleBaseModelChange();
    // baseModelId 变化时，重新取到baseModel
    const _baseModel = baseModel.find((item) => item.id === baseModelId);
    setSizeRatio(_baseModel?.baseModelDefaultParams?.sizeRatio || []);
    // 切换模型设置提示词强度
    form.setFieldValue(
      'promptWeight',
      _baseModel?.baseModelDefaultParams?.cfgScale
    );
    setModelDefaultParams(_baseModel?.baseModelDefaultParams);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [baseModelId]);

  // console.log('baseModelId', baseModel);

  const PopModelCardList = () => {
    const baseModel = useBaseModel();
    const activeItemRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    // 检查元素是否在可视区域内
    const isElementInViewport = (
      element: HTMLElement,
      container: HTMLElement
    ) => {
      const elementRect = element.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();

      // 检查元素是否完全在容器可视区域内
      return (
        elementRect.top >= containerRect.top &&
        elementRect.bottom <= containerRect.bottom &&
        elementRect.left >= containerRect.left &&
        elementRect.right <= containerRect.right
      );
    };

    // 当弹窗打开时，滚动到 active 的选中项
    useEffect(() => {
      if (showPop && activeItemRef.current && containerRef.current) {
        // 添加小延迟确保 DOM 完全渲染
        const timer = setTimeout(() => {
          if (activeItemRef.current && containerRef.current) {
            // 检查元素是否已经在可视区域内
            if (
              !isElementInViewport(activeItemRef.current, containerRef.current)
            ) {
              activeItemRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'nearest'
              });
            }
          }
        }, 150);

        return () => clearTimeout(timer);
      }
    }, [showPop]);

    return (
      <>
        <div className={styles.popModelCardListTitle}>选择模型</div>
        <div ref={containerRef} className={styles.popModelCardList}>
          <div
            style={
              {
                //  marginTop: 20
              }
            }
          >
            {baseModel.map((item) => (
              <div
                key={item.id}
                ref={baseModelId === item.id ? activeItemRef : null}
                className={classNames(
                  styles.popModelCardListItem,
                  baseModelId === item.id && styles.active
                )}
                onClick={() => {
                  form.setFieldValue('baseModelId', item.id);
                  setModelMvType(item.mvType);
                  setSizeRatio(item?.baseModelDefaultParams?.sizeRatio || []);
                  // 切换模型设置提示词强度
                  form.setFieldValue(
                    'promptWeight',
                    item.baseModelDefaultParams.cfgScale
                  );
                  setModelDefaultParams(item?.baseModelDefaultParams);
                  setShowPop(false);
                }}
              >
                <Image
                  src={item.src}
                  className={styles.popModelCardListItemImage}
                  preview={false}
                  placeholder={<Loading />}
                />

                <div className={styles.popModelCardListItemContent}>
                  <div className={styles.title}>
                    {item.title}{' '}
                    <div className={styles.tagBox}>
                      {item?.tags?.url && (
                        <Image
                          className={styles.tagIcon}
                          placeholder={<Loading />}
                          preview={false}
                          alt={item.title}
                          src={toAtlasImageView2URL(item?.tags.url, {
                            mode: 2,
                            width: 50
                          })}
                        />
                      )}
                    </div>
                  </div>
                  <div className={styles.desc}>{item.desc}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </>
    );
  };
  return (
    <div>
      <Popover
        content={<PopModelCardList />}
        trigger="click"
        placement="right"
        arrow={false}
        open={showPop}
        onOpenChange={setShowPop}
        rootClassName={styles.popModelCardListPopover}
      >
        <Form.Item noStyle name="baseModelId">
          <ModelCard
            className={styles.modelCard}
            list={baseModel}
            onClick={handleCardClick}
            onExtraClick={handleCardClick}
            showCornerLabelUrl={false}
            extra={
              <div className={styles.extraIcon}>
                <ChevronRightBlack />
              </div>
            }
          />
        </Form.Item>
      </Popover>
      {isAdvanced && (
        <Form.Item noStyle valuePropName="checked" name="recommendParams">
          <Checkbox className={styles.recommendParams}>使用推荐参数</Checkbox>
        </Form.Item>
      )}
    </div>
  );
};
