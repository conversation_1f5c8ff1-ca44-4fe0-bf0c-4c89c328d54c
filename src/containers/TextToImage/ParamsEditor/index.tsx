import type { TextToImageParams } from './types';
import type { OnFormFinish } from '@/types';

import {
  PermissionComponentBaseModel,
  PermissionComponentStyleModel,
  PermissionComponentControlNet
} from '@/constants/permission';
import {
  ControlNetSection,
  ControlNetSectionHandle
} from './ControlNetSection';
import { PromptSection } from './PromptSection';
import { ParamsSection } from './ParamsSection';
import { StyleModel } from './StyleModel';
import {
  DEEPSEEK_PROMPT_OPTIMIZATION_KEY_FIRST_CLICK,
  EditorMode
} from '@/constants';
import { BaseModel } from './BaseModel/popIndex2';
import { Form, Image } from 'antd';

import {
  EditorConfigProvider,
  Permission,
  Collapse,
  Authorized
} from '@/components';

import { useEditorImageState } from '@/hooks/useEditorImageState';
import { getSearchParams, trackTextToImageEvent } from '@/utils/draftTracking';
import { scroll2Error1st } from '@/utils/formUtils';
import { toEditorQueryParams } from './utils';
import { useInitForm } from './useInitForm';
import {
  EditorConfigState,
  useEditorMode,
  useGetEditorConfig,
  useSearchParams,
  useTracerParams
} from '@/hooks';
import { MeiDouButton, MeiDouPrice } from '@/components/MeiDouPrice';
import { FunctionCode } from '@/api/types/meidou';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { fixturesMeidouBalanceTracer } from '@/utils/fixturesMeidouBalanceTracer';
import { useOnRecommendParamsChange } from '@/hooks/useGetEditorConfig/useRecommendParams';
import { getSource } from '@/utils';
import { getControlNetTrackParams } from '@/utils/controlNetUtils';
import {
  getLocalStorageItem,
  setLocalStorageItem,
  toAtlasImageView2URL,
  toCamelCase
} from '@meitu/util';
import { forwardRef, useImperativeHandle, useRef } from 'react';
import { useTextToImageContext } from '../Provider';
import styles from './index.module.less';
import { useRecoilValue } from 'recoil';
import { ModelMvType } from '@/constants/model';
import { StyleModelProvider } from '@/components/StyleModel/Provider';
import { DraftType } from '@/api/types';
import { useBaseModel } from './useBaseModel';
import { InfoCircle } from '@meitu/candy-icons';

export interface ParamsEditorHandle {
  activateControlNetTab(index: number): void;
}

export const ParamsEditor = forwardRef<ParamsEditorHandle>((props, ref) => {
  const {
    setUserSubmitValue,
    meidouPriceRef,
    modelMvType,
    deepSeekPrompt,
    isModifyContent
  } = useTextToImageContext();
  const [editorMode] = useEditorMode();
  const isAdvancedMode = editorMode === EditorMode.Advanced;
  const { id } = useSearchParams();
  const { tracerParams } = useTracerParams();

  const config = useRecoilValue(EditorConfigState);

  /** -----------------会员相关hook和方法----------------------- */
  const { updateMeiDouBalance, availableAmount } = useMeiDouBalance({
    meidouPriceRef
  }); /** -----------------会员相关hook和方法----------------------- */
  const { loading, createImageTask } = useEditorImageState(updateMeiDouBalance);

  const form = useInitForm({
    onInitialized: () => {}
  });

  const onFinish: OnFormFinish<TextToImageParams> = async (values) => {
    // 产品临时决定，在梳理下需求，这个逻辑可能会变，先行注释
    // if (userSubmitValue) {
    //   values.userPrompt = userSubmitValue;
    // } else {
    //   values.userPrompt = values.prompt;
    // }
    const editorQueryParams = toEditorQueryParams(values);

    // TODO: 时间比较赶，临时暴力处理 创作模式后面要放在表单收集里头，因为参数回填应该也要回填回去 会约定自己的字段格式再转成后端的格式
    let simplifyVersion = editorMode === EditorMode.Advanced ? 0 : 1;
    await createImageTask({
      params: editorQueryParams,
      simplifyVersion,
      effectId: id ? +id : undefined
    });
    if (setUserSubmitValue) {
      setUserSubmitValue('');
    }
  };

  const onRecommendParamsChange = useOnRecommendParamsChange(form);

  const { editorConfig } = useGetEditorConfig();

  const controlnetSectionRef = useRef<ControlNetSectionHandle>(null);
  function activateControlNetTab(index: number) {
    controlnetSectionRef.current?.activateTab(index);
  }

  useImperativeHandle(ref, () => {
    return {
      activateControlNetTab
    };
  });

  const baseModelId = Form.useWatch('baseModelId');
  const baseModel = useBaseModel();
  const baseModelItem = baseModel?.find((item) => item.id === baseModelId);

  return (
    <EditorConfigProvider>
      <StyleModelProvider from={DraftType.TEXT_TO_IMAGE}>
        <Form
          form={form}
          onFinish={onFinish}
          onFinishFailed={(err) => {
            scroll2Error1st(form, err);
          }}
          onValuesChange={onRecommendParamsChange}
        >
          <Collapse
            className="collapse"
            defaultActiveKey={[
              'prompt',
              'model',
              'styles',
              'generate-params',
              'image',
              'control-net'
            ]}
          >
            <Collapse.Panel
              key="prompt"
              header={null}
              collapsible="icon"
              showArrow={false}
            >
              <PromptSection />
            </Collapse.Panel>

            <Permission permissions={[PermissionComponentBaseModel]}>
              <Collapse.Panel
                key="model"
                header={null}
                collapsible="icon"
                showArrow={false}
                forceRender
              >
                <Collapse.Panel.Section
                  title={
                    <div
                      style={{
                        // 禁止选中
                        userSelect: 'none'
                      }}
                    >
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between'
                        }}
                      >
                        <div>
                          <strong>模型</strong>
                          {isAdvancedMode && config?.baseTagUrl && (
                            <Image
                              className={styles.icon}
                              preview={false}
                              src={toAtlasImageView2URL(
                                config?.baseTagUrl ?? '',
                                {
                                  mode: 2,
                                  width: 50
                                }
                              )}
                            ></Image>
                          )}
                          {!isAdvancedMode && config?.recommendTagUrl && (
                            <Image
                              className={styles.icon}
                              preview={false}
                              src={toAtlasImageView2URL(
                                config?.recommendTagUrl ?? '',
                                {
                                  mode: 2,
                                  width: 50
                                }
                              )}
                            ></Image>
                          )}
                        </div>
                        <div>
                          {baseModelItem?.tips && (
                            <div className={styles.tips}>
                              <InfoCircle /> {baseModelItem?.tips}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  }
                >
                  <BaseModel />
                </Collapse.Panel.Section>
              </Collapse.Panel>
            </Permission>

            <Permission permissions={[PermissionComponentStyleModel]}>
              <Collapse.Panel key="styles" header="风格模型" forceRender>
                {modelMvType === ModelMvType.Special ||
                modelMvType === ModelMvType.External ? (
                  <div className={styles.notSupport}>
                    当前生图模型不支持添加风格
                  </div>
                ) : (
                  <StyleModel />
                )}
              </Collapse.Panel>
            </Permission>

            <Permission permissions={[PermissionComponentControlNet]}>
              <Collapse.Panel key="control-net" header="画面参考" forceRender>
                {modelMvType === ModelMvType.Special ||
                modelMvType === ModelMvType.External ? (
                  <div className={styles.notSupport}>
                    当前生图模型不支持参考图
                  </div>
                ) : (
                  <ControlNetSection ref={controlnetSectionRef} />
                )}
              </Collapse.Panel>
            </Permission>

            <Collapse.Panel key="generate-params" header="参数设定" forceRender>
              <ParamsSection />
            </Collapse.Panel>
          </Collapse>

          <MeiDouPrice
            ref={meidouPriceRef}
            functionCode={FunctionCode.text2img}
            // 外接模型需求， 在baseModelId变化时， 需要重新计算价格
            fields={['quantity', 'batches', 'baseModelId']}
            getFunctionBody={() => {
              return {
                params: toEditorQueryParams(form.getFieldsValue())
              };
            }}
          >
            {(price, fetchLoading) => (
              <MeiDouButton
                hasBorder={isAdvancedMode}
                price={price}
                block
                htmlType="submit"
                loading={loading}
                fetchPriceLoading={fetchLoading}
                onClick={() => {
                  const editorComponentParams = form.getFieldsValue();
                  const editorQueryParams = toEditorQueryParams(
                    editorComponentParams
                  );
                  const localPrompt = getLocalStorageItem(
                    DEEPSEEK_PROMPT_OPTIMIZATION_KEY_FIRST_CLICK
                  );

                  if (!price) return;
                  /** 立即生成按钮点击 */
                  trackTextToImageEvent('create_btn_click', {
                    ...editorQueryParams,
                    ...getSearchParams(),
                    isVip: Number(price?.isVip),
                    beautyCoinBalanceSufficient: fixturesMeidouBalanceTracer(
                      price.amount,
                      availableAmount ?? 0
                    ),
                    freeBatchSize: price?.useFreeNum,
                    function: getSource(),
                    ...tracerParams,
                    ...getControlNetTrackParams(
                      toCamelCase(
                        toCamelCase(editorQueryParams).controlnetUnits
                      ),
                      editorConfig?.moduleList
                    ),
                    isModifyContent: isModifyContent ? '1' : 0,
                    createType: isAdvancedMode
                      ? EditorMode.Advanced
                      : EditorMode.Recommend,
                    ...deepSeekPrompt,
                    prompt: localPrompt || editorQueryParams.prompt,
                    modifyContent: isModifyContent
                      ? editorQueryParams.prompt
                      : ''
                  });
                }}
              />
            )}
          </MeiDouPrice>
        </Form>
      </StyleModelProvider>
    </EditorConfigProvider>
  );
});
