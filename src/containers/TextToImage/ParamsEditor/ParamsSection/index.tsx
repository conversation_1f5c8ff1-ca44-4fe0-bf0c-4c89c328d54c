import {
  Collapse,
  SliderInput,
  SizeSelector2,
  SizeSelector3,
  SeedInput,
  Permission,
  FacialRepair
  // PresetSizeMode
} from '@/components';

import { Form } from 'antd';
import { SamplerType } from './SamplerType';

import { RANDOM_SEED_VALUE } from '@/constants';
import {
  PermissionComponentPromptWeight,
  PermissionComponentSamplerType,
  PermissionComponentSamplerStep,
  PermissionComponentSeed,
  PermissionComponentExtraFunctions
} from '@/constants/permission';
import './index.less';
import { useTextToImageContext } from '../../Provider';
import { maxFluxPromptWeight, ModelMvType } from '@/constants/model';
import { useBaseModel } from '../useBaseModel';
import { TextToImageParams } from '../types';
export function ParamsSection() {
  const { modelMvType, sizeRatio, modelDefaultParams } =
    useTextToImageContext();
  // 提示词强度最大值
  // const maxPromptWeight =
  //   modelMvType === ModelMvType.SpecialBeta ? maxFluxPromptWeight : 20;

  const baseModelId = Form.useWatch('baseModelId');
  const baseModel = useBaseModel();
  const baseModelItem = baseModel?.find((item) => item.id === baseModelId);

  // console.log('baseModelItem', baseModelItem);

  const form = Form.useFormInstance<TextToImageParams>();

  //
  const disableQuantity = baseModelItem?.modelFlag === 5;

  if (disableQuantity) {
    // 设置form 的quantity 的默认值为4
    form.setFieldValue('quantity', 4);
  }

  return (
    <div className="no-select">
      <Collapse.Panel.Section>
        <Form.Item name="canvas" noStyle>
          {/* <SizeSelector presetSizeMode={PresetSizeMode.AUTO_MAX_PRESET_SIZE} /> */}
          <SizeSelector3 type="text2img" sizeRatio={sizeRatio} />
        </Form.Item>
      </Collapse.Panel.Section>

      <Permission permissions={[PermissionComponentPromptWeight]}>
        <Collapse.Panel.Section>
          {/*  改动， 步骤 和 最大值 需要根据modelDefaultParams中的cfgScaleStep 和 maxCfgScale 来设置 */}
          <Form.Item name="promptWeight" noStyle>
            <SliderInput
              markNum={modelDefaultParams.cfgScale || 5}
              min={modelDefaultParams?.minCfgScale}
              max={modelDefaultParams.maxCfgScale || 20}
              step={modelDefaultParams.cfgScaleStep || 0.5}
              title="提示词强度"
              tooltip="值越高，生成的结果就越接近提示词内容，但较高的值容易降低生成质量，建议使用推荐默认值。"
            />
          </Form.Item>
        </Collapse.Panel.Section>
      </Permission>

      <Collapse.Panel.Section>
        <Form.Item name="quantity" noStyle>
          <SliderInput
            title="生成张数"
            tooltip="生成张数越多，耗时越久。"
            min={1}
            max={4}
            disabled={disableQuantity}
          />
        </Form.Item>
      </Collapse.Panel.Section>
      {/* // 如果是外接模型， 不展示 */}
      {modelMvType === ModelMvType.External ? null : (
        <Permission permissions={[PermissionComponentSamplerType]}>
          <SamplerType />
        </Permission>
      )}

      {/* 如果是外接模型， 不展示 */}
      {modelMvType === ModelMvType.External ? null : (
        <Permission permissions={[PermissionComponentSamplerStep]}>
          <Collapse.Panel.Section>
            <Form.Item name={['sampler', 'steps']} noStyle>
              <SliderInput
                title="采样步骤"
                tooltip="生成图片所需的步骤，步骤越多耗时越久，通常25个步骤足以获得高质量的图片。"
                min={1}
                max={50}
                step={1}
              />
            </Form.Item>
          </Collapse.Panel.Section>
        </Permission>
      )}

      {/* 如果是外接模型， 不展示 */}
      {modelMvType === ModelMvType.External ? null : (
        <Permission permissions={[PermissionComponentSeed]}>
          <Collapse.Panel.Section>
            <Form.Item name="seed">
              <SeedInput />
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.seed !== currentValues.seed
              }
            >
              {({ getFieldValue }) => {
                const seed = getFieldValue('seed');

                return seed === RANDOM_SEED_VALUE ? (
                  <Form.Item name="batches">
                    <SliderInput
                      title="生成批次"
                      tooltip="随机多个Seed生成多批次图片"
                      min={1}
                      max={4}
                      step={1}
                    />
                  </Form.Item>
                ) : null;
              }}
            </Form.Item>
            {modelMvType === ModelMvType.Special ||
            modelMvType == ModelMvType.SpecialBeta ? null : (
              <Form.Item name="moreDetailWeight">
                <SliderInput
                  title="画面细节"
                  tooltip=" 正向可丰富画面线条细节，负向可使内容更具扁平化。"
                  min={-2}
                  max={2}
                  step={0.1}
                  origin={0}
                />
              </Form.Item>
            )}
          </Collapse.Panel.Section>{' '}
        </Permission>
      )}
      {modelMvType == ModelMvType.SpecialBeta ||
      modelMvType == ModelMvType.External ? null : (
        <Permission permissions={[PermissionComponentExtraFunctions]}>
          <FacialRepair />
        </Permission>
      )}

      {/* <Button type="primary" block>
        导入配置
      </Button> */}
    </div>
  );
}
