import { EditorConfigState } from '@/hooks';
import { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useRecoilValue } from 'recoil';
import { initialEditorParams, toEditorComponentParams } from './utils';
import { TextToImageContext } from '../context';
import { TextToImageParams } from './types';
import produce from 'immer';
import { useCachedTextToImgFields } from '@/hooks/useCachedPageFields';
import { useSearchParams } from '@/hooks';
import { isExistInBaseModelConfig } from '@/utils/isModelConfigExists';
import { EditorMode } from '@/constants';
import { useGetBaseModelResponse } from './useBaseModel';
import { fetchSameStyleGallery } from '@/api';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { toEditorComponentDefaultParams } from '@/utils/editor';
import { getStyleType } from '@/api/model';

import _ from 'lodash';
import { useEditorConfigStore } from '@/hooks/useEditorConfigStore';

export const useInitForm = ({
  onInitialized
}: {
  onInitialized: (params: TextToImageParams) => void;
}) => {
  const [initialized, setInitialized] = useState(false);
  const loading = useRef(false);

  const {
    form,
    setTempInitFormValues,
    setEditorMode,
    reeditFlag,
    setSizeRatio,
    setModelDefaultParams
  } = useContext(TextToImageContext);
  const editorConfig = useRecoilValue(EditorConfigState);
  const editorConfigStore = useEditorConfigStore();
  const {
    baseModelId,
    id,
    editorMode: editorModeFromParams,
    prompt,
    styleModelId
  } = useSearchParams();

  const getBaseModels = useGetBaseModelResponse();
  const {
    cachedFields: { paramsEditor },
    applyFlushParams
  } = useCachedTextToImgFields();

  const initializeForm = useCallback(async () => {
    let editorMode = editorModeFromParams ?? EditorMode.Recommend;
    const baseModels = getBaseModels(editorMode);

    if (initialized || !editorConfig || !baseModels || loading.current) {
      return;
    }

    loading.current = true;

    let initBaseModel = baseModels?.[0]?.list[0];
    // console.log(baseModels, 'baseModels =>>>>>>>');
    // 硬编码 flux 模型的处理
    // 处理请求
    if (styleModelId) {
      // 查询 baseModel 列表中的list列表是否存在 mvType === 3 的模型
      const res = await getStyleType(styleModelId);
      if (res.isFlux) {
        const fluxCategory = baseModels.find((model) =>
          model.list.some((item) => item.mvType === 3)
        );

        if (fluxCategory) {
          // 从分类中找到第一个 mvType === 3 的模型
          const fluxModelItem = fluxCategory.list.find(
            (item) => item.mvType === 3
          );
          if (fluxModelItem) {
            initBaseModel = fluxModelItem;
          }
        }
      } else {
        // 找到 baseModel 列表中的第一个mvType !==3 的模型
        initBaseModel =
          baseModels?.[0]?.list.find((item) => item.mvType !== 3) ||
          baseModels?.[0]?.list[0];
      }
    }

    // console.log(initBaseModel, 'initBaseModel =>>>>>>>');

    const initRecommendParams = toEditorComponentDefaultParams(
      baseModels?.[0]?.list[0]?.defaultParams
    );

    let fixturesConfig = produce(
      _.merge(
        {},
        initialEditorParams,
        initRecommendParams,
        paramsEditor ?? {},
        applyFlushParams() ?? {}
      ),
      (draft) => {
        draft.baseModelId = initBaseModel?.id;
        if (!draft.sampler.type) {
          // 优先取基础模型绑定的采样器，如果没有值再默认取的第一个采样器
          draft.sampler.type = editorConfig?.samplerIndex[0].value;
        }
      }
    );

    if (!!id) {
      try {
        const { params, simplifyVersion } = await fetchSameStyleGallery(id);
        editorMode = !!simplifyVersion
          ? EditorMode.Recommend
          : EditorMode.Advanced;
        fixturesConfig = {
          // HACK 创作同款时不使用推荐参数
          ..._.omit(fixturesConfig, ['recommendParams']),
          ...(toEditorComponentParams(
            params,
            editorConfigStore.getFlatAllControlNets()
          ) ?? {})
        };
      } catch (error) {
        defaultErrorHandler(error);
      }
    }

    fixturesConfig = produce(fixturesConfig, (draft) => {
      if (
        !!baseModelId &&
        isExistInBaseModelConfig(baseModels, Number(baseModelId))
      ) {
        draft.baseModelId = Number(baseModelId);
      }

      if (prompt) {
        draft.prompt = prompt;
      }
    });
    setInitialized(true);
    setEditorMode(editorMode);
    // console.log('fixturesConfig', fixturesConfig);
    setTempInitFormValues?.(fixturesConfig as unknown as TextToImageParams);

    //  根据fixturesConfig中的baseModelId, 获取对应的item 选项 处理baseModelDefaultParams中的sizeRatio
    const baseModelItem = baseModels
      .find((item) =>
        item.list.find((item) => item.id === fixturesConfig.baseModelId)
      )
      ?.list.find((item) => item.id === fixturesConfig.baseModelId);
    setSizeRatio(baseModelItem?.defaultParams?.sizeRatio || []);

    // console.log('baseModelItem?.defaultParams=>>>>>.', baseModelItem);
    setModelDefaultParams(baseModelItem?.defaultParams || {});

    // TODO
    setTimeout(() => {
      reeditFlag.current = true;
      form.resetFields();
      form.setFieldsValue(fixturesConfig);
      onInitialized?.(fixturesConfig);
      loading.current = false;
      setTimeout(() => {
        reeditFlag.current = false;
      }, 100);
    });
  }, [
    editorModeFromParams,
    getBaseModels,
    initialized,
    editorConfig,
    paramsEditor,
    applyFlushParams,
    id,
    setEditorMode,
    setTempInitFormValues,
    baseModelId,
    prompt,
    form,
    onInitialized
  ]);

  // TODO 等模型训练成功弹窗支持跳到文生图时启用
  // useLocationSearchParamsChanged(() => {
  //   setInitialized(false);
  // });

  useEffect(() => {
    if (!initialized) {
      initializeForm();
    }
  }, [initializeForm, initialized]);

  return form;
};
function setSizeRatio(arg0: any) {
  throw new Error('Function not implemented.');
}
