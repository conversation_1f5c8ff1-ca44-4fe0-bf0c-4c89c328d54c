@import (reference) '~@/styles/variables.less';
@import (reference) '../variables';

.actions {
  position: fixed;
  z-index: 1;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 calc(-1 * @size-ms);
  width: calc(320px + @size-ms * 2);
  height: @actions-height;
  background: #ddd;

  background: linear-gradient(
    0deg,
    @background-system-main-background-flat 20%,
    transparent 88%
  );
}

.not-support {
  color: @content-system-quaternary;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  text-align: center;
}

:global(.@{ant-prefix}-image) {
  :global(.@{ant-prefix}-image-img) {
    &.icon {
      width: 36px;
      height: 18px;
      margin-top: -2px;
      margin-left: 4px;
    }
  }
}
.collapse {
  overflow-y: scroll;
}
.tips {
  color: var(--system-content-thirdary, #6a7b94);
  /* text_12 */
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}
