import { TextToImageContext } from '@/containers/TextToImage/context';
import { Segmented } from '@/components';
import { EditorMode } from '@/constants';
import { type SegmentedProps, ConfigProvider, Form } from 'antd';

import { useContext } from 'react';
import { TextToImageParams } from '../types';
import { useGetAllModel } from '@/hooks';

const editorModeOptions = [
  { label: '快捷创作', value: EditorMode.Recommend },
  { label: '高级创作', value: EditorMode.Advanced }
];

interface EditorModeSectionProps extends Omit<SegmentedProps, 'options'> {}

export function EditorModeSection(props: EditorModeSectionProps) {
  const { editorMode, setEditorMode, setSizeRatio, setModelMvType } =
    useContext(TextToImageContext);
  const form = Form.useFormInstance<TextToImageParams>();
  const { recommendBaseModel, baseModel } = useGetAllModel();

  return (
    <ConfigProvider
      theme={{
        token: { controlHeight: 34 }
      }}
    >
      <Segmented
        {...props}
        options={editorModeOptions}
        value={editorMode}
        large
        onChange={(value) => {
          const nextBaseModel =
            value === EditorMode.Recommend
              ? recommendBaseModel[0]
              : baseModel[0];

          // 每次切换 快捷创建/高级创作 模型列表时 默认选中列表第一个
          form.setFieldsValue({
            baseModelId: nextBaseModel?.id
          });
          setSizeRatio(nextBaseModel?.baseModelDefaultParams?.sizeRatio || []);

          setModelMvType(nextBaseModel?.mvType);
          // 同时切换默认的采样器
          form.setFieldValue(
            ['sampler', 'type'],
            nextBaseModel.baseModelDefaultParams.samplerIndex
          );

          setEditorMode(value as EditorMode);
        }}
      />
    </ConfigProvider>
  );
}
