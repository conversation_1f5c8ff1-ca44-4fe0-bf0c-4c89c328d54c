import React, { createContext } from 'react';
import type { FormInstance } from 'antd';
import { EditorMode } from '@/constants';
import { TextToImageParams } from './ParamsEditor/types';
import { Dispatch, SetStateAction } from 'react';
import { ParamsEditorHandle } from './ParamsEditor';
import { MeiDouPriceRef } from '@/components/MeiDouPrice';
import { ModelMvType } from '@/constants/model';

type SetEditorMode = (mode: EditorMode) => void;

function initialContextMethod() {
  console.warn('正在使用 `TextToImageContext` 的默认值');
}

export interface TextToImageContextValue {
  form: FormInstance<TextToImageParams>;

  tempInitFormValues?: TextToImageParams;
  setTempInitFormValues?: Dispatch<
    SetStateAction<TextToImageParams | undefined>
  >;

  editorMode: EditorMode;

  setEditorMode: SetEditorMode;

  loading: boolean;

  setLoading: React.Dispatch<React.SetStateAction<boolean>>;

  paramsEditorRef: React.MutableRefObject<ParamsEditorHandle | null>;
  userSubmitValue: string;
  setUserSubmitValue: React.Dispatch<React.SetStateAction<string>>;

  meidouPriceRef: React.RefObject<MeiDouPriceRef>;
  modelMvType: ModelMvType;
  setModelMvType: (type: number) => void;
  deepSeekPrompt: {
    prompt: string;
    content: string;
    direct: string;
  };
  setDeepSeekPrompt: (prompt: string, content: string, direct: string) => void;
  isModifyContent: boolean;
  setIsModifyContent: (isModifyContent: boolean) => void;

  /**
   * 用来标记是否正在重新编辑
   * 重新编辑可能导致基础模型的类型发生改变（mv -> flux），这会导致清空controlNet和风格模型（业务需求T，T）
   *
   * 但这个时候，不希望清空controlNet，这个标志就是用来跳过清空controlNet和风格模型的
   */
  reeditFlag: React.MutableRefObject<boolean>;

  /**
   * 模型配置的比例信息
   */
  sizeRatio: any[];
  setSizeRatio: (ratio: any[]) => void;

  /**
   * 模型默认配置
   */
  modelDefaultParams: any;
  setModelDefaultParams: (params: any) => void;
}

export const TextToImageContext = createContext<TextToImageContextValue>({
  form: {} as FormInstance,
  /** 存储表单初始值, 当切换回推荐操作时coverage */
  tempInitFormValues: undefined,
  setTempInitFormValues: undefined,

  /** 编辑器的创作模式  推荐模式/高级模式*/
  editorMode: EditorMode.Recommend,

  setEditorMode: initialContextMethod,

  /** 加载中状态 */
  loading: false,

  setLoading: initialContextMethod,

  paramsEditorRef: { current: null },

  /**v1.9.6 新增需求记录 记录用户点击智能联想之前字段 */
  userSubmitValue: '',
  setUserSubmitValue: initialContextMethod,
  meidouPriceRef: { current: null },
  modelMvType: 0,
  setModelMvType: initialContextMethod,
  deepSeekPrompt: {
    prompt: '',
    content: '',
    direct: ''
  },
  setDeepSeekPrompt: initialContextMethod,
  isModifyContent: false,
  setIsModifyContent: initialContextMethod,
  reeditFlag: { current: false },
  sizeRatio: [],
  setSizeRatio: initialContextMethod,
  modelDefaultParams: {},
  setModelDefaultParams: initialContextMethod
});
