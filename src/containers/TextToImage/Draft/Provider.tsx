import type { ReactNode } from 'react';

import { DraftType } from '@/api/types';
import { AppModule, generateRouteTo } from '@/services';

import {
  ImagesContainerContext,
  useImagesContainerProvider
} from '@/components/ImagesContainer';

import { toEditorComponentParams } from '@/containers/TextToImage/ParamsEditor/utils';
import { useEditorImageState } from '@/hooks/useEditorImageState';
import {
  fetchDraft,
  fetchTaskByIds,
  removeDraft,
  fetchDraftParamsById
} from '@/api';
import { useNavigate } from 'react-router-dom';

import { useDraftInitialState } from './Hooks/useDraftState';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { getValidSeed } from '@/utils/editor';
import { useTextToImageContext } from '../Provider';
import { useEditorConfigStore } from '@/hooks/useEditorConfigStore';
import { BaseModelType } from '@/constants/model';
import { useBaseModel } from '../ParamsEditor/useBaseModel';

export function DraftProvider({ children }: { children: ReactNode }) {
  const initialState = useDraftInitialState();
  const contextValue = useImagesContainerProvider({
    ...initialState,
    imageWatermark: true
  });
  const navigate = useNavigate();
  const goBack = () => {
    navigate(generateRouteTo(AppModule.TextToImage), {
      replace: true
    });
  };
  const { tasks, resetTasks, setLoading } = useEditorImageState();
  const {
    form,
    setEditorMode,
    paramsEditorRef,
    reeditFlag,
    setModelMvType,
    setModelDefaultParams,
    setSizeRatio
  } = useTextToImageContext();
  const editorConfigStore = useEditorConfigStore();

  const baseModel = useBaseModel();

  return (
    <ImagesContainerContext.Provider
      value={{
        ...contextValue,
        goBack,
        tasks,
        clearTasks() {
          // 更新参数栏状态
          resetTasks();
          setLoading(false);
        },
        async setFormValue(id, coverParmas) {
          reeditFlag.current = true;
          try {
            const { params } = await fetchDraftParamsById(
              id,
              DraftType.TEXT_TO_IMAGE
            );
            form.resetFields();
            const reeditParams = toEditorComponentParams(
              Object.assign(params, coverParmas, {
                // HACK coverParmas中的seed若为-1则取params中的seed
                seed: getValidSeed(coverParmas.seed, params.seed)
              }),
              editorConfigStore.getFlatAllControlNets()
            );

            form.setFieldsValue(reeditParams);

            const baseModelItem = baseModel?.find(
              (item) => item.id === reeditParams.baseModelId
            );
            // console.log(baseModelItem, 'baseModelItem=>>>>>.');
            setModelMvType(baseModelItem?.mvType || 0);
            setModelDefaultParams(baseModelItem?.baseModelDefaultParams || {});
            setSizeRatio(
              baseModelItem?.baseModelDefaultParams?.sizeRatio || []
            );

            const controlNet = reeditParams.controlNet;
            // 激活第一个开启controlnet的tab
            const firstEnabled = controlNet?.findIndex((c) => c.enable) ?? 0;
            // 如果没有开启controlnet 默认激活第一个tab
            const needsActivate = firstEnabled !== -1 ? firstEnabled : 0;
            paramsEditorRef.current?.activateControlNetTab(needsActivate);
          } catch (error) {
            reeditFlag.current = false;
            defaultErrorHandler(error);
          } finally {
            setTimeout(() => {
              reeditFlag.current = false;
            }, 200);
          }
        },
        fetchImages: fetchDraft.bind(null, DraftType.TEXT_TO_IMAGE),
        removeImage: removeDraft,
        fetchTasks: fetchTaskByIds,
        setEditorMode(mode) {
          mode && setEditorMode(mode);
        },

        toPreview(id) {
          navigate(generateRouteTo(AppModule.TextToImagePreviewer, id), {
            replace: true
          });
        }
      }}
    >
      {children}
    </ImagesContainerContext.Provider>
  );
}
