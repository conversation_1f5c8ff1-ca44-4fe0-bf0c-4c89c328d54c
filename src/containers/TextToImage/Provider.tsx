import type { TextToImageParams } from './ParamsEditor/types';
import type { TextToImageContextValue } from './context';

import {
  type ReactNode,
  useMemo,
  useState,
  useContext,
  useRef,
  useCallback
} from 'react';
import { PermissionProvider } from '@/components';
import { TextToImageContext } from './context';
import { Form } from 'antd';

import { useEditorMode } from '@/hooks';
import { ParamsEditorHandle } from './ParamsEditor';
import { MeiDouPriceRef } from '@/components/MeiDouPrice';
import { MobXProviderContext } from 'mobx-react';
import { EditorConfigStore } from '@/hooks/useEditorConfigStore/editorConfigStore';
import { EditorConfigStoreProvider } from '@/hooks/useEditorConfigStore';

export interface ProviderProps {
  children?: ReactNode;
}

// Confuse: editorMode为啥走atom 又要走context?
export function Provider(props: ProviderProps) {
  // FIXME: 移除全局的 `formInstance`
  const [form] = Form.useForm<TextToImageParams>();
  const [loading, setLoading] = useState(false);
  const [userSubmitValue, setUserSubmitValue] = useState<string>('');

  const meidouPriceRef = useRef<MeiDouPriceRef>(null);

  // 用于存储表单初始值
  const [tempInitFormValues, setTempInitFormValues] =
    useState<TextToImageParams>();

  // FIXME: 移除存储全局的 `editorMode`
  const [editorMode, setEditorMode] = useEditorMode(); // 存储在 store 里

  const paramsEditorRef = useRef<ParamsEditorHandle>(null);

  const [modelMvType, setModelMvType] = useState(0);
  const [deepSeekPrompt, setDeepSeekPrompt] = useState({
    prompt: '',
    content: '',
    direct: ''
  });
  const [isModifyContent, setIsModifyContent] = useState(false);

  // Create a wrapper function with the correct signature
  const handleSetDeepSeekPrompt = useCallback(
    (prompt: string, content: string, direct: string) => {
      setDeepSeekPrompt({ prompt, content, direct });
    },
    []
  );

  /**
   * 用来标记是否正在重新编辑
   * 重新编辑可能导致基础模型的类型发生改变（mv -> flux），这会导致清空controlNet和风格模型（业务需求T，T）
   *
   * 但这个时候，不希望清空controlNet，这个标志就是用来跳过清空controlNet和风格模型的
   */
  const reeditFlag = useRef<boolean>(false);

  const [sizeRatio, setSizeRatio] = useState<any[]>([]);

  const [modelDefaultParams, setModelDefaultParams] = useState<any>({});

  const handleSetSizeRatio = useCallback((ratio: any[]) => {
    setSizeRatio(ratio);
  }, []);

  const handleSetModelDefaultParams = useCallback((params: any) => {
    setModelDefaultParams(params);
  }, []);

  const contextValue = useMemo<TextToImageContextValue>(() => {
    return {
      form,
      editorMode,
      setEditorMode,
      tempInitFormValues,
      setTempInitFormValues,
      loading,
      setLoading,
      paramsEditorRef,
      userSubmitValue,
      setUserSubmitValue,
      meidouPriceRef,
      modelMvType,
      setModelMvType,
      deepSeekPrompt,
      setDeepSeekPrompt: handleSetDeepSeekPrompt,
      reeditFlag,
      isModifyContent,
      setIsModifyContent,
      sizeRatio,
      setSizeRatio: handleSetSizeRatio,
      modelDefaultParams,
      setModelDefaultParams: handleSetModelDefaultParams
    };
  }, [
    editorMode,
    form,
    setEditorMode,
    tempInitFormValues,
    loading,
    setLoading,
    paramsEditorRef,
    userSubmitValue,
    setUserSubmitValue,
    meidouPriceRef,
    modelMvType,
    setModelMvType,
    deepSeekPrompt,
    handleSetDeepSeekPrompt,
    reeditFlag,
    isModifyContent,
    setIsModifyContent,
    sizeRatio,
    setSizeRatio,
    modelDefaultParams,
    setModelDefaultParams
  ]);

  const editorConfigStore = useRef(new EditorConfigStore());

  return (
    <TextToImageContext.Provider value={contextValue}>
      <EditorConfigStoreProvider>
        <PermissionProvider>{props.children}</PermissionProvider>
      </EditorConfigStoreProvider>
    </TextToImageContext.Provider>
  );
}

export function useTextToImageContext() {
  return useContext<TextToImageContextValue>(TextToImageContext);
}
