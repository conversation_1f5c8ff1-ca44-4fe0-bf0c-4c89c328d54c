import { useImageToImagEditorConfig } from '@/containers/ImageToImage/hooks';
import type { FormInstance } from 'antd';
import {
  initialEditorParams,
  toEditorComponentParams
} from '@/containers/ImageToImage/ParamsEditor/utils';
import produce from 'immer';
import type { ImageToImageParams } from '@/containers/ImageToImage/ParamsEditor/types';
import { useCachedImgToImgFields } from '@/hooks/useCachedPageFields';
import { useSearchParams } from 'react-router-dom';
import { useLocationSearchParamsChanged } from '@/hooks';
import { fetchSameStyleGallery as fetchSameStyleGalleryApi } from '@/api';
import { useImageToImageContext } from '../Provider';
import { EditorConfigResponse } from '@/api/types';
import { isExistInBaseModelConfig } from '@/utils/isModelConfigExists';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { toEditorComponentDefaultParams } from '@/utils/editor';
import _ from 'lodash';
import { useEffect } from 'react';
import { useEditorConfigStore } from '@/hooks/useEditorConfigStore';
import { getStyleType } from '@/api/model';

/**
 * 初始化表单
 */
export const useInitImageToImagForm = (
  form: FormInstance<ImageToImageParams>,
  { onInitialized }: { onInitialized?: (params: ImageToImageParams) => void }
) => {
  const [searchParams] = useSearchParams();
  const baseModelId = searchParams.get('baseModelId');
  const id = searchParams.get('id');
  const styleModelId = searchParams.get('styleModelId');
  const { setLoading, reeditFlag, setSizeRatio, setModelDefaultParams } =
    useImageToImageContext();
  const { fetchImageToImageEditorConfig } = useImageToImagEditorConfig();
  const editorConfigStore = useEditorConfigStore();
  const {
    cachedFields: { paramsEditor },
    applyFlushParams
  } = useCachedImgToImgFields();

  // useLocationSearchParamsChanged(() => {
  //   (async () => {
  //     try {
  //       // 保持重构前的逻辑， 只有id存在的时候才会有整个组件的loading
  //       !!id && setLoading(true);
  //       const res = await fetchImageToImageEditorConfig();
  //       await initForm(res);
  //     } catch (error) {
  //       console.log(error);
  //     } finally {
  //       setLoading(false);
  //     }
  //   })();
  // });

  const initForm = async (config: EditorConfigResponse | undefined) => {
    // console.log('initForm', config);
    if (!config) return;

    let initBaseModel = config.baseModel[0]?.list[0];

    if (styleModelId) {
      // 查询 baseModel 列表中的list列表是否存在 mvType === 3 的模型
      const res = await getStyleType(styleModelId);
      if (res.isFlux) {
        const fluxCategory = config.baseModel.find((model) =>
          model.list.some((item) => item.mvType === 3)
        );
        if (fluxCategory) {
          // 从分类中找到第一个 mvType === 3 的模型
          const fluxModelItem = fluxCategory.list.find(
            (item) => item.mvType === 3
          );
          if (fluxModelItem) {
            initBaseModel = fluxModelItem;
          }
        }
      } else {
        // 找到 baseModel 列表中的第一个mvType !==3 的模型
        initBaseModel =
          config.baseModel[0]?.list.find((item) => item.mvType !== 3) ||
          config.baseModel[0]?.list[0];
      }
    }

    const initRecommendParams = toEditorComponentDefaultParams(
      config.baseModel[0]?.list[0]?.defaultParams
    );

    let fixturesConfig = produce(
      {
        ...initialEditorParams,
        ...initRecommendParams,
        ...(paramsEditor ?? {}),
        ...(applyFlushParams() ?? {})
      },
      (draft) => {
        draft.baseModelId = initBaseModel?.id; // 默认取第二个分类(第一个后端排序为收藏)的第一个模型(如果是通过url传过来的 优先)
        if (!draft.sampler.type) {
          // 优先取基础模型绑定的采样器，如果没有值再默认取的第一个采样器
          draft.sampler.type = config?.samplerIndex[0].value;
        }
      }
    );

    if (!!id) {
      // TODO is need retry when fetchStyleGalleryConfig fail?
      try {
        const { params } = await fetchSameStyleGalleryApi(id);
        fixturesConfig = {
          // HACK 创作同款时不使用推荐参数
          ..._.omit(fixturesConfig, ['recommendParams']),
          ...(toEditorComponentParams(
            params,
            editorConfigStore.getFlatAllControlNets()
          ) ?? {})
        };
      } catch (error) {
        defaultErrorHandler(error);
      }
    }

    fixturesConfig = produce(fixturesConfig, (draft) => {
      if (!!baseModelId) {
        const isExists = isExistInBaseModelConfig(
          config.baseModel,
          Number(baseModelId)
        );

        if (isExists) {
          draft.baseModelId = Number(baseModelId);
        }
      }
    });

    //  根据fixturesConfig中的baseModelId, 获取对应的item 选项 处理baseModelDefaultParams中的sizeRatio
    const baseModelItem = config.baseModel
      .find((item) =>
        item.list.find((item) => item.id === fixturesConfig.baseModelId)
      )
      ?.list.find((item) => item.id === fixturesConfig.baseModelId);
    setSizeRatio(baseModelItem?.defaultParams?.sizeRatio || []);
    setModelDefaultParams(baseModelItem?.defaultParams || {});

    reeditFlag.current = true;
    form.resetFields();
    form.setFieldsValue(fixturesConfig);
    onInitialized?.(fixturesConfig);
    setTimeout(() => {
      reeditFlag.current = false;
    }, 100);
  };

  useEffect(() => {
    setLoading(true);
    editorConfigStore
      .refreshConfig('image-to-image')
      .then(() => {
        return fetchImageToImageEditorConfig();
      })
      .then((res) => {
        return initForm(res);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);
};
