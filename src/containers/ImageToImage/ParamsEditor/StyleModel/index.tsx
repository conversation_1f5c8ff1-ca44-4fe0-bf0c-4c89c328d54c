import type { ImageToImageParams } from '../types';
import type { ModalRef } from '@/components';

import {
  Button,
  Form,
  Space,
  ConfigProvider,
  message,
  type FormListFieldData
} from 'antd';
import { FormListItem } from './FormListItem';
import {
  StyleModel as StyleModelComponent,
  useStyleModelList,
  StyleModelModal
} from '@/components/StyleModel';
import { StyleModelContext } from '@/components/StyleModel/Provider';
import { useInitialStyleModel } from '@/hooks';

import { useContext, useMemo, useRef, useState } from 'react';
import { STRENGTH } from '@/constants';
import { getCategoryIds } from '@/utils/getCategoryId';
import { DraftType } from '@/api/types';
import { trackEvent } from '@/services';
import { getSource } from '@/utils';
import { ImageToImageContext } from '../../context';
import { ModelMvType } from '@/constants/model';

const STYLE_MODEL_NUMS_LIMIT = 3;
function StyleModelList() {
  const modelModalRef = useRef<ModalRef>(null);
  const form = Form.useFormInstance<ImageToImageParams>();
  const [namePath, setNamePath] = useState<
    FormListFieldData['name'] | undefined
  >(undefined);
  const onModelCardClick = (field?: any) => {
    trackEvent('edit_model_add_click', {
      function: getSource()
    });

    modelModalRef.current?.setVisible?.(true);
    setNamePath(field?.name);
  };
  const { styleModel, styleModelResponse } = useStyleModelList();
  const { modelMvType } = useContext(ImageToImageContext);
  useInitialStyleModel();

  const checkDisabled = (model: any) => {
    // 模型如果是flux(modelMvType === ModelMvType.SpecialBeta), 非flux模型禁用。 如果是其他模型， 禁用flux模型
    if (
      modelMvType === ModelMvType.SpecialBeta ||
      modelMvType === ModelMvType.External
    ) {
      // 非 flux 模型禁用
      return !model.isFlux;
    } else {
      // flux 模型禁用
      return model.isFlux;
    }
  };

  const tooltip = useMemo(() => {
    if (modelMvType === ModelMvType.SpecialBeta) {
      return '当前不可用，需要将生图模型切换为MiracleVision 4.0';
    } else {
      return '当前不可用，需要将生图模型切换为Miracle F1';
    }
  }, [modelMvType]);
  return (
    <>
      <Form.List name="styleModel">
        {(fields, { add, remove }, { errors }) => {
          return (
            <Space direction="vertical" style={{ width: '100%' }} size={12}>
              {fields.map((field) => {
                return (
                  <FormListItem
                    styleModel={styleModel}
                    key={field.name}
                    onModelCardClick={onModelCardClick.bind(null, field)}
                    namePath={[field.name]}
                    remove={() => remove(field.name)}
                  />
                );
              })}
              <Form.Item
                noStyle
                shouldUpdate={(prev, cur) =>
                  prev.styleModel?.length !== cur.styleModel?.length
                }
              >
                {({ getFieldValue }) => {
                  const selectedNums = getFieldValue('styleModel')?.length ?? 0;

                  // 已选中的风格模型的数量"超过"最大上限的值
                  // 若小于0 则仍然可以添加
                  const exceedLimit = selectedNums - STYLE_MODEL_NUMS_LIMIT;

                  // 需要删除几个才可以再次添加
                  // 例：如果当前超过上限1个，则需要删除2个才能添加
                  const needsDelete = Math.max(exceedLimit + 1, 0);

                  return (
                    <ConfigProvider theme={{ token: { controlHeight: 32 } }}>
                      <Button
                        style={{ marginTop: '12px' }}
                        onClick={(field) => {
                          if (needsDelete) {
                            const numsTip =
                              needsDelete > 1 ? `${needsDelete}个` : '';

                            message.warning(
                              `风格模型已达到上限，请删除${numsTip}后添加。`
                            );
                            return;
                          }

                          onModelCardClick(field);
                        }}
                        block
                      >
                        添加风格模型
                      </Button>
                    </ConfigProvider>
                  );
                }}
              </Form.Item>
            </Space>
          );
        }}
      </Form.List>

      <StyleModelModal
        ref={modelModalRef}
        modelMvType={modelMvType}
        checkDisabled={checkDisabled}
        tooltip={tooltip}
        onModelClick={({
          id,
          styleModelWeight,
          images,
          name,
          typeName,
          isFlux
        }) => {
          if (
            checkDisabled({
              id,
              styleModelWeight,
              images,
              name,
              typeName,
              isFlux
            })
          ) {
            if (modelMvType === ModelMvType.SpecialBeta) {
              message.warning(
                '当前不可用，需要将生图模型切换为MiracleVision 4.0'
              );
            } else {
              message.warning('当前不可用，需要将生图模型切换为Miracle F1');
            }
            return;
          }
          const categoryIds = getCategoryIds(styleModelResponse, id);
          const value = {
            id,
            strength: styleModelWeight ?? STRENGTH,
            categoryIds,
            title: name,
            src: images as string,
            tag: typeName
          };
          const styleList = form.getFieldValue(
            'styleModel'
          ) as ImageToImageParams['styleModel'];

          if (styleList?.some((s) => s.id === id)) {
            message.warning('该模型已经设置在风格模型中啦~');
          } else {
            if (namePath === undefined) {
              const styleModel = [...(styleList ?? []), value];
              form.setFieldsValue({ styleModel });
            } else {
              form.setFieldValue(['styleModel', namePath], value);
            }
          }
          modelModalRef.current?.setVisible?.(false);
        }}
      />
    </>
  );
}

export const StyleModel = () => {
  const { modelMvType } = useContext(ImageToImageContext);
  // const styleContext = useContext(StyleModelContext);
  return (
    <StyleModelComponent modelMvType={modelMvType}>
      <StyleModelList />
    </StyleModelComponent>
  );
};
