import { ModelCard, ModalRef, ModelModalV2, Loading } from '@/components';
import { Checkbox, Form, Image, Popover } from 'antd';
import { ModelCardProps } from '@/components';
import { ImageToImageParams } from '../types';
import { useRef, useContext, useEffect, useState } from 'react';
import { useRecommendParams } from '@/hooks/useGetEditorConfig/useRecommendParams';

import { EditorConfigModelResponse } from '@/api/types/editorConfig';
import { useGetAllModel } from '@/containers/ImageToImage/hooks';
import { tabs as controlNetTabs } from '@/components/ControlNetSection';
import { ControlNetUtils } from '@/utils/controlNetUtils';

import styles from './index.module.less';
import { initialEditorParams } from '../utils';
import { ChevronRightBlack } from '@meitu/candy-icons';
import { ImageToImageContext } from '../../context';
import { usePrevious } from 'react-use';
import { useStyleModelList } from '@/components/StyleModel';
import { StyleModelContext } from '@/components/StyleModel/Provider';
import { ModelMvType } from '@/constants/model';

import classNames from 'classnames';

export interface BaseModelProps {
  baseModel: ModelCardProps<number>['list'];
  baseModelResponse: EditorConfigModelResponse[];
  refetch(): Promise<any>;
}

export const BaseModel = (props: BaseModelProps) => {
  const modelModalRef = useRef<ModalRef>(null);
  const { baseModel, baseModelResponse } = props;

  const styleModelContext = useContext(StyleModelContext);

  const { modelMvType } = useContext(ImageToImageContext);

  const { initStyleModelList } = styleModelContext;
  const { fetchStyleModels, styleModelResponse } = useStyleModelList();

  const form = Form.useFormInstance<ImageToImageParams>();
  const { setModelMvType, reeditFlag, setSizeRatio, setModelDefaultParams } =
    useContext(ImageToImageContext);
  const setModalVisible = (visible: boolean) => {
    modelModalRef.current?.setVisible(visible);
  };
  const { baseModelMap } = useGetAllModel();

  const [showPop, setShowPop] = useState(false);

  useRecommendParams(baseModelMap, initialEditorParams);

  function handleCardClick() {
    baseModel.length > 1 && setModalVisible(true);
    setShowPop(true);
  }

  const baseModelId = Form.useWatch('baseModelId');
  const prevBaseModelId = usePrevious(baseModelId);

  const handleBaseModelChange = () => {
    const prevModel = baseModel?.find((baseModel) => {
      return baseModel.id === prevBaseModelId;
    });

    const nextModel = baseModel?.find((baseModel) => {
      return baseModel.id === baseModelId;
    });

    setModelMvType(nextModel?.mvType ?? 0);

    if (reeditFlag.current) {
      return;
    }

    if (nextModel?.mvType === ModelMvType.SpecialBeta) {
      form.setFieldValue(['styleModel'], []);
      try {
        // 直接调用 initStyleModelList，确保它被执行
        initStyleModelList(() => {
          // 在回调中获取风格模型
          fetchStyleModels({
            baseModelType: 2,
            reset: true // 确保重置数据
          });
        });
      } catch (error) {}
    } else {
      form.setFieldValue(['styleModel'], []);
      // fetchStyleModels({
      //   baseModelType: 1,
      //   reset: true // 确保重置数据
      // });
    }

    // 当基础模型的类型发生变化时 清空controlnet配置
    if (prevModel?.mvType !== nextModel?.mvType) {
      controlNetTabs.forEach((_, index) => {
        const namePath = ControlNetUtils.getImageProcessParamsPath(index);
        form.setFieldValue(namePath, {});
      });
    }
  };
  useEffect(() => {
    handleBaseModelChange();
  }, [baseModelId]);
  useEffect(() => {
    // if (reeditFlag.current) {
    //   return;
    // }

    if (modelMvType === ModelMvType.SpecialBeta) {
      initStyleModelList(() => {
        // 在回调中获取风格模型
        fetchStyleModels({
          baseModelType: 2,
          reset: true // 确保重置数据
        });
      });
    } else {
      fetchStyleModels({
        baseModelType: 1,
        reset: true // 确保重置数据
      });
    }
  }, [modelMvType]);

  const PopModelCardList = (props: any) => {
    const { baseModel } = props;

    return (
      <div className={styles.popModelCardList}>
        <div className={styles.popModelCardListTitle}>选择模型</div>
        <div>
          {baseModel.map((item: any) => (
            <div
              key={item.id}
              className={classNames(
                styles.popModelCardListItem,
                baseModelId === item.id && styles.active
              )}
              onClick={() => {
                form.setFieldValue('baseModelId', item.id);
                // setModelMvType(item.mvType);
                setSizeRatio(item?.baseModelDefaultParams?.sizeRatio || []);
                // 切换模型设置提示词强度
                form.setFieldValue(
                  'promptWeight',
                  item.baseModelDefaultParams.cfgScale
                );
                setModelDefaultParams(item?.baseModelDefaultParams);
                setShowPop(false);
              }}
            >
              <Image
                src={item.src}
                className={styles.popModelCardListItemImage}
                preview={false}
                placeholder={<Loading />}
              />

              <div className={styles.popModelCardListItemContent}>
                <div className={styles.title}>{item.title}</div>
                <div className={styles.desc}>{item.desc}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <>
      <Popover
        content={<PopModelCardList baseModel={baseModel} />}
        trigger="click"
        placement="right"
        arrow={false}
        open={showPop}
        onOpenChange={setShowPop}
        rootClassName={styles.popModelCardListPopover}
      >
        <Form.Item noStyle name="baseModelId">
          <ModelCard
            className={styles.modelCard}
            list={baseModel}
            onClick={handleCardClick}
            onExtraClick={handleCardClick}
            showCornerLabelUrl={false}
            extra={
              <div className={styles.extraIcon}>
                <ChevronRightBlack />
              </div>
            }
          />
        </Form.Item>
      </Popover>
      <Form.Item noStyle name="recommendParams" valuePropName="checked">
        <Checkbox className={styles.recommendParams}>使用推荐参数</Checkbox>
      </Form.Item>
      {/* <ModelModalV2
        title="生图模型"
        list={baseModelResponse}
        ref={modelModalRef}
        onModelClick={async (baseModel) => {
          form.setFieldsValue({ baseModelId: baseModel.id });
          setModalVisible(false);
        }}
        onCollectMutation={props.refetch}
      /> */}
    </>
  );
};
