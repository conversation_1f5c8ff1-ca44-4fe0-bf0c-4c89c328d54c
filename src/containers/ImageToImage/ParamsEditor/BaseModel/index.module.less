@import '~@/styles/variables.less';

.recommend-params:global(.@{ant-prefix}-checkbox-wrapper) {
  height: 20px;
  line-height: 20px;
  font-weight: 400;
  margin-top: 8px;

  :global(.@{ant-prefix}-checkbox) + span {
    padding: 0 4px;
  }
}

.model-card {
  :global(.model-card-extra) {
    background-color: transparent;

    .extra-icon {
      position: absolute;
      top: @size;
      right: @size-sm;
    }
  }
}
.pop-model-card-list {
  display: flex;
  // width: 280px;
  height: 244px;
  overflow-y: auto;
  padding-bottom: var(--spacing-12, 12px);
  flex-direction: column;
  padding: 12px 16px;
  border-radius: 12px;
  // align-items: center;
  background: @background-system-frame-floatpanel;
  // box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08),
  //   0px 24px 128px 0px rgba(0, 0, 0, 0.16);
  &-title {
    color: var(--system-content-secondary, #293545);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    margin-bottom: 10px;
  }
  &-item {
    display: flex;
    padding: var(--spacing-6, 6px) var(--spacing-8, 8px) var(--spacing-6, 6px)
      var(--spacing-6, 6px);
    align-items: center;
    gap: var(--spacing-8, 8px);
    align-self: stretch;
    border-radius: var(--radius-12, 12px);
    border: 1px solid var(--system-stroke-input, #e2e8f0);
    background: #fff;
    height: 52px;
    margin-bottom: 8px;
    cursor: pointer;
    :global(.@{ant-prefix}-image) {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      border: 1px solid @stroke-system-border-overlay;
      object-fit: cover;
      overflow: hidden;
      img {
        height: 40px;
        vertical-align: middle;
      }
    }
    &-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      gap: 4px;
      .title {
        color: var(--system-content-secondary, #293545);
        /* text_14_medium */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
      }
      .desc {
        overflow: hidden;
        color: var(--system-content-thirdary, #6a7b94);
        text-overflow: ellipsis;
        white-space: nowrap;
        /* text_12 */
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
      }
    }
  }
  .active {
    border: 1px solid var(--system-content-brandPrimary, #3549ff);
  }
}
