import { MediaType, MtccFuncCode, type ImageTasks } from '@/api/types';
import type { ImageToImageParams } from './types';
import type { OnFormFinish, CreateImageTaskParams } from '@/types';

import { ControlNetSection } from './ControlNetSection';
import { ParamsSection } from './ParamsSection';
import { PromptSection } from './PromptSection';
import { ImageSection } from './ImageSection';
import { StyleModel } from './StyleModel';
import { BaseModel } from './BaseModel/popIndex';
import { Form, message, Image } from 'antd';
import { InfoCircle } from '@meitu/candy-icons';

import { EditorConfigProvider, Collapse } from '@/components';

import { createImageToImageTask as createImageToImageTaskApi } from '@/api';
import {
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
  useContext
} from 'react';
import { getSearchParams, trackImageToImageEvent } from '@/utils/draftTracking';
import { useImageToImageContext } from '../Provider';
import { toEditorQueryParams } from './utils';

import {
  useImageToImagEditorConfig,
  useInitImageToImagForm,
  useGetAllModel
} from '../hooks';
import { useSearchParams, useTracerParams } from '@/hooks';
import { MeiDouButton, MeiDouPrice } from '@/components/MeiDouPrice';
import { FunctionCode } from '@/api/types/meidou';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useSyncMemberDescErrorHandler } from '@/hooks/useMember';
import { fixturesMeidouBalanceTracer } from '@/utils/fixturesMeidouBalanceTracer';
import { useOnRecommendParamsChange } from '@/hooks/useGetEditorConfig/useRecommendParams';
import { getSource } from '@/utils';
import { getControlNetTrackParams } from '@/utils/controlNetUtils';
import {
  getLocalStorageItem,
  setLocalStorageItem,
  toAtlasImageView2URL,
  toCamelCase
} from '@meitu/util';
import { ControlNetSectionHandle } from '@/components/ControlNetSection';
import { StyleModelProvider } from '@/components/StyleModel/Provider';
import { DraftType } from '@/api/types';

import styles from './index.module.less';
import { useCachedImgToImgFields } from '@/hooks/useCachedPageFields';
import { ModelMvType } from '@/constants/model';
import { DEEPSEEK_PROMPT_OPTIMIZATION_KEY_FIRST_CLICK } from '@/constants';
import { BaseModelInfo } from '@/hooks/useGetEditorConfig/getAllModelByConfig';
// import { ImageToImageContext } from '../context';

export interface ParamsEditorRef {
  reeditImageToImageParams: (params: Partial<ImageToImageParams>) => void;
}

export const ParamsEditor = forwardRef<ParamsEditorRef>((props, ref) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm<ImageToImageParams>();
  const { id } = useSearchParams();
  const { tracerParams } = useTracerParams();
  const { setTasks, setUserSubmitValue, meidouPriceRef } =
    useImageToImageContext();
  const { editorConfig, fetchImageToImageEditorConfig } =
    useImageToImagEditorConfig();
  const { baseModel } = useGetAllModel();

  /** -----------------会员相关hook和方法----------------------- */
  const { updateMeiDouBalance, availableAmount } = useMeiDouBalance({
    meidouPriceRef
  });

  const { modelMvType, deepSeekPrompt, isModifyContent } =
    useImageToImageContext();

  const handleError = useSyncMemberDescErrorHandler();

  /** -----------------会员相关hook和方法----------------------- */

  useInitImageToImagForm(form, {
    onInitialized: () => {}
  });

  const { cachedFields } = useCachedImgToImgFields();

  const onFinish: OnFormFinish<ImageToImageParams> = async (values) => {
    // 产品临时决定，在梳理下需求，这个逻辑可能会变，先行注释
    // if (userSubmitValue) {
    //   values.userPrompt = userSubmitValue;
    // } else {
    //   values.userPrompt = values.prompt;
    // }
    if (modelMvType === ModelMvType.SpecialBeta) {
      values.faceSimilarity = 0;
    }
    const params = toEditorQueryParams(values);
    // 外采模型且用户没有输入prompt时， 提示用户输入prompt
    if (modelMvType === ModelMvType.External && !values.prompt) {
      message.error('请输入提示词');
      return;
    }

    if (!values.image?.key) {
      message.error('请上传图片');
      return;
    }

    createImageToImageTask({
      params,
      effectId: id ? +id : undefined,
      msgId: cachedFields?.paramsEditor?.msgId
    });
  };

  const baseModelResponse = editorConfig?.baseModel ?? [];
  const moduleList = editorConfig?.moduleList ?? [];
  /** 创建图生图 图片的任务 */
  const createImageToImageTask = async (
    createParams: CreateImageTaskParams
  ) => {
    try {
      setLoading(true);

      const { width, height } = createParams.params;
      let result = await createImageToImageTaskApi({
        ...createParams,
        functionName: MtccFuncCode.FuncCodeImage2Image,
        mediaType: MediaType.Photo,
        resMediaType: MediaType.Photo,
        modelName: baseModel?.find((baseModel) => {
          return baseModel.id === toCamelCase(createParams.params)?.baseModelId;
        })?.title
      });
      // 设置图片任务id和宽高 供任务轮训那使用
      const tasks = result.map(({ id }) => {
        return {
          id,
          size: [width, height]
        };
      });
      setTasks(tasks as ImageTasks);
      setUserSubmitValue('');
    } catch (error) {
      handleError(error);
    } finally {
      updateMeiDouBalance();
      setLoading(false);
      setUserSubmitValue('');
    }
  };

  const controlnetSectionRef = useRef<ControlNetSectionHandle>(null);

  useImperativeHandle(
    ref,
    () => {
      return {
        reeditImageToImageParams(params: Partial<ImageToImageParams>) {
          form.resetFields();
          form.setFieldsValue(params);

          const controlNet = params.controlNet;
          // 激活第一个开启controlnet的tab
          const firstEnabled = controlNet?.findIndex((c) => c.enable) ?? 0;
          // 如果没有开启controlnet 默认激活第一个tab
          const needsActivate = firstEnabled !== -1 ? firstEnabled : 0;
          controlnetSectionRef.current?.activateTab(needsActivate);
        }
      };
    },
    [form]
  );

  const onRecommendParamsChange = useOnRecommendParamsChange(form);

  const baseModelId = form.getFieldValue('baseModelId');
  console.log('baseModelId', baseModelId);
  console.log('baseModel', baseModel);
  const baseModelItem = baseModel?.find((item) => item.id === baseModelId);
  console.log('baseModelItem', baseModelItem);

  return (
    <EditorConfigProvider>
      <StyleModelProvider from={DraftType.IMAGE_TO_IMAGE}>
        <Form
          // className={styles.paramsEditor}
          form={form}
          onFinish={onFinish}
          onValuesChange={onRecommendParamsChange}
          scrollToFirstError={{
            behavior: 'smooth',
            block: 'nearest'
          }}
        >
          <Collapse
            defaultActiveKey={[
              'prompt',
              'model',
              'styles',
              'generate-params',
              'image',
              'control-net'
            ]}
          >
            <Collapse.Panel
              key="image"
              header="上传参考图"
              showArrow={false}
              forceRender
            >
              <ImageSection />
            </Collapse.Panel>

            <Collapse.Panel
              key="prompt"
              header={null}
              collapsible="icon"
              showArrow={false}
            >
              <PromptSection />
            </Collapse.Panel>

            <Collapse.Panel
              key="model"
              header={null}
              collapsible="icon"
              showArrow={false}
            >
              <Collapse.Panel.Section
                title={
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}
                  >
                    <div>
                      <strong>模型</strong>
                      {editorConfig?.baseTagUrl && (
                        <Image
                          className={styles.icon}
                          preview={false}
                          src={toAtlasImageView2URL(
                            editorConfig?.baseTagUrl ?? '',
                            {
                              mode: 2,
                              width: 50
                            }
                          )}
                        ></Image>
                      )}
                    </div>
                    <div>
                      {baseModelItem?.tips && (
                        <div className={styles.tips}>
                          <InfoCircle /> {baseModelItem?.tips}
                        </div>
                      )}
                    </div>
                  </div>
                }
              >
                <BaseModel
                  refetch={fetchImageToImageEditorConfig}
                  baseModel={baseModel}
                  baseModelResponse={baseModelResponse}
                />
              </Collapse.Panel.Section>
            </Collapse.Panel>

            <Collapse.Panel key="styles" header="风格模型" forceRender>
              {modelMvType === ModelMvType.External ? (
                <div className={styles.notSupport}>
                  当前生图模型不支持添加风格
                </div>
              ) : (
                <StyleModel />
              )}
            </Collapse.Panel>

            <Collapse.Panel key="control-net" header="画面参考" forceRender>
              {modelMvType === ModelMvType.External ? (
                <div className={styles.notSupport}>
                  当前生图模型不支持参考图
                </div>
              ) : (
                <ControlNetSection ref={controlnetSectionRef} />
              )}
            </Collapse.Panel>

            <Collapse.Panel key="generate-params" header="参数设定" forceRender>
              <ParamsSection baseModelItem={baseModelItem as BaseModelInfo} />
            </Collapse.Panel>
          </Collapse>

          <MeiDouPrice
            ref={meidouPriceRef}
            functionCode={FunctionCode.img2img}
            // 外接模型需求， 在baseModelId变化时， 需要重新计算价格
            fields={['quantity', 'batches', 'baseModelId']}
            getFunctionBody={() => {
              return {
                params: toEditorQueryParams(form.getFieldsValue())
              };
            }}
          >
            {(price, fetchLoading) => (
              <MeiDouButton
                price={price}
                block
                htmlType="submit"
                loading={loading}
                fetchPriceLoading={fetchLoading}
                onClick={() => {
                  const editorComponentParams = form.getFieldsValue();
                  const localPrompt = getLocalStorageItem(
                    DEEPSEEK_PROMPT_OPTIMIZATION_KEY_FIRST_CLICK
                  );
                  const editorQueryParams = toEditorQueryParams(
                    editorComponentParams
                  );

                  if (!price) return;
                  // sameModelFrom: getLocation() ?? '',

                  /** 立即生成按钮点击上报 */
                  trackImageToImageEvent('create_btn_click', {
                    ...editorQueryParams,
                    ...getSearchParams(),
                    beautyCoinBalanceSufficient: fixturesMeidouBalanceTracer(
                      price.amount,
                      availableAmount ?? 0
                    ),
                    isVip: Number(price?.isVip),
                    freeBatchSize: price?.useFreeNum,
                    function: getSource(),
                    ...tracerParams,
                    ...getControlNetTrackParams(
                      toCamelCase(
                        toCamelCase(editorQueryParams).controlnetUnits
                      ),
                      editorConfig?.moduleList
                    ),
                    ...deepSeekPrompt,
                    prompt: localPrompt || editorQueryParams.prompt,
                    isModifyContent: isModifyContent ? '1' : '0',
                    modifyContent: isModifyContent
                      ? editorQueryParams.prompt
                      : ''
                  });
                  // setLocalStorageItem(
                  //   DEEPSEEK_PROMPT_OPTIMIZATION_KEY_FIRST_CLICK,
                  //   deepSeekPrompt.content
                  // );
                }}
              />
            )}
          </MeiDouPrice>
        </Form>
      </StyleModelProvider>
    </EditorConfigProvider>
  );
});
