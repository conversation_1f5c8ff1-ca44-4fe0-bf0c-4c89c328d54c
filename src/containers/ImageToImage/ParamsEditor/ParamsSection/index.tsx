import {
  Collapse,
  SliderInput,
  SizeSelector3,
  SeedInput,
  FacialRepair,
  PresetSizeMode
} from '@/components';
import { Form, Tooltip } from 'antd';
import { SamplerType } from './SamplerType';
import { QuestionMarkCircleBold } from '@meitu/candy-icons';
import { SelectZoomMode } from '@/components';

import { RANDOM_SEED_VALUE } from '@/constants';
import { Proportion } from '@/components/SizeSelector/constants';
import { maxFluxPromptWeight, ModelMvType } from '@/constants/model';
import './index.less';
import { ImageToImageContext } from '../../context';
import { useContext, useEffect } from 'react';
import { BaseModelInfo } from '@/hooks/useGetEditorConfig/getAllModelByConfig';

export function ParamsSection(props: { baseModelItem: BaseModelInfo }) {
  const { modelMvType, sizeRatio, modelDefaultParams } =
    useContext(ImageToImageContext);
  const isFluxModel = modelMvType === ModelMvType.SpecialBeta;
  const form = Form.useFormInstance();

  useEffect(() => {
    if (isFluxModel) {
      form.setFieldValue('facialDetection', false);
      form.setFieldValue('adetailerFace', false);
      // form.setFieldValue('faceSimilarity', 0);
    } else {
      form.setFieldValue('facialDetection', true);
    }
  }, [isFluxModel]);
  // 提示词强度最大值
  // const maxPromptWeight =
  //   modelMvType === ModelMvType.SpecialBeta ? maxFluxPromptWeight : 20;

  //
  const disableQuantity = props?.baseModelItem?.modelFlag === 5;
  if (disableQuantity) {
    // 设置form 的quantity 的默认值为4
    form.setFieldValue('quantity', 4);
  }

  return (
    <div className="no-select">
      <Collapse.Panel.Section>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.image?.key !== currentValues.image?.key
          }
        >
          {({ getFieldValue }) => (
            <Form.Item name="canvas" noStyle>
              {/* <SizeSelector
                imageUrl={
                  getFieldValue('image')?.preview ?? getFieldValue('image')?.key
                }
                type="adaption"
                presetSizeMode={PresetSizeMode.AUTO_MAX_PRESET_SIZE}
              /> */}
              <SizeSelector3
                type="self-adaption"
                sizeRatio={sizeRatio}
                imageUrl={
                  getFieldValue('image')?.preview ?? getFieldValue('image')?.key
                }
              />
            </Form.Item>
          )}
        </Form.Item>
      </Collapse.Panel.Section>

      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.canvas?.proportion !== currentValues.canvas?.proportion
        }
      >
        {({ getFieldValue }) =>
          getFieldValue(['canvas', 'proportion']) !== Proportion.ADAPTION && (
            <Collapse.Panel.Section
              title={
                <>
                  画面缩放
                  <Tooltip title="当上传图片的比例与设定比例不一致时按照选择的模式进行缩放">
                    <QuestionMarkCircleBold />
                  </Tooltip>
                </>
              }
            >
              <Form.Item name={['image', 'zoomMode']} noStyle>
                <SelectZoomMode />
              </Form.Item>
            </Collapse.Panel.Section>
          )
        }
      </Form.Item>

      {modelMvType === ModelMvType.External ? null : (
        <Collapse.Panel.Section>
          <Form.Item name={['image', 'strength']} noStyle>
            <SliderInput
              title="重绘幅度"
              tooltip="强度越低结果越接近原始图，但较低的强度会影响风格创意与质量。建议强度适中。"
              min={0}
              max={100}
              suffix="%"
              controls={false}
            />
          </Form.Item>
        </Collapse.Panel.Section>
      )}
      <Collapse.Panel.Section>
        <Form.Item name="promptWeight" noStyle>
          <SliderInput
            markNum={modelDefaultParams.cfgScale || 5}
            min={1}
            max={modelDefaultParams.maxCfgScale || 20}
            step={modelDefaultParams.cfgScaleStep || 0.5}
            title="提示词强度"
            tooltip="值越高，生成的结果就越接近提示词内容，但较高的值容易降低生成质量，建议使用推荐默认值。"
          />
        </Form.Item>
      </Collapse.Panel.Section>

      <Collapse.Panel.Section>
        <Form.Item name="quantity" noStyle>
          <SliderInput
            title="生成张数"
            tooltip="生成张数越多，耗时越久。"
            min={1}
            max={4}
            disabled={disableQuantity}
          />
        </Form.Item>
      </Collapse.Panel.Section>
      {modelMvType === ModelMvType.External ? null : <SamplerType />}
      {modelMvType === ModelMvType.External ? null : (
        <Collapse.Panel.Section>
          <Form.Item name={['sampler', 'steps']} noStyle>
            <SliderInput
              title="采样步骤"
              tooltip="生成图片所需的步骤，步骤越多耗时越久，通常25个步骤足以获得高质量的图片。"
              min={1}
              max={50}
              step={1}
            />
          </Form.Item>
        </Collapse.Panel.Section>
      )}
      {modelMvType === ModelMvType.External ? null : (
        <Collapse.Panel.Section>
          <Form.Item name="seed">
            <SeedInput />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.seed !== currentValues.seed
            }
          >
            {({ getFieldValue }) => {
              const seed = getFieldValue('seed');

              return seed === RANDOM_SEED_VALUE ? (
                <Form.Item name="batches">
                  <SliderInput
                    title="生成批次"
                    tooltip="随机多个Seed生成多批次图片"
                    min={1}
                    max={4}
                    step={1}
                  />
                </Form.Item>
              ) : null;
            }}
          </Form.Item>
          {isFluxModel ? null : (
            <Form.Item name="moreDetailWeight">
              <SliderInput
                title="画面细节"
                tooltip=" 正向可丰富画面线条细节，负向可使内容更具扁平化。"
                min={-2}
                max={2}
                step={0.1}
                origin={0}
              />
            </Form.Item>
          )}
        </Collapse.Panel.Section>
      )}

      {isFluxModel || modelMvType === ModelMvType.External ? null : (
        <FacialRepair />
      )}
    </div>
  );
}
