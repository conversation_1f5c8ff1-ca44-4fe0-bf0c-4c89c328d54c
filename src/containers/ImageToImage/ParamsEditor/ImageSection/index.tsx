import type { ImageToImageParams } from '../types';
import { Form, Switch, Image } from 'antd';
import { Collapse, Upload, Title } from '@/components';
import { FaceSimilarity } from './FaceSimilarity';
import { trackEvent } from '@/services';
import { AppModuleParam } from '@/types';
import { DraftType } from '@/api/types';
import styles from '../index.module.less';
import { useNavigate } from 'react-router-dom';
import { useImageToImagEditorConfig } from '../../hooks';
import { getSource } from '@/utils';
import { ImageToImageContext } from '../../context';
import { useContext } from 'react';
import { ModelMvType } from '@/constants/model';

interface DraggableUploadProps {
  value?: string;
  onChange?: (value: string) => void;
}

function DraggableUpload({ value, onChange }: DraggableUploadProps) {
  const form = Form.useFormInstance<ImageToImageParams>();
  const onValueChange = (url: string, previewUrl?: string) => {
    onChange?.(previewUrl ?? '');

    if (!!previewUrl) {
      trackEvent('whee_upload_image_success', {
        function: AppModuleParam.ImageToImage
      });
    }

    form.setFieldValue(['image', 'preview'], previewUrl);

    if (!previewUrl) {
      return;
    }

    const getNamePath = (index: number) => [
      'controlNet',
      index,
      'imageProcessParams'
    ];
    const controlNetNumbers = form.getFieldValue('controlNet')?.length ?? 0;

    for (let i = 0; i < controlNetNumbers; ++i) {
      const imageProcessParamsPath = getNamePath(i);
      // 重新上传时 默认选中新上传的原图
      const imageProcessParams = form.getFieldValue(imageProcessParamsPath);
      // 如果controlnet没有开启 不需要替换为原图
      if (imageProcessParams && imageProcessParams.model) {
        form.setFieldValue([...imageProcessParamsPath, 'image'], previewUrl);
      }
    }
  };

  const track = () => {
    trackEvent('whee_upload_image_click', {
      function: AppModuleParam.ImageToImage
    });
  };

  return (
    <div onClick={track}>
      <Upload.Dragger
        value={value}
        onDrop={track}
        onChange={onValueChange}
        taskCategory={DraftType.IMAGE_TO_IMAGE}
      />
    </div>
  );
}

/** 图片识别 */
function FacialDetection() {
  return (
    <Collapse.Panel.Section
      title={
        <Title
          tooltip="开启后会自动识别原图内的人像，自动优化创意结果。"
          title="图片识别"
        />
      }
      extra={
        <Form.Item name="facialDetection" valuePropName="checked" noStyle>
          <Switch defaultChecked />
        </Form.Item>
      }
    />
  );
}

/**
 * 参考图片模块
 */
export function ImageSection() {
  const { editorConfig } = useImageToImagEditorConfig();
  const navigate = useNavigate();
  const source = getSource();
  const { modelMvType } = useContext(ImageToImageContext);
  // 如果是flux 模型， mvtype 为 specialBeta
  const isFluxModel = modelMvType === ModelMvType.SpecialBeta;

  const isExternalModel = modelMvType === ModelMvType.External;

  // console.log('isExternalModel', isExternalModel, isFluxModel, isFluxModel || isExternalModel);

  return (
    <>
      <Collapse.Panel.Section
        title={
          <div className={styles.titleBox}>
            <strong>上传原图</strong>
            {editorConfig?.functionEntry?.content && (
              <div
                className={styles.rightBox}
                onClick={() => {
                  trackEvent('whee_edit_page_click', {
                    function: source,
                    clickType: AppModuleParam.IPDesign
                  });
                  navigate(editorConfig?.functionEntry?.scheme ?? '', {
                    state: {
                      source
                    }
                  });
                }}
              >
                <Image
                  className={styles.icon}
                  src={editorConfig?.functionEntry?.icon}
                  preview={false}
                />
                <span>{editorConfig?.functionEntry?.content}</span>
              </div>
            )}
          </div>
        }
      >
        <Form.Item name={['image', 'key']}>
          <DraggableUpload />
        </Form.Item>
      </Collapse.Panel.Section>
      <div
        style={{
          display: isFluxModel || isExternalModel ? 'none' : 'block'
        }}
      >
        <FacialDetection />
        <Form.Item name="faceSimilarity" noStyle>
          <FaceSimilarity />
        </Form.Item>
      </div>
    </>
  );
}
