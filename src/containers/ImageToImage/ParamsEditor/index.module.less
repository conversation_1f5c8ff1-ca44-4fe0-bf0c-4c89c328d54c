@import '~@/styles/variables.less';

:global(.@{ant-prefix}-image) {
  :global(.@{ant-prefix}-image-img) {
    &.icon {
      width: 36px;
      height: 18px;
      margin-top: -2px;
      margin-left: 4px;
    }
  }
}

.title-box {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .right-box {
    padding: 2px 8px;
    border-radius: 999px;
    background: rgba(53, 73, 255, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }

    .icon {
      width: 14px;
      height: 14px;
    }

    span {
      color: @content-system-link;
      font-size: 13px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px;
      margin-left: 4px;
    }
  }
}

.not-support {
  color: @content-system-quaternary;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  text-align: center;
}
.tips {
  color: var(--system-content-thirdary, #6a7b94);
  /* text_12 */
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}
