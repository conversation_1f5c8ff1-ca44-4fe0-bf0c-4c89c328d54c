import React, { createContext } from 'react';
import { ImageTasks } from '@/api/types';
import { ImageToImageParams } from './ParamsEditor/types';
import { ParamsEditorRef } from './ParamsEditor';
import { MeiDouPriceRef } from '@/components/MeiDouPrice';
import { ModelMvType } from '@/constants/model';

export interface ImageToImageContextValue {
  /** 图生图任务 */
  tasks: ImageTasks;

  /** 设置图生图任务 */
  setTasks: React.Dispatch<React.SetStateAction<ImageTasks>>;

  /** 图生图页面级别loading */
  loading: boolean;

  setLoading: React.Dispatch<React.SetStateAction<boolean>>;

  /** 重置图生图任务 */
  resetTasks: () => void;

  paramsEditorRef: React.MutableRefObject<ParamsEditorRef | null>;

  reeditImageToImageParams?: (params: Partial<ImageToImageParams>) => void;
  userSubmitValue: string;
  setUserSubmitValue: React.Dispatch<React.SetStateAction<string>>;

  meidouPriceRef: React.RefObject<MeiDouPriceRef>;
  modelMvType: ModelMvType;
  setModelMvType: (type: number) => void;
  deepSeekPrompt: {
    prompt: string;
    content: string;
    direct: string;
  };
  setDeepSeekPrompt: (prompt: string, content: string, direct: string) => void;

  /**
   * 用来标记是否正在重新编辑
   * 重新编辑可能导致基础模型的类型发生改变（mv -> flux），这会导致清空controlNet和风格模型（业务需求T，T）
   *
   * 但这个时候，不希望清空controlNet，这个标志就是用来跳过清空controlNet和风格模型的
   */
  reeditFlag: React.MutableRefObject<boolean>;
  isModifyContent: boolean;
  setIsModifyContent: (isModifyContent: boolean) => void;
  sizeRatio: any[];
  setSizeRatio: (sizeRatio: any[]) => void;
  modelDefaultParams: any;
  setModelDefaultParams: (modelDefaultParams: any) => void;
}

function initialContextMethod() {
  console.warn('正在使用 `ImageToImageContext` 的默认值');
}

export const ImageToImageContext = createContext<ImageToImageContextValue>({
  tasks: [],
  loading: false,
  setLoading: initialContextMethod,
  setTasks: initialContextMethod,
  resetTasks: initialContextMethod,
  paramsEditorRef: { current: null },
  meidouPriceRef: { current: null },
  reeditImageToImageParams: initialContextMethod,
  /**v1.9.6 新增需求记录 记录用户点击智能联想之前字段 */
  userSubmitValue: '',
  setUserSubmitValue: initialContextMethod,
  modelMvType: ModelMvType.Normal,
  setModelMvType: initialContextMethod,
  deepSeekPrompt: {
    prompt: '',
    content: '',
    direct: ''
  },
  setDeepSeekPrompt: initialContextMethod,
  reeditFlag: { current: false },
  isModifyContent: false,
  setIsModifyContent: initialContextMethod,
  sizeRatio: [],
  setSizeRatio: initialContextMethod,
  modelDefaultParams: {},
  setModelDefaultParams: initialContextMethod
});
