import {
  type ReactNode,
  useMemo,
  useContext,
  useState,
  useRef,
  useCallback
} from 'react';
import { ImageToImageContext } from './context';
import type { ImageToImageContextValue } from './context';
import { ImageTasks } from '@/api/types';
import { ImageToImageParams } from './ParamsEditor/types';
import { ParamsEditorRef } from './ParamsEditor';
import { MeiDouPriceRef } from '@/components/MeiDouPrice';
import { EditorConfigStoreProvider } from '@/hooks/useEditorConfigStore';

export interface ProviderProps {
  children?: ReactNode;

  reeditImageToImageParams?: (params: Partial<ImageToImageParams>) => void;
}

export function Provider(props: ProviderProps) {
  const paramsEditorRef = useRef<ParamsEditorRef>(null);
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState<ImageTasks>([]);
  const [userSubmitValue, setUserSubmitValue] = useState<string>('');
  const meidouPriceRef = useRef<MeiDouPriceRef>(null);
  const [modelMvType, setModelMvType] = useState(0);
  const [deepSeekPrompt, setDeepSeekPrompt] = useState({
    prompt: '',
    content: '',
    direct: ''
  });
  const [isModifyContent, setIsModifyContent] = useState(false);
  const handleSetDeepSeekPrompt = useCallback(
    (prompt: string, content: string, direct: string) => {
      // console.log('prompt222', prompt, content, direct);
      setDeepSeekPrompt({ prompt, content, direct });
    },
    []
  );
  const reeditFlag = useRef(false);
  const [sizeRatio, setSizeRatio] = useState<any[]>([]);

  const handleSetSizeRatio = useCallback((sizeRatio: any[]) => {
    setSizeRatio(sizeRatio);
  }, []);

  const [modelDefaultParams, setModelDefaultParams] = useState<any>({});
  const handleSetModelDefaultParams = useCallback((modelDefaultParams: any) => {
    setModelDefaultParams(modelDefaultParams);
  }, []);

  const contextValue = useMemo(() => {
    return {
      loading,
      setLoading,
      tasks,
      setTasks,
      resetTasks() {
        setTasks([]);
      },
      userSubmitValue,
      setUserSubmitValue,
      paramsEditorRef,
      meidouPriceRef,
      reeditImageToImageParams: (params: Partial<ImageToImageParams>) => {
        paramsEditorRef.current?.reeditImageToImageParams(params);
      },
      modelMvType,
      setModelMvType,
      deepSeekPrompt,
      setDeepSeekPrompt: handleSetDeepSeekPrompt,
      reeditFlag,
      isModifyContent,
      setIsModifyContent,
      sizeRatio,
      setSizeRatio: handleSetSizeRatio,
      modelDefaultParams,
      setModelDefaultParams: handleSetModelDefaultParams
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    loading,
    tasks,
    userSubmitValue,
    modelMvType,
    setModelMvType,
    deepSeekPrompt,
    handleSetDeepSeekPrompt,
    isModifyContent,
    setIsModifyContent,
    sizeRatio,
    handleSetSizeRatio,
    modelDefaultParams,
    handleSetModelDefaultParams
  ]);

  return (
    <ImageToImageContext.Provider value={contextValue}>
      <EditorConfigStoreProvider>{props.children}</EditorConfigStoreProvider>
    </ImageToImageContext.Provider>
  );
}

export function useImageToImageContext() {
  return useContext<ImageToImageContextValue>(ImageToImageContext);
}
