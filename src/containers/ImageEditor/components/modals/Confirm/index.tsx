import { Modal, ModalProps } from 'antd';
import styles from './index.module.less';
import { CrossBlack } from '@meitu/candy-icons';
import classNames from 'classnames';

type ConfirmProps = React.PropsWithChildren<{
  // 取消按钮的文本
  cancelTitle?: string;

  // 确定按钮的文本
  okTitle?: string;

  // 右上角关闭点击的回调
  onClickClose?(): void;

  // 取消按钮点击的回调
  onClickCancel?(): void;

  // 确定按钮点击的回调
  onClickOk?(): void;

  contentClassName?: string;
}> &
  ModalProps;

export function Confirm(props: ConfirmProps) {
  const {
    children,
    cancelTitle = '取消',
    okTitle = '确定',
    onClickClose,
    onClickCancel,
    onClickOk,
    contentClassName,
    ...modalProps
  } = props;

  return (
    <Modal
      {...modalProps}
      rootClassName={styles.confirmModal}
      closeIcon={null}
      footer={null}
    >
      <div className={styles.confirm}>
        <div className={classNames('confirm-content', contentClassName)}>
          {children}
        </div>

        <div className="confirm-footer">
          <div
            className="confirm-footer-btn confirm-footer-btn-cancel"
            onClick={onClickCancel}
          >
            {cancelTitle}
          </div>
          <div
            className="confirm-footer-btn confirm-footer-btn-ok"
            onClick={onClickOk}
          >
            {okTitle}
          </div>
        </div>

        <div className="confirm-close-btn" onClick={onClickClose}>
          <CrossBlack />
        </div>
      </div>
    </Modal>
  );
}
