@import '~@/styles/variables.less';

.history-box {
  width: 100%;
  max-width: 100%;
  border-top: 1px solid #29292e;
  padding: 15px 15px 0;
  background: var(--background-editorMainPanel, #17171a);
  z-index: 1;
  min-height: 190px;

  .title-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;

    .arrow {
      transform: rotate(180deg);
    }

    span {
      color: #7f818a;
      transition: all 0.2s;
    }
  }

  .content-box {
    height: 159px;
    display: flex;
    justify-content: center;
    align-items: center;

    .list-box {
      width: 100%;
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      overflow-y: auto;
      padding-bottom: 50px;
      scrollbar-width: none;
      align-content: flex-start;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }

      .list-item {
        box-sizing: border-box;
        cursor: pointer;
        width: 48px;
        height: 48px;
        position: relative;

        &.active {
          border-radius: 4px;
          border: 1px solid #4053ff;
          padding: 1px;
        }

        .name-box {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 30px;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.75) 100%
          );
          display: flex;
          justify-content: center;
          align-items: flex-end;
          padding-bottom: 2px;
          color: #fff;
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }

        :global .ant-image {
          width: 100%;
          height: 100%;
        }

        .image {
          width: 100%;
          height: 100%;
          border-radius: 4px;
          background: var(
            --background-editorSpaceHolder,
            rgba(235, 238, 255, 0.08)
          );
          object-fit: contain;
        }

        .err-img {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          border-radius: 4px;
          background: var(
            --background-editorSpaceHolder,
            rgba(235, 238, 255, 0.08)
          );

          svg {
            width: 20px;
            height: 20px;
            color: #5e5e6b;
          }
        }
      }
    }

    .empty-box {
      margin: 50px auto;
      color: var(--content-editorTertiary, #5e5e6b);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      transform: translateY(-50%);
    }
  }
}
