import { ChevronUpBlack } from '@meitu/candy-icons';
import styles from './index.module.less';
import { useEffect, useRef, useState } from 'react';
import { observer, useLocalObservable } from 'mobx-react';
import classNames from 'classnames';
import { Image as ImageAntd } from 'antd';
import { Loading } from '@/components';
import useStore from '@/hooks/useStore';
import { fetchHistoryList } from '@/api/imageEditor';
import { ResultImage } from '@/api/types';
import { TaskCategory } from '@/api/types/imageEditor';
import {
  BaseShape,
  CanvasNodeEventName,
  CanvasViewEventName,
  ShapeConfig
} from '@/editor/core/src';
import { useMouseResize } from '@/containers/ImageEditor/hooks/useMouseResizer';
import { optimizeImage } from '@/containers/ImageEditor/utils/image';
import { trackEvent } from '@/services';
import { showTaskHistoryTutorials } from '@/containers/ImageEditor/tutorials/taskHistroy';

type ResultImageType = ResultImage & { type?: string };

export const LayerHistoryBoxId = 'layer-history';

function HistoryList() {
  const editorStore = useStore('EditorStore');
  const {
    editor: { app },
    getTasksByLayer,
    activeLayerId,
    setExtendCompleteCallback,
    setLayerListHover,
    maxEditorHeight,
    maxEditorWidth,
    editorLayersHandler,
    setUpdateLayerHistoryList,
    layersIsOpen
  } = editorStore;

  const [showContent, setShowContent] = useState(true);
  const [activeItemIndex, setActiveItemIndex] = useState(0);
  const container = useRef<HTMLDivElement>(null);
  const contentBox = useRef<HTMLDivElement>(null);

  const local = useLocalObservable(() => ({
    currentUrl: '',
    historyList: [] as Array<ResultImageType>
  }));

  useEffect(() => {
    app.on(CanvasNodeEventName.select, handleSelectLayer);
    app.on(CanvasViewEventName.redoAfter, (data) => {
      handleOpt(data, 'redo');
    });
    app.on(CanvasViewEventName.undoAfter, (data) => {
      handleOpt(data, 'undo');
    });

    return () => {
      app.off(CanvasNodeEventName.select, handleSelectLayer);
      app.off(CanvasViewEventName.redoAfter, (data) => {
        handleOpt(data, 'redo');
      });
      app.off(CanvasViewEventName.undoAfter, (data) => {
        handleOpt(data, 'undo');
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [app]);

  //手动更新 历史历史记录
  setUpdateLayerHistoryList((url?: string) => {
    local.currentUrl = url ?? '';
    effectHistoryHandler();
  });

  // 监听快捷键的撤销回退
  const handleOpt = (
    data: {
      nodes: BaseShape<ShapeConfig>[];
      oprations: any;
    },
    type: string
  ) => {
    const { oprations } = data;
    oprations?.forEach((operation: any) => {
      const { afterData, preData } = operation.options;

      (type === 'redo' ? afterData : preData)?.forEach((option: any) => {
        const currentUrl = option.image?.currentSrc;
        // 设置当前画板展示的图片
        local.currentUrl = currentUrl ?? '';
        // 不匹配kt参数
        const index = local.historyList.findIndex(
          (item) => item.url.split('?')[0] === local.currentUrl.split('?')[0]
        );

        setActiveItemIndex(index === -1 ? 0 : index);
      });
    });
  };

  // 画布选中图层回调
  const handleSelectLayer = (payload: { nodes: BaseShape<ShapeConfig>[] }) => {
    const { nodes } = payload;
    const first = nodes[0];
    if (!first) {
      return;
    }
    const currentUrl = first.config.image?.currentSrc;
    // 设置当前画板展示的图片
    local.currentUrl = currentUrl ?? '';
  };

  const effectHistoryHandler = () => {
    if (!activeLayerId) return;

    const temp = getTasksByLayer(activeLayerId);
    if (!temp || Object.keys(temp).length === 0) {
      local.historyList = [];
    } else {
      let foo = {} as Record<TaskCategory, string>;
      for (const key in temp) {
        foo[key as TaskCategory] = temp[key as TaskCategory].join(',');
      }
      getHistoryList(JSON.stringify(foo));
    }
  };

  // 设置扩图完成的回调
  setExtendCompleteCallback((url: string) => {
    // 设置扩完图 当前画板展示的图片
    local.currentUrl = url ?? '';
    effectHistoryHandler();
  });

  const layerContentBox = document.getElementById('layerContentBox');
  const firstFlag = useRef(true);
  // activeLayerId变化时，获取生成记录
  useEffect(() => {
    // 获取生成记录
    effectHistoryHandler();

    // 没有选中图层时候
    if (!activeLayerId && layerContentBox) {
      firstFlag.current = true;
      layerContentBox.style.paddingBottom = '0';
      return;
    }

    // 非第一次切换图层，不重置拖动后的高度
    if (firstFlag.current === false) return;

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeLayerId, getTasksByLayer]);

  // 去除相同url的记录
  function uniqueOnce(arr: ResultImageType[]) {
    return arr.reduce((pre, cur) => {
      if (pre.findIndex((item1) => item1.url === cur.url) === -1) {
        pre.push(cur);
      }
      return pre;
    }, [] as ResultImageType[]);
  }

  const getHistoryList = (params: string) => {
    // 获取原图
    const layerList = editorLayersHandler();
    const originUrl = layerList.find(
      (item) => item.id === activeLayerId
    )?.source;

    fetchHistoryList({
      typeIds: params
    }).then((res) => {
      let tempList: ResultImageType[] = [];

      res.list.forEach((item) => {
        if (item.resultImages) {
          // 生成失败的图不展示过滤掉
          const successImg = item.resultImages.filter(
            (item) => item.imageStatus === 1
          );
          tempList = [...tempList, ...successImg];
        }
      });

      if (originUrl) {
        tempList = [
          ...tempList,
          { url: originUrl, imageStatus: 1, type: 'origin' }
        ];
      }
      local.historyList = uniqueOnce(tempList);

      // 不匹配kt参数
      const index = tempList.findIndex(
        (item) => item.url.split('?')[0] === local.currentUrl.split('?')[0]
      );
      setActiveItemIndex(index === -1 ? 0 : index);
    });
  };

  // 更新画板的图片url
  const updateEditorImage = (url: string) => {
    const node = app.findShapeById(activeLayerId);
    if (!node) {
      return;
    }

    const image = new Image();
    image.src = optimizeImage(url, {
      width: maxEditorWidth,
      height: maxEditorHeight
    });
    image.crossOrigin = 'Anonymous';
    image.onload = () => {
      const imageWidth = image.width;
      const imageHeight = image.height;
      const sourceHeight = node.getAttr('target')?.config.height;
      const { width, height } = fitImage(sourceHeight, imageWidth, imageHeight);
      app.update({
        ...node.getAttr('target')?.config,
        image,
        width,
        height
      });
    };
  };

  const fitImage = (
    frameHeight: number,
    imageWidth: number,
    imageHeight: number
  ) => {
    // 修改图片是根据图片设置为容器的高度，宽度自适应
    const ratio = frameHeight / imageHeight;
    const height = frameHeight;
    const width = imageWidth * ratio;
    return {
      width,
      height
    };
  };
  useMouseResize(container.current!, {
    mouseDownHandler: () => {
      setLayerListHover(false);
    },
    mouseUpHandler: () => {
      setLayerListHover(true);
    },
    contentBox: contentBox.current!
  });

  const changeContentHandler = () => {
    setShowContent((pre) => {
      if (pre) {
        if (container.current) {
          container.current.style.height = 'auto';
          container.current.style.minHeight = '0px';
        }
      } else {
        if (container.current) {
          container.current.style.height = '190px';
          container.current.style.minHeight = '190px';
        }
      }
      return !pre;
    });
  };

  useEffect(() => {
    setTimeout(() => {
      if (container.current && layersIsOpen) {
        const rect = container.current.getBoundingClientRect();

        if (layerContentBox) {
          layerContentBox.style.paddingBottom = `${rect.height}px`;
        }
      }
    }, 50);
  }, [activeLayerId, layerContentBox, layersIsOpen]);

  // 选中图层后 需要展示新手引导
  useEffect(() => {
    if (!local.historyList.length || !editorStore.layersIsOpen) {
      return;
    }
    setTimeout(() => {
      showTaskHistoryTutorials(LayerHistoryBoxId);
    }, 500);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editorStore.layersIsOpen, local.historyList]);

  return (
    <>
      <div
        id={LayerHistoryBoxId}
        className={styles.historyBox}
        ref={container}
        style={{ display: activeLayerId ? 'initial' : 'none' }}
      >
        <div
          className={styles.titleBox}
          style={{ paddingBottom: showContent ? '16px' : '0' }}
        >
          <div>生成记录</div>
          <ChevronUpBlack
            className={classNames(showContent && styles.arrow)}
            onClick={changeContentHandler}
          />
        </div>
        {showContent && (
          <div className={styles.contentBox} ref={contentBox}>
            {local.historyList.length > 0 ? (
              <div className={styles.listBox}>
                {local.historyList.map((item, index) => (
                  <div
                    key={`${item.url}-${index}`}
                    className={classNames(
                      styles.listItem,
                      index === activeItemIndex &&
                        item.imageStatus === 1 &&
                        styles.active
                    )}
                    onClick={() => {
                      trackEvent('ai_modification_history_click', {
                        layer_id: activeLayerId
                      });

                      if (item.imageStatus !== 1) return;
                      setActiveItemIndex(index);
                      updateEditorImage(item.url);
                    }}
                  >
                    {item.url && (
                      <ImageAntd
                        src={optimizeImage(item.url, {
                          width: maxEditorWidth,
                          height: maxEditorHeight
                        })}
                        placeholder={<Loading />}
                        preview={false}
                        className={styles.image}
                      />
                    )}
                    {item.type && item.type === 'origin' && (
                      <div className={styles.nameBox}> 原图 </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className={styles.emptyBox}>该图层暂无生成记录</div>
            )}
          </div>
        )}
      </div>
    </>
  );
}

export default observer(HistoryList);
