@import '~@/styles/variables.less';

.layer-box {
  width: 100%;
  padding-bottom: 14px;
  overflow-y: auto;
  flex-grow: 1;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  .layer-list-box {
    .list-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 64px;
      padding: 0 16px;
      cursor: pointer;
      outline: none;

      &.hover-bg {
        background: var(
          --background-editorHoverOverlay,
          rgba(235, 238, 255, 0.1)
        );

        .info-name {
          color: var(--content-editorPrimary, #dcdde5) !important;
        }
      }

      &.active-bg {
        background: var(--background-editorSelectedAccentMuted, #1c2170);

        .info-name {
          color: var(--content-editorPrimary, #dcdde5) !important;
        }

        .opt-box {
          svg {
            color: var(--content-editorPrimary, #dcdde5) !important;
          }
        }
      }

      .info-box {
        display: flex;
        align-items: center;

        .img-box {
          width: 48px;
          height: 48px;
          display: flex;
          justify-content: center;
          align-items: center;
          background: rgba(235, 238, 255, 0.08);
          border-radius: 4px;
          overflow: hidden;

          &.hidden {
            opacity: 0.6;
          }

          .info-img {
            object-fit: contain;
            max-width: 48px;
            max-height: 48px;
            min-width: 48px;
          }
        }

        .info-name {
          max-width: 122px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-left: 8px;
          color: var(--content-editorSecondary, #7f818a);
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }

      .opt-box {
        display: flex;
        gap: 8px;

        span {
          color: #7f818a;
        }
      }
    }
  }

  .empty-box {
    width: 100%;
    height: calc(100% - 20px);
    display: flex;
    justify-content: center;
    align-items: center;

    .empty {
      width: 56px;
      height: 72px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 12px;
      color: var(--content-editorTertiary, #5e5e6b);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;

      span {
        color: #5e5e6b;

        svg {
          width: 40px;
          height: 40px;
        }
      }
    }
  }
}

.layer-dropdown-overlay {
  width: 120px;
  border-radius: 4px;
  background: var(--background-editorPopup, #252529);
  box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.25);

  &:global(.@{ant-prefix}-dropdown) :global .@{ant-prefix}-dropdown-menu {
    box-shadow: none;
    background: var(--background-editorPopup, #252529);
    padding: 6px 0;

    .@{ant-prefix}-dropdown-menu-item {
      overflow: hidden;
      color: var(--content-editorPrimary, #dcdde5);
      text-overflow: ellipsis;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      padding: 8px 16px;
      border-radius: 0;

      &:hover {
        background: var(
          --background-editorHoverOverlay,
          rgba(235, 238, 255, 0.1)
        );
        color: var(--content-editorPrimaryHover, #fff);
      }
    }

    .@{ant-prefix}-dropdown-menu-item-active {
      background: var(
        --background-editorHoverOverlay,
        rgba(235, 238, 255, 0.1)
      );
      color: var(--content-editorPrimaryHover, #fff);
    }

    .@{ant-prefix}-dropdown-menu-item-divider {
      background-color: #f0f2ff;
      opacity: 0.08;
    }
  }
}

.tool-tip {
  :global .@{ant-prefix}-tooltip-inner {
    color: var(--content-editorPrimaryReverse, #17171a) !important;
  }
}
