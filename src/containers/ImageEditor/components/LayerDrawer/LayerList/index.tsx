import {
  EllipsisV<PERSON>icalBlack,
  InvisibleBold,
  Layer,
  VisibleBold
} from '@meitu/candy-icons';
import { observer } from 'mobx-react';
import styles from './index.module.less';
import { Dropdown, Image, MenuProps, Button } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Loading } from '@/components';
import classNames from 'classnames';
import {
  DragDropContext,
  Droppable,
  Draggable,
  DraggableLocation
} from 'react-beautiful-dnd';
import useStore from '@/hooks/useStore';
import {
  CanvasDragEventName,
  CanvasNodeEventName
} from '@/editor/core/src/types';
import { ShapeConfig } from 'konva/lib/Shape';
import { BaseShape, ShapeType } from '@/editor/core/src/shape';
import Konva from 'konva';
import { SyncStatus } from '@/types/imageEditor';
import { uploaderFunc } from '@/containers/ImageEditor/utils/upload';
import { saveImageEditor } from '@/api/imageEditor';
import message from '@/containers/ImageEditor/components/Toast';
import { elementWithinViewport } from '@/utils/domUtil';
import { trackEvent } from '@/services';
import { TaskCategory } from '@/api/types/imageEditor';
import { optimizeImage } from '@/containers/ImageEditor/utils/image';

const TIMEOUT = 30 * 1000;

const LayerList = observer(() => {
  const editorStore = useStore('EditorStore');
  const {
    editor: { app, selectorPlugin },
    editorLayersHandler,
    activeLayerId,
    setActiveLayerId,
    setInitializeCallback,
    setSyncStatus,
    exportCoverImage,
    setUploadImagePromise,
    isEditorInit,
    layerListHover,
    layersIsOpen
  } = editorStore;
  const [openDropdown, setOpenDropdown] = useState(false);
  const [hoverLayerIndex, setHoverLayerIndex] = useState<null | number>(null);
  const [layerList, setLayerList] = useState<Array<ShapeConfig>>([]);
  const messageRef = useRef<any>(null);
  const [online, setOnline] = useState(navigator.onLine);

  // 同步图层数据
  const syncHistory = (param?: any) => {
    setLayerList(editorLayersHandler());
    // safari 15\16 会出现无法保存的问题，暂时关闭
    // if (!isEditorInit) {
    //   return;
    // }
    if (!param) {
      editorStore.saveQueue.runSaveTask(saveProject);
      return;
    }
    if (!param?.nodes) {
      editorStore.saveQueue.runSaveTask(saveProject);
      return;
    }
    if (
      param?.nodes.find(
        (item: any) =>
          item.type === ShapeType.Image || item.type === ShapeType.Paint
      )
    ) {
      editorStore.saveQueue.runSaveTask(saveProject);
      return;
    }
    if (
      param?.nodes.find(
        (item: any) =>
          item?.config?.type === ShapeType.Image ||
          item?.config?.type === ShapeType.Paint
      )
    ) {
      editorStore.saveQueue.runSaveTask(saveProject);
      return;
    }
  };
  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    let projectId =
      queryParams.get('projectId') || queryParams.get('project_id');
    if (!isEditorInit) {
      return;
    }
    if (projectId) {
      return;
    }
    editorStore.saveQueue.runSaveTask(saveProject);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEditorInit]);

  const saveProject = useCallback(
    async (type?: string) => {
      setSyncStatus(SyncStatus.SYNCING);

      const layers = editorLayersHandler();
      const editorParams = {
        ...editorStore.projectInfo,
        layers: layers
      };
      try {
        let uploadImagePromise = uploaderFunc(
          await exportCoverImage(),
          'jpg',
          TIMEOUT
        );
        setUploadImagePromise(uploadImagePromise);
        let res = await uploadImagePromise;
        const result = await saveImageEditor({
          projectId: editorStore.projectInfo.id,
          version: editorStore.projectInfo.currentVersion,
          /**此图片是base64，如需oss存储，自行上传 */
          picUrl: res?.data || '',
          editorParams: JSON.stringify(editorParams)
        });
        if (result) {
          if (messageRef.current) {
            // console.log(messageRef.current, 'messageRef.current');
            messageRef.current.destroy();
            message({
              type: 'success',
              content: '保存成功',
              duration: 3000
            });
            messageRef.current = null;
          }
          trackEvent('modification_save_success', {
            project_id: editorStore.projectInfo.id,
            save_type: type ? type : 'auto'
          });
          editorStore.setProjectInfo({
            ...editorStore.projectInfo,
            id: result.projectId,
            picUrl: res?.previewUrl || '',
            currentVersion: result.version
          });

          setTimeout(() => {
            setSyncStatus(SyncStatus.SUCCESS);
          }, 100);
          setTimeout(() => {
            setSyncStatus(SyncStatus.INIT);
          }, 1000);
        }

        return result;
      } catch (error: any) {
        // console.log(error, 'error');
        setSyncStatus(SyncStatus.FAILED);
        trackEvent('modification_save_fail', {
          project_id: editorStore.projectInfo.id,
          save_type: type ? type : 'auto',
          error_code: error.code
        });
        if (!online) {
          if (messageRef.current) {
            messageRef.current.destroy();
            messageRef.current = null;
          }
          messageRef.current = message({
            type: 'error',
            content: '保存失败，项目不存在',
            customNode: (
              <Button
                type="link"
                onClick={() => {
                  window.location.replace('/ai/image-editor');
                }}
              >
                点击刷新
              </Button>
            )
          });
          return;
        } else {
          if (error.code === '10022' || error.code === '10005') {
            message({
              type: 'error',
              content: '保存失败，重新登录' ?? '保存失败，请重试',
              customNode: (
                <Button
                  type="link"
                  onClick={() => {
                    window.location.reload();
                  }}
                >
                  刷新页面
                </Button>
              )
            });
          } else if (error.code === '11018') {
            message({
              type: 'info',
              content: '当前项目内容发生变更, 更新中...',
              duration: 3000,
              isShowMask: true
            });
            setTimeout(() => {
              window.location.reload();
            }, 3000);
          } else if (error.code === '11019') {
            if (messageRef.current) {
              messageRef.current.destroy();
              messageRef.current = null;
            }
            messageRef.current = message({
              type: 'error',
              content: '保存失败，项目已删除',
              customNode: (
                <Button
                  type="link"
                  onClick={() => {
                    window.location.replace('/ai/image-editor');
                  }}
                >
                  点击刷新
                </Button>
              )
            });
          } else {
            if (messageRef.current) {
              messageRef.current.destroy();
              messageRef.current = null;
            }
            messageRef.current = message({
              type: 'error',
              content: '保存失败，请重试',
              customNode: (
                <Button
                  type="link"
                  onClick={() => {
                    saveProject('manual');
                  }}
                >
                  点击保存
                </Button>
              )
            });
          }
        }
      }
    },
    [
      editorLayersHandler,
      editorStore,
      exportCoverImage,
      online,
      setSyncStatus,
      setUploadImagePromise
    ]
  );

  // 初始化完回调
  setInitializeCallback(syncHistory);

  // 画布添加图层回调
  const handleAddLayer = (payload: { nodes: BaseShape<ShapeConfig>[] }) => {
    const { nodes } = payload;
    if (
      !(
        nodes?.[0] instanceof Konva.Image ||
        nodes?.[0]?.instance instanceof Konva.Image
      )
    )
      return;
    syncHistory();
    //如果是涂鸦模式通过选中图层、图形事件生成的，不选中生成的图层
    if (
      editorStore.currentFeature === TaskCategory.graffiti &&
      editorStore.graffitiEditor.isSelectShapeSubmit
    ) {
      return;
    }

    if (!editorStore.autoSelectNewLayerEnabled) {
      return;
    }

    layerClicked(payload.nodes[0].config.id ?? '');
  };

  // 画布选中图层回调
  const handleSelectLayer = async (payload: {
    nodes: BaseShape<ShapeConfig>[];
  }) => {
    const { nodes } = payload;
    const first = nodes[0];
    if (!first) {
      return;
    }

    first.config.id && setActiveLayerId(first.config.id);
  };

  // 画布取消选中图层回调
  const handleUnSelectLayer = () => {
    setActiveLayerId('');
  };
  const _saveProject = useCallback(() => {
    editorStore.saveQueue.runSaveTask(saveProject);
  }, [editorStore.saveQueue, saveProject]);
  const handleOnline = () => setOnline(true);
  const handleOffline = () => setOnline(false);
  const contextMenuHandler = (e: Event) => {
    e.preventDefault();
  };

  useEffect(() => {
    window.addEventListener('customSave', _saveProject);
    return () => {
      window.removeEventListener('customSave', _saveProject);
    };
  }, [_saveProject]);

  useEffect(() => {
    app.on(CanvasNodeEventName.add, handleAddLayer);
    app.on(CanvasNodeEventName.zIndexChanged, syncHistory);
    app.on(CanvasNodeEventName.remove, syncHistory);
    app.on(CanvasNodeEventName.updateAfter, syncHistory);
    app.on(CanvasDragEventName.end, _saveProject);
    app.on(CanvasNodeEventName.transformEnd, _saveProject);
    app.on(CanvasNodeEventName.select, handleSelectLayer);
    app.on(CanvasNodeEventName.unselect, handleUnSelectLayer);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    document
      .querySelector('#scrollableBox')
      ?.addEventListener('contextmenu', contextMenuHandler);
    return () => {
      app.off(CanvasNodeEventName.add, handleAddLayer);
      app.off(CanvasNodeEventName.zIndexChanged, syncHistory);
      app.off(CanvasNodeEventName.remove, syncHistory);
      app.off(CanvasNodeEventName.updateAfter, syncHistory);
      app.off(CanvasNodeEventName.transformEnd, _saveProject);
      app.off(CanvasNodeEventName.select, handleSelectLayer);
      app.off(CanvasNodeEventName.unselect, handleUnSelectLayer);
      app.off(CanvasDragEventName.end, _saveProject);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      document
        .querySelector('#scrollableBox')
        ?.removeEventListener('contextmenu', contextMenuHandler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [app, editorStore.projectInfo, isEditorInit, online]);

  // 点击列表的图层
  const layerClicked = (id: string) => {
    const node = app.findShapeById(id);
    if (!node) {
      return;
    }
    selectorPlugin.selector?.selectNodes([node]);
  };

  // 拖动结束
  const handleDragEnd = (result: {
    destination: DraggableLocation | null | undefined;
    source: { index: number };
  }) => {
    const { destination, source } = result;
    if (!destination) {
      return;
    }

    const startIndex = source.index;
    const endIndex = destination.index;
    const dragId = layerList[startIndex].id;

    app.moveZIndexById(dragId || '', startIndex - endIndex);
  };

  useEffect(() => {
    // 当前无选择的图层，手动隐藏一下操作下拉栏
    if (activeLayerId === '') {
      setOpenDropdown(false);
    }
  }, [activeLayerId]);

  // 下拉操作点击埋点
  const optTrackEvent = (type: string) => {
    trackEvent('ai_modification_layer_click', {
      layer_id: activeLayerId,
      click_type: type
    });
  };

  // 下拉操作
  const menuItems = useMemo<NonNullable<MenuProps['items']>>(() => {
    const activeIndex = layerList.findIndex(
      (item) => item.id === activeLayerId
    );
    const isTop = activeIndex === 0;
    const isBottom = activeIndex === layerList.length - 1;

    return [
      {
        key: 'copy',
        label: '复制',
        onClick() {
          optTrackEvent('copy');
          selectorPlugin.selector?.copy();
          selectorPlugin.selector?.paste();
        }
      },
      {
        type: 'divider'
      },
      {
        key: 'last',
        label: '上一层',
        onClick() {
          optTrackEvent('last');
          if (isTop) return;
          app.moveZIndexById(activeLayerId, 'up');
        }
      },
      {
        key: 'next',
        label: '下一层',
        onClick() {
          optTrackEvent('next');
          if (isBottom) return;
          app.moveZIndexById(activeLayerId, 'down');
        }
      },
      {
        key: 'top',
        label: '置顶',
        onClick() {
          optTrackEvent('top');
          if (isTop) return;
          app.moveZIndexById(activeLayerId, 'top');
        }
      },
      {
        key: 'bottom',
        label: '置底',
        onClick() {
          optTrackEvent('bottom');
          if (isBottom) return;
          app.moveZIndexById(activeLayerId, 'bottom');
        }
      },
      {
        type: 'divider'
      },
      {
        key: 'delete',
        label: '删除',
        onClick() {
          optTrackEvent('delete');
          const node = app.findShapeById(activeLayerId);
          if (!node) {
            return;
          }

          const shape = node.getAttr('target');
          selectorPlugin.selector?.cancelSelect();
          app.remove(shape);
        }
      }
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeLayerId, app, layerList, selectorPlugin.selector]);

  // ***************选中的图层，需要滚到视口内逻辑***************
  const itemRefs = useRef<Array<HTMLDivElement | null>>([]);
  useEffect(() => {
    const index = layerList.findIndex((item) => item.id === activeLayerId);
    const desiredElement = itemRefs.current?.[index];
    // 当前元素不在视图内，让页面滚动到元素所在位置(整个item都在试图内，才会返回true)
    if (
      desiredElement &&
      !elementWithinViewport(desiredElement, {
        paddingTop: 120,
        // 生成记录高度 + item高度
        paddingBottom: 250
      })
    ) {
      setTimeout(() => {
        scrollTo(desiredElement?.offsetTop - 10);
      }, 100);
    }
    // 切换图层选中；展开面板；图层列表变化；都需要重新设置滚动位置
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeLayerId, layersIsOpen, layerList]);

  const scrollTo = (num: number) => {
    const scrollableBox = document.getElementById('scrollableBox');
    scrollableBox?.scrollTo?.({ top: num, behavior: 'smooth' });
  };

  useEffect(() => {
    if (!activeLayerId) {
      return;
    }

    // 选中图层的时候 需要询问用户是否退出
    async function exitChangePicture() {
      const exit = await editorStore.confirmExitChangePictureHandler();
      if (!exit) {
        selectorPlugin.selector?.cancelSelect();
        return;
      }
    }

    if (editorStore.currentFeature === TaskCategory.inpaint) {
      exitChangePicture();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeLayerId]);

  return (
    <div className={styles.layerBox} id="scrollableBox">
      {layerList.length > 0 ? (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="droppableBox">
            {(provided) => (
              <div
                className={styles.layerListBox}
                {...provided.droppableProps}
                ref={provided.innerRef}
              >
                {layerList.map((item, index) => (
                  <Draggable
                    key={item.id}
                    draggableId={String(item.id)}
                    index={index}
                  >
                    {(provided) => (
                      <div
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        ref={provided.innerRef}
                        className={classNames(
                          styles.listItem,
                          item.id === activeLayerId && styles.activeBg,
                          index === hoverLayerIndex && styles.hoverBg
                        )}
                        onClick={async () => {
                          if (item.id === activeLayerId) return;

                          app.unlockAllShapes();

                          // 点击右侧图层列表 需要让用户确认是否退出改图
                          if (
                            editorStore.currentFeature === TaskCategory.inpaint
                          ) {
                            const exit =
                              await editorStore.confirmExitChangePictureHandler();
                            if (!exit) {
                              selectorPlugin.selector?.cancelSelect();
                              return;
                            }
                          }

                          // 更新画布图层选中
                          layerClicked(String(item.id));
                        }}
                        onMouseEnter={() => {
                          if (!layerListHover) return;
                          if (item.id === activeLayerId) return;
                          setHoverLayerIndex(index);
                        }}
                        onMouseLeave={() => {
                          setHoverLayerIndex(null);
                        }}
                      >
                        <div
                          className={styles.infoBox}
                          ref={(el) => (itemRefs.current[index] = el)}
                        >
                          <div
                            className={classNames(
                              styles.imgBox,
                              item.hidden && styles.hidden
                            )}
                          >
                            {item.url && (
                              <Image
                                src={
                                  item.url.includes('svg')
                                    ? item.url
                                    : optimizeImage(item.url, {
                                        width: 56,
                                        height: 56
                                      })
                                }
                                placeholder={<Loading />}
                                preview={false}
                                className={styles.infoImg}
                              />
                            )}
                          </div>
                          <div className={styles.infoName}>{item.name}</div>
                        </div>
                        {/* 操作 */}
                        <div className={styles.optBox}>
                          {(index === hoverLayerIndex ||
                            item.id === activeLayerId) && (
                            <Dropdown
                              overlayClassName={styles.layerDropdownOverlay}
                              trigger={['click']}
                              menu={{ items: menuItems }}
                              open={openDropdown && item.id === activeLayerId}
                              onOpenChange={setOpenDropdown}
                              dropdownRender={(menu) => menu}
                            >
                              <EllipsisVerticalBlack />
                            </Dropdown>
                          )}
                          {(index === hoverLayerIndex ||
                            item.id === activeLayerId ||
                            item.hidden) && (
                            <div
                              onClick={(e) => {
                                // 小眼睛 图层显示隐藏
                                e.stopPropagation();
                                const node = app.findShapeById(
                                  item.id as string
                                );
                                if (!node) {
                                  return;
                                }
                                const { config } = node.getAttr('target');
                                app.update({
                                  ...config,
                                  visible: !config.visible
                                });
                                // 隐藏的是当前选中的，取消画布选择框
                                if (item.id === activeLayerId) {
                                  selectorPlugin.selector?.cancelSelect();
                                }
                              }}
                              className="pic-layer-btn"
                            >
                              {!item.hidden ? (
                                <VisibleBold />
                              ) : (
                                <InvisibleBold />
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </Draggable>
                ))}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      ) : (
        <div className={styles.emptyBox}>
          <div className={styles.empty}>
            <Layer />
            <div>暂无图层</div>
          </div>
        </div>
      )}
    </div>
  );
});

export default LayerList;
