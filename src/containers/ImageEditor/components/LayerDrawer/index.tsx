import { CrossBlack, LayerBold } from '@meitu/candy-icons';
import styles from './index.module.less';
import { Drawer } from 'antd';
import LayerList from './LayerList';
import HistoryList from './HistoryList';
import { observer } from 'mobx-react-lite';
import useStore from '@/hooks/useStore';
import { useEffect } from 'react';

export const LayerDrawer = observer(() => {
  const editorStore = useStore('EditorStore');
  const openDrawer = editorStore.layersIsOpen;
  const setOpenDrawer = editorStore.setLayersIsOpen;
  useEffect(() => {
    const layerDrawerBox = document.getElementById('layer-drawer-box');
    const width = layerDrawerBox?.clientWidth || 0;
    //更新鼠标操作位置
    editorStore.editor.app.setCheckMouseBoundaryConfig({
      right: openDrawer ? 50 + width : 50 + 15
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openDrawer]);

  return (
    <div className={styles.layerDrawerBox} id="layer-drawer-box">
      {/* 图层入口 */}
      <div
        className={styles.entryBtn}
        onClick={() => {
          setOpenDrawer(true);
        }}
      >
        <LayerBold />
        <span>图层</span>
      </div>

      <Drawer
        placement="right"
        closable={false}
        onClose={() => {}}
        open={openDrawer}
        getContainer={false}
        mask={false}
        width={254}
        rootClassName={styles.drawerBox}
        forceRender={true}
      >
        <div className={styles.topBox}>
          <div className={styles.title}>图层</div>
          <div
            className={styles.collapseBtn}
            onClick={() => {
              setOpenDrawer(false);
            }}
          >
            <CrossBlack />
          </div>
        </div>
        <div className={styles.contentBox} id="layerContentBox">
          {/* 图层列表 */}
          <LayerList />
          {/* 图层生成记录 */}
          <HistoryList />
        </div>
      </Drawer>
    </div>
  );
});
