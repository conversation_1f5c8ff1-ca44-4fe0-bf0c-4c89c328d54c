@import '~@/styles/variables.less';

.layer-drawer-box {
  position: absolute;
  top: 0;
  right: 0;
  width: 254px;
  height: calc(100vh - 56px);

  .entry-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 72px;
    height: 40px;
    border-radius: 4px;
    background: #252529;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    z-index: 5;

    span {
      color: #fff;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    svg {
      width: 16px;
      height: 16px;
    }
  }

  .drawer-box {
    color: #dcdde5;
    z-index: 6 !important;

    .top-box {
      width: 100%;
      height: 48px;
      position: sticky;
      background: #17171a;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 14px 16px;

      .title {
        color: #dcdde5;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
      }

      .collapse-btn {
        width: 14px;
        height: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;

        svg {
          width: 14px;
          height: 14px;
          color: #636370;
        }
      }
    }

    .content-box {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: calc(100% - 48px);
      position: relative;
      overflow: hidden;
    }

    :global {
      .ant-drawer-content {
        background: #17171a;
        border-left: 1px solid #29292e;

        .ant-drawer-body {
          padding: 0;
        }
      }
    }
  }
}
