@import '~@/styles/variables.less';
@content-main: #dcdde5;
@content-secondary: #7f818a;
@input-border-color: #29292e;
@accent-selected: #4053ff;
@space-holder: #ebeeff14;
@bg-thumb: #dcdde5;

.editor-slider-input {
  :global {
    .editor-slider-input-title {
      .title-content {
        color: @content-secondary;
      }
    }

    .editor-slider-input-input.@{ant-prefix}-input-number {
      display: flex;
      width: 60px;
      height: 28px;
      padding: 0px 8px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;

      &:hover {
        border: 1px solid @accent-selected;
      }

      &.@{ant-prefix}-input-number-focused {
        border: 1px solid @accent-selected;
      }

      border: 1px solid @input-border-color;

      .@{ant-prefix}-input-number-input-wrap {
        .@{ant-prefix}-input-number-input {
          color: @content-main;
          text-align: center;
          font-size: 12px;
        }
      }
    }

    .editor-slider-input-slider.@{ant-prefix}-slider {
      .@{ant-prefix}-slider-rail {
        background-color: @space-holder;
      }

      .@{ant-prefix}-slider-track {
        background-color: @accent-selected;
      }
    }
  }
}
