import { SliderInput } from '@/components';
import styles from './index.module.less';

export type EditorSliderInputProps = {
  value?: number;
  onChange?(valeu: number): void;
  title?: string;
  min?: number;
  max?: number;
};

export default function EditorSliderInput({
  value,
  title,
  onChange,
  min,
  max
}: EditorSliderInputProps) {
  return (
    <SliderInput
      title={title}
      value={value}
      onChange={onChange}
      className={styles.editorSliderInput}
      customClassNames={{
        title: 'editor-slider-input-title',
        input: 'editor-slider-input-input',
        slider: 'editor-slider-input-slider'
      }}
      markNum={0}
      min={min}
      max={max}
      showInputControls={false}
    />
  );
}
