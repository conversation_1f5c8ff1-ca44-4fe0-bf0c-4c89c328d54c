import './index.less';
import { TaskCategory } from '@/api/types/imageEditor';
type SynopsisConfigType = {
  title?: string;
  content?: string;
  url?: string | any;
};

const synopsisConfig: Partial<Record<TaskCategory, SynopsisConfigType>> = {
  [TaskCategory.inpaint]: {
    title: '局部修改',
    content: '对画面中局部区域进行修改。',
    url: 'https://titan-h5.meitu.com/whee/assets/editorTutorials/video/inpaint.mp4'
  },
  [TaskCategory.extend]: {
    title: '扩图（对画板）',
    content: '对画板中图片进行画面扩展。支持多图哦！',
    url: 'https://titan-h5.meitu.com/whee/assets/editorTutorials/video/extend.mp4'
  },
  [TaskCategory.compound]: {
    title: 'AI合成',
    content: '将画板中图片自然融合成一张图。',
    url: 'https://titan-h5.meitu.com/whee/assets/editorTutorials/video/compound.mp4'
  },
  [TaskCategory.graffiti]: {
    title: '涂鸦',
    content: '绘画工具，可配合AI合成使用。',
    url: 'https://titan-h5.meitu.com/whee/assets/editorTutorials/video/graffiti.mp4'
  },
  [TaskCategory.cutout]: {
    title: '抠图',
    content: '精准保留图片中需要的部分。',
    url: 'https://titan-h5.meitu.com/whee/assets/editorTutorials/video/cutout.mp4'
  },
  [TaskCategory.eraser]: {
    title: '消除',
    content: '效果无痕更自然！',
    url: 'https://titan-h5.meitu.com/whee/assets/editorTutorials/video/eraser.mp4'
  },
  [TaskCategory.extendLayer]: {
    title: '扩图（对图层）',
    content: '对选中的图层进行画面扩展。',
    url: 'https://titan-h5.meitu.com/whee/assets/editorTutorials/video/extendLayer.mp4'
  }
};

type SynopsisProps = {
  x: number;
  y: number;
  hidden: boolean;
  type?: TaskCategory;
};
const Synopsis = (props: SynopsisProps) => {
  const { x, y, hidden, type } = props;
  if (!type) return null;
  const config: any = synopsisConfig[type];
  if (hidden) return null;
  if (!config) return null;
  const { url, title, content } = config;

  return (
    <div
      className="editor-synopsis-box"
      style={{
        left: x,
        top: y
      }}
    >
      <div className="editor-synopsis-content-box">
        <div className="editor-synopsis-content-img-box">
          {url ? (
            <video
              className="editor-synopsis-content-video"
              loop
              muted
              autoPlay
              playsInline
              preload="auto"
              controls={false}
              disablePictureInPicture
            >
              <source src={url} type="video/mp4" />
            </video>
          ) : null}
        </div>
        <div className="editor-synopsis-content">
          {title ? <h3>{title}</h3> : null}
          {content ? <p>{content}</p> : null}
        </div>
      </div>
    </div>
  );
};

export default Synopsis;
