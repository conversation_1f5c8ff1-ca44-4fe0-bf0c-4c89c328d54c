.editor-synopsis-box {
  position: fixed;
  z-index: 300;
  left: 0;
  top: 0;
  animation: fadeIn 0.2s ease-in-out;

  .editor-synopsis-content-box {
    width: 300px;
    height: auto;
    padding: 8px;
    padding-bottom: 20px;
    border-radius: 8px;
    background: #f1f2f6;
    box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.25);
    @video-height: 178px;
    .editor-synopsis-content-img-box {
      margin-bottom: 16px;
      width: 100%;
      height: @video-height;
      border-radius: 4px;
      background: rgba(6, 16, 37, 0.08);

      .editor-synopsis-content-video {
        width: 100%;
        height: @video-height;
        border-radius: 4px;
      }
    }

    .editor-synopsis-content {
      padding: 0 8px;

      h3 {
        color: #17171a;
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 6px;
      }

      p {
        color: #17171a;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-bottom: 0;
      }
    }
  }
}
/* 定义淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
