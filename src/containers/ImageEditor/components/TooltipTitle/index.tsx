import classNames from 'classnames';
import { Tooltip } from 'antd';
import { QuestionMarkCircleBold } from '@meitu/candy-icons';

import styles from './index.module.less';
import { useCallback, useEffect, useRef } from 'react';
import { observer } from 'mobx-react';
import useStore from '@/hooks/useStore';
import { TaskCategory } from '@/api/types/imageEditor';

type TooltipTitleProps = {
  title: string;
  tooltips?: React.ReactNode;
  triggerIcon?: React.ReactNode;
  className?: string;
};

export default function TooltipTitle({
  title,
  tooltips,
  triggerIcon = <QuestionMarkCircleBold />,
  className
}: TooltipTitleProps) {
  return (
    <div className={classNames(styles.tooltipTitle, className)}>
      <span className="tooltip-title-title">{title}</span>
      <EditorTooltip
        triggerIcon={triggerIcon}
        tooltips={tooltips}
        triggerClassName="tooltip-title-trigger"
      />
    </div>
  );
}

type EditorTooltipProps = {
  triggerIcon?: React.ReactNode;
  tooltips?: React.ReactNode;
  triggerClassName?: string;
  color?: string;
  synopsisConfig?: {
    x: number;
    y: number;
    hidden: boolean;
    type: TaskCategory;
  };
  dataKey?: string;
};
export const EditorTooltip = observer(
  ({
    triggerIcon = <QuestionMarkCircleBold />,
    tooltips,
    triggerClassName,
    color = '#F1F2F6',
    synopsisConfig,
    dataKey
  }: EditorTooltipProps) => {
    const editorStore = useStore('EditorStore');
    const hoverTimer = useRef<any>(null);
    const icon = useRef<HTMLElement>(null);
    const showSynopsis = useCallback(() => {
      clearTimer();
      hoverTimer.current = setTimeout(() => {
        if (synopsisConfig)
          editorStore.setSynopsisConfig({ ...synopsisConfig });
      }, 300); // 3秒后执行 onHover
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [synopsisConfig]);
    const hideSynopsis = useCallback(() => {
      clearTimer();
      editorStore.setSynopsisConfig({ x: 0, y: 0, hidden: true });
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [editorStore.synopsisConfig]);
    const clearTimer = () => {
      if (hoverTimer.current) {
        clearTimeout(hoverTimer.current); // 鼠标离开时清除计时器
      }
    };
    useEffect(() => {
      const iconNode = icon.current;
      if (iconNode) {
        iconNode.addEventListener('mouseenter', showSynopsis);
        iconNode.addEventListener('mouseleave', hideSynopsis);
      }
      return () => {
        if (iconNode) {
          iconNode.removeEventListener('mouseenter', showSynopsis);
          iconNode.removeEventListener('mouseleave', hideSynopsis);
        }
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [showSynopsis, hideSynopsis]);

    if (synopsisConfig)
      return (
        <span className={triggerClassName} ref={icon}>
          {triggerIcon}
        </span>
      );
    return (
      <Tooltip
        title={tooltips}
        color={color}
        overlayClassName={styles.tooltipOverlay}
      >
        <span className={triggerClassName}>{triggerIcon}</span>
      </Tooltip>
    );
  }
);
