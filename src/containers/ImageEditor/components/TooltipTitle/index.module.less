@import '~@/styles/variables.less';
@content-primary-color: #dcdde5;

@content-tertiary-color: #5e5e6b;

@separator-color: #29292e;

@tooltip-content-color: #17171a;

@content-hover: rgba(139, 139, 158, 1);

.tooltip-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 1.42em;
  :global {
    .tooltip-title-title {
      color: @content-primary-color;
    }

    .tooltip-title-trigger {
      margin-left: 4px;
      color: @content-tertiary-color;
      &:hover {
        color: @content-hover;
      }
    }
  }
}

.tooltip-overlay:global(.@{ant-prefix}-tooltip) {
  :global {
    .@{ant-prefix}-tooltip-inner {
      color: @tooltip-content-color;
      font-size: 12px;
      border-radius: 4px;
    }
  }
}
