@import '~@/styles/variables.less';

@border-color-selected: #4053ff;
@bg-color: rgba(235, 238, 255, 0.08);
@content-color: #dcdde5;

.generate-nums {
  &:global(.@{ant-prefix}-radio-group) {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin: 2px;
    :global {
      .generate-nums-item:nth-child(1),
      .generate-nums-item:nth-child(5),
      .generate-nums-item:nth-child(9) {
        margin-left: 0;
      }
      .generate-nums-item:hover {
        background: var(
          --background-editorHoverOverlay,
          rgba(235, 238, 255, 0.12)
        ) !important;
      }
      .generate-nums-item {
        flex: 0 0 auto;
        flex-wrap: wrap;
        box-sizing: border-box;
        width: 61px;
        padding: 2px;
        height: 61px;

        margin-left: 6px;
        margin-bottom: 6px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        border-radius: 4px;
        background: @bg-color;
        color: var(--content-editorSecondary, #7f818a);
        box-sizing: border-box;
        border: none;
        svg {
          width: 20px;
          height: 20px;
        }
        &::before {
          display: none;
        }
        &::hover {
          background: var(
            --background-editorHoverOverlay,
            rgba(235, 238, 255, 0.12)
          ) !important;
        }
        &-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
        }

        &.generate-nums-item-selected {
          box-shadow: 0 0 0 2px @border-color-selected;
          color: @content-color;
        }
        .generate-nums-item-name {
          display: block;
          margin-top: 2px;
          height: 17px;
          line-height: 17px;
          font-size: 12px;
        }
      }
    }
  }
}
