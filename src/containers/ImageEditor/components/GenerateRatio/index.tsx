import classNames from 'classnames';
import { Radio } from 'antd';

import styles from './index.module.less';
import useStore from '@/hooks/useStore';
import { observer } from 'mobx-react';

import {
  RatioFreeBold,
  Ratio11Bold,
  Ratio34Bold,
  Ratio43Bold,
  Ratio23Bold,
  Ratio32Bold,
  Ratio916Bold,
  PictureBold
} from '@meitu/candy-icons';

type GenerateRatioProps = {
  value?: string;
  onChange?(newValue: string): void;

  options?: Array<{
    label: string;
    image_ratio: string;
    icon?: React.ReactNode;
  }>;
};
const ratioOptions = [
  {
    label: '自由',
    image_ratio: 'free',
    icon: <RatioFreeBold />
  },
  {
    label: '原比例',
    image_ratio: 'original',
    icon: <PictureBold />
  },
  {
    label: '1:1',
    image_ratio: '1:1',
    icon: <Ratio11Bold />
  },
  {
    label: '3:2',
    image_ratio: '3:2',
    icon: <Ratio32Bold />
  },
  {
    label: '2:3',
    image_ratio: '2:3',
    icon: <Ratio23Bold />
  },
  {
    label: '4:3',
    image_ratio: '4:3',
    icon: <Ratio43Bold />
  },
  {
    label: '3:4',
    image_ratio: '3:4',
    icon: <Ratio34Bold />
  },
  {
    label: '9:16',
    image_ratio: '9:16',
    icon: <Ratio916Bold />
  }
];
function GenerateRatio({
  value,
  onChange,
  options = ratioOptions
}: GenerateRatioProps) {
  const editorStore = useStore('EditorStore');
  return (
    <Radio.Group
      value={value}
      onChange={(e) => {
        const scale = editorStore.isExtendEditor ? 1 : 1.2;
        onChange?.(e.target.value.image_ratio);
        if (e.target.value.image_ratio === 'free') {
          editorStore.extendEditor?.wheeExtendPlugin.plugin?.setFreeRatio(
            false,
            scale
          );
          return;
        }
        if (e.target.value.image_ratio === 'original') {
          editorStore.extendEditor?.wheeExtendPlugin.plugin?.setFreeRatio(
            true,
            1.2
          );
          return;
        }
        const val = e.target.value.image_ratio.split(':');
        editorStore.extendEditor?.wheeExtendPlugin.plugin?.setRatio(
          Number(val[0]),
          Number(val[1]),
          scale
        );
      }}
      className={styles.generateNums}
    >
      {options.map((n) => {
        return (
          <Radio.Button
            className={classNames(
              'generate-nums-item',
              value === n.image_ratio && 'generate-nums-item-selected'
            )}
            key={n.image_ratio}
            value={n}
          >
            <div className="generate-nums-item-icon">
              {n.icon}
              <span className="generate-nums-item-name">{n.label}</span>
            </div>
          </Radio.Button>
        );
      })}
    </Radio.Group>
  );
}

export default observer(GenerateRatio);
