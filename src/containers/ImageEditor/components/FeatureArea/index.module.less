@import '~@/styles/variables.less';

@tab-zindex: 20;
@drawer-zindex: 19;

@tab-width: 80px;
@tab-bg-color: #000000;
@tab-btn-size: @tab-width;
@tab-btn-color: #7f818a;
@tab-btn-color--active: #dcdde5;
@tab-btn-bg-color: #000000;
@tab-btn-bg-color--active: #17171a;
@tab-btn-icon-size: 24px;
@tab-btn-label-size: 12px;

@feat-panel-fold-btn-width: 13px;
@feat-panel-fold-btn-hegiht: 80px;
@feat-panel-bg-color: #17171a;
@feat-panel-border-color: #29292e;

.feature-tab {
  width: @tab-width;
  z-index: @tab-zindex;
  // border-top: 1px solid @feat-panel-border-color;
  position: absolute;
  height: 100%;
  display: block;
  overflow: hidden;
  background-color: @tab-bg-color;

  .feed-back-entry {
    position: absolute;
    bottom: 24px;
    left: 28px;
    width: 24px;
    height: 24px;
    cursor: pointer;

    :hover {
      svg {
        color: #bcbecc;
      }
    }

    svg {
      width: 24px;
      height: 24px;
      color: #81838c;
    }
  }

  :global {
    .feature-tab-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: @tab-btn-size;
      height: @tab-btn-size;
      color: @tab-btn-color;
      background-color: @tab-btn-bg-color;
      cursor: pointer;

      &-icon {
        font-size: @tab-btn-icon-size;
        margin-bottom: 4px;
        line-height: 1;
      }

      &-label {
        font-size: @tab-btn-label-size;
        line-height: 1.4em;
      }

      &:hover {
        color: rgba(176, 178, 191, 1);
        background: #1c1c1f;
      }

      &.active {
        color: @tab-btn-color--active;
        background-color: @tab-btn-bg-color--active;

        .feature-tab-btn-label {
          font-weight: 800;
        }
      }
    }
  }
}

.feed-back-tooltip-overlay:global(.@{ant-prefix}-tooltip) {
  :global {
    .@{ant-prefix}-tooltip-arrow {
      color: #f1f2f6;

      &::before {
        background: #f1f2f6 !important;
      }
    }

    .@{ant-prefix}-tooltip-content {
      border-radius: var(--radius-4, 4px);
      background: var(--background-editorTips, #f1f2f6);
      box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.25);
    }

    .@{ant-prefix}-tooltip-inner {
      border-radius: var(--radius-4, 4px);
      background: var(--background-editorTips, #f1f2f6);
      color: #17171a;
      font-size: 12px;
      border-radius: 4px;
      padding: 4px 8px;
      min-height: 25px;
    }
  }
}

.drawer {
  width: 313px;
  left: @tab-width !important;

  &:global(.@{ant-prefix}-drawer) {
    z-index: @drawer-zindex;

    & > :global(.@{ant-prefix}-drawer-content-wrapper) {
      z-index: @drawer-zindex;
      box-shadow: none;

      :global(.@{ant-prefix}-drawer-content) {
        background-color: transparent;
        pointer-events: none;
      }

      :global(.@{ant-prefix}-drawer-body) {
        pointer-events: auto;
        background-color: transparent;
        padding: 0;
        // margin-right: 13px;

        .fold-icon {
          position: absolute;
          right: 2px;
          top: 50%;
          transform: translateY(-50%);
          cursor: pointer;

          :global {
            .fold-icon-narrow {
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }
          }
        }

        :global(.feature-panel) {
          width: 100%;
          height: 100%;
          background-color: @feat-panel-bg-color;
          border-right: 1px solid @feat-panel-border-color;
        }
      }
    }
  }
}
