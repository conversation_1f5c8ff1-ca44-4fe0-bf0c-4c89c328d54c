import {
  <PERSON><PERSON><PERSON><PERSON>,
  PaintBold,
  SelectionBoxBold,
  SelectionLassoBold,
  ToolCursorBold
} from '@meitu/candy-icons';
import AreaSelection, { SelectionWay } from '../../AreaSelection';
import CommonPanel from '../CommonPanel';
import styles from './index.module.less';
import {
  AreaSelectionMode,
  ModeSelection
} from '../../AreaSelection/ModeSelection';
import EditorSliderInput from '../../EditorSliderInput';
import EditorTextArea from '../../EditorTextArea';
import { ConfigProvider, Form } from 'antd';
import GenerateNums from '../../GenerateNums';
import TooltipTitle from '../../TooltipTitle';
import SubmitFooter from '../../SubmitFooter';
import {
  AreaSelectionParams,
  ChangePictureSelectionWay,
  ToolPluginsType,
  useAreaSelection
} from '@/containers/ImageEditor/hooks/useAreaSelection';
import { ShapeType } from '@/editor/core/src';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useMemo, useRef, useState, useCallback } from 'react';
import { v4 as uuid } from 'uuid';
import { Uploader } from '@meitu/upload';
import { cancelTask, requestInferenceTask } from '@/api/imageEditor';
import { TaskCategory } from '@/api/types/imageEditor';
import { createImage } from '@/utils/cropImage';
import { Image as ImageShape } from '@/editor/core/src';
import message from '../../Toast';
import { observer } from 'mobx-react';
import useStore from '@/hooks/useStore';
import { getBioImageBlob } from '@/containers/ImageEditor/utils/maskImageTools';
import { queryPolling } from '@/containers/ImageEditor/utils/queryPolling';
import { CancelButton } from '../CancelButton';
import { trackEvent } from '@/services';
import {
  Modification,
  Subfunction
} from '@/containers/ImageEditor/constant/trace';
import {
  handleInvalidTokenError,
  handleRequestError
} from '@/containers/ImageEditor/utils/handleRequestError';
import { getClock } from '@/containers/ImageEditor/utils/clock';
import { uploaderFunc } from '@/containers/ImageEditor/utils/upload';
import { Confirm } from '../modals/Confirm';
import classNames from 'classnames';
import { FunctionCode } from '@/api/types/meidou';
import { HistoryPlugin } from '@/editor/wukong-history-plugin/src';
import { SmartSelectionToolPlugin } from '@/containers/ImageEditor/plugins/SmartSelectionToolPlugin';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { MediaType, MtccFuncCode } from '@/api/types';

import { Tabs } from '@/components';
import { Segmented } from '@/components';
import type { TabsProps } from '@/components/Tabs';
import { ImageSection } from '../../ImageSection';

// 任务超时时间
const QUERY_POLLING_TIME_OUT = 5 * 60 * 1000;

// 取消按钮延迟展示时间
const CANCEL_BUTTON_SHOW_DELAY = 60 * 1000;

/**
 * 有涂抹区时退出改图 会通过modal提示
 * 如果用户选择了之后不再提示 则不再会弹出弹窗
 *
 * 这里通过localStorage记录用户是否选择了不再探出弹窗
 */
const EXIT_ONCE_MODAL_KEY = 'exit_change_picture_once_modal_key';

type ChangePictureProps = {
  actived?: boolean;
  onClose?: () => void;
  hasPaintedPromiseRef: React.MutableRefObject<Promise<boolean>>;
};
export default observer(
  ({ actived, hasPaintedPromiseRef, onClose }: ChangePictureProps) => {
    const editorStore = useStore('EditorStore');
    const projectId = editorStore.projectInfo.id;
    const app = editorStore.editor.app;
    const selectorPlugin = editorStore.editor.selectorPlugin;
    const wheeEditorPlugin = editorStore.editor.wheeEditorPlugin;
    const editorLayersHandler = editorStore.editorLayersHandler;
    const [genNums, setGenNums] = useState(4);
    const childRef = useRef<any>(null);

    const historyName = 'change-picture-history-plugin';
    const privateHistory = useRef<HistoryPlugin>(
      new HistoryPlugin({ max: 999 })
    );

    const smartSelectionTool = useRef<SmartSelectionToolPlugin>(
      new SmartSelectionToolPlugin({
        app,
        wheeEditorPlugin,
        historyPlugin: privateHistory.current,
        getOriginImage: async () => {
          const result = await editorStore.uploadImagePromise;
          return result?.previewUrl;
        },
        getProjectId: () => {
          return editorStore.projectInfo.id;
        },
        /**
         * 在使用之前需要通过 injectHasPaintedChecker注入该函数
         */
        getHasPainted: () => hasPaintedPromiseRef.current
      })
    );

    /**********切换历史 start********** */
    // 改图的涂抹历史需要独立于住画板
    // 在挂载改图功能挂载时：
    // 1. 禁用主画板历史
    // 2. use改图历史
    // 3. 设置proxyHistory以便header控制
    useEffect(() => {
      const mainHistory = editorStore.editor.historyPlugin;
      mainHistory.disable();

      const _privateHistory = privateHistory.current;
      _privateHistory.name = historyName;
      _privateHistory.enable();

      editorStore.editor.app.use(_privateHistory);
      editorStore.setProxyHistory(_privateHistory);
      editorStore.setProxyUndo(() => {
        _privateHistory.undo();
      });
      editorStore.setProxyRedo(() => {
        _privateHistory.redo();
      });

      return () => {
        mainHistory.enable();
        editorStore.editor.app.destroyPlugin(historyName);

        editorStore.setProxyHistory(mainHistory);
        editorStore.setProxyUndo(() => {
          mainHistory.undo();
        });
        editorStore.setProxyRedo(() => {
          mainHistory.redo();
        });
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    /**********切换历史 end********** */

    const appendLayer = (layerId: string, messageId: string) => {
      editorStore.appendTaskWithLayer(layerId, {
        id: messageId,
        type: TaskCategory.inpaint
      });
    };
    const { updateMeiDouBalance } = useMeiDouBalance();

    const toolPlugins: ToolPluginsType = useMemo(() => {
      return {
        rectTool: editorStore.editor.rectTooler,
        eraserTool: editorStore.editor.eraserTooler,
        lineTool: editorStore.editor.lineTooler,
        lassoTool: editorStore.editor.lassoTooler,
        smartSelectionTool: smartSelectionTool.current,
        ellipseTool: editorStore.editor.ellipseTooler
      };
    }, [
      editorStore.editor.rectTooler,
      editorStore.editor.eraserTooler,
      editorStore.editor.lineTooler,
      editorStore.editor.lassoTooler,
      editorStore.editor.ellipseTooler
    ]);

    const {
      selectionWay,
      onSelectionWayChange,
      selectionParams,
      onSelectionParamsChange
    } = useAreaSelection({
      app,
      toolPlugins,
      selectorPlugin,
      wheeEditorPlugin,
      hasPaintedPromiseRef
    });

    useEffect(() => {
      // 如果当前活跃的不是改图 禁用框选
      if (!actived && selectionWay) {
        onSelectionWayChange(null);
      }

      // 如果当前活跃的是改图 默认框选
      if (actived && !selectionWay) {
        handleSelectionWayChange(SelectionWay.Box);
      }

      if (actived) {
        editorStore.setSelectEffect(cancelEditorPictureStatus);
      }

      return () => {
        editorStore.setSelectEffect(() => {});
      };

      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [actived]);

    function handleModeChange<T extends ChangePictureSelectionWay>(
      mode: AreaSelectionMode,
      otherParams?: Omit<AreaSelectionParams<T>, 'selectionMode'>
    ) {
      onSelectionParamsChange({
        selectionMode: mode,
        ...otherParams
      });
    }

    function editorPictureStatus() {
      app.unlockAllShapes();
    }

    const cancelEditorPictureStatus = () => {
      onSelectionWayChange(null);
      editorPictureStatus();
    };

    function handleSelectionWayChange(way: ChangePictureSelectionWay | null) {
      onSelectionWayChange(way);
      editorStore.editor.selectorPlugin.selector?.cancelSelect();
      editorStore.editor.app.lockAllShapes();

      editorStore.unselectEffectHandler();
    }

    // // 项目创建时 默认选中框选
    // editorStore.setDefaultChangePictureHandler(() => {
    //   // 在插入图片时 会使用imageTooler 导致工具被卸载
    //   // 这样就导致当前状态虽然是Selection.Box 但对应的工具不是矩形工具
    //   // 如果直接将状态设置为SelectionWay.Box 这样状态没有发生改变 不会出发"安装"矩形工具的动作
    //   // 因此 这里先清空 然后再切换回Box 触发矩形工具的安装动作
    //   handleSelectionWayChange(null);
    //   setTimeout(() => handleSelectionWayChange(SelectionWay.Box));
    // });
    // useEffect(() => {
    //   return () => {
    //     editorStore.setDefaultChangePictureHandler(() => {});
    //   };
    //   // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, []);

    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [formValues, setFormValues] = useState({
      prompt: '',
      genNums: 4,
      referImage: ''
    });

    const [tabKey, setTabKey] = useState('1');

    /**
     * 用来防止多个重复的toast
     */
    const toastCloseHandlerSet = useRef<Set<() => void>>(new Set());

    /**
     * 在组件卸载时，要做的清理工作
     * 1. 如果改图任务没有完成 关闭弹窗
     */
    const cleanupsBeforeUnmounted = useRef<Set<() => void>>(new Set());
    useEffect(() => {
      const cleanups = cleanupsBeforeUnmounted.current;
      return () => {
        cleanups.forEach((fn) => {
          try {
            fn();
          } catch (e) {
            if (process.env.REACT_APP_ENV === 'development') {
              console.error(e);
            }
          }
        });
      };
    }, []);
    async function handleSubmit(price: any) {
      if (loading) {
        return;
      }
      if (tabKey === '2' && !formValues.referImage) {
        message({
          type: 'info',
          content: '请上传图片',
          duration: 3000
        });
        return;
      }

      toastCloseHandlerSet.current?.forEach((fn) => fn());
      toastCloseHandlerSet.current?.clear();

      if (!navigator.onLine) {
        const handler = message({
          type: 'error',
          content: '网络异常',
          duration: 3000
        });

        toastCloseHandlerSet.current?.add(handler.destroy.bind(handler));
        return;
      }

      const clock = getClock();
      let remainingTime = QUERY_POLLING_TIME_OUT;

      const prompt = formValues.prompt;
      const batchSize = formValues.genNums;
      trackEvent('ai_modification_create_btn_click', {
        text: prompt,
        negative_text: '',
        batch_size: batchSize,
        subfunction: Subfunction.Modification,
        free_batch_size: price && price?.useFreeNum + '',
        modification_type: tabKey === '1' ? 'prompt' : 'image'
      });

      // 无可见图层时
      const layers = editorLayersHandler();
      if (layers.filter((item) => item.hidden === false).length === 0) {
        const handler = message({
          type: 'error',
          content: '请开启至少一个图层才可进行生成',
          duration: 3000
        });

        toastCloseHandlerSet.current?.add(handler.destroy.bind(handler));
        return;
      }

      if (!wheeEditorPlugin.plugin) {
        return;
      }
      let _closeToast: null | (() => void) = null;
      let isCanceledByUser = false;

      function cancelBecauseOfUnmounted() {
        isCanceledByUser = true;
      }
      cleanupsBeforeUnmounted.current.add(cancelBecauseOfUnmounted);

      try {
        setLoading(true);
        // 如果可视区域没有涂抹 提示用户
        if (!(await hasPaintedPromiseRef.current)) {
          const handler = message({
            type: 'error',
            content: '请先选择想要修改的区域',
            duration: 3000
          });
          toastCloseHandlerSet.current?.add(handler.destroy.bind(handler));
          return;
        }

        const { destroy: closeToast } = message({
          type: 'loading',
          content: '生成中，请稍后...',
          isShowMask: true,
          customNode: (
            <CancelButton
              showDelay={CANCEL_BUTTON_SHOW_DELAY}
              onClick={handleCancel}
            >
              取消
            </CancelButton>
          )
        });

        _closeToast = closeToast;
        cleanupsBeforeUnmounted.current.add(_closeToast);

        //#region 创建任务
        const origin = await wheeEditorPlugin.plugin.getImagesLayer(
          'image/png'
        );

        /**
         * 这里在导出时使用jpg 否则mask图中会有透明部分
         */
        const mask = await wheeEditorPlugin.plugin.getOptionsLayer(
          'image/jpeg',
          '#000000'
        );

        /**
         * 将jpeg的mask图转换为灰度图 同时将类型转换为png
         */
        const grayMaskBlob = await getBioImageBlob(mask, 'image/png');

        if (!grayMaskBlob) {
          return;
        }

        remainingTime -= clock.getDelta();
        const maskURL = await uploaderFunc(grayMaskBlob, 'png', remainingTime);
        // const maskURL = await uploader.upload(grayMaskBlob, {
        //   ...defaultUploadParam,
        //   suffix: 'png'
        // });
        // document.body.appendChild(await createImage(maskURL.previewUrl!))

        if (isCanceledByUser) {
          return;
        }

        remainingTime -= clock.getDelta();
        // const originURL = await uploader.upload(
        //   Uploader.dataURLToFile(origin, 'origin.png'),
        //   {
        //     ...defaultUploadParam,
        //     suffix: 'png'
        //   }
        // );
        const originURL = await uploaderFunc(
          Uploader.dataURLToFile(origin, 'origin.png'),
          'png',
          remainingTime
        );
        if (isCanceledByUser) {
          return;
        }

        // console.log(originURL);

        remainingTime -= clock.getDelta();
        const layerId = uuid();
        let _taskParams: any;
        if (tabKey === '1') {
          _taskParams = {
            taskCategory: TaskCategory.inpaint,
            projectId: projectId,
            layerId,
            params: {
              initImage: originURL.url,
              maskImage: maskURL.url,
              prompt: prompt,
              negativePrompt: '',
              batchSize
            },
            functionName: MtccFuncCode.FuncCodeImageModifyInpaintRight,
            mediaType: MediaType.Photo,
            resMediaType: MediaType.Photo
          };
        } else {
          _taskParams = {
            taskCategory: TaskCategory.referInpaint,
            projectId: projectId,
            layerId,
            params: {
              initImage: originURL.url,
              maskImage: maskURL.url,
              referImage: formValues.referImage,
              negativePrompt: '',
              batchSize
            },
            functionName: MtccFuncCode.FuncCodeImageModifyInpaintRight,
            mediaType: MediaType.Photo,
            resMediaType: MediaType.Photo
          };
        }

        const createTaskRes = requestInferenceTask(_taskParams, remainingTime);
        // 重新拉取定价接口
        getPrice();
        const response = await createTaskRes;
        editorStore.setSurplus(response.surplus);
        //#endregion

        const messageId = response.id;
        if (isCanceledByUser) {
          cancelTask({
            projectId,
            msgId: messageId
          });
          // .then(() => {
          //   editorStore.updateSurplus();
          // });
          return;
        }

        remainingTime -= clock.getDelta();
        const { result, cancelPolling } = queryPolling(
          messageId,
          3000,
          TaskCategory.inpaint,
          remainingTime
        );
        const pollingResult = await result;
        if (pollingResult.isCanceled) {
          if (pollingResult.isTimeout) {
            cancelTask({
              projectId,
              msgId: messageId
            });
            // .then(() => {
            //   editorStore.updateSurplus();
            // });
            updateMeiDouBalance();
            throw new Error('生成失败，请重试');
          }

          return;
        }
        const historyList = pollingResult.data;

        function handleCancel() {
          isCanceledByUser = true;
          trackEvent('ai_modification_create_cancel', {
            text: prompt,
            negative_text: '',
            batch_size: batchSize,
            subfunction: Subfunction.Modification,
            modification_type: tabKey === '1' ? 'prompt' : 'image'
          });

          try {
            // 如果还没有创建轮训任务 取消轮训会抛出异常
            cancelPolling();

            // 运行到这里 轮训任务已经取消 还需要手动取消推理任务
            cancelTask({
              projectId,
              msgId: messageId
            });
            // .then(() => {
            //   editorStore.updateSurplus();
            // });
          } catch (e) {
            // 在轮训之前调用取消轮训会出现异常
          } finally {
            setLoading(false);
            _closeToast?.();
          }

          cleanupsBeforeUnmounted.current.delete(cancelBecauseOfUnmounted);
          _closeToast && cleanupsBeforeUnmounted.current.delete(_closeToast);
        }

        // console.log(historyList);
        const imageList: any[] = historyList[0]?.resultImages ?? [];

        const successList = imageList
          .filter((image) => image.imageStatus === 1)
          .map(({ url }) => url);
        // console.log(successList);
        if (!successList.length) {
          throw new Error('生成失败，请重试');
        }

        // 有部分图片没有审核通过时 告知用户
        if (successList.length !== imageList.length) {
          message({
            type: 'info',
            content: `${imageList.length - successList.length}张生成失败`,
            duration: 3000
          });
        }

        const first = successList[0];

        appendLayer(layerId, messageId);
        const image = await createImage(first);
        const node = new ImageShape({
          x: image.width / 2,
          y: image.height / 2,
          image,
          id: layerId,
          width: image.width,
          height: image.height,
          type: ShapeType.Image,
          draggable: true,
          enableSelect: true
        });

        wheeEditorPlugin.plugin.clearMask();

        const mainHistory = editorStore.editor.historyPlugin;
        privateHistory.current.disable();
        mainHistory.enable();
        wheeEditorPlugin.plugin.addEditor(node);
        mainHistory.disable();
        privateHistory.current.enable();

        // 改图成功后
        //1. 倒开右侧图层面板
        editorStore.setLayersIsOpen(true);
        //2. 关闭左侧功能栏
        onClose?.();
        //3. 重置表单
        form.resetFields();
        //4. 重置tabKey
        setTabKey('1');
        //5. 重置formValues
        setFormValues({
          prompt: '',
          genNums: 4,
          referImage: ''
        });
        updateMeiDouBalance();

        trackEvent('ai_modification_create_success', {
          text: prompt,
          negative_text: '',
          // 成功时仅上报成功张数
          batch_size: successList.length,
          subfunction: Subfunction.Modification,
          free_batch_size: price && price?.useFreeNum + '',
          modification_type: tabKey === '1' ? 'prompt' : 'image'
        });
      } catch (e: any) {
        if (isCanceledByUser) {
          return;
        }

        if (e.isTimeout) {
          const handler = message({
            type: 'error',
            content: '生成失败，请重试',
            duration: 5000
          });

          toastCloseHandlerSet.current?.add(handler.destroy.bind(handler));
          return;
        }

        if (handleInvalidTokenError(e)) {
          return;
        }

        handleRequestError(e);
        updateMeiDouBalance();
      } finally {
        if (!isCanceledByUser) {
          setLoading(false);
          _closeToast?.();
        }
        getPrice();

        cleanupsBeforeUnmounted.current.delete(cancelBecauseOfUnmounted);
        _closeToast && cleanupsBeforeUnmounted.current.delete(_closeToast);
      }
    }
    // 调用子组件中的getPrice方法
    const getPrice = useCallback(() => {
      childRef?.current?.getPrice();
    }, [childRef]);

    //#region 退出时弹窗提示
    const [isExitConfirmOpen, setIsExitConfirmOpen] = useState(false);
    const [onlyOnceExitConfirm, setOnlyOnceExitConfirm] = useState(false);

    function openConfirmExitModal() {
      setIsExitConfirmOpen(true);
    }

    function closeConfirmExitModal() {
      setIsExitConfirmOpen(false);
    }

    const exitResolveRef = useRef<
      ((value: boolean | PromiseLike<boolean>) => void) | null
    >(null);
    function handleClickExitModalOk() {
      closeConfirmExitModal();
      exitResolveRef.current?.(true);
      if (onlyOnceExitConfirm) {
        localStorage.setItem(EXIT_ONCE_MODAL_KEY, 'true');
      }
    }

    function handleClickExitModalCancel() {
      closeConfirmExitModal();
      exitResolveRef.current?.(false);
    }

    /**
     * 退出改图
     *
     * triggerClose参数控制是否把activeKey改为null
     * 在通过点击左侧的功能区修改activeKey时，也需要询问是否退出，但不需要我们这里修改activeKey
     * @returns 是否退出成功
     */
    async function confirmExitChangePicture(triggerClose = true) {
      // 如果没有涂抹区 直接退出成功
      const hasPainted = await hasPaintedPromiseRef.current;

      function exit() {
        editorStore.editor.app.destroyPlugin(historyName);
        hasPainted && wheeEditorPlugin.plugin?.clearMask();

        const mainHistory = editorStore.editor.historyPlugin;
        mainHistory.enable();

        editorStore.setProxyHistory(mainHistory);
        editorStore.setProxyUndo(() => {
          mainHistory.undo();
        });
        editorStore.setProxyRedo(() => {
          mainHistory.redo();
        });

        triggerClose && onClose?.();
      }

      if (!hasPainted) {
        exit();
        return true;
      }

      // 如果用户勾选了不再提示 则直接退出成功
      const skip = localStorage.getItem(EXIT_ONCE_MODAL_KEY);
      if (skip) {
        exit();
        return true;
      }

      // 弹窗询问是否退出
      const confirmPromise = new Promise<boolean>((resolve) => {
        exitResolveRef.current = resolve;
        openConfirmExitModal();
      });

      // 用户选择确认退出后 才退出
      if (await confirmPromise) {
        exit();
        return true;
      }
      return false;
    }
    useEffect(() => {
      if (actived) {
        editorStore.setExitChangePictureHandler(confirmExitChangePicture);
      }

      return () => {
        editorStore.setExitChangePictureHandler(() => true);
      };

      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [actived]);
    //#endregion
    const getFunctionBody = () => {
      return {
        prompt: tabKey === '1' ? formValues.prompt : '',
        negative_prompt: '',
        batch_size: formValues.genNums,
        referImage: tabKey === '2' ? formValues.referImage : ''
      };
    };
    // 更新
    const items: TabsProps['items'] = [
      {
        key: '1',
        label: '提示词生成',
        children: (
          <div
            style={{
              height: 130
            }}
          >
            <Form.Item name="prompt">
              <EditorTextArea
                placeholder="选择区域后，描述你想要生成的内容，如绿色的毛衣。"
                maxLength={800}
              />
            </Form.Item>
          </div>
        )
      },
      {
        key: '2',
        label: '传图生成',
        children: (
          <div
            style={{
              height: 130
            }}
          >
            <ImageSection form={form} />
          </div>
        )
      }
    ];

    // console.log(formValues, 'referImage');

    return (
      <>
        <CommonPanel
          dataKey={TaskCategory.inpaint}
          title="局部修改"
          // titleTips="选择区域后，输入想要生成的内容重新绘制。"
          synopsisConfig={{
            x: 380,
            y: 56,
            hidden: false,
            type: TaskCategory.inpaint
          }}
          actived={actived}
          extra={
            <SubmitFooter
              buttonLabel={'立即生成'}
              onClick={handleSubmit}
              ref={childRef}
              loading={loading}
              freeCounts={editorStore.surplus}
              disabled={!projectId}
              code={
                tabKey === '1'
                  ? FunctionCode.inpaint
                  : FunctionCode.referInpaint
              }
              isActivated={actived}
              getFunctionBody={getFunctionBody}
              genNums={genNums}
            />
          }
          onClose={confirmExitChangePicture}
        >
          <Form
            className={styles.changePicturePanel}
            form={form}
            initialValues={{
              prompt: '',
              genNums: 4,
              referImage: ''
            }}
            onValuesChange={(changedValues, allValues) => {
              setFormValues(allValues);
            }}
          >
            <div className="panel-content-primary" style={{ marginTop: 16 }}>
              区域选择
            </div>
            <AreaSelection
              currentSelectionWay={selectionWay}
              onSelectionWayChange={(way: ChangePictureSelectionWay) => {
                handleSelectionWayChange(way);

                if (selectionWay !== way) {
                  trackEvent('ai_modification_tool_click', {
                    tool: Modification.SelectionToolsTrace[way],
                    is_decrease:
                      Modification.IsDecreaseTrace[AreaSelectionMode.Add],
                    subfunction: Subfunction.Modification
                  });
                }
              }}
              items={[
                {
                  key: SelectionWay.Smart,
                  icon: <ToolCursorBold />,
                  label: '智能选择',
                  params: (
                    <>
                      <ModeSelection
                        currentMode={selectionParams?.selectionMode}
                        onModeChange={(mode) => {
                          handleModeChange(mode);

                          if (mode !== selectionParams?.selectionMode) {
                            trackEvent('ai_modification_tool_click', {
                              tool: Modification.SelectionToolsTrace[
                                SelectionWay.Smart
                              ],
                              is_decrease: Modification.IsDecreaseTrace[mode],
                              subfunction: Subfunction.Modification
                            });
                          }
                        }}
                      />
                    </>
                  )
                },
                {
                  key: SelectionWay.Box,
                  icon: <SelectionBoxBold />,
                  label: '框选',
                  params: (
                    <>
                      <ModeSelection
                        currentMode={selectionParams?.selectionMode}
                        onModeChange={(mode) => {
                          handleModeChange(mode);

                          if (mode !== selectionParams?.selectionMode) {
                            trackEvent('ai_modification_tool_click', {
                              tool: Modification.SelectionToolsTrace[
                                SelectionWay.Box
                              ],
                              is_decrease: Modification.IsDecreaseTrace[mode],
                              subfunction: Subfunction.Modification
                            });
                          }
                        }}
                      />
                    </>
                  )
                },
                {
                  key: SelectionWay.Lasso,
                  icon: <SelectionLassoBold />,
                  label: '套索',
                  params: (
                    <>
                      <ModeSelection
                        currentMode={selectionParams?.selectionMode}
                        onModeChange={(mode) => {
                          handleModeChange(mode);

                          if (mode !== selectionParams?.selectionMode) {
                            trackEvent('ai_modification_tool_click', {
                              tool: Modification.SelectionToolsTrace[
                                SelectionWay.Lasso
                              ],
                              is_decrease: Modification.IsDecreaseTrace[mode],
                              subfunction: Subfunction.Modification
                            });
                          }
                        }}
                      />
                    </>
                  )
                },
                {
                  key: SelectionWay.Paint,
                  icon: <PaintBold />,
                  label: '涂抹',
                  params: (
                    <>
                      <ModeSelection
                        currentMode={selectionParams?.selectionMode}
                        onModeChange={(mode) => {
                          if (mode !== selectionParams?.selectionMode) {
                            trackEvent('ai_modification_tool_click', {
                              tool: Modification.SelectionToolsTrace[
                                SelectionWay.Paint
                              ],
                              is_decrease: Modification.IsDecreaseTrace[mode],
                              subfunction: Subfunction.Modification
                            });
                          }

                          const currentParams =
                            selectionParams as AreaSelectionParams<SelectionWay.Paint>;
                          handleModeChange<SelectionWay.Paint>(mode, {
                            paintWidth: currentParams.paintWidth
                          });
                        }}
                      />
                      <div className="paint-size">
                        <EditorSliderInput
                          title="大小"
                          min={1}
                          max={100}
                          value={
                            (
                              selectionParams as unknown as AreaSelectionParams<SelectionWay.Paint>
                            )?.paintWidth
                          }
                          onChange={(value) =>
                            onSelectionParamsChange({
                              selectionMode:
                                selectionParams?.selectionMode ?? null,
                              paintWidth: value
                            })
                          }
                        />
                      </div>
                    </>
                  )
                }
              ]}
            />
            <div
              className={classNames(
                'panel-content-primary',
                styles.changePictureTabs
              )}
            >
              <Tabs
                activeKey={tabKey}
                items={items}
                onChange={(key) => {
                  setTabKey(key);
                  if (key === '1') {
                    trackEvent('modification_type_tab_click', {
                      tab: 'prompt'
                    });
                  } else {
                    trackEvent('modification_type_tab_click', {
                      tab: 'image'
                    });
                  }
                }}
                className={styles.changePictureTabsSegmented}
                type="segmented"
                size="small"
              />
              {/* </ConfigProvider> */}
            </div>
            {/* <div className="panel-content-primary prompt-title">提示词</div> */}

            <TooltipTitle
              title="生成张数"
              tooltips="生成张数越多，耗时越久。"
              className={styles.generateNumsTitle}
            />
            <Form.Item name="genNums">
              <GenerateNums
                onChange={(value) => {
                  // console.log(value, 'value')
                  setGenNums(value);
                }}
              />
            </Form.Item>

            {/* 监听 referImage 字段变化 */}
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.referImage !== currentValues.referImage
              }
            >
              {({ getFieldValue }) => {
                const referImage = getFieldValue('referImage');
                // 可以在这里添加其他逻辑，比如触发其他操作
                return null;
              }}
            </Form.Item>
          </Form>
        </CommonPanel>

        <Confirm
          open={isExitConfirmOpen}
          okText="退出"
          onClickClose={handleClickExitModalCancel}
          onClickCancel={handleClickExitModalCancel}
          onClickOk={handleClickExitModalOk}
          contentClassName={styles.confirmModal}
          centered
          width={340}
        >
          <div className="confirm-modal-main">温馨提示</div>
          <div className="confirm-modal-secondary">
            此操作将退出当前功能，是否继续退出？
          </div>
          <label className="confirm-modal-check">
            <input
              type="checkbox"
              onChange={(e) => setOnlyOnceExitConfirm(e.target.checked)}
            />
            <span
              className={classNames(
                'confirm-modal-check-box',
                onlyOnceExitConfirm && 'checked'
              )}
            >
              {onlyOnceExitConfirm && <CheckBlack />}
            </span>
            不再提示
          </label>
        </Confirm>
      </>
    );
  }
);
