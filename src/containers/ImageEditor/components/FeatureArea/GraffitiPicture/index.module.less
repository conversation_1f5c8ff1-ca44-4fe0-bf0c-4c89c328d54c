@import '~@/styles/variables.less';
@content-primary: #dcdde5;

.change-picture-panel:global(.@{ant-prefix}-form) {
  cursor: default;
  margin-bottom: 100px;

  :global {
    .panel-content-primary {
      color: #dcdde5;
      font-size: 14px;
    }

    .paint-size {
      margin: 16px;
      margin-bottom: 28px;
    }

    .prompt-title {
      margin: 36px 0 12px 0;
    }
    .color-picker-wrapper {
      display: flex;
      padding: 16px 12px;
      .color-picker-container {
        flex: 1;
      }
    }
  }

  .generate-nums-title {
    margin: 36px 0 12px 0;
    cursor: pointer;
  }
}
