import {
  <PERSON>t<PERSON>old,
  RoundCornerR<PERSON><PERSON><PERSON><PERSON>old<PERSON>ill,
  CircleBoldFill,
  EraserBold
} from '@meitu/candy-icons';
import AreaSelection, { SelectionWay } from '../../AreaSelection';
import CommonPanel from '../CommonPanel';
import styles from './index.module.less';
import EditorSliderInput from '../../EditorSliderInput';
import { Form } from 'antd';
import {
  ToolPluginsType,
  useAreaGraffiti,
  GraffitiPictureWay
} from '@/containers/ImageEditor/hooks/useAreaGraffiti';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react';
import useStore from '@/hooks/useStore';
import { trackEvent } from '@/services';
import ColorPicker, { ColorItem } from '../../ColorPicker';
import { Graffiti } from '@/containers/ImageEditor/constant/trace';
import { CanvasNodeEventName } from '@/editor/core/src';
import { BaseShape, ShapeType } from '@/editor/core/src/shape';
import { ShapeConfig } from 'konva/lib/Shape';
import { debounce } from 'lodash';
import SubmitFooter from '../../SubmitFooter';
import { TaskCategory } from '@/api/types/imageEditor';

const defaultPaintWidth = 20;
const defaultColor = 'rgba(0, 0, 0, 1)';

type ChangePictureProps = {
  actived?: boolean;
  hasPaintedPromiseRef: React.MutableRefObject<Promise<boolean>>;
  onClose?: () => void;
};

export default observer(
  ({ actived, hasPaintedPromiseRef, onClose }: ChangePictureProps) => {
    const editorStore = useStore('EditorStore');
    const app = editorStore.editor.app;
    const selectorPlugin = editorStore.editor.selectorPlugin;
    const wheeEditorPlugin = editorStore.editor.wheeEditorPlugin;
    const [selectedColor, setSelectedColor] = useState<string>(defaultColor);
    const [paintWidth, setPaintWidth] = useState<number>(defaultPaintWidth);

    const toolPlugins: ToolPluginsType = useMemo(() => {
      return {
        rectTool: editorStore.editor.rectTooler,
        eraserTool: editorStore.editor.eraserTooler,
        lineTool: editorStore.editor.lineTooler,
        lassoTool: editorStore.editor.lassoTooler,
        smartSelectionTool: editorStore.editor.smartSelectionTooler,
        ellipseTool: editorStore.editor.ellipseTooler
      };
    }, [
      editorStore.editor.smartSelectionTooler,
      editorStore.editor.rectTooler,
      editorStore.editor.eraserTooler,
      editorStore.editor.lineTooler,
      editorStore.editor.lassoTooler,
      editorStore.editor.ellipseTooler
    ]);

    const { selectionWay, onSelectionWayChange, onSelectionParamsChange } =
      useAreaGraffiti({
        app,
        toolPlugins,
        selectorPlugin,
        wheeEditorPlugin,
        hasPaintedPromiseRef
      });

    const [form] = useForm();
    // const [loading, setLoading] = useState(false);
    // const [disbaled, setDisbaled] = useState(true);

    useEffect(() => {
      //  默认画笔
      if (actived) {
        handleGraffitiWayChange(SelectionWay.Paint);
        onSelectionParamsChange({
          paintWidth: defaultPaintWidth,
          fillColor: defaultColor,
          globalCompositeOperation: 'source-over'
        });
        setPaintWidth(defaultPaintWidth);
        setSelectedColor(defaultColor);
        editorStore.setDisableSelect(true);
        editorStore.setSelectEffect(cancelEditorPictureStatus);
        // setDisbaled(true);
        // editorStore.editor.app.on(CanvasNodeEventName.add, handleDisabledBtn);
        editorStore.editor.app.on(
          CanvasNodeEventName.select,
          handleMainLayerShapeSelect
        );
      } else {
        onSelectionWayChange(null);
      }
      return () => {
        // editorStore.editor.app.off(CanvasNodeEventName.add, handleDisabledBtn);
        editorStore.editor.app.off(
          CanvasNodeEventName.select,
          handleMainLayerShapeSelect
        );

        editorStore.setSelectEffect(() => {});
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [actived]);

    function editorPictureStatus() {
      app.unlockAllShapes();
    }

    const cancelEditorPictureStatus = () => {
      onSelectionWayChange(null);
      editorPictureStatus();
    };

    function handleGraffitiWayChange(way: GraffitiPictureWay | null) {
      onSelectionWayChange(way);
      editorStore.editor.selectorPlugin.selector?.cancelSelect();
      editorStore.editor.app.lockAllShapes();
      editorStore.editor.app.lockAllShapes(
        editorStore.editor.wheeEditorPlugin.plugin?.graffitiLayer
      );
      editorStore.unselectEffectHandler();
      editorStore.setSelectDisabled();
    }

    const handleColorChange = (item: ColorItem) => {
      setSelectedColor(item.rgb);
      onSelectionParamsChange({
        fillColor: item.rgb,
        paintWidth
      });
    };

    const brushSizeChange = (value: number) => {
      onSelectionParamsChange({
        paintWidth: value,
        fillColor: selectedColor
      });
      setPaintWidth(value);
    };

    const WrapperColorPick = (
      <div className="color-picker-wrapper">
        <ColorPicker
          defaultValue={selectedColor}
          onChange={handleColorChange}
        />
      </div>
    );

    const confirmExitGraffitiPicture = async () => {
      onClose?.();
      editorStore.editor.wheeEditorPlugin.plugin?.clearGraffiti();
    };

    const submitGraffitiPicture = async () => {
      // setLoading(true);
      await editorStore.submitGraffitiPicture();
      // setLoading(false);
      onClose?.();
    };

    // const handleDisabledBtn = () => {
    //    setDisbaled(false);
    // };

    const handleMainLayerShapeSelect = debounce(
      ({ nodes }: { nodes: BaseShape<ShapeConfig>[] }) => {
        nodes.forEach(async (node) => {
          if (
            node.config.type === ShapeType.Paint ||
            node.config.type === ShapeType.Image
          ) {
            editorStore.graffitiEditor.isSelectShapeSubmit = true;
            await editorStore.submitGraffitiPicture();
            onClose?.();
            editorStore.graffitiEditor.isSelectShapeSubmit = false;
            return;
          }
        });
      },
      3000,
      { leading: true, trailing: false }
    );

    return (
      <CommonPanel
        title="涂鸦"
        synopsisConfig={{
          x: 380,
          y: 56,
          hidden: false,
          type: TaskCategory.graffiti
        }}
        actived={actived}
        onClose={confirmExitGraffitiPicture}
        extra={
          <SubmitFooter
            buttonLabel={'完成'}
            onSubmit={submitGraffitiPicture}
            loading={editorStore.graffitiEditor.createLoading}
            freeCounts={editorStore.surplus}
            disabled={false}
            isActivated={actived}
          />
        }
      >
        <Form
          className={styles.changePicturePanel}
          form={form}
          initialValues={{
            prompt: '',
            genNums: 4
          }}
        >
          <div className="panel-content-primary" style={{ height: 4 }}>
            {' '}
          </div>
          <AreaSelection
            currentSelectionWay={selectionWay}
            onSelectionWayChange={(way: GraffitiPictureWay) => {
              handleGraffitiWayChange(way);
              onSelectionParamsChange({
                paintWidth,
                fillColor: selectedColor,
                globalCompositeOperation: 'source-over'
              });
              if (selectionWay !== way) {
                trackEvent('ai_modification_tool_click', {
                  tool: Graffiti.SelectionToolsTrace[way],
                  color: selectedColor,
                  subfunction: 'sketch'
                });
              }
            }}
            items={[
              {
                key: SelectionWay.Paint,
                icon: <PaintBold />,
                label: '画笔',
                params: (
                  <>
                    {WrapperColorPick}
                    <div className="paint-size">
                      <EditorSliderInput
                        title="大小"
                        min={1}
                        max={100}
                        value={paintWidth}
                        onChange={(value) => brushSizeChange(value)}
                      />
                    </div>
                  </>
                )
              },
              {
                key: SelectionWay.Box,
                icon: <RoundCornerRectangleBoldFill />,
                label: '矩形',
                params: WrapperColorPick
              },
              {
                key: SelectionWay.Ellipse,
                icon: <CircleBoldFill />,
                label: '圆形',
                params: WrapperColorPick
              },
              {
                key: SelectionWay.Eraser,
                icon: <EraserBold />,
                label: '擦除',
                params: (
                  <>
                    <div className="paint-size">
                      <EditorSliderInput
                        title="大小"
                        min={1}
                        max={100}
                        value={paintWidth}
                        onChange={(value) => brushSizeChange(value)}
                      />
                    </div>
                  </>
                )
              }
            ]}
          />
        </Form>
      </CommonPanel>
    );
  }
);
