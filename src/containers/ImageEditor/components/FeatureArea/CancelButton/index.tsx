import { useEffect, useState } from 'react';
import styles from './index.module.less';

type CancelBUttonProps = React.PropsWithChildren<{
  showDelay: number;
  onClick?(): void;
}>;

export function CancelButton({
  showDelay = 3 * 1000,
  children,
  onClick
}: CancelBUttonProps) {
  const [isShow, setIsShow] = useState(!showDelay);

  useEffect(() => {
    setTimeout(() => {
      setIsShow(true);
    }, showDelay);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {isShow && (
        <div onClick={onClick} className={styles.cancelButton}>
          {children}
        </div>
      )}
    </>
  );
}
