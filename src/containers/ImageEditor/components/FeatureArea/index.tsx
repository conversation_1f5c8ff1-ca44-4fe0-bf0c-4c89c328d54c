import styles from './index.module.less';

import { Drawer, Toolt<PERSON> } from 'antd';
import {
  ReactElement,
  cloneElement,
  useEffect,
  useMemo,
  useRef,
  useState,
  ReactNode
} from 'react';
import classNames from 'classnames';
import { observer } from 'mobx-react';
import useStore from '@/hooks/useStore';
import message, { ToastBack } from '../Toast';
import { LocationMode, TaskCategory, TaskMap } from '@/api/types/imageEditor';
import { trackEvent } from '@/services';
import { FeedbackBold } from '@meitu/candy-icons';
import { useApp } from '@/App';

type Feature = {
  key: string;
  icon: React.ReactNode;
  label: React.ReactNode;
  featurePanel: ReactElement;
};

type FeatureAreaProps = {
  items: Feature[];
  activeKey: string | null;
  onFeatureChangeClick(key: string): void;
  isOpen: boolean;
  // onOpenChange(isOpen: boolean): void;
};
export const FeatureAreaId = 'feature-area';
export const FeatureTabId = 'feature-tab';
function FeatureArea({
  items,
  activeKey,
  onFeatureChangeClick,
  isOpen
}: // onOpenChange
FeatureAreaProps) {
  const editorStore = useStore('EditorStore');
  const itemsAppendedProps = useMemo(
    () =>
      items.map((item) => {
        return {
          ...item,
          featurePanel: cloneElement(item.featurePanel, {
            key: item.key,
            actived: item.key === activeKey,
            onClose: () => {
              onFeatureChangeClick('');
              // onOpenChange(false);
              editorStore.configHeaderBtnState({
                selected: true,
                selectedMove: false
              });
            }
          })
        };
      }),
    [items, activeKey, onFeatureChangeClick, editorStore]
  );

  const activeItem = itemsAppendedProps.find((item) => item.key === activeKey);

  useEffect(() => {
    const featureArea = document.getElementById(FeatureAreaId);
    const featureAreaWidth = featureArea?.clientWidth || 313;
    const featureTab = document.getElementById(FeatureTabId);
    const featureTabWidth = featureTab?.clientWidth || 0;
    //更新鼠标操作位置
    editorStore.editor.app.setCheckMouseBoundaryConfig({
      left: isOpen
        ? 50 + featureTabWidth + featureAreaWidth
        : 50 + featureTabWidth
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);
  const areaItemTimers = useRef<any[]>([]);
  const setAreaItemTimers = (timer: any) => {
    areaItemTimers.current.push(timer);
  };
  const clearTimer = () => {
    areaItemTimers.current.forEach((timer) => {
      clearTimeout(timer);
    });
  };

  const { openFeedbackModal } = useApp();
  const [tooltipOpen, setTooltipOpen] = useState(false);

  return (
    <div className={styles.leftBox}>
      <div className={styles.featureTab} id={FeatureTabId}>
        <Tooltip
          open={tooltipOpen}
          title={'期待您的反馈！'}
          placement="right"
          overlayClassName={styles.feedBackTooltipOverlay}
        >
          <div
            className={styles.feedBackEntry}
            onClick={() => {
              openFeedbackModal();
              setTooltipOpen(false);
            }}
            onMouseEnter={() => {
              setTooltipOpen(true);
            }}
            onMouseLeave={() => {
              setTooltipOpen(false);
            }}
          >
            <FeedbackBold />
          </div>
        </Tooltip>
        {itemsAppendedProps.map((item, key) => {
          return (
            <FeatureAreaItem
              key={item.key}
              activeKey={activeKey}
              itemLength={key}
              itemKey={item.key}
              clickHandle={() => onFeatureChangeClick(item.key)}
              label={item.label}
              icon={item.icon}
              setTimerList={setAreaItemTimers}
              clearTimer={clearTimer}
            />
          );
        })}
      </div>
      <Drawer
        id={FeatureAreaId}
        // title="Basic Drawer"
        rootClassName={styles.drawer}
        width={300}
        placement="left"
        closable={false}
        // onClose={onClose}
        open={isOpen}
        // key={placement}
        mask={false}
        getContainer={false}
      >
        {/* <FoldButton onClick={() => onOpenChange(false)} /> */}
        <div className="feature-panel">
          {activeItem && activeItem.featurePanel}
          {/* {itemsAppendedProps.map((item) => {
            return item.featurePanel;
          })} */}
        </div>
      </Drawer>
    </div>
  );
}
type FeatureAreaItemType = {
  activeKey: string | null;
  itemLength: number;
  itemKey: string;
  icon?: string | ReactNode;
  label?: string | ReactNode;
  clickHandle: () => void;
  setTimerList: (timer: any) => void;
  clearTimer: () => void;
};

export const FeatureAreaItem = observer((props: FeatureAreaItemType) => {
  const {
    activeKey,
    itemKey,
    icon,
    label,
    clickHandle,
    itemLength,
    setTimerList,
    clearTimer
  } = props;

  const editorStore = useStore('EditorStore');
  const hoverTimer = useRef<any>(null);
  const featureItemDom = useRef<HTMLDivElement>(null);
  const activeRef = useRef<string | null>(activeKey);
  useEffect(() => {
    activeRef.current = activeKey;
    if (activeKey) {
      editorStore.configHeaderBtnState({
        selected: false,
        selectedMove: false
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey]);
  useEffect(() => {
    const featureDom = featureItemDom.current;
    if (featureDom) {
      featureDom.addEventListener('mouseenter', showSynopsis);
      featureDom.addEventListener('mouseleave', hideSynopsis);
      featureDom.addEventListener('click', hideSynopsis);
    }

    return () => {
      if (featureDom) {
        featureDom.removeEventListener('mouseenter', showSynopsis);
        featureDom.removeEventListener('mouseleave', hideSynopsis);
        featureDom.removeEventListener('click', hideSynopsis);
      }
      if (hoverTimer.current) clearTimeout(hoverTimer.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const showSynopsis = () => {
    clearTimer();
    hoverTimer.current = setTimeout(() => {
      if (activeRef.current) return;
      const data = {
        x: 80,
        y: 56 + itemLength * 80,
        hidden: false,
        type: itemKey as TaskCategory
      };
      editorStore.setSynopsisConfig(data);
    }, 1000); // 1秒后执行 onHover
    setTimerList(hoverTimer.current);
  };
  const hideSynopsis = () => {
    if (hoverTimer.current) {
      clearTimeout(hoverTimer.current); // 鼠标离开时清除计时器
    }
    editorStore.setSynopsisConfig({ x: 0, y: 0, hidden: true });
  };

  return (
    <div
      className={classNames(
        'feature-tab-btn',
        activeKey === itemKey && 'active'
      )}
      key={itemKey}
      data-key={itemKey}
      onClick={clickHandle}
      ref={featureItemDom}
    >
      <div className="feature-tab-btn-icon">{icon}</div>
      <div className="feature-tab-btn-label">{label}</div>
    </div>
  );
});

export function useFeatureArea() {
  const editorStore = useStore('EditorStore');
  const activeKey = editorStore.currentFeature;
  const setActiveKey = editorStore.setCurrentFeature;
  const isOpen = !!activeKey;
  // const setIsOpen = editorStore.setFeaturePanelIsOpen;
  const [editLeftMode, setEditLeftMode] = useState<string | null>(
    editorStore.currentFeature || ''
  );
  const toastBackRef = useRef<any>(null);
  useEffect(() => {
    setEditLeftMode(activeKey);
  }, [activeKey]);
  function onFeatureChangeClick(key: string) {
    if (key === TaskCategory.extend) {
      const layers = editorStore.editor.wheeEditorPlugin.plugin
        ?.getLayersConfig()
        ?.filter((item) => item.visible);
      if (layers && layers.length === 0) {
        if (toastBackRef.current) {
          toastBackRef.current.destroy();
        }
        const toastMessage = message({
          type: 'error',
          content: '请开启至少一个图层才可进行生成',
          duration: 5000
        });
        toastBackRef.current = toastMessage as ToastBack;
        return;
      }
    }
    trackEvent('ai_modification_subfunction_click', {
      subfunction: TaskMap[key] || key,
      location: LocationMode.sideBar
    });

    if (activeKey === key) {
      // 需求变更 点击相同的tab不再关闭tab
      // setActiveKey(null)
      return;
    }

    setActiveKey(key);
    // setIsOpen(true);
  }

  return {
    isOpen,
    activeKey,
    onFeatureChangeClick,
    // onOpenChange: setIsOpen,
    editLeftMode
  };
}

export default observer(FeatureArea);
