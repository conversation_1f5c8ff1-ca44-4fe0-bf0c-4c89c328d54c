import { Image, Tooltip, Typography } from 'antd';
import styles from './index.module.less';
import classNames from 'classnames';
import { Loading } from '@/components';
import { useEffect, useState } from 'react';
import { fetchTaskConfigList } from '@/api/imageEditor';
import { TaskCategory } from '@/api/types/imageEditor';
import { EditorConfigControlNetModel } from '@/api/types';
import { toAtlasImageView2URL } from '@meitu/util';
import { ImageProcessingParams } from '@/components/ControlNet/ControlNetModelImage';
import { isMobileV2 } from '@/utils/isMobile';

export interface ControlNetProps {
  // 向表单提交的值
  value?: ImageProcessingParams;
  onChange?: (value: ImageProcessingParams) => void;
  // 通过modal选择模型后触发
  onSelectModel?: (model: EditorConfigControlNetModel) => void;
}

export const ControlNet = (props: ControlNetProps) => {
  const { value, onChange, onSelectModel } = props;

  const [controlList, setControlList] = useState<EditorConfigControlNetModel[]>(
    []
  );
  const [activeItemIndex, setActiveItemIndex] = useState(0);

  useEffect(() => {
    getConfigList();
  }, []);

  // 获取画面控制列表
  const getConfigList = () => {
    fetchTaskConfigList({ taskCategory: TaskCategory.compound }).then((res) => {
      if (res.compoundConfig) {
        setControlList(res.compoundConfig.moduleList[0].list);
      }
    });
  };

  useEffect(() => {
    // 默认第一项
    if (controlList.length) {
      onChange?.({
        ...value,
        model: controlList[activeItemIndex].model,
        module: controlList[activeItemIndex].module,
        modelId: controlList[activeItemIndex].id,
        modelConfig: controlList[activeItemIndex]
      });
      onSelectModel?.(controlList[activeItemIndex]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [controlList, activeItemIndex]);

  return (
    <div className={styles.controlBox}>
      {controlList.map((item, index) => (
        <Tooltip
          mouseEnterDelay={0.2}
          overlayClassName={styles.controlTooltipOverlay}
          title={
            !isMobileV2() && (
              <div className="content-box">
                {item.sampleTips && item.sampleTips.samplePic && (
                  <div className="content-list-box">
                    {item.sampleTips?.samplePic && (
                      <div className="content-img-box">
                        <Image
                          src={toAtlasImageView2URL(
                            item.sampleTips?.samplePic,
                            {
                              mode: 2,
                              width: 100
                            }
                          )}
                          placeholder={<Loading />}
                          preview={false}
                          className="content-img"
                        />
                        <div className="content-name">原图</div>
                      </div>
                    )}
                    {item.sampleTips?.prePic && (
                      <div className="content-img-box">
                        <Image
                          src={toAtlasImageView2URL(item.sampleTips?.prePic, {
                            mode: 2,
                            width: 100
                          })}
                          placeholder={<Loading />}
                          preview={false}
                          className="content-img"
                        />
                        <div className="content-name">预处理</div>
                      </div>
                    )}
                    {item.sampleTips?.resultPic && (
                      <div className="content-img-box">
                        <Image
                          src={toAtlasImageView2URL(
                            item.sampleTips?.resultPic,
                            {
                              mode: 2,
                              width: 100
                            }
                          )}
                          placeholder={<Loading />}
                          preview={false}
                          className="content-img"
                        />
                        <div className="content-name">结果图</div>
                      </div>
                    )}
                  </div>
                )}
                <div className="content-title">{item.name}</div>
                <div className="content-desc">{item.desc}</div>
              </div>
            )
          }
          key={item.id}
        >
          <div
            className={classNames(
              styles.itemBox,
              index === activeItemIndex && styles.active
            )}
            key={item.id}
            onClick={() => {
              setActiveItemIndex(index);
            }}
          >
            <Image
              src={toAtlasImageView2URL(item.coverPic, { mode: 2, width: 100 })}
              placeholder={<Loading />}
              preview={false}
              className={styles.itemImg}
            />
            <div className={styles.itemName}>
              <Typography.Text key={item.name} ellipsis={{ tooltip: false }}>
                {item.name ?? ''}
              </Typography.Text>
            </div>
          </div>
        </Tooltip>
      ))}
    </div>
  );
};
