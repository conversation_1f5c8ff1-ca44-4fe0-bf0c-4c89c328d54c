@import '~@/styles/variables.less';

.control-box {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .item-box {
    width: 84px;
    height: 84px;
    border: 1px solid #ebeeff14;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    position: relative;

    &.active {
      border: 2px solid #4053ff;
    }

    .item-img {
      width: 84px;
      height: 84px;
      object-fit: cover;
      user-select: none;
      -webkit-user-drag: none;
    }

    .item-name {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 36px;
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.5) 100%
      );
      display: flex;
      justify-content: center;
      align-items: flex-end;
      padding: 0 4px;
      padding-bottom: 4px;

      span {
        color: #fff;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }
    }
  }
}

.control-tooltip-overlay:global(.@{ant-prefix}-tooltip) {
  width: 300px;

  :global {
    .@{ant-prefix}-tooltip-arrow {
      color: #f1f2f6;

      &::before {
        background: #f1f2f6 !important;
      }
    }

    .@{ant-prefix}-tooltip-content {
      width: 300px;
      border-radius: var(--radius-4, 4px);
      background: var(--background-editorTips, #f1f2f6);
      box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.25);
    }

    .@{ant-prefix}-tooltip-inner {
      border-radius: var(--radius-4, 4px);
      background: var(--background-editorTips, #f1f2f6);
      color: #17171a;
      font-size: 12px;
      border-radius: 4px;
      padding: 16px;

      .content-box {
        .content-list-box {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;

          .content-img-box {
            position: relative;
            width: 84px;
            height: 84px;
            border-radius: 4px;
            overflow: hidden;

            .content-img {
              width: 84px;
              height: 84px;
              object-fit: cover;
              user-select: none;
              -webkit-user-drag: none;
            }

            .content-name {
              position: absolute;
              left: 0;
              bottom: 0;
              width: 100%;
              height: 36px;
              background: linear-gradient(
                180deg,
                rgba(0, 0, 0, 0) 0%,
                rgba(0, 0, 0, 0.5) 100%
              );
              padding-bottom: 4px;
              display: flex;
              justify-content: center;
              align-items: flex-end;
              color: #fff;
              font-size: 12px;
              font-style: normal;
              font-weight: 500;
              line-height: normal;
            }
          }
        }

        .content-title {
          margin-bottom: 6px;
          color: var(--content-editorPrimaryReverse, #17171a);
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
        }

        .content-desc {
          color: var(--content-editorPrimaryReverse, #17171a);
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }
    }
  }
}
