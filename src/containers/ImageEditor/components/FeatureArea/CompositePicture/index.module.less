@import '~@/styles/variables.less';
@content-main: #dcdde5;
@content-secondary: #e1e3eb;
@input-border-color: #29292e;
@accent-selected: #4053ff;
@space-holder: #ebeeff14;
@bg-thumb: #dcdde5;

.later-btn {
  color: @accent-selected;
  cursor: pointer;
  font-size: 14px;
  margin-left: 16px;
}

.change-picture-panel:global(.@{ant-prefix}-form) {
  cursor: default;
  margin-bottom: 100px;

  :global {
    .panel-content-primary {
      color: #dcdde5;
      font-size: 14px;
    }

    .paint-size {
      margin: 16px;
      margin-bottom: 28px;
    }

    .prompt-title {
      margin: 36px 0 12px 0;
    }
  }

  .generate-nums-title {
    margin: 36px 0 12px 0;
    cursor: pointer;
  }

  .preview-box {
    width: 100%;
    height: 129px;
    border-radius: var(--radius-4, 4px);
    border: var(--stroke-1, 1px) solid
      var(--stroke-editorSeparatorPrimary, #2e2e33);
    margin-top: 12px;

    :global .ant-image {
      width: 100%;
      height: 100%;
    }

    .preview-img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      user-select: none;
      -webkit-user-drag: none;
    }
  }

  :global {
    .editor-slider-input-title {
      .title-content {
        color: @content-secondary;
      }
    }

    .editor-slider-input-input.@{ant-prefix}-input-number {
      display: flex;
      width: 60px;
      height: 28px;
      padding: 0px 8px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;

      &:hover {
        border: 1px solid @accent-selected;
      }

      &.@{ant-prefix}-input-number-focused {
        border: 1px solid @accent-selected;
      }

      border: 1px solid @input-border-color;

      .@{ant-prefix}-input-number-input-wrap {
        .@{ant-prefix}-input-number-input {
          color: @content-main;
          text-align: center;
          font-size: 12px;
        }
      }
    }

    .editor-slider-input-slider.@{ant-prefix}-slider {
      .@{ant-prefix}-slider-rail {
        background-color: @space-holder;
      }

      .@{ant-prefix}-slider-track {
        background-color: @accent-selected;
      }
    }
  }
}

.panel-box {
  padding: 0 !important;

  :global {
    .common-panel-title {
      padding: 16px;

      .common-panel-title-close-btn {
        top: 16px;
        right: 15px;
      }
    }

    .common-panel-content-scroll-content {
      margin: 0 auto;
      width: 268px;
      padding-bottom: 16px;
    }
  }
}
