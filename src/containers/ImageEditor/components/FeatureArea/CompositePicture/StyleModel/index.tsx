import type { ModalRef } from '@/components';

import { Form, Space, ConfigProvider, message, FormListFieldData } from 'antd';
import { FormListItem } from './FormListItem';
import { useRef, useState } from 'react';
import { getCategoryIds } from '@/utils/getCategoryId';
import { STRENGTH } from '@/constants';
import { formListNamesWrapper } from '@/utils/formUtils';
import { useInitialStyleModel } from '@/hooks';

import {
  StyleModel as StyleModelComponent,
  useStyleModelList,
  StyleModelModal,
  StyleModelProvider
} from './StyleModelModal';
import { DraftType } from '@/api/types';
// import { trackEvent } from '@/services';
// import { getSource } from '@/utils';
import { TextToImageParams } from '@/containers/TextToImage/ParamsEditor/types';
import { PlusCircleBold } from '@meitu/candy-icons';
import styles from './styles.module.less';

// 风格模型数量上限
const STYLE_MODEL_NUMS_LIMIT = 3;

function StyleModelList() {
  const modelModalRef = useRef<ModalRef>(null);

  const form = Form.useFormInstance<TextToImageParams>();
  const { styleModel, styleModelResponse } = useStyleModelList();
  const [namePath, setNamePath] = useState<
    string | number | Array<string | number>
  >();

  const setModalVisible = (
    visible: boolean,
    namePath?: FormListFieldData['name']
  ) => {
    // trackEvent('edit_model_add_click', {
    //   function: getSource()
    // });

    modelModalRef.current?.setVisible(visible);

    if (!visible) return;

    const fixturesNames =
      namePath !== undefined
        ? formListNamesWrapper('styleModel')(namePath)
        : undefined;

    setNamePath(fixturesNames);
  };

  useInitialStyleModel();

  return (
    <>
      <Form.List name="styleModel">
        {(fields, { add, remove }, { errors }) => {
          return (
            <Space direction="vertical" style={{ width: '100%' }} size={12}>
              {fields.map((field) => {
                return (
                  <FormListItem
                    styleModel={styleModel}
                    key={field.name}
                    onClick={setModalVisible.bind(null, true, field.name)}
                    namePath={[field.name]}
                    remove={() => remove(field.name)}
                  />
                );
              })}
              {
                <Form.Item
                  noStyle
                  shouldUpdate={(prev, cur) =>
                    prev.styleModel?.length !== cur.styleModel?.length
                  }
                >
                  {({ getFieldValue }) => {
                    const selectedNums =
                      getFieldValue('styleModel')?.length ?? 0;

                    // 已选中的风格模型的数量“超过”最大上限的值
                    // 若小于0 则仍然可以添加
                    const exceedLimit = selectedNums - STYLE_MODEL_NUMS_LIMIT;

                    // 需要删除几个才可以再次添加
                    // 例：如果当前超过上限1个，则需要删除2个才能添加
                    const needsDelete = Math.max(exceedLimit + 1, 0);

                    return (
                      <ConfigProvider theme={{ token: { controlHeight: 40 } }}>
                        <div
                          className={styles.modelBtn}
                          onClick={() => {
                            if (needsDelete) {
                              const numsTip =
                                needsDelete > 1 ? `${needsDelete}个` : '';

                              message.warning(
                                `风格模型已达到上限，请删除${numsTip}后添加。`
                              );
                              return;
                            }

                            setModalVisible(true);
                          }}
                        >
                          <PlusCircleBold style={{ marginRight: '4px' }} />
                          添加风格模型
                        </div>
                      </ConfigProvider>
                    );
                  }}
                </Form.Item>
              }
            </Space>
          );
        }}
      </Form.List>

      <StyleModelModal
        ref={modelModalRef}
        onModelClick={({ id, styleModelWeight, images, name, typeName }) => {
          const categoryIds = getCategoryIds(styleModelResponse, id);
          const value = {
            id,
            strength: styleModelWeight ?? STRENGTH,
            categoryIds,
            title: name,
            src: images as string,
            tag: typeName
          };
          const styleList = form.getFieldValue(
            'styleModel'
          ) as TextToImageParams['styleModel'];

          if (styleList?.some((s) => s.id === id)) {
            message.warning('该模型已经设置在风格模型中啦~');
          } else {
            if (namePath === undefined) {
              const styleModel = [...(styleList ?? []), value];
              form.setFieldsValue({ styleModel });
            } else {
              form.setFieldValue(namePath, value);
            }
          }
          modelModalRef.current?.setVisible?.(false);
        }}
      />
    </>
  );
}

export function StyleModel() {
  return (
    <StyleModelProvider from={DraftType.TEXT_TO_IMAGE}>
      <StyleModelComponent>
        <StyleModelList />
      </StyleModelComponent>
    </StyleModelProvider>
  );
}
