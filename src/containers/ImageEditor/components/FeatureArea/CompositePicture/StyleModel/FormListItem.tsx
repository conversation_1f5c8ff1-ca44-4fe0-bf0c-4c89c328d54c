import type { ModelCardProps } from '@/components';

import { SliderInput } from '@/components';
import { TrashCanBold } from '@/icons';
import { formListNamesWrapper } from '@/utils/formUtils';
import { Form } from 'antd';
import styles from './styles.module.less';
import { useStyleModelTooltipOnSlider } from '@/hooks';
import { ModelCard } from './ModelCard';

export interface FormListItemProps {
  styleModel: ModelCardProps<number>['list'];
  namePath: Parameters<typeof formListNamesWrapper>;
  onClick: () => void;
  remove(): void;
}

export const FormListItem = ({
  styleModel,
  namePath,
  onClick,
  remove
}: FormListItemProps) => {
  const { tooltipOnSlider, onPointerEnter, onPointerLeave } =
    useStyleModelTooltipOnSlider({ autoHideDelay: 5000 });

  return (
    <Form.Item noStyle name={namePath}>
      <ModelCard
        childrenFormName="strength"
        list={styleModel}
        onClick={onClick}
        onExtraClick={remove}
        extra={<TrashCanBold />}
      >
        <SliderInput
          controls={false}
          showInputControls={false}
          suffix="%"
          title="强度"
          min={0}
          max={100}
          step={1}
          markNum={3}
          className={styles.sliderInputBox}
          tooltipOnSlider={tooltipOnSlider}
          onPointerEnterSlider={onPointerEnter}
          onPointerLeaveSlider={onPointerLeave}
          customClassNames={{
            title: 'editor-slider-input-title',
            input: 'editor-slider-input-input',
            slider: 'editor-slider-input-slider'
          }}
        />
      </ModelCard>
    </Form.Item>
  );
};
