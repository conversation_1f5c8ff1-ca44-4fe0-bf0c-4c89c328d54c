@import '~@/styles/variables.less';

.model-modal:global(.@{ant-prefix}-modal) {
  font-size: 12px;

  :global(.@{ant-prefix}-modal-content) {
    padding: 12px 20px;
    padding-right: 0;
    background: var(--background-editor<PERSON>ain<PERSON>anel, #1c1c1f) !important;
    box-shadow: 0px 20px 60px 0px rgba(0, 0, 0, 0.35);

    :global(.anticon-search-bold) {
      color: @color-text;
    }

    :global(.ant-modal-close) {
      top: 16px;
      right: 20px;

      svg {
        color: #636370;
      }
    }
  }

  .pr-40 {
    padding-right: 52px;
  }

  .title {
    color: var(--content-editorPrimary, #e1e3eb);
    font-size: @text-16;
    font-weight: 500;
    line-height: 22px;
  }

  .search:global(.@{ant-prefix}-input-affix-wrapper) {
    border-radius: 8px;
    width: 218px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid var(--stroke-editorSeparatorPrimary, #2e2e33);
    background: var(--background-editorInput, #0d0e0f);

    svg {
      color: #e1e3eb;
    }

    input {
      background: var(--background-editorInput, #0d0e0f);
      color: var(--content-editorTertiary, #e1e3eb);

      &::placeholder {
        color: var(--content-editorTertiary, #636370);
      }
    }
  }
}

.empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-bottom: 100px;

  svg {
    width: 40px;
    height: 40px;
  }

  span {
    margin-top: 12px;
    color: var(--content-editorTertiary, #636370);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}

.model-container {
  height: 600px;
  overflow: hidden auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  .list-box {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }
}

.model-container,
.model-container * {
  overflow: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.model-container::-webkit-scrollbar,
.model-container *::-webkit-scrollbar {
  display: none;
}

.modal-tabs {
  &:global {
    &.ant-tabs-small {
      .ant-tabs-nav {
        margin-bottom: 17px !important;
      }
    }
  }

  :global(.@{ant-prefix}-tabs-nav) {
    margin-bottom: 17px !important;

    :global(.@{ant-prefix}-tabs-nav-list) {
      // 对于gap生效的浏览器 需要用上这个属性覆盖掉已经有的生效的gap
      gap: 0;

      // 接下来不论gap是否在浏览器中生效 gap都为0
      // 使用margin-left代替gap
      & > :global(.@{ant-prefix}-tabs-tab) {
        color: var(--content-editorSecondary, #81838c);
        font-size: 16px;
        font-weight: 400;

        :global(.@{ant-prefix}-tabs-tab-btn) {
          color: var(--content-editorSecondary, #81838c);
          font-size: 16px;
          font-weight: 400;
        }

        &:not(:first-child) {
          margin-left: 40px;
        }
      }

      :global(.@{ant-prefix}-tabs-tab-active) {
        color: var(--content-editorPrimary, #e1e3eb);
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;

        :global(.@{ant-prefix}-tabs-tab-btn) {
          color: var(--content-editorPrimary, #e1e3eb);
          font-size: 16px;
          font-weight: 500;
        }
      }
    }
  }
}
