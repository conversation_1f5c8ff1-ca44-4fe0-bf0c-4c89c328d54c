import { Card, Image, Spin, Typography } from 'antd';
import styles from './modelItem.module.less';
import { EditorConfigModelListResponse } from '@/api/types/editorConfig';
import { useState } from 'react';
import { Loading } from '@/components';
import { toAtlasImageView2URL } from '@meitu/util';
import {
  FivePointedStarBold,
  FivePointedStarBoldFill
} from '@meitu/candy-icons';
const { Meta } = Card;

export interface ModelItemProps {
  item: EditorConfigModelListResponse;
  onClick?(): void;
  onIconClick?(): Promise<void>;
  isShowIcon?: boolean;
  cornerLabelUrl?: string;
}

export const ModelItem = ({
  item,
  isShowIcon,
  onClick,
  onIconClick: onIconClickFromProps,
  cornerLabelUrl
}: ModelItemProps) => {
  const [loading, setLoading] = useState(false);
  const onIconClick = async () => {
    if (loading) return;

    setLoading(true);

    try {
      await onIconClickFromProps?.();
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };
  const image = Array.isArray(item.images) ? item.images[0] : item.images;

  return (
    <Card
      onClick={onClick}
      className={styles.modelItem}
      cover={
        <Image
          placeholder={<Loading />}
          preview={false}
          alt={item.name}
          src={toAtlasImageView2URL(image, {
            mode: 2,
            width: 172
          })}
        />
      }
    >
      <Meta
        title={
          <Typography.Text
            ellipsis={{
              tooltip: {
                title: item.name,
                destroyTooltipOnHide: true
              }
            }}
            className={styles.f12}
          >
            {item.name}
          </Typography.Text>
        }
        description={
          <Typography.Text
            type="secondary"
            ellipsis={{
              tooltip: {
                title: item.desc,
                destroyTooltipOnHide: true
              }
            }}
            className={styles.f12}
          >
            {item.desc ?? ''}
          </Typography.Text>
        }
      />
      {isShowIcon && (
        <span
          className={styles.icon}
          onClick={(e) => {
            e.stopPropagation();
            onIconClick();
          }}
        >
          {loading ? (
            <Spin spinning size="small" />
          ) : item.isCollect ? (
            <FivePointedStarBoldFill className={styles.active} />
          ) : (
            <FivePointedStarBold />
          )}
        </span>
      )}
      {cornerLabelUrl && (
        <span
          className={styles.cornerLabel}
          style={{ backgroundImage: `url(${cornerLabelUrl})` }}
        />
      )}
    </Card>
  );
};
