@import '~@/styles/variables.less';

.model-item:global(.@{ant-prefix}-card) {
  border-radius: 4px;
  width: 172px;
  height: 260px;
  user-select: none;
  cursor: pointer;
  max-width: 172px;
  border: none;
  background: var(--background-editor<PERSON><PERSON><PERSON>older, rgba(235, 238, 255, 0.08));

  :global(.@{ant-prefix}-card-cover) {
    width: 100%;
    overflow: hidden;
    border-radius: 4px 4px 0 0;
    margin: 0;

    img {
      border-radius: 4px 4px 0 0;
      height: 172px;
      object-fit: cover;
      transition: 0.6s;

      &[alt] {
        display: block;
      }
    }
  }

  &:global(.@{ant-prefix}-card):hover {
    img {
      transform: scale(1.1);
    }
  }

  :global(.@{ant-prefix}-card-body) {
    & {
      height: 88px;
      padding: 12px 12px 16px 12px;
    }
  }

  :global(.@{ant-prefix}-card-meta) {
    & {
      padding: 0;
      margin: 0;
    }

    &-title {
      margin-bottom: 0px !important;

      span {
        line-height: normal;
        color: var(--content-editorPrimary, #e1e3eb);
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
      }
    }

    &-description {
      span {
        color: var(--content-editorSecondary, #81838c);
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
  }

  .corner-label {
    padding: 1px 6px;
    border-radius: 0px 3px;
    background-size: cover;
    position: absolute;
    top: 3px;
    right: 3px;
    width: 35px;
    height: 18px;
  }

  .f-12 {
    font-size: 12px;
  }

  .icon {
    position: absolute;
    right: 8px;
    top: 8px;
    display: flex;
    width: 28px;
    height: 28px;
    justify-content: center;
    align-items: center;
    border-radius: var(--radius-4, 4px);
    background: var(--background-editorBackgroundMask, rgba(0, 0, 0, 0.5));

    .active {
      svg {
        color: #ffd257;
      }
    }

    svg {
      color: #fff;
    }
  }
}
