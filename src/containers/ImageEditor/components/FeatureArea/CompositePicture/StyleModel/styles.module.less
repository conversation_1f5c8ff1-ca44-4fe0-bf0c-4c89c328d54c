@import '~@/styles/variables.less';

@content-main: #dcdde5;
@content-secondary: #7f818a;
@input-border-color: #29292e;
@accent-selected: #4053ff;
@space-holder: #ebeeff14;
@bg-thumb: #dcdde5;

.model-btn {
  display: flex;
  width: 268px;
  height: 40px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 4px;
  color: var(--content-editorButtonSecondary, #e1e3eb);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
  background: var(
    --background-editorButtonSecondary,
    rgba(235, 238, 255, 0.12)
  );
}

.slider-input-box {
  :global {
    .editor-slider-input-title {
      .title-content {
        color: #81838c;
      }
    }

    .editor-slider-input-input {
      display: flex;
      width: 60px;
      height: 28px;
      padding: 0px 8px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;

      &:hover {
        border: 1px solid @accent-selected;
      }

      &.@{ant-prefix}-input-number-focused {
        border: 1px solid @accent-selected;
      }

      border: 1px solid @input-border-color;

      .@{ant-prefix}-input-number-prefix {
        color: @content-main;
        margin: 0 !important;
      }

      .@{ant-prefix}-input-number {
        width: 22px !important;
        background: none;
      }

      .@{ant-prefix}-input-number-input-wrap {
        input {
          color: @content-main;
          text-align: center;
          font-size: 12px;
        }
      }
    }

    .editor-slider-input-slider.@{ant-prefix}-slider {
      .@{ant-prefix}-slider-rail {
        background-color: @space-holder;
      }

      .@{ant-prefix}-slider-track {
        background-color: @accent-selected;
      }
    }
  }
}
