import { Card, Col, Image, Row, Space, Tag, Typography } from 'antd';
import styles from './index.module.less';
import classNames from 'classnames';
import { Loading } from '@/components';

export interface BaseModelCardProps {
  extra?: React.ReactNode;
  onClick?(): void;
  onExtraClick?(): void;
  children?: React.ReactNode;
  src?: string;
  desc?: string;
  tag?: string;
  title?: string;
  className?: string;
  // 角标背景图
  cornerLabelUrl?: string;

  /**
   *  自定义预览图
   */
  renderPreview?: (src?: string) => React.ReactElement;

  /**
   * 自定义描述
   */
  renderDesc?: (desc?: string) => React.ReactElement;
}

export const BaseModelCard = ({
  extra,
  children,
  onClick,
  src,
  desc,
  tag,
  title,
  onExtraClick,
  className,
  cornerLabelUrl,
  renderPreview,
  renderDesc
}: BaseModelCardProps) => {
  const hasChild = !!children;

  return (
    <Card className={classNames(styles.modelCard, className)}>
      <Row>
        <Col span={24}>
          <Row
            onClick={() => {
              onClick?.();
            }}
            gutter={[12, 0]}
            className={styles.container}
          >
            {renderPreview ? (
              <Col flex="70px" style={{ height: '60px' }}>
                {renderPreview(src)}
              </Col>
            ) : (
              <Col flex="60px" className={styles.imageContainer}>
                <Image
                  key={src ?? ''}
                  src={src ?? ''}
                  placeholder={<Loading />}
                  className="image"
                  width={60}
                  height={60}
                  preview={false}
                />
              </Col>
            )}

            <Col
              flex={1}
              className={classNames({
                [styles.pr29]: !!extra,
                [styles.pr6]: !extra,
                [styles.m0]: hasChild,
                [styles.mAuto]: !hasChild
              })}
            >
              <Space direction="vertical" wrap={false} size={[0, 4]}>
                <div className={styles.responsive}>
                  <Typography.Text
                    key={title}
                    ellipsis={{ tooltip: true }}
                    strong={hasChild}
                  >
                    {title ?? ''}
                  </Typography.Text>

                  {cornerLabelUrl && (
                    <span
                      className={styles.cornerLabel}
                      style={{ backgroundImage: `url(${cornerLabelUrl})` }}
                    />
                  )}
                </div>

                {hasChild && <Tag>{tag ?? ''}</Tag>}
                {!hasChild &&
                  (renderDesc ? (
                    renderDesc(desc)
                  ) : (
                    <Typography.Text type="secondary" className={styles.f12}>
                      {desc ?? ''}
                    </Typography.Text>
                  ))}
              </Space>
            </Col>
          </Row>
        </Col>
        {children && (
          <Col span={24}>
            <div className={styles.children}>{children}</div>
          </Col>
        )}
      </Row>

      {extra && (
        <span className="model-card-extra" onClick={onExtraClick}>
          {extra}
        </span>
      )}
    </Card>
  );
};
