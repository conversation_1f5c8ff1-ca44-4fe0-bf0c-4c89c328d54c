@import '~@/styles/variables.less';

.model-card {
  transition: all @motion-duration-mid;
  position: relative;
  width: 100%;
  height: 158px;

  :global(.model-card) {
    &-extra {
      width: 29px;
      height: 28px;
      position: absolute;
      top: 16px;
      right: 16px;
      background: var(
        --background-editor<PERSON><PERSON>onSecondary,
        rgba(235, 238, 255, 0.12)
      );
      padding: 4px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      svg {
        color: #e1e3eb;
      }
    }
  }

  :global(.@{ant-prefix}-space-vertical) {
    gap: 6px 0px !important;
  }

  &:global(.@{ant-prefix}-card) {
    border-radius: var(--radius-4, 4px);
    background: var(--background-editorBackgroundDefault, #0d0e0f);
    border: none;

    &:hover {
      border-color: @background-slide-fill;
      box-shadow: 0 0 0 2px rgba(5, 155, 255, 0.06);

      img {
        transform: scale(1.1);
      }
    }

    :global(.@{ant-prefix}-card) {
      &-body {
        padding: 16px;
      }
    }
  }

  :global(.@{ant-prefix}-tag) {
    width: 44px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    background: var(--background-editorSelectedAccentMuted, #1c2170);
    border: none;
    color: var(--content-editorPrimary, #e1e3eb);
    font-family: 'SF Pro';
    font-size: 12px;
    font-style: italic;
    font-weight: 700;
    line-height: normal;
  }

  :global(.@{ant-prefix}-image) {
    border-radius: 4px;
    overflow: hidden;

    img {
      border-radius: 4px;
      transition: 0.6s;
      object-fit: cover;
    }

    img[src=''],
    img:not([src]) {
      opacity: 0.3;
    }
  }

  .container {
    flex-wrap: nowrap;
    cursor: pointer;

    .image-container {
      height: 60px;
    }

    .pr-29 {
      padding-right: 35px !important;
    }

    .pr-6 {
      padding-right: 6px !important;
    }

    .m-0 {
      margin: 0;
    }

    .m-auto {
      margin: auto;
    }

    .f-12 {
      font-size: 12px;
    }
  }

  .children {
    margin-top: 16px;
    width: 100%;
  }

  .responsive {
    display: grid;
    grid-template-columns: minmax(auto, max-content) auto;
    gap: 6px;
    align-items: center;

    :global {
      .ant-typography {
        color: #e1e3eb;
        font-weight: 500;
      }
    }

    .corner-label {
      padding: 1px 6px;
      border-radius: 4px;
      background-size: cover;
      width: 36px;
      height: 18px;
    }
  }
}
