import { cloneElement, isValidElement, useMemo } from 'react';
import { BaseModelCard, BaseModelCardProps } from './BaseModelCard';
import _ from 'lodash';
import { BaseModelDefaultParams } from '@/api/types';

export type ModelCardValueType =
  | number
  | {
      id: number;
      [k: string]: any;
    };

export interface ModelCardProps<T>
  extends Omit<BaseModelCardProps, 'src' | 'title' | 'tag' | 'desc'> {
  onChange?: (value: T) => void;
  value?: T;
  list: Required<
    Pick<
      BaseModelCardProps,
      'desc' | 'src' | 'tag' | 'title' | 'cornerLabelUrl'
    > & { id: number; baseModelDefaultParams: BaseModelDefaultParams }
  >[];
  childrenFormName?: string;
}

export const ModelCard = <T extends ModelCardValueType>({
  children,
  value,
  list,
  onChange,
  childrenFormName,
  ...props
}: ModelCardProps<T>) => {
  const matched = useMemo(() => {
    if (typeof value === 'number') {
      return list.find((item) => item.id === value) ?? list[0];
    }

    return list.find((item) => item.id === value?.id) ?? value;
  }, [list, value]);

  const fixtureProps = useMemo(() => {
    if (matched) {
      return {
        ...props,
        ..._.omit(matched as Partial<BaseModelCardProps>, ['id'])
      };
    }
    return props;
  }, [matched, props]);

  const childrenValue = useMemo(() => {
    if (!children || typeof value === 'number' || !childrenFormName)
      return null;
    return value?.[childrenFormName] ?? null;
  }, [children, childrenFormName, value]);

  const onChangeChildren = (v: any) => {
    if (children && childrenFormName && typeof value === 'object')
      onChange?.({ ...(value ?? {}), [childrenFormName]: v });
  };

  if (!matched) return null;

  if (!!children && !childrenFormName) {
    throw new Error('childrenFormName is required when children is not null');
  }

  return (
    <BaseModelCard {...fixtureProps}>
      {isValidElement(children) &&
        cloneElement(children as React.ReactElement, {
          value: childrenValue,
          onChange: onChangeChildren
        })}
    </BaseModelCard>
  );
};
