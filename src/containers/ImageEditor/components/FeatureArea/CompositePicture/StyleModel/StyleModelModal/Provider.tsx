import { type StyleModelResponse, DraftType } from '@/api/types';
import type { PropsWithChildren, Dispatch, SetStateAction } from 'react';

import { createContext, useState } from 'react';

export interface StyleModelContextValue {
  styleModelList: (StyleModelResponse & {
    fetchingCursor?: string;
    preKeyword?: string;
  })[];
  setStyleModelList: (
    styleModelList: StyleModelContextValue['styleModelList']
  ) => void;

  from:
    | DraftType.IMAGE_TO_IMAGE
    | DraftType.TEXT_TO_IMAGE
    | DraftType.AI_MODEL_IMAGE;

  keyword: string;
  setKeyword: Dispatch<SetStateAction<string>>;
}

function initialContextMethod() {
  console.warn('正在使用 `StyleModelContext` 的默认值');
}

export const StyleModelContext = createContext<StyleModelContextValue>({
  styleModelList: [],
  setStyleModelList: initialContextMethod,
  from: DraftType.TEXT_TO_IMAGE,
  keyword: '',
  setKeyword: initialContextMethod
});

interface StyleModelProviderProps {
  from:
    | DraftType.IMAGE_TO_IMAGE
    | DraftType.TEXT_TO_IMAGE
    | DraftType.AI_MODEL_IMAGE;
}

export function StyleModelProvider({
  children,
  from
}: PropsWithChildren<StyleModelProviderProps>) {
  const [styleModelList, setStyleModelList] = useState<
    StyleModelContextValue['styleModelList']
  >([]);
  const [keyword, setKeyword] = useState<string>('');

  return (
    <StyleModelContext.Provider
      value={{
        from,
        styleModelList,
        setStyleModelList,
        keyword,
        setKeyword
      }}
    >
      {children}
    </StyleModelContext.Provider>
  );
}
