import CommonPanel from '../CommonPanel';
import styles from './index.module.less';
import EditorTextArea from '../../EditorTextArea';
import { Form } from 'antd';
import GenerateNums from '../../GenerateNums';
import TooltipTitle from '../../TooltipTitle';
import SubmitFooter from '../../SubmitFooter';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState, useCallback, useRef } from 'react';
import { v4 as uuid } from 'uuid';
import { cancelTask, requestInferenceTask } from '@/api/imageEditor';
import { TaskCategory } from '@/api/types/imageEditor';
import { ToastBack } from '../../Toast';
import { observer } from 'mobx-react';
import useStore from '@/hooks/useStore';
import {
  handleInvalidTokenError,
  handleRequestError
} from '@/containers/ImageEditor/utils/handleRequestError';
import { Image } from 'antd';
import { Loading, SliderInput } from '@/components';
import { StyleModel } from './StyleModel';
import { ControlNet } from './ControlNet';
import { StyleModel as StyleModelType } from '@/types';
import { EditorConfigControlNetModel } from '@/api/types/editorConfig';
import { CancelButton } from '../CancelButton';
import { queryPolling } from '@/containers/ImageEditor/utils/queryPolling';
import { MediaType, MtccFuncCode, ResultImage } from '@/api/types';
import { createImage } from '@/utils/cropImage';
import {
  CanvasNodeEventName,
  CanvasViewEventName,
  Image as ImageShape,
  ShapeType
} from '@/editor/core/src';
import { trackEvent } from '@/services';
import { Subfunction } from '@/containers/ImageEditor/constant/trace';
import { FunctionCode } from '@/api/types/meidou';
import { optimizeImage } from '@/containers/ImageEditor/utils/image';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { uploaderFunc } from '@/containers/ImageEditor/utils/upload';
import { Uploader } from '@meitu/upload';
import { useAutoCloseToast } from '@/containers/ImageEditor/hooks/useToast';

// 任务超时时间
const QUERY_POLLING_TIME_OUT = 10 * 60 * 1000;

type ChangePictureProps = {
  actived?: boolean;
  onClose?: () => void;
};
function CompositePicture({ actived, onClose }: ChangePictureProps) {
  const editorStore = useStore('EditorStore');
  const projectId = editorStore.projectInfo.id;
  const { wheeEditorPlugin, app } = editorStore.editor;
  const [form] = useForm();

  const [loading, setLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | undefined>('');
  const [genNums, setGenNums] = useState<number>(4);
  const toastBackRef = useRef<any>(null);
  const childRef = useRef<any>(null);

  const { updateMeiDouBalance } = useMeiDouBalance();
  const { message } = useAutoCloseToast();

  useEffect(() => {
    app.on(CanvasNodeEventName.select, handleSelectLayer);
    app.on(CanvasNodeEventName.zIndexChanged, handleSelectLayer);
    app.on(CanvasNodeEventName.updateAfter, getCurrentUrl);
    app.on(CanvasViewEventName.redoAfter, getCurrentUrl);
    app.on(CanvasViewEventName.undoAfter, getCurrentUrl);

    return () => {
      app.off(CanvasNodeEventName.select, handleSelectLayer);
      app.off(CanvasNodeEventName.zIndexChanged, handleSelectLayer);
      app.off(CanvasNodeEventName.updateAfter, getCurrentUrl);
      app.off(CanvasViewEventName.redoAfter, getCurrentUrl);
      app.off(CanvasViewEventName.undoAfter, getCurrentUrl);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [app]);

  useEffect(() => {
    getCurrentUrl();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 获取当前可见图层
  const getCurrentUrl = () => {
    requestAnimationFrame(() => {
      editorStore.editor.wheeEditorPlugin.plugin
        ?.getImagesLayer('image/png')
        .then((src) => {
          setPreviewUrl(src);
        });
    });
  };

  // 退出AI合成tab
  const handleSelectLayer = () => {
    if (editorStore.currentFeature === TaskCategory.compound) {
      editorStore.switchFeature('');
      editorStore.setFeaturePanelIsOpen(false);
    }
  };

  // 进入合成，取消画板图层选中状态
  useEffect(() => {
    if (actived) {
      editorStore.unselectMoveHandler();
      editorStore.editor.selectorPlugin.selector?.cancelSelect();
      editorStore.editor.selectorPlugin.selector?.disable();
      app.lockAllShapes();
      editorStore.unselectEffectHandler();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [actived]);

  // 合成中 loading toast
  const MessageContent = (_props: { delay: number }) => {
    const [content, setContent] = useState('AI合成中，请稍后...');
    useEffect(() => {
      setTimeout(() => {
        setContent('云端算力正在加速生成中...');
      }, _props.delay);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return <span>{content}</span>;
  };

  async function handleSubmit(price: any) {
    if (loading) {
      return;
    }

    if (!navigator.onLine) {
      message({
        type: 'error',
        content: '网络异常',
        duration: 3000
      });
      return;
    }

    // 无可见图层时
    const layers = editorStore.editorLayersHandler();
    if (layers.filter((item) => item.hidden === false).length === 0) {
      if (toastBackRef.current) {
        toastBackRef.current.destroy();
      }
      const toastMessage = message({
        type: 'error',
        content: '请开启至少一个图层才可进行生成',
        duration: 3000
      });
      toastBackRef.current = toastMessage as ToastBack;
      return;
    }

    setLoading(true);

    const originURL = await uploaderFunc(
      Uploader.dataURLToFile(previewUrl ?? '', 'origin.png'),
      'png',
      1 * 60 * 1000
    );

    // 组装请求参数
    const prompt = form.getFieldValue('prompt');
    const batchSize = form.getFieldValue('genNums');
    const styleModel: StyleModelType[] = form.getFieldValue('styleModel') ?? [];
    const controlNet = form.getFieldValue('controlNet');
    const weight = form.getFieldValue('weight');
    const rect = wheeEditorPlugin.plugin?.getVisibleAreaRect();
    const params = {
      prompt,
      batchSize,
      width: Math.round(rect?.width ?? 0),
      height: Math.round(rect?.height ?? 0),
      styleModelConfig: styleModel.map(({ id, strength, categoryIds }) => {
        return {
          styleModelId: id,
          styleModelWeight: strength,
          styleModelCategories: categoryIds
        };
      }),
      controlnetUnits: [
        {
          ...controlNet,
          enabled: true,
          inputImage: originURL.url,
          weight: weight / 100,
          guidanceStart: controlNet.modelConfig?.guidanceStart,
          guidanceEnd: controlNet.modelConfig?.guidanceEnd,
          isEdit: weight / 100 === controlNet.modelConfig?.weight ? 0 : 1
        }
      ]
    };

    trackEvent('ai_modification_create_btn_click', {
      text: prompt,
      negative_text: '',
      batch_size: batchSize,
      is_text: prompt === '' ? 0 : 1,
      controlnet_units: params.controlnetUnits,
      style_model_config: params.styleModelConfig,
      subfunction: Subfunction.Compound,
      free_batch_size: price && price?.useFreeNum + ''
    });

    let _closeToast: null | (() => void) = null;
    let isCanceledByUser = false;
    try {
      const { destroy: closeToast } = message({
        type: 'loading',
        content: '',
        isShowMask: true,
        customNode: (
          <>
            <MessageContent delay={60 * 1000} />
            <CancelButton showDelay={60 * 1000} onClick={handleCancel}>
              取消合成
            </CancelButton>
          </>
        )
      });
      _closeToast = closeToast;

      let layerId = uuid();
      const { id: messageId } = await requestInferenceTask({
        taskCategory: TaskCategory.compound,
        projectId: editorStore.projectInfo.id,
        layerId,
        params,
        functionName: MtccFuncCode.FuncCodeAiSynthesisRight,
        mediaType: MediaType.Photo,
        resMediaType: MediaType.Photo
      });
      // 重新拉取定价接口
      getPrice();
      await editorStore.updateSurplus();

      if (isCanceledByUser) {
        cancelTask({
          projectId,
          msgId: messageId
        });
        return;
      }

      const { result, cancelPolling } = await queryPolling(
        messageId,
        3000,
        TaskCategory.compound,
        QUERY_POLLING_TIME_OUT
      );
      const pollingResult = await result;
      if (pollingResult.isCanceled) {
        if (pollingResult.isTimeout) {
          cancelTask({
            projectId,
            msgId: messageId
          });
          throw new Error('生成失败，请重试');
        }

        return;
      }
      const historyList = pollingResult.data;

      function handleCancel() {
        isCanceledByUser = true;
        try {
          // 如果还没有创建轮训任务 取消轮训会抛出异常
          cancelPolling();
          // 运行到这里 轮训任务已经取消 还需要手动取消推理任务
          cancelTask({
            projectId,
            msgId: messageId
          });
        } catch (e) {
          // 在轮训之前调用取消轮训会出现异常
        } finally {
          setLoading(false);
          _closeToast?.();
        }
      }

      const imageList: ResultImage[] = historyList[0]?.resultImages ?? [];
      let successList = imageList
        .filter((image) => image.imageStatus === 1)
        .map(({ url }) => url);
      successList = [...successList];

      trackEvent('ai_modification_create_success', {
        text: prompt,
        negative_text: '',
        is_text: prompt === '' ? 0 : 1,
        controlnet_units: params.controlnetUnits,
        style_model_config: params.styleModelConfig,
        // 成功时仅上报成功张数
        batch_size: successList.length,
        subfunction: Subfunction.Compound,
        free_batch_size: price && price?.useFreeNum + ''
      });

      if (successList.length === 0) return;
      // 同步结果到编辑器
      editorStore.appendTaskWithLayer(layerId, {
        id: messageId,
        type: TaskCategory.compound
      });
      const image = await createImage(
        optimizeImage(successList[0], {
          width: editorStore.maxEditorWidth,
          height: editorStore.maxEditorHeight
        })
      );
      const node = new ImageShape({
        x: image.width / 2,
        y: image.height / 2,
        image,
        id: layerId,
        width: image.width,
        height: image.height,
        type: ShapeType.Image,
        draggable: true,
        enableSelect: true
      });
      wheeEditorPlugin?.plugin?.addEditor(node);
      // 清空功能tab选中态
      editorStore.switchFeature('');
      editorStore.setFeaturePanelIsOpen(false);
      editorStore.setLayersIsOpen(true);
      form.resetFields();
      setLoading(false);
      _closeToast();
    } catch (e: any) {
      if (isCanceledByUser) {
        return;
      }

      if (e.isTimeout) {
        message({
          type: 'error',
          content: '生成失败，请重试',
          duration: 5000
        });
        return;
      }

      if (handleInvalidTokenError(e)) {
        return;
      }

      handleRequestError(e);
    } finally {
      await editorStore.updateSurplus();
      updateMeiDouBalance();
      getPrice();
      if (!isCanceledByUser) {
        setLoading(false);
        _closeToast?.();
      }
    }
  }
  // 调用子组件中的getPrice方法
  const getPrice = useCallback(() => {
    // console.log('childRef', childRef);
    childRef?.current?.getPrice();
  }, []);
  const getFunctionBody = () => {
    const prompt = form.getFieldValue('prompt');
    const batchSize = form.getFieldValue('genNums');
    const styleModel: StyleModelType[] = form.getFieldValue('styleModel') ?? [];
    const controlNet = form.getFieldValue('controlNet');
    const weight = form.getFieldValue('weight');
    const rect = wheeEditorPlugin.plugin?.getVisibleAreaRect();
    const params = {
      prompt,
      batchSize,
      width: Math.round(rect?.width ?? 0),
      height: Math.round(rect?.height ?? 0),
      styleModelConfig: styleModel.map(({ id, strength, categoryIds }) => {
        return {
          styleModelId: id,
          styleModelWeight: strength,
          styleModelCategories: categoryIds
        };
      }),
      controlnetUnits: [
        {
          ...controlNet,
          enabled: true,
          inputImage: previewUrl?.split('?')[0],
          weight: weight / 100,
          guidanceStart: controlNet?.modelConfig?.guidanceStart,
          guidanceEnd: controlNet?.modelConfig?.guidanceEnd
        }
      ]
    };
    return {
      params: {
        ...params
      }
    };
  };

  return (
    <CommonPanel
      title="AI合成"
      synopsisConfig={{
        x: 380,
        y: 56,
        hidden: false,
        type: TaskCategory.compound
      }}
      actived={actived}
      extra={
        <SubmitFooter
          buttonLabel={'立即生成'}
          onClick={handleSubmit}
          loading={loading}
          freeCounts={editorStore.surplus}
          disabled={!projectId}
          isActivated={actived}
          ref={childRef}
          code={FunctionCode.compound}
          getFunctionBody={getFunctionBody}
          genNums={genNums}
        />
      }
      onClose={onClose}
      className={styles.panelBox}
    >
      <Form
        className={styles.changePicturePanel}
        form={form}
        initialValues={{
          prompt: '',
          genNums: 4
        }}
      >
        <div className="panel-content-primary" style={{ marginTop: 16 }}>
          合并可见图层
        </div>
        <Form.Item name="preview">
          <div className={styles.previewBox}>
            {previewUrl && (
              <Image
                src={previewUrl}
                placeholder={<Loading />}
                preview={false}
                className={styles.previewImg}
              />
            )}
          </div>
        </Form.Item>

        <div className="panel-content-primary prompt-title">提示词</div>
        <Form.Item name="prompt">
          <EditorTextArea placeholder="请输入你的创意" maxLength={800} />
        </Form.Item>

        <div className="panel-content-primary prompt-title">画面参考</div>
        <Form.Item name="controlNet">
          <ControlNet
            onSelectModel={(model: EditorConfigControlNetModel | undefined) => {
              form.setFieldValue('weight', Number(model?.weight) * 100);
            }}
          />
        </Form.Item>

        <Form.Item name="weight">
          <SliderInput
            title="合成力度"
            min={1}
            max={100}
            className={styles.editorSliderInput}
            customClassNames={{
              title: 'editor-slider-input-title',
              input: 'editor-slider-input-input',
              slider: 'editor-slider-input-slider'
            }}
            markNum={0}
            showInputControls={false}
          />
        </Form.Item>

        <div className="panel-content-primary prompt-title">风格模型</div>
        <StyleModel />

        <TooltipTitle
          title="生成张数"
          tooltips="生成张数越多，耗时越久。"
          className={styles.generateNumsTitle}
        />
        <Form.Item name="genNums">
          <GenerateNums
            onChange={(value) => {
              // console.log(value, 'value')
              setGenNums(value);
            }}
          />
        </Form.Item>
      </Form>
    </CommonPanel>
  );
}

export default observer(CompositePicture);
