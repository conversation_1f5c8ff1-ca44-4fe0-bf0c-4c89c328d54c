import styles from './index.module.less';

type FoldButtonProps = {
  onClick?(): void;
};

export function FoldButton({ onClick }: FoldButtonProps) {
  return (
    <div className={styles.foldIcon} onClick={onClick}>
      <svg
        className="fold-icon-narrow"
        xmlns="http://www.w3.org/2000/svg"
        width="9"
        height="10"
        viewBox="0 0 9 10"
        fill="none"
      >
        <path
          d="M5.60295 0.525746C5.88783 0.720663 5.96075 1.10961 5.76584 1.39449L3.29898 4.9999L5.76584 8.6053C5.96075 8.89018 5.88783 9.27913 5.60295 9.47405C5.31807 9.66896 4.92912 9.59604 4.7342 9.31116L2.18685 5.58811C1.94423 5.2335 1.94423 4.7663 2.18685 4.41168L4.7342 0.688636C4.92912 0.403758 5.31807 0.33083 5.60295 0.525746Z"
          fill="#7F818A"
        />
      </svg>
      <svg
        className="fold-icon-background"
        xmlns="http://www.w3.org/2000/svg"
        width="12"
        height="80"
        viewBox="0 0 12 80"
        fill="none"
      >
        <path
          d="M-0.5 79.2185V0.781455L2.4954 2.16395C4.76245 3.21028 6.42731 3.97939 7.68577 4.73009C8.93403 5.47471 9.74353 6.17942 10.3175 7.07651C10.8915 7.97359 11.1921 9.0039 11.3451 10.4493C11.4994 11.9065 11.5 13.7405 11.5 16.2373V63.7627C11.5 66.2595 11.4994 68.0935 11.3451 69.5507C11.1921 70.9961 10.8915 72.0264 10.3175 72.9235C9.74353 73.8206 8.93404 74.5253 7.68577 75.2699C6.42731 76.0206 4.76245 76.7897 2.4954 77.8361L-0.5 79.2185Z"
          fill="#17171A"
          stroke="#29292E"
        />
      </svg>
    </div>
  );
}
