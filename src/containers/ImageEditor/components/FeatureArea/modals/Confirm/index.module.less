@import '~@/styles/variables.less';

@background-color: #1c1c1f;
@close-btn-color: #636370;
@close-btn-color--hover: #a5a7b8;

@footer-ok-bg-color: #3549ff;
@footer-ok-text-color: #fff;

@footer-cancel-bg-color: rgba(235, 238, 255, 0.12);
@footer-cancel-text-color: #e1e3eb;

.confirm-modal:global(.@{ant-prefix}-modal-root) {
  :global(.@{ant-prefix}-modal-content) {
    padding: 20px;
    width: 340px;
    box-sizing: border-box;
    border-radius: 8px;
    position: relative;
    background-color: @background-color;

    .confirm {
      overflow: hidden;
      :global {
        .confirm-footer {
          margin-top: 20px;
          display: flex;
          width: 100%;
          height: 40px;
          justify-content: space-between;

          &-btn {
            width: 142px;
            height: 40px;
            line-height: 40px;
            border-radius: 4px;
            text-align: center;
            font-size: 14px;
            cursor: pointer;
          }

          &-btn-ok {
            background-color: @footer-ok-bg-color;
            color: @footer-ok-text-color;
            font-weight: 600;
          }

          &-btn-cancel {
            background-color: @footer-cancel-bg-color;
            color: @footer-cancel-text-color;
          }
        }

        .confirm-close-btn {
          position: absolute;
          right: 15px;
          top: 16px;
          color: @close-btn-color;
          font-size: 16px;
          line-height: 1;
          cursor: pointer;

          &:hover {
            color: @close-btn-color--hover;
          }
        }
      }
    }
  }
}
