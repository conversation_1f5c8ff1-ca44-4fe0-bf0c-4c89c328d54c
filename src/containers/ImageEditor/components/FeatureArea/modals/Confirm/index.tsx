import { Modal, ModalProps } from 'antd';
import styles from './index.module.less';
import { CrossBlack } from '@meitu/candy-icons';
import classNames from 'classnames';
import { useEffect } from 'react';
import useStore from '@/hooks/useStore';
import { observer } from 'mobx-react';
type ConfirmProps = React.PropsWithChildren<{
  // 取消按钮的文本
  cancelTitle?: string;

  // 确定按钮的文本
  okTitle?: string;

  // 右上角关闭点击的回调
  onClickClose?(): void;

  // 取消按钮点击的回调
  onClickCancel?(): void;

  // 确定按钮点击的回调
  onClickOk?(): void;

  contentClassName?: string;
}> &
  ModalProps;

export const Confirm = observer((props: ConfirmProps) => {
  const editorStore = useStore('EditorStore');
  const {
    children,
    cancelTitle = '取消',
    okTitle = '确定',
    onClickClose,
    onClickCancel,
    onClickOk,
    contentClassName,
    afterOpenChange,
    ...modalProps
  } = props;

  /**
   * 当选中图层鼠标按下时 如果没有抬起 移动鼠标就会触发图层的拖动
   * 通过组织事件传播的方式组织图片被拖动
   */
  function handleAfterOpenChange(open: boolean) {
    if (open) {
      window.addEventListener('mousemove', preventMouseMove, true);
      editorStore.editor.app.keyCommand.stop();
    } else {
      window.removeEventListener('mousemove', preventMouseMove, true);
      editorStore.editor.app.keyCommand.restore();
    }

    afterOpenChange?.(open);
  }

  /**
   * 保证阻断事件传播的处理器被卸载
   */
  useEffect(() => {
    return () => {
      window.removeEventListener('mousemove', preventMouseMove, true);
    };
  }, []);

  return (
    <Modal
      {...modalProps}
      rootClassName={styles.confirmModal}
      closeIcon={null}
      footer={null}
      afterOpenChange={handleAfterOpenChange}
    >
      <div className={styles.confirm}>
        <div className={classNames('confirm-content', contentClassName)}>
          {children}
        </div>

        <div className="confirm-footer">
          <div
            className="confirm-footer-btn confirm-footer-btn-cancel"
            onClick={onClickCancel}
          >
            {cancelTitle}
          </div>
          <div
            className="confirm-footer-btn confirm-footer-btn-ok"
            onClick={onClickOk}
          >
            {okTitle}
          </div>
        </div>

        <div className="confirm-close-btn" onClick={onClickClose}>
          <CrossBlack />
        </div>
      </div>
    </Modal>
  );
});

function preventMouseMove(e: MouseEvent) {
  e.stopPropagation();
}
