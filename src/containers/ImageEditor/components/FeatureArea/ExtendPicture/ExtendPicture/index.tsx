import CommonPanel from '../../CommonPanel';
import styles from './index.module.less';
import EditorTextArea from '../../../EditorTextArea';
import { Form } from 'antd';
import TooltipTitle from '../../../TooltipTitle';
import GenerateNums from '../../../GenerateNums';
import SubmitFooter from '../../../SubmitFooter';
import GenerateRatio from '../../../GenerateRatio';
import useStore from '@/hooks/useStore';
import { useCallback, useEffect, useState, useRef } from 'react';
import { queryPolling } from '@/containers/ImageEditor/utils/queryPolling';
import { cancelTask, requestInferenceTask } from '@/api/imageEditor';
import { TaskCategory } from '@/api/types/imageEditor';
import { observer } from 'mobx-react-lite';
import { v4 as uuidv4 } from 'uuid';
import { trackEvent } from '@meitu/subscribe';
import { Subfunction } from '@/containers/ImageEditor/constant/trace';
import { CancelButton } from '../../CancelButton';
import { handleRequestError } from '@/containers/ImageEditor/utils/handleRequestError';
import { FunctionCode } from '@/api/types/meidou';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useAutoCloseToast } from '@/containers/ImageEditor/hooks/useToast';
import {
  PictureBold,
  Ratio11Bold,
  Ratio169Bold,
  Ratio23Bold,
  Ratio32Bold,
  Ratio34Bold,
  Ratio43Bold,
  Ratio916Bold,
  RatioFreeBold
} from '@meitu/candy-icons';
import { MediaType, MtccFuncCode } from '@/api/types';

// 任务超时时间
const QUERY_POLLING_TIME_OUT = 5 * 60 * 1000;
type ExtendPictureProps = {
  actived?: boolean;
  showClose?: boolean;
  backMainEdit?: () => void;
  onClose?: () => void;
};

const ratioOptions = [
  {
    label: '自由',
    image_ratio: 'free',
    icon: <RatioFreeBold />
  },
  {
    label: '原比例',
    image_ratio: 'original',
    icon: <PictureBold />
  },
  {
    label: '1:1',
    image_ratio: '1:1',
    icon: <Ratio11Bold />
  },
  {
    label: '3:2',
    image_ratio: '3:2',
    icon: <Ratio32Bold />
  },
  {
    label: '2:3',
    image_ratio: '2:3',
    icon: <Ratio23Bold />
  },
  {
    label: '4:3',
    image_ratio: '4:3',
    icon: <Ratio43Bold />
  },
  {
    label: '3:4',
    image_ratio: '3:4',
    icon: <Ratio34Bold />
  },
  {
    label: '16:9',
    image_ratio: '16:9',
    icon: <Ratio169Bold />
  },
  {
    label: '9:16',
    image_ratio: '9:16',
    icon: <Ratio916Bold />
  }
];

function ExtendPicture({
  actived,
  showClose = true,
  backMainEdit = () => {},
  onClose
}: ExtendPictureProps) {
  const editorStore = useStore('EditorStore');
  const [loading, setLoading] = useState(false);
  const projectId = editorStore.projectInfo.id;
  const [genNums, setGenNums] = useState(4);
  const [form] = Form.useForm();
  const childRef = useRef<any>(null);
  const { updateMeiDouBalance } = useMeiDouBalance();
  const { message } = useAutoCloseToast();

  useEffect(() => {
    form.resetFields();
  }, [form, actived]);

  const changeImageRatio = (value: string) => {
    trackEvent('ai_modification_tool_click', {
      subfunction: 'extension',
      size: value
    });
  };

  async function handleSubmit(price: any) {
    let trackParams: any = {
      text: form.getFieldValue('prompt'),
      negative_text: '',
      batch_size: form.getFieldValue('genNums'),
      // subfunction: Subfunction.Extension,
      size: form.getFieldValue('imageRatio'),
      free_batch_size: price && price?.useFreeNum + ''
    };
    if (editorStore.isExtendEditor) {
      trackParams['subfunction'] = Subfunction.Extension;
    } else {
      trackParams['secondary_function'] = Subfunction.Extension;
    }
    trackEvent('ai_modification_create_btn_click', {
      ...trackParams
    });
    if (!navigator.onLine) {
      message({
        type: 'error',
        content: '网络异常',
        duration: 3000
      });
      return;
    }
    let _closeToast: null | (() => void) = null;
    setLoading(true);
    try {
      const { destroy: closeToast } = message({
        type: 'loading',
        content: '生成中，请稍后...',
        isShowMask: true,
        customNode: (
          <CancelButton showDelay={60 * 1000} onClick={handleCancel}>
            取消
          </CancelButton>
        )
      });
      _closeToast = closeToast;
      let extendParam = await editorStore.extendParamsHandler();
      let layerId = editorStore.selectExtendLayerId[0];
      if (editorStore.isExtendEditor) {
        layerId = uuidv4();
      }
      const response = await requestInferenceTask({
        taskCategory: TaskCategory.extend,
        projectId: editorStore.projectInfo.id,
        layerId: layerId,
        params: {
          prompt: form.getFieldValue('prompt'),
          imageRatio: form.getFieldValue('imageRatio'),
          generateNum: form.getFieldValue('genNums'),
          imageFile: extendParam.image_file ?? '',
          width: extendParam.width,
          height: extendParam.height,
          centerX: extendParam.center_x,
          centerY: extendParam.center_y,
          scale: extendParam.scale
        },
        functionName: MtccFuncCode.FuncCodeImageModifyExtendRight,
        mediaType: MediaType.Photo,
        resMediaType: MediaType.Photo
      });
      // 重新拉取定价接口
      getPrice();
      const messageId = response.id;
      await editorStore.updateSurplus();

      const { result, cancelPolling } = await queryPolling(
        messageId,
        3000,
        TaskCategory.extend,
        QUERY_POLLING_TIME_OUT
      );
      const pollingResult = await result;
      const historyList = pollingResult.data;

      function handleCancel() {
        trackEvent('ai_modification_create_cancel', {
          text: form.getFieldValue('prompt'),
          negative_text: '',
          batch_size: form.getFieldValue('genNums'),
          subfunction: Subfunction.Extension
        });

        try {
          // 如果还没有创建轮训任务 取消轮训会抛出异常
          cancelPolling();

          // 运行到这里 轮训任务已经取消 还需要手动取消推理任务
          cancelTask({
            projectId,
            msgId: messageId
          });
          // .then(() => {
          //   editorStore.updateSurplus();
          // });
        } catch (e) {
          // 在轮训之前调用取消轮训会出现异常
        } finally {
          setLoading(false);
          _closeToast?.();
        }
      }

      const imageList: any[] = historyList[0]?.resultImages ?? [];
      // const originImage = historyList[0]?.originPic;
      let successList = imageList
        .filter((image) => image.imageStatus === 1)
        .map(({ url }) => url);
      successList = [...successList];
      trackEvent('ai_modification_create_success', {
        ...trackParams
      });

      if (successList.length === 0) return;
      // 同步结果到编辑器
      await editorStore.syncExtendResultToEditor(
        successList[0],
        messageId,
        layerId
      );
      await editorStore.saveQueue.runningPromise;
      editorStore.destroyExtendEditor();
      editorStore.switchFeature('');
      editorStore.setFeaturePanelIsOpen(false);
      editorStore.setLayersIsOpen(true);
      form.resetFields();
      setLoading(false);
      closeToast();
      // useMeiDouBalance();
      backMainEdit && backMainEdit();
    } catch (error: any) {
      // console.log(error);
      handleRequestError(error);
      // useMeiDouBalance();
    } finally {
      await editorStore.updateSurplus();
      updateMeiDouBalance();
      setLoading(false);
      _closeToast?.();
      getPrice();
    }
  }
  const getFunctionBody = () => {
    return {
      prompt: form.getFieldValue('prompt'),
      imageRatio: form.getFieldValue('imageRatio'),
      generateNum: form.getFieldValue('genNums')
    };
  };
  // 调用子组件中的getPrice方法
  const getPrice = useCallback(() => {
    // console.log('childRef', childRef);
    childRef?.current?.getPrice();
  }, []);

  return (
    <CommonPanel
      title="扩图"
      synopsisConfig={{
        x: 380,
        y: 56,
        hidden: false,
        type: editorStore.isExtendEditor
          ? TaskCategory.extend
          : TaskCategory.extendLayer
      }}
      actived={actived}
      showClose={showClose}
      extra={
        <SubmitFooter
          buttonLabel="立即生成"
          onClick={handleSubmit}
          ref={childRef}
          loading={loading}
          freeCounts={editorStore.surplus}
          isActivated={actived}
          code={FunctionCode.extend}
          disabled={editorStore.surplus <= 0}
          getFunctionBody={getFunctionBody}
          genNums={genNums}
        />
      }
      onClose={() => {
        onClose && onClose();
        editorStore.destroyExtendEditor();
        editorStore.editor.selectorPlugin.selector?.cancelSelect();
      }}
      dataKey={TaskCategory.extend}
    >
      <Form
        className={styles.changePicturePanel}
        form={form}
        initialValues={{
          prompt: '',
          imageRatio: 'free',
          genNums: 4
        }}
        style={{
          marginBottom: '100px'
        }}
      >
        <div className="panel-content-primary prompt-title">提示词</div>
        <Form.Item name="prompt">
          <EditorTextArea
            // placeholder="请输入你的创意&#10;可直接输入想要修改的描述，如红色的车，围巾"
            placeholder="描述你想要的扩图内容。"
            maxLength={800}
          />
        </Form.Item>

        <div className="panel-content-primary extend-ratio">扩展比例</div>
        <Form.Item name="imageRatio">
          <GenerateRatio
            onChange={(val) => {
              changeImageRatio(val);
            }}
            options={ratioOptions}
          ></GenerateRatio>
        </Form.Item>

        <TooltipTitle
          title="生成张数"
          tooltips="生成张数越多，耗时越久。"
          className={styles.generateNumsTitle}
        />
        <Form.Item name="genNums">
          <GenerateNums
            onChange={(value) => {
              setGenNums(value);
            }}
          />
        </Form.Item>
      </Form>
    </CommonPanel>
  );
}

export default observer(ExtendPicture);
