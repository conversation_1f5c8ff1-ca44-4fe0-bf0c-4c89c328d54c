import styles from './index.module.less';
import classNames from 'classnames';
import { EditorTooltip } from '../../TooltipTitle';
import { CrossBlack } from '@meitu/candy-icons';
import { TaskCategory } from '@/api/types/imageEditor';
type CommonPanelProps = React.PropsWithChildren<{
  title: string;
  titleTips?: string;
  extra?: React.ReactNode;
  actived?: boolean;
  onClose?: () => void;
  showClose?: boolean;
  className?: string;

  dataKey?: string;
  synopsisConfig?: {
    x: number;
    y: number;
    hidden: boolean;
    type: TaskCategory;
  };
}>;

export default function CommonPanel({
  title,
  titleTips,
  children,
  extra,
  onClose,
  showClose = true,
  className,
  dataKey,
  synopsisConfig
}: CommonPanelProps) {
  return (
    <div
      className={classNames(styles.commonPanel, className)}
      data-key={dataKey}
    >
      <div className="common-panel-title">
        <span className="common-panel-title-content">{title}</span>
        {synopsisConfig && (
          <EditorTooltip
            tooltips={titleTips}
            triggerClassName="common-panel-title-tips-icon"
            synopsisConfig={synopsisConfig}
            dataKey={dataKey}
          />
        )}
        {titleTips && (
          <EditorTooltip
            tooltips={titleTips}
            triggerClassName="common-panel-title-tips-icon"
          />
        )}
        {showClose && (
          <div className="common-panel-title-close-btn" onClick={onClose}>
            <CrossBlack />
          </div>
        )}
      </div>

      <div className="common-panel-content">
        <div className="common-panel-content-scroll">
          <div className="common-panel-content-scroll-content">{children}</div>
        </div>
      </div>

      {extra}
    </div>
  );
}
