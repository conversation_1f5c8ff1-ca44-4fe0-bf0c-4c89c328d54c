@content-primary-color: #dcdde5;

@content-tertiary-color: #5e5e6b;
@content-tertiary-color--hover: #a5a7b8;

@separator-color: #29292e;

@content-hover: rgba(139, 139, 158, 1);

.common-panel {
  width: 100%;
  height: 100%;
  padding: 16px;
  padding-right: 15px;
  display: flex;
  flex-direction: column;
  position: relative;

  &:global(.hidden) {
    display: none;
  }

  :global {
    .common-panel-title {
      flex: 0 0 auto;
      border-bottom: 1px solid @separator-color;
      padding-bottom: 16px;
      position: relative;
      z-index: 1;

      &-content {
        font-size: 16px;
        font-weight: 600;
        color: @content-primary-color;
      }

      &-tips-icon {
        margin-left: 4px;
        font-size: 14px;
        color: @content-tertiary-color;
        cursor: pointer;

        &:hover {
          color: @content-tertiary-color--hover;
        }
      }

      z-index: 1;

      &-close-btn {
        color: @content-tertiary-color;
        position: absolute;
        right: 0;
        top: 0;
        font-size: 14px;
        cursor: pointer;

        &:hover {
          color: #a5a7b8;
        }
      }
    }

    .common-panel-content {
      flex: 1 1 0;
      // margin-top: 16px;
      overflow: hidden;
      z-index: 1;

      .common-panel-content-scroll {
        width: 100%;
        height: 100%;
        overflow: auto;

        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
          display: none;
        }
      }
    }
  }
}
