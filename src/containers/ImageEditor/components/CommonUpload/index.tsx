import classNames from 'classnames';
import { PropsWithChildren, useRef, useState } from 'react';
import type { UploadFile } from 'antd';
import { App, Modal, Progress } from 'antd';

import {
  type UploadProps,
  Upload,
  beforeUploadHelper
} from '@/components/Upload';
import styles from './style.module.less';
import { UploadStatus } from '@/components/Upload/Upload';
import { UPLOAD_ERR_STATUS } from '@/components/Upload/constants';
import { mimeMap } from '@/constants/mimeMap';
import { fileToArrayBuffer, isWebPAnimated } from '../../utils/upload';

export type UploadResult = {
  url: string;
  previewUrl: string;
};

export interface CommonUploadProps
  extends Pick<UploadProps, 'disabled' | 'multiple' | 'type' | 'accept'>,
    Pick<
      Parameters<typeof beforeUploadHelper>[0],
      'limit' | 'num' | 'supports'
    > {
  onFinish?: (value: UploadResult[]) => void;
  openFileDialogOnClick?: boolean;

  /**
   * 是否跟随父级元素自适应大小
   */
  adaptive?: boolean;

  className?: string;
  /** 上传结束后的提示  */
  customUploadedToast?({
    successTotal,
    failedTotal,
    reviewErrorTotal,
    isExceededLimit
  }: {
    // 上传成功的总数
    successTotal: number;
    // 上传失败的总数
    failedTotal: number;
    // 审核失败的总数
    reviewErrorTotal: number;
    // 是否超过限制
    isExceededLimit: boolean;
  }): string | undefined;

  onClick?(): void;
}

export function CommonUpload(props: PropsWithChildren<CommonUploadProps>) {
  const {
    onFinish: onFinished,
    adaptive = false,
    children,
    limit,
    num = 200,
    className,
    customUploadedToast,
    supports = ['image/png', 'image/jpeg', 'image/jpg'],
    onClick,
    ...restProps
  } = props;

  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [open, setOpen] = useState(false); // 是否显示图片进度
  const [percent, setPercent] = useState(0); // 图片上传进度
  const [finishedTotal, setFinishedTotal] = useState(0); // 图片上传完成总数
  const [hasLimitError, setHasLimitError] = useState(false);
  const [hasNumberError, setHasNumberError] = useState(false);
  const [hasTypeError, setHasTypeError] = useState(false);
  const fileChangeNum = useRef(0); // 用于记录上传文件变化次数
  const hasMessageError = useRef(false);

  const { message } = App.useApp();

  const onFileChange: UploadProps['onChange'] = async (info) => {
    let { fileList } = info;
    const animationFile = fileList.map(async (item) => ({
      status: isWebPAnimated(
        await fileToArrayBuffer(item.originFileObj as unknown as File)
      ),
      result: item
    }));
    fileList = (await Promise.all(animationFile))
      .filter((item) => !item.status)
      .map((item) => item.result);
    if (fileList.length === 0) {
      message.info(`暂不支持该格式，请重新上传。`);
      return;
    }

    setFileList(fileList);
    const filesTotal = fileList.length;
    const filesValid = fileList.filter((item) => item.status); // 需要过滤掉没有通过校验的文件
    const filesStatus = filesValid.map((item) => item.status);
    const filesResponse = filesValid.map((item) => item.response);
    const filesValidResponse = filesResponse.filter(Boolean); // 过滤掉无效响应的文件

    const uploading = filesStatus.some(
      (status) => status === UploadStatus.Uploading
    );

    const errors = filesStatus.filter(
      (status) => status === UploadStatus.Error
    );

    const reviewErrorLength = fileList.filter(
      (file) => file.error?.status === UPLOAD_ERR_STATUS.REVIEW_FAILED
    )?.length;

    // 无有效文件的情况
    if (!filesValid.length) {
      fileChangeNum.current++;

      if (fileChangeNum.current === filesTotal) {
        let limitMsg = '';
        if (hasTypeError) {
          limitMsg = `文件类型不支持，仅支持${supports
            .map((mimeType) => mimeMap[mimeType])
            .join('、')}格式。`;
        } else if (hasNumberError) {
          limitMsg = `图片张数不能超过${num}张！`;
        } else if (hasLimitError) {
          limitMsg = `单张图片大小不能超过${limit}M！`;
        }

        if (restProps.multiple) {
          message.info(`${limitMsg} 已上传 0 张，失败 ${filesTotal} 张`);
        } else {
          message.info(`${limitMsg}`);
        }

        setFileList([]);
        onFinished?.([]);
        setHasLimitError(false);
        setHasNumberError(false);
        setHasTypeError(false);
        fileChangeNum.current = 0;
      }
      return;
    }

    const fileFinishedTotal = filesStatus.filter(
      (item) => item === UploadStatus.Done
    );
    const currentFinishedTotal = fileFinishedTotal.length;

    setOpen(true); // 显示图片上传进度
    setFinishedTotal(currentFinishedTotal); // 设置图片上传完成总数
    setPercent(Math.floor((currentFinishedTotal / fileList.length) * 100));

    if (!uploading) {
      setPercent(100);

      setTimeout(() => {
        const limitMsg = hasLimitError ? `单张图片大小不能超过${limit}M！` : '';
        const failedLength = filesTotal - currentFinishedTotal;
        const reviewErrorMsg = reviewErrorLength
          ? `，包含${reviewErrorLength}张不合规。`
          : '。';
        const reUploadMsg =
          hasLimitError || !!errors.length ? '请重新选择照片上传。' : '';
        const uploadInfo = `已上传 ${currentFinishedTotal} 张，失败 ${failedLength} 张${reviewErrorMsg}${reUploadMsg}`;

        const toast =
          customUploadedToast?.({
            successTotal: currentFinishedTotal,
            failedTotal: failedLength,
            reviewErrorTotal: reviewErrorLength,
            isExceededLimit: hasLimitError
          }) ?? `${limitMsg}${uploadInfo}`;
        if (!hasMessageError.current) {
          if (toast) message.info(toast);
        }

        setPercent(0);
        setOpen(false);
        setHasLimitError(false);
        setHasTypeError(false);
        setFileList([]);
        onFinished?.(filesValidResponse);
        hasMessageError.current = false;
      }, 1000);
    }
  };

  return (
    <div
      className={classNames(
        styles.commonUpload,
        {
          [styles.adaptive]: adaptive
        },
        className
      )}
      onClick={onClick}
    >
      <Upload
        isShowError={false}
        type="drag"
        className={styles.upload}
        fileList={fileList}
        onChange={onFileChange}
        showUploadList={false}
        beforeUpload={beforeUploadHelper({
          onLimitError: () => {
            setHasLimitError(true);
          },
          limit,
          num,
          supports,
          onNumberError: () => {
            setHasNumberError(true);
          },
          onTypeError: () => {
            setHasTypeError(true);
          },
          showErrorMessage: true
        })}
        onDrop={onClick}
        onMessageError={(msg) => {
          if (msg.includes('limited mimeType')) {
            message.info(`暂不支持该格式，请重新上传。`);
            hasMessageError.current = true;
          }
        }}
        {...restProps}
        multiple={false}
      >
        {children}
      </Upload>
      <Modal
        open={open}
        closable={false}
        footer={null}
        width={360}
        centered
        destroyOnClose
        getContainer={document.getElementById('root')!}
        className={styles.uploadProgressModal}
      >
        <div className={styles.uploadProgress}>
          <span className={styles.uploadProgressTitle}>
            图片上传中：{`${finishedTotal}/${fileList.length}`}
          </span>
          <Progress
            percent={percent}
            status="active"
            size="small"
            trailColor="#EBEEFF14"
            showInfo={false}
            strokeColor={{ from: '#3549FF', to: '#3549FF' }}
          />
        </div>
      </Modal>
    </div>
  );
}
