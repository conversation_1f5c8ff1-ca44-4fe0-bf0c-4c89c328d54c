@import '~@/styles/variables.less';

.common-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;

  .upload-descWrap {
    margin-top: 8px;
    color: @color-text-secondary;
  }

  .upload-desc {
    margin-right: 4px;
  }

  &.adaptive {
    width: auto;
    height: 100%;
    .upload {
      width: 100%;
      height: 100%;

      :global {
        .ant-upload-drag {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .upload {
    display: block;
    height: 100%;
    width: 100%;
  }
}

.upload-progress-modal {
  border-radius: 8px;
  width: 360px;
  height: 109px;
  background: var(--background-editorMainPanel, #17171a);
  box-shadow: 0px 20px 60px 0px rgba(0, 0, 0, 0.35);

  :global {
    .ant-modal,
    .ant-modal-content {
      background: var(--background-editorMainPanel, #17171a) !important;
      box-shadow: 0px 20px 60px 0px rgba(0, 0, 0, 0.35);
    }
  }
  .upload-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .upload-progress-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--content-editorPrimary, #e1e3eb);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      margin-bottom: 20px;
    }

    :global {
      .ant-progress-line {
        margin: 0;
        line-height: 0;
      }
    }
  }
}

.toolTip {
  width: 500px;
}
