@import '~@/styles/variables.less';

:global(.@{ant-prefix}-image) {
  :global(.@{ant-prefix}-image-img) {
    &.icon {
      width: 36px;
      height: 18px;
      margin-top: -2px;
      margin-left: 4px;
    }
  }
}

.title-box {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .right-box {
    padding: 2px 8px;
    border-radius: 999px;
    background: rgba(53, 73, 255, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }

    .icon {
      width: 14px;
      height: 14px;
    }

    span {
      color: @content-system-link;
      font-size: 13px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px;
      margin-left: 4px;
    }
  }
}
.inpaint-upload {
  :global {
    .ant-upload:hover {
      background: rgba(53, 73, 255, 0.1);
    }
    .ant-btn-default:not(:disabled):not(.ant-btn-disabled):hover {
      color: #fff;
    }
    .ant-upload-drag {
      border: 1px dashed #464652 !important;
      // height: 120px !important;
    }
    .ant-btn-default {
      border-radius: 8px;
      background: #33353a;
      border: none;
      color: #c3c8d3;

      /* text_14 */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
    }
    .ant-typography,
    .ant-typography.ant-typography-secondary {
      color: var(--content-editorTertiary, #555961);
      text-align: center;
      /* text_12 */
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }
  }
}
