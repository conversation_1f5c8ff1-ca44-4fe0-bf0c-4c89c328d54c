import { Form, Switch, Image, FormInstance } from 'antd';
import { Collapse, Upload, Title } from '@/components';
import { trackEvent } from '@/services';
import { AppModuleParam } from '@/types';
import { DraftType } from '@/api/types';
import styles from './index.module.less';
import { useNavigate } from 'react-router-dom';

import { getSource } from '@/utils';

interface DraggableUploadProps {
  value?: string;
  onChange?: (value: string) => void;
  form: FormInstance<any>;
}

function DraggableUpload({ value, onChange, form }: DraggableUploadProps) {
  const onValueChange = (url: string, previewUrl?: string) => {
    onChange?.(previewUrl ?? '');

    if (!!previewUrl) {
      trackEvent('image_modification_upload_image_success', {});
    }

    if (!previewUrl) {
      return;
    }
  };

  const track = () => {
    trackEvent('image_modification_upload_image_click', {});
  };

  return (
    <div onClick={track}>
      <Upload.Dragger
        value={value}
        onDrop={track}
        onChange={onValueChange}
        taskCategory={DraftType.INPAINT}
        className={styles.inpaintUpload}
        typographtText="传图替换选区,"
        typographtText2="使用透明底图效果更佳"
      />
    </div>
  );
}

/**
 * 参考图片模块
 */
export function ImageSection({ form }: { form: FormInstance<any> }) {
  // const { editorConfig } = useImageToImagEditorConfig();
  // const navigate = useNavigate();
  // const source = getSource();

  return (
    <>
      <Collapse.Panel.Section title={null}>
        <Form.Item name={['referImage']}>
          <DraggableUpload form={form} />
        </Form.Item>
      </Collapse.Panel.Section>
    </>
  );
}
