@import '~@/styles/variables.less';

@bg-color: #000;
@border-color: #29292e;
@border-color-focused: #4053ff;
@placeholder-color: #5e5e6b;
@input-color: #dcdde5;
@clear-btn-color: #7f818a;
@count-color: #5e5e6b;
@divider-color: #29292e;
@content-hover: rgba(139, 139, 158, 1);

.editor-text-area {
  padding: 4px;
  border: 1px solid @border-color;
  border-radius: 4px;
  background-color: @bg-color;
  transition: border-color 0.3s ease-in-out;

  &:hover,
  &:global(.editor-text-area-focused) {
    border-color: @border-color-focused;
  }

  :global {
    .editor-text-area-component {
      border: none;
      textarea {
        height: 80px !important;
        overflow-y: auto;
        /* -webkit 浏览器隐藏垂直滚动条 */
        ::-webkit-scrollbar {
          width: 0px;
          height: 0px;
        }
        color: @border-color-focused;
        caret-color: @border-color-focused;
        /* 在非 Webkit 浏览器中隐藏滚动条 */
        scrollbar-width: none;
        /* Firefox */
      }
      .@{ant-prefix}-input-affix-wrapper {
        background-color: @bg-color;
        .@{ant-prefix}-input {
          color: @input-color;
          background-color: @bg-color;
          padding-right: 0;
          &::placeholder {
            color: @placeholder-color;
          }
        }

        .@{ant-prefix}-input-suffix {
          .@{ant-prefix}-input-data-count {
            color: @count-color;
          }

          .@{ant-prefix}-divider {
            background-color: @divider-color;
            margin: 0 10px;
          }

          .@{ant-prefix}-input-clear-icon {
            right: 0;
            .candyicon {
              color: @clear-btn-color;
            }
          }
        }
      }
    }
    .ant-input-suffix svg {
      font-size: 14px;
      &:hover {
        color: @content-hover;
      }
    }
  }
}
