import classNames from 'classnames';
import { useState } from 'react';

import { TextArea } from '@/components';

import styles from './index.module.less';

type EditorTextAreaProps = {
  placeholder?: string;
  maxLength?: number;
  value?: string;
  onChange?(newValue: string): void;
};
export default function EditorTextArea({
  placeholder,
  maxLength,
  onChange,
  value
}: EditorTextAreaProps) {
  const [focused, setFocused] = useState(false);

  return (
    <div
      className={classNames(
        styles.editorTextArea,
        focused && 'editor-text-area-focused'
      )}
    >
      <TextArea
        wrapperClassName="editor-text-area-component"
        placeholder={placeholder}
        allowClear
        maxLength={maxLength}
        showCount
        // autoSize={autoSize}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        onChange={(e) => onChange?.(e.target.value)}
        value={value}
      />
    </div>
  );
}
