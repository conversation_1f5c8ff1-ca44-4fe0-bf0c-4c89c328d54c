@import '~@/styles/variables.less';
@content-primary: #dcdde5;

.Ai-eraser-panel:global(.@{ant-prefix}-form) {
  cursor: default;
  margin-bottom: 100px;

  :global {
    .panel-content-primary {
      color: #dcdde5;
      font-size: 14px;
    }

    .paint-size {
      margin: 16px;
      margin-bottom: 28px;
    }

    .prompt-title {
      margin: 36px 0 12px 0;
    }
  }

  .generate-nums-title {
    margin: 36px 0 12px 0;
    cursor: pointer;
  }
}

@modal-main-color: #e1e3eb;
@modal-secondary-color: #81838c;
@modal-checked-color: #3549ff;
@modal-unchecked-color: #0d0e0f;
@modal-unchecked-outline: #2e2e33;
.confirm-modal {
  display: flex;
  flex-direction: column;
  align-items: center;

  :global {
    .confirm-modal-main {
      font-size: 16px;
      font-weight: 600;
      color: @modal-main-color;
      margin-bottom: 12px;
    }

    .confirm-modal-secondary {
      font-size: 14px;
      color: @modal-secondary-color;
      text-align: center;
      margin-bottom: 16px;
    }

    .confirm-modal-check {
      font-size: 14px;
      line-height: 1;
      display: flex;
      align-items: center;
      color: @modal-main-color;
      user-select: none;
      cursor: pointer;
      margin-bottom: 12px;

      input {
        display: none;
      }

      &-box {
        display: inline-block;
        width: 12px;
        height: 12px;
        padding: 3px;
        box-sizing: content-box;
        border-radius: 4px;
        background-color: @modal-unchecked-color;
        box-shadow: 0 0 0 1px @modal-unchecked-outline inset;
        color: #fff;
        font-size: 12px;
        line-height: 1;
        margin-right: 8px;

        &.checked {
          background-color: @modal-checked-color;
        }
      }
    }
  }
}
