import {
  PaintBold,
  SelectionBoxBold,
  SelectionLassoBold,
  ToolCursorBold,
  CheckBlack
} from '@meitu/candy-icons';
import AreaSelection, { SelectionWay } from '../../AreaSelection';
import CommonPanel from '../../FeatureArea/CommonPanel';
import styles from './index.module.less';
import {
  AreaSelectionMode,
  ModeSelection
} from '../../AreaSelection/ModeSelection';
import EditorSliderInput from '../../EditorSliderInput';
// import EditorTextArea from '../../EditorTextArea';
import { Form } from 'antd';
// import GenerateNums from '../../GenerateNums';
// import TooltipTitle from '../../TooltipTitle';
import SubmitFooter from '../../SubmitFooter';
import {
  AreaSelectionParams,
  ToolPluginsType,
  useAreaSelection,
  DEFAULT_PAINT_WIDTH,
  AIEraserSelectionWay
} from '@/containers/ImageEditor/hooks/useAiEraserAreaSelection';
// import { ShapeType } from '@/editor/core/src';
import { useForm } from 'antd/es/form/Form';
import React, {
  useEffect,
  useMemo,
  useState,
  forwardRef,
  useRef,
  useCallback
} from 'react';
import { v4 as uuid } from 'uuid';
import { cancelTask, requestInferenceTask } from '@/api/imageEditor';
import { TaskCategory } from '@/api/types/imageEditor';
import { createImage } from '@/utils/cropImage';
// import { Image as ImageShape } from '@/editor/core/src';
import { observer } from 'mobx-react';
import useStore from '@/hooks/useStore';
import { getBioImageBlob } from '@/containers/ImageEditor/utils/maskImageTools';
import { queryPolling } from '@/containers/ImageEditor/utils/queryPolling';
import { CancelButton } from '../../FeatureArea/CancelButton';
import { trackEvent } from '@/services';
import {
  Modification,
  Subfunction
} from '@/containers/ImageEditor/constant/trace';
// import { handleRequestError } from '@/containers/ImageEditor/utils/handleRequestError';
import { getClock } from '@/containers/ImageEditor/utils/clock';
import { uploaderFunc } from '@/containers/ImageEditor/utils/upload';
import Konva from 'konva';
import { Confirm } from '../../modals/Confirm';
import classNames from 'classnames';
import { FunctionCode } from '@/api/types/meidou';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { handleRequestError } from '@/containers/ImageEditor/utils/handleRequestError';
import {
  optimizeImage,
  resetImageUrl
} from '@/containers/ImageEditor/utils/image';
import { useAutoCloseToast } from '@/containers/ImageEditor/hooks/useToast';
import { MediaType, MtccFuncCode } from '@/api/types';
// 任务超时时间
const QUERY_POLLING_TIME_OUT = 5 * 60 * 1000;

// 取消按钮延迟展示时间
const CANCEL_BUTTON_SHOW_DELAY = 60 * 1000;

const AI_ERASER_EXIT_ONCE_MODAL_KEY = 'AI_ERASER_EXIT_ONCE_MODAL_KEY';

type AiEraserProps = {
  actived?: boolean;
  ********************: React.MutableRefObject<Promise<boolean>>;
  hasPainted: any;
  showClose?: boolean;
  close?: () => void;
};
type AiEraserRef = {};
const AiEraser = forwardRef<AiEraserRef, AiEraserProps>(
  (
    {
      actived,
      ********************,
      hasPainted,
      showClose = true,
      close
    }: AiEraserProps,
    ref: React.Ref<AiEraserRef>
  ) => {
    const editorStore = useStore('EditorStore');
    const projectId = editorStore.projectInfo.id;
    const app = editorStore.layerEditor?.app;
    const wheeEditorPlugin = editorStore.layerEditor?.wheeLayerEditorPlugin;
    const historyPlugin = editorStore.layerEditor?.historyPlugin;
    const childRef = useRef<any>(null);
    const { message } = useAutoCloseToast();

    const [onlyOnceExitConfirm, setOnlyOnceExitConfirm] =
      useState<boolean>(false);
    const [isExitConfirmOpen, setIsExitConfirmOpen] = useState<boolean>(false);
    const AiEraserSelf = useRef({ closeEditor: () => {} });

    const appendLayer = (layerId: string, messageId: string) => {
      editorStore.appendTaskWithLayer(layerId, {
        id: messageId,
        type: TaskCategory.eraser
      });
    };
    const { updateMeiDouBalance } = useMeiDouBalance();

    const toolPlugins: ToolPluginsType = useMemo(() => {
      return {
        rectTool: editorStore.layerEditor?.rectTooler,
        eraserTool: editorStore.layerEditor?.eraserTooler,
        lineTool: editorStore.layerEditor?.lineTooler,
        lassoTool: editorStore.layerEditor?.lassoTooler,
        smartSelectionTool: editorStore.layerEditor?.smartSelectionTooler
      };
    }, [
      editorStore.layerEditor?.smartSelectionTooler,
      editorStore.layerEditor?.rectTooler,
      editorStore.layerEditor?.eraserTooler,
      editorStore.layerEditor?.lineTooler,
      editorStore.layerEditor?.lassoTooler
    ]);

    const {
      selectionWay,
      onSelectionWayChange,
      selectionParams,
      onSelectionParamsChange
    } = useAreaSelection({
      app: editorStore.layerEditor?.app,
      toolPlugins,
      wheeEditorPlugin: editorStore.layerEditor?.wheeLayerEditorPlugin,
      ********************
    });

    const [paintWidth, setPaintWidth] = useState(
      selectionParams?.paintWidth || DEFAULT_PAINT_WIDTH
    );

    useEffect(() => {
      // 如果当前活跃的不是改图 禁用框选
      if (!actived && selectionWay) {
        onSelectionWayChange(null);
      }

      // 如果当前活跃的是改图 默认框选
      if (actived && !selectionWay) {
        handleSelectionWayChange(SelectionWay.Box);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [actived]);

    React.useImperativeHandle(ref, function () {
      return {
        closeHandle: closeHandle
      };
    });

    function closeHandle(closeEditor: () => void) {
      AiEraserSelf.current.closeEditor = closeEditor;
      const skip = localStorage.getItem(AI_ERASER_EXIT_ONCE_MODAL_KEY);
      if (!hasPainted) {
        saveData(closeEditor);
        return;
      }
      if (skip !== 'true') {
        setIsExitConfirmOpen(true);
        return;
      }
      saveData(closeEditor);
    }

    function saveData(closeEditor: () => void) {
      const originId = editorStore.layerEditor?.originId;
      originId && editorStore.setActiveLayerId(originId);
      editorStore.updateLayerHistoryList();
      closeEditor(); //处理结束后进行编辑器关闭
    }
    function handleModeChange<T extends AIEraserSelectionWay>(
      mode: AreaSelectionMode,
      otherParams?: Omit<AreaSelectionParams<T>, 'selectionMode'>
    ) {
      onSelectionParamsChange({
        selectionMode: mode,
        ...otherParams
      });
    }

    function editorPictureStatus() {
      app?.unlockAllShapes();
    }

    const cancelEditorPictureStatus = () => {
      onSelectionWayChange(null);
      editorPictureStatus();
    };

    function handleSelectionWayChange(way: AIEraserSelectionWay | null) {
      onSelectionWayChange(way);
      // editorStore.editor.app.lockAllShapes();

      editorStore.unselectEffectHandler();
    }

    editorStore.setSelectEffect(cancelEditorPictureStatus);
    useEffect(() => {
      return () => {
        editorStore.setSelectEffect(() => {});
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // // 项目创建时 默认选中框选
    // editorStore.setDefaultAiEraserHandler(() => {
    //   // 在插入图片时 会使用imageTooler 导致工具被卸载
    //   // 这样就导致当前状态虽然是Selection.Box 但对应的工具不是矩形工具
    //   // 如果直接将状态设置为SelectionWay.Box 这样状态没有发生改变 不会出发“安装”矩形工具的动作
    //   // 因此 这里先清空 然后再切换回Box 触发矩形工具的安装动作
    //   handleSelectionWayChange(null);
    //   setTimeout(() => handleSelectionWayChange(SelectionWay.Box));
    // });
    // useEffect(() => {
    //   return () => {
    //     editorStore.setDefaultAiEraserHandler(() => {});
    //   };
    //   // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, []);

    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const noHasPaintedMessage = useRef<null | (() => void)>(null);
    async function handleSubmit(price: any) {
      if (loading) {
        return;
      }
      trackEvent('ai_modification_create_btn_click', {
        secondary_function: Subfunction.Clear,
        repair_size: paintWidth,
        free_batch_size: price && price?.useFreeNum + ''
      });
      if (!navigator.onLine) {
        message({
          type: 'error',
          content: '网络异常',
          duration: 3000
        });
        return;
      }

      const clock = getClock();
      let remainingTime = QUERY_POLLING_TIME_OUT;

      if (!wheeEditorPlugin?.plugin) {
        return;
      }
      let _closeToast: null | (() => void) = null;
      let isCanceledByUser = false;
      try {
        setLoading(true);
        // 如果可视区域没有涂抹 提示用户
        if (!(await ********************.current)) {
          if (noHasPaintedMessage.current) noHasPaintedMessage.current();
          let { destroy: messageDestroy } = message({
            type: 'error',
            content: '请先选择想要消除的区域',
            duration: 3000
          });
          noHasPaintedMessage.current = messageDestroy;
          return;
        }

        const { destroy: closeToast } = message({
          type: 'loading',
          content: '生成中，请稍后...',
          isShowMask: true,
          customNode: (
            <CancelButton
              showDelay={CANCEL_BUTTON_SHOW_DELAY}
              onClick={handleCancel}
            >
              取消
            </CancelButton>
          )
        });

        _closeToast = closeToast;
        remainingTime -= clock.getDelta();
        let originURL = '';
        if (isCanceledByUser) {
          return;
        }
        const stageImage = app?.stage.find('Image')[0];
        if (stageImage) {
          originURL = stageImage.getAttr('target').config.source;
          originURL = resetImageUrl(originURL);
        }
        const imgOrigin = await createImage(originURL);
        const mask = await wheeEditorPlugin.plugin?.getOptionsLayerMaxImage(
          'image/jpeg',
          '#000000',
          imgOrigin.width,
          imgOrigin.height
        );

        /**
         * 将jpeg的mask图转换为灰度图 同时将类型转换为png
         */
        const grayMaskBlob = await getBioImageBlob(mask, 'image/png');

        if (!grayMaskBlob) {
          return;
        }

        remainingTime -= clock.getDelta();
        const maskURL = await uploaderFunc(grayMaskBlob, 'png', remainingTime);
        // const maskURL = await uploader.upload(grayMaskBlob, {
        //   ...defaultUploadParam,
        //   suffix: 'png'
        // });
        // document.body.appendChild(await createImage(maskURL.previewUrl!))

        if (isCanceledByUser) {
          return;
        }

        remainingTime -= clock.getDelta();
        const layerId = uuid();
        const createTaskRes = requestInferenceTask(
          {
            taskCategory: TaskCategory.eraser,
            projectId: projectId,
            layerId,
            params: {
              initImage: originURL,
              maskImage: maskURL.url
            },
            functionName: MtccFuncCode.FuncCodeImageModifyEraserRight,
            mediaType: MediaType.Photo,
            resMediaType: MediaType.Photo
          },
          remainingTime
        );
        // 重新拉取定价接口
        getPrice();
        const response = await createTaskRes;
        editorStore.setSurplus(response.surplus);
        //#endregion

        const messageId = response.id;
        if (isCanceledByUser) {
          cancelTask({
            projectId,
            msgId: messageId
          });
          // .then(() => {
          //   editorStore.updateSurplus();
          // });
          return;
        }

        remainingTime -= clock.getDelta();
        const { result, cancelPolling } = queryPolling(
          messageId,
          3000,
          TaskCategory.eraser,
          remainingTime
        );
        const pollingResult = await result;
        if (pollingResult.isCanceled) {
          if (pollingResult.isTimeout) {
            cancelTask({
              projectId,
              msgId: messageId
            });
            // .then(() => {
            //   editorStore.updateSurplus();
            // });
            throw new Error('生成失败，请重试');
          }

          return;
        }
        const historyList = pollingResult.data;

        function handleCancel() {
          isCanceledByUser = true;
          // trackEvent('ai_modification_create_cancel', {
          //   text: prompt,
          //   negative_text: '',
          //   batch_size: batchSize,
          //   subfunction: Subfunction.Modification
          // });

          try {
            // 如果还没有创建轮训任务 取消轮训会抛出异常
            cancelPolling();

            // 运行到这里 轮训任务已经取消 还需要手动取消推理任务
            cancelTask({
              projectId,
              msgId: messageId
            });
            // .then(() => {
            //   editorStore.updateSurplus();
            // });
          } catch (e) {
            // 在轮训之前调用取消轮训会出现异常
          } finally {
            setLoading(false);
            _closeToast?.();
          }
        }

        // console.log(historyList);
        const imageList: any[] = historyList[0]?.resultImages ?? [];

        const successList = imageList
          .filter((image) => image.imageStatus === 1)
          .map(({ url }) => url);
        // console.log(successList);
        if (!successList.length) {
          throw new Error('生成失败，请重试');
        }

        // 有部分图片没有审核通过时 告知用户
        if (successList.length !== imageList.length) {
          message({
            type: 'info',
            content: `${imageList.length - successList.length}张生成失败`,
            duration: 3000
          });
        }

        const first = successList[0];
        const image = await createImage(first);

        historyPlugin?.enableCache();
        wheeEditorPlugin.plugin.clearMask({ autoCommitHistory: false });
        const img = getStageImg() as Konva.Image | null;
        if (img) {
          const config = {
            ...img.getAttr('target').config,
            image
          };
          app?.update(config);
        }
        historyPlugin?.commitCache();
        const originId = editorStore.layerEditor?.originId;
        if (!originId) return;
        appendLayer(originId, messageId);
        const originImg = editorStore.editor.app.findShapeById(originId);
        if (!originImg) return;
        const image1080Url = optimizeImage(first, {
          width: editorStore.maxEditorWidth,
          height: editorStore.maxEditorHeight
        });
        const image1080 = await createImage(image1080Url);
        const originImgConfig = {
          ...originImg.getAttr('target').config,
          image: image1080
        };
        editorStore.editor.app?.update(originImgConfig);
        close && saveData(close);
        trackEvent('ai_modification_create_success', {
          // 成功时仅上报成功张数
          batch_size: successList.length,
          repair_size: paintWidth,
          secondary_function: Subfunction.Clear,
          free_batch_size: price && price?.useFreeNum + ''
        });
      } catch (e: any) {
        if (isCanceledByUser) {
          return;
        }

        if (e.isTimeout) {
          message({
            type: 'error',
            content: '生成失败，请重试',
            duration: 5000
          });
        } else {
          handleRequestError(e);
          // message({
          //   type: 'error',
          //   content: '生成失败，请重试',
          //   duration: 5000
          // });
        }
      } finally {
        updateMeiDouBalance();
        getPrice();
        if (!isCanceledByUser) {
          setLoading(false);
          _closeToast?.();
        }
      }
    }
    // 调用子组件中的getPrice方法
    const getPrice = useCallback(() => {
      childRef?.current?.getPrice();
    }, []);
    const getStageImg = () => {
      if (!app) return;
      const layer = app.getLayerByName('meitu:main:layer');
      if (!layer) return true;
      const shapeNode = layer
        .getChildren()
        .find(
          (node) => node.name() === 'meitu:editor:group'
        ) as Konva.Group | null;
      if (!shapeNode) return;
      if (shapeNode.children && shapeNode.children.length > 0) {
        return shapeNode.children[0];
      }
    };

    const handleClickExitModalCancel = () => {
      setIsExitConfirmOpen(false);
    };
    const handleClickExitModalOk = () => {
      setIsExitConfirmOpen(false);
      localStorage.setItem(
        AI_ERASER_EXIT_ONCE_MODAL_KEY,
        onlyOnceExitConfirm.toString()
      );
      saveData(AiEraserSelf.current.closeEditor);
    };
    const getFunctionBody = () => {
      return {};
    };
    return (
      <>
        <CommonPanel
          title="消除"
          // titleTips="效果无痕更自然！"
          actived={actived}
          showClose={showClose}
          synopsisConfig={{
            x: 380,
            y: 56,
            hidden: false,
            type: TaskCategory.eraser
          }}
          extra={
            <SubmitFooter
              buttonLabel={'立即生成'}
              onClick={handleSubmit}
              loading={loading}
              ref={childRef}
              freeCounts={editorStore.surplus}
              disabled={!projectId}
              code={FunctionCode.aiEraser}
              isActivated={actived}
              genNums={1}
              getFunctionBody={getFunctionBody}
            />
          }
        >
          <Form
            className={styles.aiEraserPanel}
            form={form}
            initialValues={{
              prompt: '',
              genNums: 4
            }}
          >
            <div className="panel-content-primary" style={{ marginTop: 16 }}>
              区域选择
            </div>
            <AreaSelection
              currentSelectionWay={selectionWay}
              onSelectionWayChange={(way: AIEraserSelectionWay) => {
                handleSelectionWayChange(way);

                if (selectionWay !== way) {
                  trackEvent('ai_modification_tool_click', {
                    tool: Modification.SelectionToolsTrace[way],
                    is_decrease:
                      Modification.IsDecreaseTrace[AreaSelectionMode.Add],
                    subfunction: Subfunction.Clear
                  });
                }
              }}
              items={[
                {
                  key: SelectionWay.Smart,
                  icon: <ToolCursorBold />,
                  label: '智能选择',
                  params: (
                    <>
                      <ModeSelection
                        currentMode={selectionParams?.selectionMode}
                        onModeChange={(mode) => {
                          handleModeChange(mode);

                          if (mode !== selectionParams?.selectionMode) {
                            trackEvent('ai_modification_tool_click', {
                              tool: Modification.SelectionToolsTrace[
                                SelectionWay.Smart
                              ],
                              is_decrease: Modification.IsDecreaseTrace[mode],
                              subfunction: Subfunction.Clear
                            });
                          }
                        }}
                      />
                    </>
                  )
                },
                {
                  key: SelectionWay.Box,
                  icon: <SelectionBoxBold />,
                  label: '框选',
                  params: (
                    <>
                      <ModeSelection
                        currentMode={selectionParams?.selectionMode}
                        onModeChange={(mode) => {
                          handleModeChange(mode);

                          if (mode !== selectionParams?.selectionMode) {
                            trackEvent('ai_modification_tool_click', {
                              tool: Modification.SelectionToolsTrace[
                                SelectionWay.Box
                              ],
                              is_decrease: Modification.IsDecreaseTrace[mode],
                              subfunction: Subfunction.Clear
                            });
                          }
                        }}
                      />
                    </>
                  )
                },
                {
                  key: SelectionWay.Lasso,
                  icon: <SelectionLassoBold />,
                  label: '套索',
                  params: (
                    <>
                      <ModeSelection
                        currentMode={selectionParams?.selectionMode}
                        onModeChange={(mode) => {
                          handleModeChange(mode);

                          if (mode !== selectionParams?.selectionMode) {
                            trackEvent('ai_modification_tool_click', {
                              tool: Modification.SelectionToolsTrace[
                                SelectionWay.Lasso
                              ],
                              is_decrease: Modification.IsDecreaseTrace[mode],
                              subfunction: Subfunction.Clear
                            });
                          }
                        }}
                      />
                    </>
                  )
                },
                {
                  key: SelectionWay.Repair,
                  icon: <PaintBold />,
                  label: '手动修补',
                  params: (
                    <>
                      <ModeSelection
                        currentMode={selectionParams?.selectionMode}
                        onModeChange={(mode) => {
                          if (mode !== selectionParams?.selectionMode) {
                            trackEvent('ai_modification_tool_click', {
                              tool: Modification.SelectionToolsTrace[
                                SelectionWay.Repair
                              ],
                              is_decrease: Modification.IsDecreaseTrace[mode],
                              subfunction: Subfunction.Clear
                            });
                          }

                          const currentParams =
                            selectionParams as AreaSelectionParams<SelectionWay.Repair>;
                          handleModeChange<SelectionWay.Repair>(mode, {
                            paintWidth: currentParams.paintWidth
                          });
                        }}
                      />
                      <div className="paint-size">
                        <EditorSliderInput
                          title="大小"
                          min={1}
                          max={100}
                          value={
                            (
                              selectionParams as unknown as AreaSelectionParams<SelectionWay.Repair>
                            )?.paintWidth
                          }
                          onChange={(value) => {
                            onSelectionParamsChange({
                              selectionMode:
                                selectionParams?.selectionMode ?? null,
                              paintWidth: value
                            });
                            setPaintWidth(value);
                          }}
                        />
                      </div>
                    </>
                  )
                }
              ]}
            />
          </Form>
        </CommonPanel>
        <Confirm
          open={isExitConfirmOpen}
          okText="退出"
          onClickClose={handleClickExitModalCancel}
          onClickCancel={handleClickExitModalCancel}
          onClickOk={handleClickExitModalOk}
          contentClassName={styles.confirmModal}
          centered
          width={340}
        >
          <div className="confirm-modal-main">温馨提示</div>
          <div className="confirm-modal-secondary">
            此操作将退出当前功能，是否继续退出？
          </div>
          <label className="confirm-modal-check">
            <input
              type="checkbox"
              onChange={(e) => {
                const checked = e.target.checked;
                setOnlyOnceExitConfirm(checked);
              }}
            />
            <span
              className={classNames(
                'confirm-modal-check-box',
                onlyOnceExitConfirm && 'checked'
              )}
            >
              {onlyOnceExitConfirm && <CheckBlack />}
            </span>
            不再提示
          </label>
        </Confirm>
      </>
    );
  }
);

export default observer(AiEraser);
