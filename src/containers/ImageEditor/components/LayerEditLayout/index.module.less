@keyframes layerEditLayoutLeftAnimation {
  0% {
    transform: translateX(-100%);
    opacity: 1;
  }

  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes layerEditLayoutRightAnimation {
  0% {
    transform: translateX(100%);
    opacity: 1;
  }

  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes layerEditLayoutLeftAnimationBack {
  0% {
    transform: translateY(0);
    opacity: 1;
  }

  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes layerEditLayoutRightAnimationBack {
  0% {
    transform: translateX(0);
    opacity: 1;
  }

  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

.layerEditLayout {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 30;
  display: flex;
  /* 使用弹性布局 */
  height: 100%;
  /* 容器高度 */
  width: 100%;
  opacity: 0;
  // background-color: #000;

  .layerEditLayoutLeft {
    width: 380px;
    opacity: 0;
    transform: translateX(-100%);
  }

  .layerEditLayoutRight {
    width: calc(~'100vw -  380px');
    opacity: 0;
    transform: translateX(100%);
  }

  &.inEditor {
    opacity: 1;

    .layerEditLayoutLeft {
      animation: layerEditLayoutLeftAnimation 0.5s ease-out;
      transform: translateX(0);
      opacity: 1;
    }

    .layerEditLayoutRight {
      animation: layerEditLayoutRightAnimation 0.5s ease-out;
      opacity: 1;
      background-color: #000;
      transform: translateX(0);
    }
  }

  &.back {
    .layerEditLayoutLeft {
      animation: layerEditLayoutLeftAnimationBack 0.5s ease;
    }

    .layerEditLayoutRight {
      animation: layerEditLayoutRightAnimationBack 0.5s ease;
    }
  }

  .layerEditContainer {
    width: 100%;
    height: 100%;
  }

  .selectionControlsBox {
    & > div {
      margin-left: 190px;
    }
  }
}

:global {
  .layer-edit-mask-mouse-move {
    width: calc(~'100vw -  380px');
    right: 0;
    bottom: 0;
  }
}

.layerEditBtnBox {
  position: fixed;
  z-index: 200;
  transform: translate(-50%);
  border-radius: 6px;
  background: #252529;
  box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.25);

  ul {
    list-style: none;
    margin-bottom: 0;
    padding-left: 0;

    display: flex;
    font-size: 14px;
    color: #e1e3eb;
    font-family: 'PingFang SC';

    li {
      padding: 10px;
      cursor: pointer;
      white-space: nowrap;

      &.active {
        background: rgba(235, 238, 255, 0.1);
      }

      &:hover {
        background: rgba(235, 238, 255, 0.1);
      }
    }
  }
}
