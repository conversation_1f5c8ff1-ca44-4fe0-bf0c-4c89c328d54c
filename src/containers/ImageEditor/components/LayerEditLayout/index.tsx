import styles from './index.module.less';
import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import EditorLayoutLeft from './EditorLayoutLeft';
import { observer } from 'mobx-react';
import useStore from '@/hooks/useStore';
import SelectionControls from '../SelectionControls';
import { useHasPainted } from '../../hooks/useHasPainted';
import {
  CanvasNodeEventName,
  CanvasZoomEventName,
  CanvasViewEventName,
  CanvasDragEventName,
  BaseShape,
  ShapeConfig
} from '@/editor/core/src';
import {
  layerEditorType,
  layerEditorShowModel,
  LayerEditConfigType
} from './type';
import { TaskCategory, TaskMap, LocationMode } from '@/api/types/imageEditor';
import Konva from 'konva';
import classNames from 'classnames';
import {
  Subfunction,
  subFunctionClick
} from '@/containers/ImageEditor/constant/trace';
import { trackEvent } from '@/services';
import { useExitEditLayerTutorials } from '../../tutorials/exitEditLayer';
import { refreshActiveHighlight } from '../../tutorials/refresh';
import { currentIsLayerEditBtnTutorials } from '../../tutorials/overview';
const LayerBtnsConfig = [
  {
    name: '消除',
    key: layerEditorType.AiEraser,
    showLayerModel: layerEditorShowModel.OriginImage,
    isDirectReturn: false,
    disableSelect: true, //禁用选中按钮
    disableDownload: true, //禁用下载按钮
    disableHistory: false, //禁用历史记录按钮
    disableAddImg: true,
    disableMoveStage: false,
    disableScaleStage: false
  },
  {
    name: '抠图',
    key: layerEditorType.AICutout,
    showLayerModel: layerEditorShowModel.OriginImage,
    isDirectReturn: false,
    disableSelect: true,
    disableDownload: true,
    disableHistory: false,
    disableAddImg: true,
    disableMoveStage: false,
    disableScaleStage: false
  },
  {
    name: '扩图',
    key: layerEditorType.Extend,
    showLayerModel: layerEditorShowModel.OriginImage,
    isDirectReturn: true,
    disableSelect: true,
    disableDownload: true,
    disableHistory: true,
    disableAddImg: true,
    disableMoveStage: true,
    disableScaleStage: true
  }
];

export const LayerEditorBtnBoxId = 'layer-editor-btn-box';

type LayerBtnUIType = {
  clickHandle: (type: string | null) => void;
  configs: LayerEditConfigType[];
  mainActiveKey?: string | null;
};
const LayerBtnUI = observer(
  ({ clickHandle, configs, mainActiveKey }: LayerBtnUIType) => {
    const editorStore = useStore('EditorStore');
    const [btnPosition, setBtnPosition] = useState({ x: -9999, y: -9999 });
    const selectNode = useRef<Konva.Image | null>(null);
    const layerBtnUINode = useRef<HTMLDivElement | null>(null);
    //事件监听
    useEffect(() => {
      const selectedNodes =
        editorStore.editor.selectorPlugin.selector?.selectedNodes;
      if (selectedNodes && selectedNodes.length > 0) {
        selectLayer({ nodes: selectedNodes });
      }
      editorStore.editor.app.on(CanvasNodeEventName.select, selectLayer);
      editorStore.editor.app.on(CanvasNodeEventName.unselect, unSelectLayer);
      editorStore.editor.app.on(CanvasZoomEventName.end, upDateCalcPosition);
      editorStore.editor.app.on(
        CanvasViewEventName.globalMoveAfter,
        upDateCalcPosition
      );
      editorStore.editor.app.on(
        CanvasNodeEventName.updateBefore,
        updateNodeBeforeHandle
      );
      editorStore.editor.app.on(
        CanvasNodeEventName.updateAfter,
        updateNodeAfterHandle
      );

      editorStore.editor.app.on(
        CanvasDragEventName.move,
        transformStartHandler
      );
      editorStore.editor.app.on(CanvasDragEventName.end, transformEndHandler);
      editorStore.editor.app.on(
        CanvasViewEventName.resized,
        upDateCalcPosition
      );

      return () => {
        editorStore.editor.app.off(CanvasNodeEventName.select, selectLayer);
        editorStore.editor.app.off(CanvasNodeEventName.unselect, unSelectLayer);
        editorStore.editor.app.off(CanvasZoomEventName.end, upDateCalcPosition);
        editorStore.editor.app.off(
          CanvasViewEventName.globalMoveAfter,
          upDateCalcPosition
        );
        editorStore.editor.app.off(
          CanvasNodeEventName.updateBefore,
          updateNodeBeforeHandle
        );
        editorStore.editor.app.off(
          CanvasNodeEventName.updateAfter,
          updateNodeAfterHandle
        );

        editorStore.editor.app.off(
          CanvasDragEventName.move,
          transformStartHandler
        );
        editorStore.editor.app.off(
          CanvasDragEventName.end,
          transformEndHandler
        );
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [editorStore]);

    const [isShowBtn, setIsShowBtn] = useState<boolean>(false);

    const mainEditTabKey = useRef(mainActiveKey);
    useEffect(() => {
      mainEditTabKey.current = mainActiveKey;
      if (mainActiveKey === TaskCategory.extend) {
        setIsShowBtn(false);
        return;
      }
      // const selectedNode = upDateSelectRef();
      //切换节点 为改图切有选中的节点
      // if (mainActiveKey === TaskCategory.inpaint && selectedNode) {
      if (
        selectNode.current &&
        selectNode.current.getAttr('target').config.type !== 'paint' && //涂鸦图层
        selectNode.current.getAttr('name') !== 'graffiti_group' //涂鸦节点) {
      ) {
        setIsShowBtn(true);
        calcPosition(selectNode.current);
      } else {
        setIsShowBtn(false);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [mainActiveKey]);

    const upDateCalcPosition = () => {
      setTimeout(() => {
        if (selectNode.current) calcPosition(selectNode.current);
      }, 0);
    };
    const updateNodeBeforeHandle = () => {
      setIsShowBtn(false);
    };

    const transformStartHandler = (event: any) => {
      const draggedNode = event.event.target;
      if (draggedNode.name() === 'meitu:selector:transformer') {
        updateNodeBeforeHandle();
      }
    };

    const transformEndHandler = (event: any) => {
      const draggedNode = event.event.target;
      if (draggedNode.name() === 'meitu:selector:transformer') {
        updateNodeAfterHandle();
      }
    };
    const updateNodeAfterHandle = () => {
      if (
        selectNode.current &&
        selectNode.current.getAttr('target').config.type !== 'paint' && //涂鸦图层
        selectNode.current.getAttr('name') !== 'graffiti_group' //涂鸦节点) {
      ) {
        calcPosition(selectNode.current);
        setIsShowBtn(true);
      }
    };
    const selectLayer = ({ nodes }: any) => {
      if (nodes.length <= 0) return;
      upDateSelectRef(nodes);
      if (mainEditTabKey.current === TaskCategory.extend) {
        setIsShowBtn(false);
        return;
      }
      if (
        selectNode.current &&
        selectNode.current.getAttr('target').config.type !== 'paint' && //涂鸦图层
        selectNode.current.getAttr('name') !== 'graffiti_group' //涂鸦节点
      ) {
        setIsShowBtn(true);
        calcPosition(selectNode.current);
      }
    };

    const upDateSelectRef = (nodes?: any) => {
      const selectedNodes =
        nodes || editorStore.editor.selectorPlugin.selector?.selectedNodes;
      if (selectedNodes && selectedNodes.length > 0) {
        let showBtnNode = null;
        if (selectNode.current) {
          showBtnNode = selectNode.current;
        } else {
          showBtnNode = selectedNodes[0].instance;
          selectNode.current = showBtnNode;
        }
        return showBtnNode;
      }
      return;
    };
    const calcPosition = (showBtnNode: Konva.Image) => {
      const { width, x, y } = showBtnNode.getClientRect();
      const padding = 6;
      let btnPosition = { x: x + width / 2, y: y - 22 };
      const defaultRect = {
        bottom: 0,
        height: 0,
        left: 0,
        right: 0,
        top: 0,
        width: 0,
        x: 0,
        y: 0
      };

      const getBoundingClientRect = (id: string) =>
        document.getElementById(id)?.getBoundingClientRect() || defaultRect;
      const stageRect =
        editorStore.editor.app.stage.content.getBoundingClientRect();
      const tabRect = getBoundingClientRect('feature-tab');
      // const featureRect = getBoundingClientRect('feature-area');
      const featureRect = {
        x: 0,
        y: 0,
        width: 0,
        height: 0
      };
      const scrollableRect = getBoundingClientRect('scrollableBox');
      const leftBoundary = tabRect.x + tabRect.width + featureRect.width;
      const showPageInStage = {
        left: Math.max(leftBoundary, stageRect.x),
        top: stageRect.y,
        right:
          Math.min(scrollableRect.x, stageRect.x + stageRect.width) ||
          stageRect.x + stageRect.width,
        bottom: stageRect.y + stageRect.height
      };

      const layerBtnUINodeRect =
        layerBtnUINode.current?.getBoundingClientRect() || defaultRect;
      const halfLayerBtnWidth = layerBtnUINodeRect.width / 2;
      btnPosition.x = Math.max(
        showPageInStage.left + halfLayerBtnWidth + padding,
        btnPosition.x
      );
      btnPosition.x = Math.min(
        showPageInStage.right - halfLayerBtnWidth - padding,
        btnPosition.x
      );
      btnPosition.y = Math.max(showPageInStage.top + padding, btnPosition.y);
      btnPosition.y = Math.min(
        btnPosition.y,
        showPageInStage.bottom - padding - layerBtnUINodeRect.height
      );
      setBtnPosition({ ...btnPosition });
    };

    // 当按钮位置发生改变时 要更新教程高亮区的位置
    useLayoutEffect(() => {
      /**
       * 如果当前不处于图层编辑按钮的教程
       * 不需要走下面这段逻辑
       */
      if (currentIsLayerEditBtnTutorials) {
        refreshActiveHighlight();
      }
    }, [btnPosition]);

    const unSelectLayer = ({ nodes }: any) => {
      // if (nodes.length <= 0) return;
      setIsShowBtn(false);
      selectNode.current = null;
    };

    const LayerBtnUI = (
      <div
        id={LayerEditorBtnBoxId}
        className={styles.layerEditBtnBox}
        style={
          isShowBtn
            ? {
                left: 0,
                top: 0,
                transform: `translate(calc(${btnPosition.x}px - 50%),${btnPosition.y}px)`
              }
            : {
                left: -9999,
                top: -9999
              }
        }
        ref={layerBtnUINode}
      >
        <ul>
          {configs.map((config) => (
            <li
              key={config.key}
              className={styles.layerEditBtnItem}
              onClick={() => {
                clickHandle(config.key);
              }}
            >
              {config.name}
            </li>
          ))}
        </ul>
      </div>
    );
    return <>{LayerBtnUI}</>;
  }
);

type LayerEditLayoutProps = {
  activeKey: string | null;
  onFeatureChangeClick: (key: string) => void;
};

function LayerEditLayout({
  onFeatureChangeClick,
  activeKey
}: LayerEditLayoutProps) {
  const editorStore = useStore('EditorStore');
  const [layerEditActive, setLayerEditActive] = useState<boolean>(false);
  const [layerEditType, setLayerEditType] = useState<string | null>(null);
  const [layerEditConfig, setLayerEditConfig] =
    useState<LayerEditConfigType | null>(null);

  const layerEditContainer = useRef<HTMLDivElement>(null);
  const [backStyle, setBackStyle] = useState<boolean>(false);
  const [inStyle, setInStyle] = useState<boolean>(false);
  const layerEdit = useRef({
    getOriginImage: {} as any,
    mainEditorMouseStyleType: editorStore.mouseStyleType,
    originActiveKey: activeKey,
    checkImgId: null as string | null,
    originApp: null as any | null,
    originHeaderBtnState: null as any | null,
    originDisableDownload: null as any | null,
    originHeaderBtnStateUI: null as any | null
  });
  const { hasPainted, promiseRef: hasPaintedPromiseRef } = useHasPainted({
    app: editorStore.layerEditor?.app,
    wheeEditorPlugin: editorStore.layerEditor?.wheeLayerEditorPlugin,
    enabled: true
  });
  //初始化 编辑器
  useEffect(() => {
    editorStore.editor.app.on(CanvasNodeEventName.select, selectLayer);
    layerEdit.current.originApp = editorStore.editor;

    init();
    return () => {
      restore();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [layerEditActive, layerEditType]);

  useExitEditLayerTutorials({
    layerEditActive,
    inStyle,
    hasPaintedPromiseRef,
    delay: 600 // 等待入场动画完成
  });

  const timerByLoading = useRef<any>(null);
  const init = async () => {
    if (!layerEditContainer.current) return;
    timerByLoading.current = setTimeout(() => {
      editorStore.setGlobalLoading(true);
    }, 100);
    setBackStyle(false);

    const checkImgId = layerEdit.current.checkImgId;
    const headerBtnConfig = editorStore.headerBtnConfig;
    layerEdit.current.originHeaderBtnState = {
      ...headerBtnConfig
    };
    layerEdit.current.originDisableDownload = editorStore.disableDownload;
    layerEdit.current.originHeaderBtnStateUI = {
      ...editorStore.headerBtnState
    };
    layerEdit.current.mainEditorMouseStyleType = {
      ...editorStore.mouseStyleType
    };

    if (layerEditorType.Extend === layerEditType) {
      checkImgId && editorStore.setSelectExtendLayerId(checkImgId);
      layerEditTypeUpdate(layerEditType);
      await editorStore.initExtendEditor(layerEditContainer.current);
      editorStore.editor.app.keyCommand.stop();
      if (timerByLoading.current) clearTimeout(timerByLoading.current);
      editorStore.setGlobalLoading(false);
      setInStyle(true);
      return;
    }
    const showLayerModel = layerEditConfig?.showLayerModel;
    layerEdit.current.getOriginImage[layerEditorShowModel.OriginImage] =
      editorStore.getOriginImgData;
    layerEdit.current.getOriginImage[layerEditorShowModel.TransformAfter] =
      editorStore.layerSourceHandler;
    const imgData =
      showLayerModel && layerEdit.current.getOriginImage[showLayerModel]
        ? await layerEdit.current.getOriginImage[showLayerModel]()
        : null;

    await editorStore.initLayerEditor(layerEditContainer.current, imgData);
    editorStore.layerEditor?.smartSelectionTooler?.injectHasPaintedChecker(
      () => hasPaintedPromiseRef.current
    );
    if (timerByLoading.current) clearTimeout(timerByLoading.current);
    editorStore.setGlobalLoading(false);
    setInStyle(true);
    layerEditTypeUpdate(layerEditType);
  };

  const selectLayer = ({ nodes }: { nodes: BaseShape<ShapeConfig>[] }) => {
    if (nodes.length === 0) return;
    layerEdit.current.checkImgId = nodes[0].config.id;
  };
  //变更编辑器与主编器状态
  const layerEditTypeUpdate = (type: string | null) => {
    if (!type) return;

    const { app, historyPlugin } = editorStore.layerEditor || {};
    if (app && historyPlugin) {
      editorStore.setHeaderMainConfig({
        app,
        historyPlugin: historyPlugin,
        editor: editorStore.layerEditor?.wheeLayerEditorPlugin
      });
    }
    const {
      disableSelect,
      disableDownload,
      disableHistory,
      disableAddImg,
      disableMoveStage,
      disableScaleStage
    } = layerEditConfig || {};
    editorStore.headerBtnConfig = {
      addImg: !disableAddImg,
      moveStage: !disableMoveStage,
      select: !disableSelect,
      history: !disableHistory,
      scaleStage: !disableScaleStage
    };
    editorStore.setDisableDownload(disableDownload || false);

    //子编辑器状态
    editorStore.setDisableSelect(Boolean(disableSelect));
    editorStore.setDisableDownload(Boolean(disableDownload));
    if (!disableHistory) {
      editorStore.setProxyUndo(() => {
        editorStore.layerEditor?.historyPlugin.undo();
      });
      editorStore.setProxyRedo(() => {
        editorStore.layerEditor?.historyPlugin.redo();
      });
      if (editorStore.layerEditor?.historyPlugin) {
        editorStore.setProxyHistory(editorStore.layerEditor.historyPlugin);
      }
    }
  };
  //关闭弹窗
  const close = () => {
    setBackStyle(true);
    setTimeout(() => {
      setLayerEditActive(false);
      restore();
      setInStyle(false);
      editorStore.setLayerEditorKey(null);
      // setLayerEditType(null);
      //退场动画执行完成后 进行销毁处理
    }, 450);
  };
  //还原主画布状态
  const restore = () => {
    //还原鼠标状态
    editorStore.setMouseStyleType({
      ...layerEdit.current.mainEditorMouseStyleType,
      selectionWay: null
    });
    // editorStore.setEditor({
    //   ...layerEdit.current.originApp
    // })
    editorStore.configHeaderBtnState(layerEdit.current.originHeaderBtnStateUI);

    //主编辑器状态
    editorStore.setProxyUndo(() => {
      editorStore.editor.historyPlugin.undo();
    });
    editorStore.setProxyRedo(() => {
      editorStore.editor.historyPlugin.redo();
    });
    editorStore.setProxyHistory(editorStore.editor.historyPlugin);
    editorStore.setDisableSelect(false);
    editorStore.setDisableDownload(false);
    if (layerEdit.current.originHeaderBtnState)
      editorStore.headerBtnConfig = {
        ...layerEdit.current.originHeaderBtnState
      };
    editorStore.setDisableDownload(
      layerEdit.current.originDisableDownload || false
    );
    editorStore.setHeaderMainConfig({
      app: layerEdit.current.originApp.app,
      historyPlugin: layerEdit.current.originApp.historyPlugin,
      editor: layerEdit.current.originApp.wheeEditorPlugin
    });
    editorStore.editor.app.keyCommand.restore();

    if (layerEditorType.Extend === layerEditType) {
      editorStore.destroyExtendEditor();
    } else {
      editorStore.destroyLayerEditor();
      editorStore.setGlobalLoading(false);
    }

    return;
  };
  /**
   * 设置当前编辑器类型确定左侧展示内容
   * @param type
   */
  const LayerEditTypeHandle = (type: string | null) => {
    const layerEditConfigData = LayerBtnsConfig.find(
      (item) => item.key === type
    );
    if (layerEditConfigData) setLayerEditConfig(layerEditConfigData);
    setLayerEditType(type);
    editorStore.setLayerEditorKey(type);
    let subfunctionType = null;
    switch (type) {
      case layerEditorType.AiEraser:
        trackEvent('ai_modification_subfunction_click', {
          subfunction: TaskMap[TaskCategory.eraser],
          location: LocationMode.floating
        });
        setLayerEditActive(true);
        subfunctionType = Subfunction.Clear;
        break;
      case layerEditorType.AICutout:
        trackEvent('ai_modification_subfunction_click', {
          subfunction: TaskMap[TaskCategory.cutout],
          location: LocationMode.floating
        });
        setLayerEditActive(true);
        subfunctionType = Subfunction.Matting;
        break;
      case layerEditorType.Extend:
        trackEvent('ai_modification_subfunction_click', {
          subfunction: TaskMap[TaskCategory.extend],
          location: LocationMode.floating
        });
        setLayerEditActive(true);
        subfunctionType = Subfunction.Extension;
        break;
      default:
        break;
    }
    if (subfunctionType) {
      //埋点监听
      trackEvent('ai_modification_subfunction_click', {
        subfunction: subfunctionType,
        location: subFunctionClick.Floating
      });
    }
  };

  const LayerEditUI = (
    <>
      {/* back  退场动画   layerEditLayoutLeftAnimation 进场动画*/}
      <div
        className={classNames(
          styles.layerEditLayout,
          backStyle && styles.back,
          inStyle && styles.inEditor
        )}
      >
        <div className={styles.layerEditLayoutLeft}>
          <EditorLayoutLeft
            close={close}
            type={layerEditType}
            hasPainted={hasPainted}
            hasPaintedPromiseRef={hasPaintedPromiseRef}
            layerEditConfig={layerEditConfig}
          />
        </div>
        <div className={styles.layerEditLayoutRight}>
          <div
            ref={layerEditContainer}
            className={styles.layerEditContainer}
          ></div>
          <div id="editor-btns-portal-box"></div>
        </div>
        {hasPainted && (
          <div className={styles.selectionControlsBox}>
            <SelectionControls
              onClearSelection={() => {
                editorStore.layerEditorClearMaskEffectBefore();
                editorStore.layerEditor?.wheeLayerEditorPlugin?.plugin?.clearMask(
                  {
                    autoCommitHistory: editorStore.layerEditorAutoCommitHistory
                  }
                );
                editorStore.layerEditorClearMaskEffectAfter();
              }}
            />
          </div>
        )}
      </div>
    </>
  );

  const layerBtnUI = (
    <LayerBtnUI
      clickHandle={LayerEditTypeHandle}
      mainActiveKey={activeKey}
      configs={LayerBtnsConfig}
    />
  );

  return <>{!layerEditActive ? layerBtnUI : LayerEditUI}</>;
}

export default observer(LayerEditLayout);
