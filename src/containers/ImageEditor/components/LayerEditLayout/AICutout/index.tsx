import { PaintBold, Tool<PERSON>ursorBold, CheckBlack } from '@meitu/candy-icons';
import { createPortal } from 'react-dom';
import AreaSelection, { SelectionWay } from '../../AreaSelection';
import CommonPanel from '../../FeatureArea/CommonPanel';
import { CanvasMouseEventName } from '@/editor/core/src';
import styles from './index.module.less';
import {
  AreaSelectionMode,
  ModeSelection,
  OptionsType
} from '../../AreaSelection/ModeSelection';
import EditorSliderInput from '../../EditorSliderInput';

import { Form } from 'antd';
import SubmitFooter from '../../SubmitFooter';
import {
  AreaSelectionParams,
  ToolPluginsType,
  useAreaSelection,
  AICutoutSelectionWay
} from '@/containers/ImageEditor/hooks/useAICutoutAreaSelection';
import { useForm } from 'antd/es/form/Form';
import React, { useEffect, useMemo, useState, forwardRef, useRef } from 'react';
import { v4 as uuid } from 'uuid';
import { cancelTask, requestInferenceTask } from '@/api/imageEditor';
import { TaskCategory } from '@/api/types/imageEditor';
import { createImage } from '@/utils/cropImage';
import { observer } from 'mobx-react';
import useStore from '@/hooks/useStore';
import {
  canvasToBlob,
  getBioImageBlob,
  getGrayMaskDataURL
} from '@/containers/ImageEditor/utils/maskImageTools';
import { filterImageWithMask } from '@/containers/ImageEditor/utils/filterImage';
import { queryPolling } from '@/containers/ImageEditor/utils/queryPolling';
import { CancelButton } from '../../FeatureArea/CancelButton';
import { trackEvent } from '@/services';
import {
  Modification,
  Subfunction
} from '@/containers/ImageEditor/constant/trace';
import { getClock } from '@/containers/ImageEditor/utils/clock';
import { uploaderFunc } from '@/containers/ImageEditor/utils/upload';

import { CanvasNodeEventName, CanvasViewEventName } from '@/editor/core/src';
import { Confirm } from '../../modals/Confirm';
import classNames from 'classnames';
import { handleRequestError } from '@/containers/ImageEditor/utils/handleRequestError';
import {
  optimizeImage,
  resetImageUrl
} from '@/containers/ImageEditor/utils/image';

import {
  MinusCircleBold,
  MinusCircleBoldFill,
  PlusCircleBold,
  PlusCircleBoldFill,
  SelectionCursorBold
} from '@meitu/candy-icons';
import Konva from 'konva';
import { useAutoCloseToast } from '@/containers/ImageEditor/hooks/useToast';
import { MediaType, MtccFuncCode } from '@/api/types';
// 任务超时时间
const QUERY_POLLING_TIME_OUT = 5 * 60 * 1000;

// 取消按钮延迟展示时间
const CANCEL_BUTTON_SHOW_DELAY = 60 * 1000;

const AI_CUTOUT_EXIT_ONCE_MODAL_KEY = 'AI_CUTOUT_EXIT_ONCE_MODAL_KEY';

const selectionOptions = {
  [AreaSelectionMode.Auto]: {
    label: '自动识别',
    icon: <SelectionCursorBold />,
    activeIcon: <SelectionCursorBold />
  },
  [AreaSelectionMode.Add]: {
    label: '增加选区',
    icon: <PlusCircleBold />,
    activeIcon: <PlusCircleBoldFill />
  },
  [AreaSelectionMode.Remove]: {
    label: '减少选区',
    icon: <MinusCircleBold />,
    activeIcon: <MinusCircleBoldFill />
  }
} as OptionsType;

const defaultItems = [
  AreaSelectionMode.Auto,
  AreaSelectionMode.Add,
  AreaSelectionMode.Remove
];

type AICutoutProps = {
  actived?: boolean;
  hasPaintedPromiseRef: React.MutableRefObject<Promise<boolean>>;
  hasPainted: any;
  close: () => void;
  showClose?: boolean;
};
type AICutoutRef = {};
const AICutout = forwardRef<AICutoutRef, AICutoutProps>(
  (
    {
      actived,
      hasPaintedPromiseRef,
      hasPainted,
      close,
      showClose
    }: AICutoutProps,
    ref: React.Ref<AICutoutRef>
  ) => {
    const editorStore = useStore('EditorStore');
    const [pageState, setPageState] = useState('mask');
    const projectId = editorStore.projectInfo.id;
    const app = editorStore.layerEditor?.app;
    const wheeEditorPlugin = editorStore.layerEditor?.wheeLayerEditorPlugin;
    // const historyPlugin = editorStore.layerEditor?.historyPlugin;
    const editorLayersHandler = editorStore.editorLayersHandler;
    const { message } = useAutoCloseToast();

    const appendLayer = (layerId: string, messageId: string) => {
      editorStore.appendTaskWithLayer(layerId, {
        id: messageId,
        type: TaskCategory.cutout
      });
    };
    const originURLRef = useRef<string>('');
    const originIdRef = useRef<string>('');
    const [onlyOnceExitConfirm, setOnlyOnceExitConfirm] =
      useState<boolean>(false);
    const [isExitConfirmOpen, setIsExitConfirmOpen] = useState<boolean>(false);
    const AICutoutSelf = useRef({ closeEditor: close });

    const toolPlugins: ToolPluginsType = useMemo(() => {
      return {
        rectTool: editorStore.layerEditor?.rectTooler,
        eraserTool: editorStore.layerEditor?.eraserTooler,
        lineTool: editorStore.layerEditor?.lineTooler,
        lassoTool: editorStore.layerEditor?.lassoTooler,
        smartSelectionTool: editorStore.layerEditor?.smartSelectionTooler
      };
    }, [
      editorStore.layerEditor?.smartSelectionTooler,
      editorStore.layerEditor?.rectTooler,
      editorStore.layerEditor?.eraserTooler,
      editorStore.layerEditor?.lineTooler,
      editorStore.layerEditor?.lassoTooler
    ]);

    const {
      selectionWay,
      onSelectionWayChange,
      selectionParams,
      onSelectionParamsChange,
      autoRecognitionHandle
    } = useAreaSelection({
      app: editorStore.layerEditor?.app,
      toolPlugins,
      wheeEditorPlugin: editorStore.layerEditor?.wheeLayerEditorPlugin,
      hasPaintedPromiseRef,
      hooks: {
        autoRecognitionAfter: async () => {
          if (firstAddImage.current === 2) {
            return await addMaskNodeAfterHook(() => {
              onSelectionParamsChange({
                ...selectionParams,
                selectionMode: AreaSelectionMode.Add
              });
            });
          }
        }
      }
    });
    useEffect(() => {
      // 默认选择智能选择
      handleSelectionWayChange(SelectionWay.Smart);

      editorStore.setSmartSelectionAddMaskNodeAfterHook(addMaskNodeAfterHook);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
      // app?.on(CanvasMouseEventName.down, onDrawStart);
      app?.on(CanvasMouseEventName.up, onDraw);
      return () => {
        app?.off(CanvasMouseEventName.up, onDraw);
        // app?.off(CanvasMouseEventName.down, onDrawStart);
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [app]);

    React.useImperativeHandle(ref, function () {
      return {
        closeHandle: closeHandle
      };
    });

    function closeHandle(closeEditor: () => void) {
      AICutoutSelf.current.closeEditor = closeEditor;
      const skip = localStorage.getItem(AI_CUTOUT_EXIT_ONCE_MODAL_KEY);
      if (!hasPainted) {
        saveData(closeEditor);
        return;
      }
      if (skip !== 'true') {
        setIsExitConfirmOpen(true);
        return;
      }
      saveData(closeEditor);
    }

    function saveData(closeEditor: () => void) {
      const originId = editorStore.layerEditor?.originId;
      originId && editorStore.setActiveLayerId(originId);
      editorStore.updateLayerHistoryList();
      closeEditor(); //处理结束后进行编辑器关闭
    }

    function handleModeChange<T extends AICutoutSelectionWay>(
      mode: AreaSelectionMode,
      otherParams?: Omit<AreaSelectionParams<T>, 'selectionMode'>
    ) {
      onSelectionParamsChange({
        selectionMode: mode,
        ...otherParams
      });
    }

    function editorPictureStatus() {
      app?.unlockAllShapes();
    }

    const cancelEditorPictureStatus = () => {
      onSelectionWayChange(null);
      editorPictureStatus();
    };

    function handleSelectionWayChange(way: AICutoutSelectionWay | null) {
      onSelectionWayChange(way);
      // editorStore.editor.selectorPlugin.selector?.cancelSelect();
      // editorStore.editor.app.lockAllShapes();

      editorStore.unselectEffectHandler();
    }

    editorStore.setSelectEffect(cancelEditorPictureStatus);
    useEffect(() => {
      return () => {
        editorStore.setSelectEffect(() => {});
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
      const app = editorStore.layerEditor?.app;
      if (!app) {
        return;
      }
      app.on(CanvasNodeEventName.add, handleLayer);

      app.on(CanvasViewEventName.undoAfter, historyUpdate);
      app.on(CanvasViewEventName.redoAfter, historyUpdate);

      editorStore.setLayerEditorClearMaskEffectAfter(async () => {
        // editorStore.layerEditor?.historyPlugin?.commitCache();
        return await addMaskNodeAfterHook();
      });
      editorStore.setLayerEditorClearMaskEffectBefore(() => {
        editorStore.layerEditor?.historyPlugin?.enableCache();
      });
      return () => {
        app.off(CanvasNodeEventName.add, handleLayer);
        app.off(CanvasViewEventName.undoAfter, historyUpdate);
        app.off(CanvasViewEventName.redoAfter, historyUpdate);
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [editorStore.layerEditor]);
    const historyUpdate = () => {
      upDataPreviewImage();
    };

    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const noHasPaintedMessage = useRef<null | (() => void)>(null);
    const firstAddImage = useRef<number>(0);
    const handleLayer = async (param: any) => {
      if (param?.nodes[0]?.config?.type === 'image') {
        //仅第一次加载图片的时候 创建
        if (firstAddImage.current === 0) {
          // 把图片链接存到 ref中
          originURLRef.current = resetImageUrl(
            param?.nodes[0].config?.image?.currentSrc || ''
          );
          originIdRef.current = param?.nodes[0].config?.id;
          firstAddImage.current = 1;
          await imageDataHaveMaskImg();
          editorStore.layerEditor?.historyPlugin?.destroy();
          await autoRecognitionHandle();
        }
        return;
      }
      if (firstAddImage.current === 1) {
        firstAddImage.current = 2;
        //仅第一次加载mask 创建
        setTimeout(async () => {
          await upDataPreviewImage();
          editorStore.layerEditor?.historyPlugin?.install(
            editorStore.layerEditor.app
          );
        });
      } else {
        await upDataPreviewImage();
      }
    };
    const handleClip = async (imgUrl: string, type?: string) => {
      try {
        let mask = null;
        if (type) {
          mask =
            await editorStore.layerEditor?.wheeLayerEditorPlugin.plugin?.getOptionsLayer(
              'image/jpeg',
              '#000000'
            );
        } else {
          const imgOrigin = await createImage(imgUrl);
          mask =
            await editorStore.layerEditor?.wheeLayerEditorPlugin.plugin?.getOptionsLayerMaxImage(
              'image/jpeg',
              '#000000',
              imgOrigin.width,
              imgOrigin.height
            );
        }

        if (!mask) return;
        // 生成一个image 对象
        const imageMaskUrl = await getGrayMaskDataURL(mask);
        if (!imageMaskUrl) return;
        const imageMask = await createImage(imageMaskUrl);
        // image 新增一个id属性
        imageMask.id = 'mask';
        // 如果已经有图片了 先删除
        const oldImage = document.getElementById('mask');
        if (oldImage) {
          oldImage.remove();
        }
        let originImage = await createImage(imgUrl);

        let clip = await filterImageWithMask(originImage, imageMask);
        if (!clip) return;
        // 将clip canvas 转换为图片对象

        // 根据mask mask是黑底白色内容，获取白色内容区块最小矩形选区, 并且记录图片在选区中的位置 x,y

        let canvas = document.createElement('canvas');
        canvas.width = imageMask.width;
        canvas.height = imageMask.height;
        let ctx = canvas.getContext('2d');
        if (!ctx) return;
        ctx.drawImage(imageMask, 0, 0);
        let imageData = ctx.getImageData(
          0,
          0,
          imageMask.width,
          imageMask.height
        );
        let data = imageData.data;
        let minX = imageMask.width;
        let minY = imageMask.height;
        let maxX = 0;
        let maxY = 0;
        for (let i = 0; i < data.length; i += 4) {
          if (data[i] === 255) {
            let x = (i / 4) % imageMask.width;
            let y = Math.floor(i / 4 / imageMask.width);
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x);
            maxY = Math.max(maxY, y);
          }
        }
        let clipCanvas = document.createElement('canvas');

        clipCanvas.width = maxX - minX;
        clipCanvas.height = maxY - minY;
        let clipCtx = clipCanvas.getContext('2d');
        if (!clipCtx) return;
        clipCtx.drawImage(
          clip,
          minX,
          minY,
          maxX - minX,
          maxY - minY,
          0,
          0,
          maxX - minX,
          maxY - minY
        );
        let top = minY;
        let left = minX;

        // document.body.appendChild(clipCanvas);
        return {
          clipCanvas,
          top,
          left,
          originImage: {
            width: originImage.width,
            height: originImage.height
          }
        };
      } catch (error) {
        // console.log(error, 'error');
        return {
          clipCanvas: null,
          top: 0,
          left: 0,
          originImage: {
            width: 0,
            height: 0
          }
        };
      }
    };
    // const showDom = (node: HTMLElement) => {
    //   node.style.position = 'absolute'
    //   node.style.left = '0'
    //   node.style.top = '0'
    //   node.style.zIndex = '100000'
    //   node.style.pointerEvents = 'none'
    //   document.body.appendChild(node)
    // }

    async function handleSubmit() {
      if (loading) {
        return;
      }

      if (!navigator.onLine) {
        message({
          type: 'error',
          content: '网络异常',
          duration: 3000
        });
        return;
      }

      trackEvent('ai_modification_create_btn_click', {
        secondary_function: Subfunction.Matting,
        smear_size: selectionParams?.paintWidth,
        smear_hardness: (
          selectionParams as unknown as AreaSelectionParams<SelectionWay.Paint>
        )?.hardness,
        batch_size: 1
      });

      const clock = getClock();
      let remainingTime = QUERY_POLLING_TIME_OUT;

      // 无可见图层时
      const layers = editorLayersHandler();
      if (layers.filter((item) => item.hidden === false).length === 0) {
        message({
          type: 'error',
          content: '请开启至少一个图层才可进行生成',
          duration: 3000
        });
        return;
      }

      if (!wheeEditorPlugin?.plugin) {
        return;
      }
      let _closeToast: null | (() => void) = null;
      let isCanceledByUser = false;
      try {
        setLoading(true);
        // 如果可视区域没有涂抹 提示用户
        if (!(await hasPaintedPromiseRef.current)) {
          if (noHasPaintedMessage.current) noHasPaintedMessage.current();
          let { destroy: messageDestroy } = message({
            type: 'error',
            content: '请先选择想要保留的区域',
            duration: 3000
          });
          noHasPaintedMessage.current = messageDestroy;
          return;
        }

        const { destroy: closeToast } = message({
          type: 'loading',
          content: '生成中，请稍后...',
          isShowMask: true,
          customNode: (
            <CancelButton
              showDelay={CANCEL_BUTTON_SHOW_DELAY}
              onClick={handleCancel}
            >
              取消
            </CancelButton>
          )
        });

        _closeToast = closeToast;

        const resultImageData = await cutoutImage(originURLRef.current);
        if (!resultImageData) return;
        const {
          resultBlob,
          grayMaskBlob,
          imageClip: { originImage, left, top }
        } = resultImageData;
        const resultURL = await uploaderFunc(resultBlob, 'png', remainingTime);
        remainingTime -= clock.getDelta();
        const maskURL = await uploaderFunc(grayMaskBlob, 'png', remainingTime);

        if (isCanceledByUser) {
          return;
        }

        remainingTime -= clock.getDelta();
        if (isCanceledByUser) {
          return;
        }

        remainingTime -= clock.getDelta();
        const layerId = uuid();
        const createTaskRes = requestInferenceTask(
          {
            taskCategory: TaskCategory.cutout,
            projectId: projectId,
            layerId,
            params: {
              initImage: origin,
              maskImage: maskURL.url,
              resultImage: resultURL.url
            },
            functionName: MtccFuncCode.FuncCodeImageCutout,
            mediaType: MediaType.Photo,
            resMediaType: MediaType.Photo
          },
          remainingTime
        );
        const response = await createTaskRes;
        editorStore.setSurplus(response.surplus);
        //#endregion

        const messageId = response.id;
        if (isCanceledByUser) {
          cancelTask({
            projectId,
            msgId: messageId
          });
          // .then(() => {
          //   editorStore.updateSurplus();
          // });
          return;
        }

        remainingTime -= clock.getDelta();
        const { result, cancelPolling } = queryPolling(
          messageId,
          3000,
          TaskCategory.cutout,
          remainingTime
        );
        const pollingResult = await result;
        if (pollingResult.isCanceled) {
          if (pollingResult.isTimeout) {
            cancelTask({
              projectId,
              msgId: messageId
            });
            // .then(() => {
            //   editorStore.updateSurplus();
            // });
            throw new Error('生成失败，请重试');
          }

          return;
        }
        const historyList = pollingResult.data;

        function handleCancel() {
          isCanceledByUser = true;
          trackEvent('ai_modification_create_cancel', {
            subfunction: Subfunction.Matting
          });

          try {
            // 如果还没有创建轮训任务 取消轮训会抛出异常
            cancelPolling();

            // 运行到这里 轮训任务已经取消 还需要手动取消推理任务
            cancelTask({
              projectId,
              msgId: messageId
            });
            // .then(() => {
            //   editorStore.updateSurplus();
            // });
          } catch (e) {
            // 在轮训之前调用取消轮训会出现异常
          } finally {
            setLoading(false);
            _closeToast?.();
          }
        }

        const imageList: any[] = historyList[0]?.resultImages ?? [];

        const successList = imageList
          .filter((image) => image.imageStatus === 1)
          .map(({ url }) => url);
        if (!successList.length) {
          throw new Error('生成失败, 请重试');
        }
        trackEvent('ai_modification_create_success', {
          secondary_function: Subfunction.Matting,
          smear_size: selectionParams?.paintWidth,
          smear_hardness: (
            selectionParams as unknown as AreaSelectionParams<SelectionWay.Paint>
          )?.hardness,
          batch_size: 1
        });

        // 有部分图片没有审核通过时 告知用户
        if (successList.length !== imageList.length) {
          message({
            type: 'info',
            content: `${imageList.length - successList.length}张生成失败`,
            duration: 3000
          });
        }

        const first = successList[0];
        // 获取画板的宽高

        const image = await createImage(first);

        const originId = editorStore.layerEditor?.originId;
        if (!originId) return;
        appendLayer(originId, messageId);
        const originImg = editorStore.editor.app.findShapeById(originId);

        if (!originImg) return;
        const image1080Url = optimizeImage(first, {
          width: editorStore.maxEditorWidth,
          height: editorStore.maxEditorHeight
        });
        const originImage1080URL = optimizeImage(originURLRef.current, {
          width: editorStore.maxEditorWidth,
          height: editorStore.maxEditorHeight
        });
        const originImage1080 = await createImage(originImage1080URL);

        const image1080 = await createImage(image1080Url);
        const o_config = originImg.getAttr('target').config;
        // 根据画板的大小

        const scaleX = o_config.width / originImage1080.width;
        const scaleY = o_config.height / originImage1080.height;
        // left top 位置是基于原图的位置

        let scaleX_O = originImage1080.width / originImage.width;
        let scaleY_O = originImage1080.height / originImage.height;

        let leftNum = left * scaleX_O * scaleX;
        let topNum = top * scaleY_O * scaleY;

        // 根据缩放比例 重新设置 image 图片的宽高
        const nextImageWidth = image.width * scaleX_O * scaleX;
        const nextImageHeight = image.height * scaleY_O * scaleY;
        const originImgConfig = {
          ...originImg.getAttr('target').config,
          image: image1080,
          width: nextImageWidth,
          height: nextImageHeight,
          x: leftNum + nextImageWidth / 2 + (o_config.x - o_config.width / 2), //变更的是原始图的大小 所以调整缩放中心 也要取原属图的宽高
          y: topNum + nextImageHeight / 2 + (o_config.y - o_config.height / 2)
        };
        editorStore.editor.app?.update(originImgConfig);
        // editorStore.setLayersIsOpen(true);
        //
        close();
        editorStore.updateLayerHistoryList();

        // 关闭
        // saveData(AICutoutSelf.current.closeEditor);
      } catch (e: any) {
        // console.log(e, 'e');
        if (isCanceledByUser) {
          return;
        }

        if (e.isTimeout) {
          message({
            type: 'error',
            content: '生成失败，请重试',
            duration: 5000
          });
        } else {
          // message({
          //   type: 'error',
          //   content: '生成失败，请重试',
          //   duration: 5000
          // });
          handleRequestError(e);
        }
      } finally {
        if (!isCanceledByUser) {
          setLoading(false);
          _closeToast?.();
        }
      }
    }
    const handleClickExitModalCancel = () => {
      setIsExitConfirmOpen(false);
    };
    const handleClickExitModalOk = () => {
      setIsExitConfirmOpen(false);
      localStorage.setItem(
        AI_CUTOUT_EXIT_ONCE_MODAL_KEY,
        onlyOnceExitConfirm.toString()
      );
      saveData(AICutoutSelf.current.closeEditor);
      if (wheeEditorPlugin) {
        wheeEditorPlugin?.plugin?.clearMask({ autoCommitHistory: false });
      }
    };
    const cutoutImage = async (imgUrl: string, type?: string) => {
      const {
        clipCanvas = null,
        top = 0,
        left = 0,
        originImage
      } = (await handleClip(imgUrl, type)) as {
        clipCanvas: HTMLCanvasElement;
        top: number;
        left: number;
        originImage: {
          width: number;
          height: number;
        };
      };
      //
      //#region 创建任务
      const imgOrigin = await createImage(imgUrl);
      /**
       * 这里在导出时使用jpg 否则mask图中会有透明部分
       */
      const mask =
        await editorStore.layerEditor?.wheeLayerEditorPlugin.plugin?.getOptionsLayerMaxImage(
          'image/jpeg',
          '#000000',
          imgOrigin.width,
          imgOrigin.height
        );
      /**
       * 将jpeg的mask图转换为灰度图 同时将类型转换为png
       */
      if (!mask) return null;
      const grayMaskBlob = await getBioImageBlob(mask, 'image/png');
      if (!grayMaskBlob) {
        return null;
      }
      if (!clipCanvas) return null;
      const resultBlob = await canvasToBlob(clipCanvas, 'image/png');
      return {
        resultBlob,
        grayMaskBlob,
        imageClip: {
          originImage,
          top,
          left
        }
      };
    };
    const pageStateRef = useRef<string | null>(null);
    const selectionWayRef = useRef<string | null>(null);
    // const selectionWayRef = useRef<string | null>(null)
    useEffect(() => {
      pageStateRef.current = pageState;
      selectionWayRef.current = selectionWay;
    }, [pageState, selectionWay]);

    const onDraw = async () => {
      if (selectionWayRef.current === 'smart') {
        showMask();
        return;
      }
      // if (selectionWayRef.current === 'paint') {
      //   // setTimeout(async () => {
      //   //   await upDataPreviewImage();
      //   // }, 0);
      // }
    };

    const addMaskNodeAfterHook = async (after?: () => void) => {
      await upDataPreviewImage();
      editorStore.layerEditor?.historyPlugin?.commitCache();
      after && after();
    };
    // const optionsLayerZIndex = useRef<any>(null);
    // const preImageRef = useRef<any>(null);
    const haveMaskImgRef = useRef<any>(null);
    const haveMaskImgDomRef = useRef<any>(null);
    const imageDataHaveMaskImg = async () => {
      const imageList = app?.stage.find('Image');
      if (!(imageList && imageList.length > 0)) return null;
      const _haveMaskImg = imageList[0] as Konva.Image;
      haveMaskImgRef.current = _haveMaskImg;
      haveMaskImgDomRef.current = await createImage(
        haveMaskImgRef.current.getAttr('target').config.source
      );
    };
    const upDataPreviewImage = () => {
      if (pageStateRef.current === 'mask') return;
      runImagePoint();
    };
    //预览的画布
    const previewImage = async () => {
      setPageState('preview');
      const haveMaskImg = haveMaskImgRef.current;
      if (!haveMaskImg) return;
      haveMaskImg.opacity(0);
      runImagePoint();
    };
    const preImageNode = useRef<any>();
    // const preImageNodeBack = useRef<any>();
    // const preImageNodeShow = useRef<any>();
    const runImagePoint = async () => {
      const optionsGroup =
        editorStore.layerEditor?.wheeLayerEditorPlugin?.plugin?.optionsGroup;
      if (!optionsGroup) return;
      const stage = app?.stage as Konva.Stage;
      if (!stage) return;
      const mainLayer = app?.getLayerByName('meitu:main:layer');
      if (!mainLayer) return;
      const width = mainLayer?.find('Image')[0]?.width();
      const height = mainLayer?.find('Image')[0]?.height();
      const x = 0;
      const y = 0;
      const imgDom =
        haveMaskImgDomRef.current ||
        (await createImage(
          haveMaskImgRef.current.getAttr('target').config.source
        ));
      // if (preImageNodeBack.current) {
      //   preImageNodeBack.current.destroy()
      //   preImageNodeBack.current = null
      // }
      // if (preImageNodeShow.current) {
      //   preImageNodeShow.current.destroy()
      //   preImageNodeShow.current = null
      // }
      removePreImageEffect();
      const layerCanvas =
        await editorStore.layerEditor?.wheeLayerEditorPlugin?.plugin?.getOptionsLayerWithCanvas();
      if (!layerCanvas) return;

      var offscreenCanvas = document.createElement('canvas');
      offscreenCanvas.width = width || 0;
      offscreenCanvas.height = height || 0;
      var offscreenContext = offscreenCanvas.getContext('2d');
      if (!offscreenContext) return;
      offscreenContext.drawImage(layerCanvas, 0, 0);
      // 获取图片数据
      var imageData = offscreenContext.getImageData(
        0,
        0,
        offscreenCanvas.width,
        offscreenCanvas.height
      );
      var data = imageData.data;
      // 遍历像素数据，将透明像素填充为不透明的白色
      for (var i = 0; i < data.length; i += 4) {
        if (data[i] || data[i + 1] || data[i + 2]) {
          data[i + 3] *= 2; // Alpha
        }
      }
      offscreenContext?.putImageData(imageData, 0, 0);
      // 将修改后的数据放回离屏画
      const backGroundImage = new Konva.Image({
        width,
        height,
        x,
        y,
        image: offscreenCanvas,
        draggable: false
      });
      const preImageNode = new Konva.Image({
        width,
        height,
        x,
        y,
        image: imgDom,
        draggable: false,
        globalCompositeOperation: 'source-in'
      });

      // preImageNodeBack.current = backGroundImage
      // preImageNodeShow.current = preImageNode
      optionsGroup.add(backGroundImage, preImageNode);
      optionsGroup.draw();
    };
    const removePreImageEffect = () => {
      const optionsGroup =
        editorStore.layerEditor?.wheeLayerEditorPlugin?.plugin?.optionsGroup;
      if (!optionsGroup) return;
      const nodes = optionsGroup.getChildren();
      [...nodes].forEach((item, key) => {
        if (item instanceof Konva.Image) {
          item.destroy();
        }
      });
    };
    //显示带mask的画布
    const showMask = () => {
      setPageState('mask');
      const imageList = app?.stage.find('Image');
      if (imageList && imageList.length > 0) {
        const haveMaskImg = imageList[0];
        haveMaskImg.opacity(1);
      }
      if (preImageNode.current) {
        preImageNode.current.destroy();
        preImageNode.current = null;
      }
      removePreImageEffect();
    };
    return (
      <>
        <CommonPanel
          title="抠图"
          // titleTips="选择区域后，输入想要生成的内容重新绘制。"
          synopsisConfig={{
            x: 380,
            y: 56,
            hidden: false,
            type: TaskCategory.cutout
          }}
          actived={actived}
          showClose={showClose}
          extra={
            <SubmitFooter
              buttonLabel={'立即抠图'}
              onSubmit={handleSubmit}
              loading={loading}
              freeCounts={editorStore.surplus}
              disabled={!projectId}
            />
          }
        >
          <Form
            className={styles.aiEraserPanel}
            form={form}
            initialValues={{
              prompt: '',
              genNums: 4
            }}
          >
            <div className="panel-content-primary" style={{ marginTop: 16 }}>
              区域选择
            </div>
            <AreaSelection
              currentSelectionWay={selectionWay}
              onSelectionWayChange={(way: AICutoutSelectionWay) => {
                handleSelectionWayChange(way);

                if (selectionWay !== way) {
                  trackEvent('ai_modification_tool_click', {
                    tool: Modification.SelectionToolsTrace[way],
                    is_decrease:
                      Modification.IsDecreaseTrace[AreaSelectionMode.Add],
                    subfunction: Subfunction.Matting
                  });
                }
              }}
              items={[
                {
                  key: SelectionWay.Smart,
                  icon: <ToolCursorBold />,
                  label: '智能选择',
                  params: (
                    <>
                      <ModeSelection
                        options={selectionOptions}
                        items={defaultItems}
                        currentMode={selectionParams?.selectionMode}
                        onModeChange={(mode) => {
                          handleModeChange(mode);
                          if (mode === AreaSelectionMode.Auto) {
                            showMask();
                            trackEvent('ai_modification_tool_click', {
                              tool: 'auto_identify',
                              subfunction: Subfunction.Matting
                            });
                            return;
                          }
                          if (
                            mode !== selectionParams?.selectionMode &&
                            (mode === AreaSelectionMode.Add ||
                              AreaSelectionMode.Remove)
                          ) {
                            trackEvent('ai_modification_tool_click', {
                              tool: Modification.SelectionToolsTrace[
                                SelectionWay.Smart
                              ],
                              is_decrease: Modification.IsDecreaseTrace[mode],
                              subfunction: Subfunction.Matting
                            });
                          }
                        }}
                      />
                    </>
                  )
                },
                {
                  key: SelectionWay.Paint,
                  icon: <PaintBold />,
                  label: '涂抹',
                  params: (
                    <>
                      <ModeSelection
                        currentMode={selectionParams?.selectionMode}
                        onModeChange={(mode) => {
                          if (mode !== selectionParams?.selectionMode) {
                            trackEvent('ai_modification_tool_click', {
                              tool: Modification.SelectionToolsTrace[
                                SelectionWay.Paint
                              ],
                              is_decrease: Modification.IsDecreaseTrace[mode],
                              subfunction: Subfunction.Matting
                            });
                          }
                          const currentParams =
                            selectionParams as AreaSelectionParams<SelectionWay.Paint>;
                          handleModeChange<SelectionWay.Paint>(mode, {
                            paintWidth: currentParams.paintWidth,
                            hardness: currentParams.hardness
                          });
                        }}
                        addText="保留"
                        removeText="擦除"
                      />
                      <div className="paint-size">
                        <EditorSliderInput
                          title="大小"
                          min={1}
                          max={100}
                          value={
                            (
                              selectionParams as unknown as AreaSelectionParams<SelectionWay.Paint>
                            )?.paintWidth
                          }
                          onChange={(value) =>
                            onSelectionParamsChange({
                              ...selectionParams,
                              selectionMode:
                                selectionParams?.selectionMode ?? null,
                              paintWidth: value
                            })
                          }
                        />
                      </div>
                      {/* <div className="paint-size">
                        <EditorSliderInput
                          title="硬度"
                          min={1}
                          max={100}
                          value={
                            (
                              selectionParams as unknown as AreaSelectionParams<SelectionWay.Paint>
                            )?.hardness
                          }
                          onChange={(value) => {
                            onSelectionParamsChange({
                              ...selectionParams,
                              selectionMode:
                                selectionParams?.selectionMode ?? null,
                              hardness: value
                            });
                            // console.log(selectionParams, 'value');
                          }}
                        />
                      </div> */}
                    </>
                  )
                }
              ]}
            />
          </Form>
        </CommonPanel>
        <Confirm
          open={isExitConfirmOpen}
          okText="退出"
          onClickClose={handleClickExitModalCancel}
          onClickCancel={handleClickExitModalCancel}
          onClickOk={handleClickExitModalOk}
          contentClassName={styles.confirmModal}
          centered
          width={340}
        >
          <div className="confirm-modal-main">温馨提示</div>
          <div className="confirm-modal-secondary">
            此操作将退出当前功能，是否继续退出？
          </div>
          <label className="confirm-modal-check">
            <input
              type="checkbox"
              onChange={(e) => {
                const checked = e.target.checked;
                setOnlyOnceExitConfirm(checked);
              }}
            />
            <span
              className={classNames(
                'confirm-modal-check-box',
                onlyOnceExitConfirm && 'checked'
              )}
            >
              {onlyOnceExitConfirm && <CheckBlack />}
            </span>
            不再提示
          </label>
        </Confirm>
        {document.getElementById('editor-btns-portal-box') &&
          createPortal(
            <>
              <div className={styles['aiCutout-btns-box']}>
                <div
                  className={pageState === 'mask' ? styles.active : ''}
                  onClick={showMask}
                >
                  蒙版
                </div>
                <div
                  className={pageState === 'preview' ? styles.active : ''}
                  onClick={previewImage}
                >
                  预览
                </div>
              </div>
            </>,
            document.getElementById('editor-btns-portal-box') || document.body
          )}
      </>
    );
  }
);

export default observer(AICutout);
