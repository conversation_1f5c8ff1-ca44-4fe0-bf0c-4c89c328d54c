import FeatureArea from '../../FeatureArea';
import { ArrowBackCircleBold } from '@meitu/candy-icons';
import { observer } from 'mobx-react';
// import useStore from '@/hooks/useStore';
import AiEraser from '../AiEraser';
import AI<PERSON>utout from '../AICutout';
import { layerEditorType, LayerEditConfigType } from '../type';
import { useRef } from 'react';
import ExtendPicture from '../../FeatureArea/ExtendPicture/ExtendPicture';
type EditorLayoutLeftProps = {
  close: () => void;
  type: string | null;
  hasPainted: boolean;
  ********************: React.MutableRefObject<Promise<boolean>>;
  layerEditConfig?: LayerEditConfigType | null;
};
function EditorLayoutLeft({
  close,
  type,
  hasPainted,
  ********************,
  layerEditConfig
}: EditorLayoutLeftProps) {
  // const editorStore = useStore('EditorStore');
  const showFeatureAreaNodeRef = useRef<any>(null);
  const handleClose = () => {
    const { isDirectReturn } = layerEditConfig || {};
    if (isDirectReturn) {
      close();
    } else {
      //调用子节点确认 关闭时机
      showFeatureAreaNodeRef.current?.closeHandle(close);
    }
  };
  const showFeatureAreaNode = (key: string | null) => {
    let node = <></>;
    switch (key) {
      case layerEditorType.AiEraser:
        node = (
          <AiEraser
            ********************={********************}
            hasPainted={hasPainted}
            ref={showFeatureAreaNodeRef}
            showClose={false}
            close={close}
          />
        );
        break;
      case layerEditorType.AICutout:
        node = (
          <AICutout
            ********************={********************}
            hasPainted={hasPainted}
            ref={showFeatureAreaNodeRef}
            showClose={false}
            close={close}
          />
        );
        break;

      case layerEditorType.Extend:
        node = <ExtendPicture showClose={false} backMainEdit={close} />;
        break;
      default:
        break;
    }
    return node;
  };

  return (
    <>
      <FeatureArea
        items={[
          {
            key: 'back',
            icon: <ArrowBackCircleBold />,
            label: '返回',
            featurePanel: showFeatureAreaNode(type)
          }
        ]}
        activeKey={'back'}
        onFeatureChangeClick={() => {
          handleClose();
        }}
        isOpen={true}
        // onOpenChange={() => {}}
      />
    </>
  );
}

export default observer(EditorLayoutLeft);
