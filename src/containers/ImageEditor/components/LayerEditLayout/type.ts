export enum layerEditorType {
  // AI智能消除
  AiEraser = 'AiEraser',
  AICutout = 'AICutout',
  Extend = 'Extend'
}

export enum layerEditorShowModel {
  // 原始图大小 最长边 1080 缩放
  OriginImage = 'OriginImage',
  //变型后的大小
  TransformAfter = 'TransformAfter'
}

export type LayerEditConfigType = {
  name: string;
  key: string;
  showLayerModel: string; //展示图片方式 原始图/主编辑器缩放后大小 影响子编辑器画板大小
  isDirectReturn: boolean; //是否直接返回主编辑器
  disableSelect?: boolean; //禁用选中按钮
  disableDownload?: boolean; //禁用下载按钮
  disableHistory?: boolean; //禁用历史记录按钮
  disableAddImg?: boolean;
  disableMoveStage?: boolean;
  disableScaleStage?: boolean;
};
