import React, {
  useState,
  useEffect,
  useL<PERSON><PERSON><PERSON><PERSON><PERSON>,
  MouseEventHandler
} from 'react';
import { observer } from 'mobx-react';

import { Space, Image, Flex, Form } from 'antd';

import { Button } from '@/components/Button';
import { CommonUpload } from '../CommonUpload';
import { getImageEditorConfig } from '@/api/imageEditor';

import useStore from '@/hooks/useStore';

import {
  UploadBlack,
  ExclamationMarkCircleBoldFill,
  FileAddNewBold
} from '@meitu/candy-icons';

import { AppModule, generateRouteTo, trackEvent } from '@/services';
import { AppModuleParam, TabItemsType } from '@/types';
import { toAtlasImageView2URL } from '@meitu/util';
import { maxCanvasSize } from '@/utils/imageEditor';
import './index.less';
import { isChrome } from '../../utils/equipment';
import { Link } from 'react-router-dom';
import { optimizeImage } from '../../utils/image';
import { refreshActiveHighlight } from '../../tutorials/refresh';

import { useResizeDrawArea } from '../ResizeDrawArea';
import { maxDefaultEdge } from '../ResizeDrawArea/config';

export const InitUploadButtonId = 'editor-init-upload-button';

const InitState = observer(() => {
  const [projectList, setProjectList] = useState<any[]>([]);
  const [accept, setAccept] = useState<string>('.png,.jpg,.jpeg,.bmp');
  const editorStore = useStore('EditorStore');
  const [ResizeAreaCanvasForm] = Form.useForm();
  const queryParams = new URLSearchParams(window.location.search);
  let projectId = queryParams.get('projectId') || queryParams.get('project_id');
  useEffect(() => {
    // if (projectId) {
    //   return;
    // }
    getImageEditorConfig().then((res) => {
      setProjectList(res.projectList || []);
      let error = res.projectList.map((item) => {
        return true;
      });
      editorStore.setProjectErrorList(error);
      editorStore.setGlobalLoading(false);
    });
    if (isChrome()) {
      setAccept((val) => (val += ',.webp'));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editorStore, projectId]);

  // 当历史记录被渲染后 按钮的位置会发生改变 导致高亮区域有问题
  useLayoutEffect(() => {
    refreshActiveHighlight();
  });

  const historyClick = (
    e: React.MouseEvent<HTMLDivElement, MouseEvent>,
    item: any
  ) => {
    e.stopPropagation();
    // 数据上报
    trackEvent('modification_history_click', {
      project_id: item.projectId,
      function: AppModuleParam.ImageEditor
    });
    window.location.href = `/ai/image-editor?projectId=${item?.projectId}`;
    // getProjectInfo({ projectId: item.projectId, withEditorParams: true }).then(
    //   (res) => {
    //     // window.open(`/ai/image-editor?project_id=${res?.projectId}`, '_blank');
    //     // navigate(`/ai/image-editor?project_id=${res?.projectId}`);

    //   }
    // );
  };

  const dialogCreateProject = (
    width: number,
    height: number,
    ratio: string
  ) => {
    trackEvent('blank_canvas_create_confirm', {
      size: ratio,
      pixel: `${width}*${height}`,
      is_modify: 2
    });
    editorStore.editor?.wheeEditorPlugin.plugin?.init({
      width,
      height,
      layers: []
    });
    editorStore.setIsEditorInit(true);
    editorStore.setIsNewProject(true);
    editorStore.setProjectIsFromBlankCanvas(true);
    editorStore.calcScaleWheeEditor({ width, height });
    editorStore.setProjectInfo({
      width,
      height
    });
    trackEvent('blank_canvas_create_success', {
      size: ratio,
      pixel: `${width}*${height}`
    });
  };

  const { setShowModifyCanvasDialog, Component } = useResizeDrawArea({
    modifyCanvasSizeHandler: dialogCreateProject
  });

  const openProjectDialog: MouseEventHandler<HTMLElement> = (e) => {
    e.stopPropagation();
    setShowModifyCanvasDialog();
    trackEvent('blank_canvas_click');
  };
  return (
    <>
      <div className="editor-init">
        <div className="editor-init-right">
          <div className="editor-init-center">
            <CommonUpload
              customUploadedToast={({ successTotal, reviewErrorTotal }) => {
                if (successTotal) return '';

                if (reviewErrorTotal) return '请重新上传合规的图片';
              }}
              className="editor-init-upload"
              adaptive
              onClick={() => {
                trackEvent('whee_upload_image_click', {
                  function: AppModuleParam.ImageEditor
                });
              }}
              multiple={false}
              accept={accept}
              supports={[
                'image/jpeg',
                'image/png',
                'image/jpg',
                'image/bmp',
                'image/webp'
              ]}
              limit={30}
              onFinish={(v) => {
                if (!v[0]?.previewUrl) return;
                trackEvent('whee_upload_image_success', {
                  function: AppModuleParam.ImageEditor
                });
                editorStore.finishInitUploadTutorial();
                // onUploaded(v[0]?.previewUrl);
                editorStore.editor.app.setTool(editorStore.editor.imageTooler);
                const image = document.createElement('img');

                // 如果是webp格式的图片，转换成jpg格式
                // 文档：https://cf.meitu.com/confluence/pages/viewpage.action?pageId=257462561

                const path = v[0]?.previewUrl.split('?')?.[0];
                const query = v[0]?.previewUrl.split('?')?.[1];
                if (path.endsWith('.webp')) {
                  v[0].previewUrl = path + '?imageMogr2/format/jpg&' + query;
                }
                image.src = image.src = optimizeImage(v[0]?.previewUrl || '', {
                  width: editorStore.maxEditorWidth,
                  height: editorStore.maxEditorHeight
                });
                image.crossOrigin = 'Anonymous';

                image.onload = () => {
                  // 更新wheeEditorPlugin画布大小
                  const { width, height } = maxCanvasSize({
                    width: image.width,
                    height: image.height
                  });
                  //TODO: 暂时不做最小宽高限制4.23晚上确认
                  editorStore.editor?.wheeEditorPlugin.plugin?.init({
                    width,
                    height,
                    layers: []
                  });

                  // 插入前需要禁用掉历史记录
                  // 在首次创建项目插入图片时 不需要记录历史
                  try {
                    editorStore.proxyHistory?.disable();
                    editorStore.editor?.imageTooler.insertImage(
                      image,
                      image.src,
                      {
                        width,
                        height
                      },
                      {
                        x: width / 2,
                        y: height / 2
                      }
                    );
                    editorStore.setIsEditorInit(true);
                    editorStore.setIsNewProject(true);
                  } catch (e) {
                    throw e;
                  } finally {
                    editorStore.proxyHistory?.enable();
                  }

                  editorStore.calcScaleWheeEditor({ width, height });
                  editorStore.setProjectInfo({
                    width,
                    height
                  });
                  editorStore.editor.invertSelectPlugin.setSelectArea({
                    width:
                      editorStore.editor.wheeEditorPlugin.plugin?.getDrawAreaDetail()
                        ?.width || 0,
                    height:
                      editorStore.editor.wheeEditorPlugin.plugin?.getDrawAreaDetail()
                        ?.height || 0,
                    x:
                      editorStore.editor.wheeEditorPlugin.plugin?.getDrawAreaDetail()
                        ?.x || 0,
                    y:
                      editorStore.editor.wheeEditorPlugin.plugin?.getDrawAreaDetail()
                        ?.y || 0
                  });
                  editorStore.updateInit(false);

                  // 默认选中改图的“框选”
                  // editorStore.defaultChangePictureHandler();
                };
              }}
            >
              <Space size={10} direction="vertical">
                <Flex gap={24}>
                  <Button
                    id={InitUploadButtonId}
                    className={'button'}
                    icon={<UploadBlack />}
                  >
                    上传图片
                  </Button>
                  <Button
                    className={'defaultButton'}
                    icon={<FileAddNewBold />}
                    type="default"
                    onClick={openProjectDialog}
                  >
                    空白画布
                  </Button>
                </Flex>
                <div className="tips">点击/拖拽图片到此处</div>
                <div className="tips">
                  支持jpg、jpeg、bmp、png格式图片，30M以内
                </div>
              </Space>
              <div className="history">
                {projectList?.map((item: any, index: number) => {
                  return (
                    <div
                      key={index}
                      className="history-item"
                      onClick={(e) => historyClick(e, item)}
                    >
                      {item.picUrl && editorStore.projectErrorList[index] ? (
                        <Image
                          src={
                            // "https://aigc-pre.meitudata.com/editor/in/662a05ad04774iaucf81z0934.png"
                            // // item.picUrl
                            toAtlasImageView2URL(item.picUrl || '', {
                              mode: 2,
                              width: 128
                            })
                          }
                          key={index}
                          preview={false}
                          onError={(e) => {
                            let error = editorStore.projectErrorList;
                            error[index] = false;
                            editorStore.setProjectErrorList(error);
                          }}
                        />
                      ) : (
                        <div className="history-item-error">
                          <ExclamationMarkCircleBoldFill size={20} />
                          加载失败
                        </div>
                      )}
                    </div>
                  );
                })}

                {projectList.length > 0 && (
                  <div
                    className="history-item"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Link
                      className="more-projects-btn"
                      to={generateRouteTo(AppModule.Personal, {
                        tab: TabItemsType.Projects
                      })}
                      target="_blank"
                    >
                      更多历史
                    </Link>
                  </div>
                )}
              </div>
            </CommonUpload>
          </div>
        </div>
      </div>
      <Component
        form={ResizeAreaCanvasForm}
        defaultHeight={maxDefaultEdge}
        defaultWidth={maxDefaultEdge}
      ></Component>
    </>
  );
});

export default InitState;
