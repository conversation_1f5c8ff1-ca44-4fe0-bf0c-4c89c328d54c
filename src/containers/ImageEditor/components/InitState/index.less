.editor-init {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 50;
  background-color: rgba(0, 0, 0, 0.75);
  &-right {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    //width: calc(100vw - 380px);
    width: 100%;
  }
  &-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 268px;
  }
  &-upload {
    color: #fff;
    .ant-upload-wrapper .ant-upload-drag {
      border: none !important;
    }
  }
  &-box {
    width: 616px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
  .ant-space-item {
    text-align: center;
  }
  .button {
    display: flex;
    width: 160px;
    margin: 0 auto;
    height: 56px;
    padding: var(--spacing-0, 0px) var(--spacing-12, 12px);
    justify-content: center;
    font-size: 16px;
    align-items: center;
    font-weight: 500;
    border-radius: var(--radius-6, 6px);
    background: var(--background-editorButtonPrimaryAccent, #3549ff);
    color: #fff;
    margin-bottom: 14px;
  }
  .defaultButton {
    background: var(--background-editorButtonPrimary, #f1f2f6);
    color: var(--content-editorButtonOnPrimary, #131314);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    display: flex;
    width: 160px;
    height: 56px;
    padding: 0px 12px;
    justify-content: center;
    align-items: center;
    gap: 6px;
  }
  .tips {
    color: var(--content-editorTertiary, #5e5e6b);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    width: 330px;
    text-align: center;
  }
  .history {
    width: 680px;
    margin: 50px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    &-item {
      width: 92px;
      height: 93px;
      border-radius: 4px;
      overflow: hidden;
      background: rgba(235, 238, 255, 0.08);
      border: 2px solid #131314;
      border-width: 2px;
      margin-right: 8px;
      &:last-child {
        margin-right: 0;
      }
      &-error {
        width: 92px;
        height: 93px;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        border-width: 2px;
        border: 2px solid #131314;
        color: var(--content-editorTertiary, #636370);
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        svg {
          font-size: 20px;
          margin-bottom: 6px;
        }
      }

      .ant-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 4px;
        &-img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          border-radius: 4px;
        }
      }

      .more-projects-btn {
        display: flex;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
        color: #81838c;
        font-size: 14px;
        line-height: 20px;
      }
    }
    &-item:hover {
      border: 2px solid var(--stroke-editorSelectedAccent, #4053ff);
    }
  }
}
