// react header 组件
import { useCallback, useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react';
import classNames from 'classnames';

import { AccountAvatar } from '@/layouts/Header/AccountAvatar';
import { Button } from '@/components/Button';
import { CommonUpload } from '../CommonUpload';
import {
  CanvasNodeEventName,
  CanvasMouseEventName,
  CanvasDragEventName,
  CanvasZoomEventName
} from '@/editor/core/src/types';
import { fitImageToFrame } from '@/utils/imageEditor';

import {
  DownloadBlack,
  CloudSyn,
  UndoBlack,
  RedoBlack,
  PictureAddBold,
  ToolSelectBold,
  HandBold,
  CloudSynDone,
  CloudSynError,
  CloudBold,
  RatioFreeBold
} from '@meitu/candy-icons';
import { WheeWithBG, WheeOnlyFont } from '@/icons';

import { downloadFile } from '@/utils/blob';

import useStore from '@/hooks/useStore';
import { SyncStatus } from '@/types/imageEditor';

import { TaskCategory, TaskMap } from '@/api/types/imageEditor';
import { trackEvent } from '@/services';
import { AppModuleParam } from '@/types';

import './index.less';
import { isChrome } from '../../utils/equipment';
import { optimizeImage } from '../../utils/image';

import StageScale from '../StageScale';

import { HotKeyTooltip } from '../HotKeyTooltip';

import Explain from './Explain';
import { useResizeDrawArea } from '../ResizeDrawArea';
import { Form } from 'antd';
export const HEADER_SELECT_ID = 'header-select';

const SaveStatus = () => {
  let { syncStatus } = useStore('EditorStore');
  if (syncStatus === SyncStatus.INIT) {
    return (
      <>
        <CloudBold className="save-icon" />
      </>
    );
  } else if (syncStatus === SyncStatus.SYNCING) {
    return (
      <>
        <CloudSyn className="save-icon" />
        保存中
      </>
    );
  } else if (syncStatus === SyncStatus.SUCCESS) {
    return (
      <>
        <CloudSynDone className="save-icon" />
        保存成功
      </>
    );
  } else if (syncStatus === SyncStatus.FAILED) {
    return (
      <>
        <CloudSynError className="save-icon" />
        保存失败
      </>
    );
  }
};

const HeaderLeft = observer(() => {
  return (
    <>
      <div className="editor-header-logo">
        <a className="editor-header-logo-icon" href="/" target="_blank">
          <WheeWithBG className="logo" />
          <WheeOnlyFont className="whee" />
          AI改图
        </a>
        <Explain />
        <div className="editor-header-save">{SaveStatus()}</div>
      </div>
    </>
  );
});

const HeaderCenter = observer(() => {
  let editorStore = useStore('EditorStore');
  const {
    //  editor: { app },
    extendEditor,
    disableRedo,
    disableUndo,
    setHeaderMainConfig,
    headerMainConfig,
    headerBtnConfig,
    setConfigHeaderBtnState,
    setHeaderBtnState,
    configHeaderBtnState
  } = useStore('EditorStore');
  const { app, editor } = headerMainConfig || {};
  // const { addImg, moveStage, select, history } = headerBtnConfig

  // 默认选中“框选” 所以这里默认不选中

  const [selected, setSelected] = useState(false);

  // 移动画板模式
  const [selectedMove, setSelectedMove] = useState(false);

  const [ResizeAreaCanvasForm] = Form.useForm();
  const selectedRef = useRef(false);
  const [accept, setAccept] = useState<string>('.png,.jpg,.jpeg,.bmp');
  const [isMac, setIsMac] = useState<string>('');
  // 启动移动画板
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const selectMoveHandler = () => {
    trackEvent('ai_modification_top_click', {
      subfunction: TaskMap[editorStore.currentFeature || TaskCategory.inpaint],
      click_type: 'move'
    });
    unselectEffectHandler();
    setSelectedMove(true);
    unselectEffectHandlerStore();
    editorStore.editor.selectorPlugin.selector?.cancelSelect();
    if (editor) {
      editor.plugin?.enableGlobalMove();
      return;
    }
    editorStore.editor.wheeEditorPlugin.plugin?.enableGlobalMove();
  };
  // 关闭移动画板
  const unselectMoveHandler = () => {
    setSelectedMove(false);
    if (editor) {
      editor.plugin?.disableGlobalMove();
      return;
    }
    editorStore.editor.wheeEditorPlugin.plugin?.disableGlobalMove();
  };
  //同步状态
  useEffect(() => {
    const btnState = {
      selected,
      selectedMove
    };
    setHeaderBtnState(btnState);
    configHeaderBtnState(btnState);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selected, selectedMove]);
  editorStore.setUnselectMoveHandler(unselectMoveHandler);

  function selectorHandler() {
    if (!editorStore.editor.selectorPlugin.isEnable()) {
      editorStore.editor.selectorPlugin.enable();
    }
    editorStore.setMouseStyleType({
      selectionParams: '',
      //selectionWay: 'selector'
      selectionWay: ''
    });
    unselectMoveHandler();
    setSelected(true);
    selectedRef.current = true;
  }

  const unselectEffectHandler = () => {
    setSelected(false);
    selectedRef.current = false;
    unselectMoveHandler();
  };

  const unselectEffectHandlerStore = () => {
    editorStore.selectEffectHandler();
  };

  editorStore.setunSelectEffect(unselectEffectHandler);

  const keyUpHandler = useCallback(
    (e: any) => {
      if (e.defaultPrevented) {
        return;
      }
      if (selectedMove && e.code === 'Space') {
        selectMoveHandler();
      }
    },
    [selectMoveHandler, selectedMove]
  );
  useEffect(() => {
    window.addEventListener('keyup', keyUpHandler);
    return () => {
      window.removeEventListener('keyup', keyUpHandler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [keyUpHandler]);

  useEffect(() => {
    //所有模块加载结束后设置 默认激活状态
    setSelected(true);
    if (isChrome()) {
      setAccept((val) => (val += ',.webp'));
    }
    const isInMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    setIsMac(isInMac ? '⌘' : 'Ctrl');
    return () => {
      editorStore.setunSelectEffect(() => {});
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const effectHistory = () => {
    setTimeout(() => {
      // console.log(editorStore?.proxyHistory, 'editorStore?.proxyHistory-----')

      // console.log(editorStore?.proxyHistory?.exportHistory(), 'editorStore?.proxyHistory.exportHistory-----')
      let undoStack =
        editorStore?.proxyHistory?.exportHistory().undoStack ?? [];
      let redoStack =
        editorStore?.proxyHistory?.exportHistory().redoStack ?? [];
      let disableUndo = undoStack.length === 0;
      let disableRedo = redoStack.length === 0;
      if (!headerBtnConfig.history) {
        disableUndo = true;
        disableRedo = true;
      } else {
        disableUndo = undoStack.length === 0 ? true : false;
        disableRedo = redoStack.length === 0 ? true : false;
      }
      editorStore.setDisableRedo(disableRedo);
      editorStore.setDisableUndo(disableUndo);
    }, 10);
  };
  useEffect(() => {
    effectHistory();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [headerBtnConfig, headerBtnConfig.history, editorStore.currentFeature]);

  useEffect(() => {
    setHeaderMainConfig({
      app: editorStore.editor.app,
      historyPlugin: editorStore.editor?.historyPlugin,
      editor: editorStore.editor.wheeEditorPlugin
    });
    setConfigHeaderBtnState(setHeaderBtnStateHandle);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const setHeaderBtnStateHandle = (data: any) => {
    if (!data) return;
    const editorPlugin = editorStore.editor.wheeEditorPlugin.plugin;
    if (editorPlugin) {
      //监听按钮是否被选中  激活选择按钮 为可见轮廓光
      editorPlugin.disableHoverOutline = !data.selected;
    }
    if (data.selected) {
      if (!headerBtnConfig.select) return;
      selectHandle();
    }
    //移动画布方法
    data.selectedMove ? selectMoveHandler() : unselectMoveHandler();
  };

  useEffect(() => {
    if (!app) return;
    app.on(CanvasNodeEventName.select, selectorHandler);
    // app.on(CanvasNodeEventName.unselect, unselectEffectHandler);
    app.on(CanvasDragEventName.move, transformStartHandler);
    app.on(CanvasDragEventName.end, transformEndHandler);
    app.on(CanvasNodeEventName.add, effectHistory);
    app.on(CanvasNodeEventName.remove, effectHistory);
    app.on(CanvasNodeEventName.zIndexChanged, effectHistory);
    app.on(CanvasNodeEventName.updateAfter, effectHistory);
    app.on(CanvasZoomEventName.start, handleZoomStart);
    app.on(CanvasZoomEventName.end, handleZoomEnd);
    app.keyCommand.register(
      'v,V',
      (event) => {
        event.preventDefault();
        if (event.type === 'keydown') {
          if (!headerBtnConfig.select) return;
          selectHandle();
        }
      },
      {
        keyup: true
      }
    );
    app.keyCommand.register('ctrl+z,command+z', (event: Event) => {
      event.preventDefault();
      editorStore.proxyUndo();
    });
    app.keyCommand.register('ctrl+y,command+y', (event: Event) => {
      event.preventDefault();
      editorStore.proxyRedo();
    });

    return () => {
      app.off(CanvasNodeEventName.select, selectorHandler);
      // app.off(CanvasNodeEventName.unselect, unselectEffectHandler);
      app.off(CanvasDragEventName.move, transformStartHandler);
      app.off(CanvasDragEventName.end, transformEndHandler);
      app.off(CanvasMouseEventName.moveOutStage, moveOutStageHandler);
      app.off(CanvasNodeEventName.add, effectHistory);
      app.off(CanvasNodeEventName.remove, effectHistory);
      app.off(CanvasNodeEventName.zIndexChanged, effectHistory);
      app.off(CanvasNodeEventName.updateAfter, effectHistory);
      app.off(CanvasZoomEventName.start, handleZoomStart);
      app.off(CanvasZoomEventName.end, handleZoomEnd);
      app.keyCommand.unbind('v,V');
      app.keyCommand.unbind('ctrl+z,command+z');
      app.keyCommand.unbind('ctrl+y,command+y');
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [app, editorStore]);
  useEffect(() => {
    const app = extendEditor?.app;
    if (!app) return;
    app.on(CanvasNodeEventName.select, selectorHandler);
    app.on(CanvasNodeEventName.unselect, unselectEffectHandler);
    app.on(CanvasDragEventName.move, transformStartHandler);
    app.on(CanvasDragEventName.end, transformEndHandler);
    app.on(CanvasNodeEventName.add, effectHistory);
    app.on(CanvasNodeEventName.remove, effectHistory);
    app.on(CanvasNodeEventName.zIndexChanged, effectHistory);
    app.on(CanvasNodeEventName.updateAfter, effectHistory);

    return () => {
      app.off(CanvasNodeEventName.select, selectorHandler);
      app.off(CanvasNodeEventName.unselect, unselectEffectHandler);
      app.off(CanvasDragEventName.move, transformStartHandler);
      app.off(CanvasDragEventName.end, transformEndHandler);
      app.off(CanvasMouseEventName.moveOutStage, moveOutStageHandler);
      app.off(CanvasNodeEventName.add, effectHistory);
      app.off(CanvasNodeEventName.remove, effectHistory);
      app.off(CanvasNodeEventName.zIndexChanged, effectHistory);
      app.off(CanvasNodeEventName.updateAfter, effectHistory);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [extendEditor?.app]);

  const transformStartHandler = (event: any) => {
    const draggedNode = event.event.target;
    if (draggedNode.name() === 'meitu:selector:transformer') {
      editorStore.setMouseStyleType({
        selectionParams: '',
        selectionWay: 'selector_move'
      });
    }
  };
  const transformEndHandler = (event: any) => {
    const draggedNode = event.event.target;
    if (draggedNode.name() === 'meitu:selector:transformer') {
      editorStore.setMouseStyleType({
        selectionParams: '',
        selectionWay: ''
      });
    }
    effectHistory();
  };
  const moveOutStageHandler = () => {};

  //监听移动端缩放禁用选择
  const handleZoomStart = () => {
    app?.lockAllShapes();
  };

  //监听移动端缩放完成恢复选择
  const handleZoomEnd = () => {
    if (!selectedRef.current) return;
    app?.unlockAllShapes();
  };

  const selectHandle = () => {
    unselectMoveHandler();
    setSelected(true);
    selectedRef.current = true;
    editorStore.editor.app.unlockAllShapes();
    editorStore.editor.app.unlockAllShapes(
      editorStore.editor.wheeEditorPlugin.plugin?.graffitiLayer
    );
    if (!editorStore.editor.selectorPlugin.isEnable()) {
      editorStore.editor.selectorPlugin.enable();
    }
    unselectEffectHandlerStore();
    editorStore.setMouseStyleType({
      selectionParams: null,
      selectionWay: null
    });
  };

  // //手动更新 触发select
  // setActiveSelect(() => {
  //   selectHandle()
  // });
  // 修改画布尺寸
  const syncModifyCanvasSizeHandler = (
    width: number,
    height: number,
    ratio: string
  ) => {
    trackEvent('blank_canvas_create_confirm', {
      size: ratio,
      pixel: `${width}*${height}`,
      is_modify: 1,
      project_id: editorStore.projectInfo.id
    });
    editorStore.modifyCanvasSize(width, height);
    window.dispatchEvent(new Event('customSave'));
  };

  const { setShowModifyCanvasDialog, Component } = useResizeDrawArea({
    modifyCanvasSizeHandler: syncModifyCanvasSizeHandler
  });
  const modifyCanvasSizeHandler = () => {
    if (!headerBtnConfig.select) {
      return;
    }
    trackEvent('ai_modification_top_click', {
      click_type: `canvas_size`
    });
    setShowModifyCanvasDialog();
    setTimeout(() => {
      ResizeAreaCanvasForm.setFieldsValue({
        canvasSize: {
          width: editorStore.projectInfo.width,
          height: editorStore.projectInfo.height
        }
      });
    });
  };

  return (
    <div className="editor-header-center">
      <Component
        form={ResizeAreaCanvasForm}
        defaultHeight={editorStore.projectInfo.height}
        defaultWidth={editorStore.projectInfo.width}
      ></Component>
      <div
        className={classNames(
          !headerBtnConfig.select && 'editor-header-center-select-disable',
          'editor-header-center-static'
        )}
        onClick={modifyCanvasSizeHandler}
      >
        <RatioFreeBold></RatioFreeBold> &nbsp;画布尺寸
      </div>
      <HotKeyTooltip title={`撤销  ${isMac}+Z`}>
        <div
          className={
            disableUndo
              ? 'editor-header-center-undo editor-header-center-undo-disable'
              : 'editor-header-center-undo'
          }
          onClick={() => {
            editorStore.proxyUndo();
            // editorStore.editor.historyPlugin.undo();
            if (headerBtnConfig.select)
              editorStore.editor?.selectorPlugin.selector?.cancelSelect();
          }}
        >
          <UndoBlack fill={disableUndo ? '#48444d' : '#fff'} />
        </div>
      </HotKeyTooltip>
      <HotKeyTooltip title={`重做  ${isMac}+Y`}>
        <div
          className={
            disableRedo
              ? 'editor-header-center-redo editor-header-center-redo-disable'
              : 'editor-header-center-redo'
          }
          onClick={() => {
            editorStore.proxyRedo();
            // editorStore.editor.historyPlugin.redo();
            if (headerBtnConfig.select)
              editorStore.editor?.selectorPlugin.selector?.cancelSelect();
          }}
        >
          <RedoBlack fill={disableRedo ? '#48484d' : '#fff'} />
        </div>
      </HotKeyTooltip>
      <div className="editor-header-center-separator"></div>
      <HotKeyTooltip title={'选择 V'}>
        <div
          id={HEADER_SELECT_ID}
          className={classNames(
            'editor-header-center-select',
            'driver-active-element',
            !headerBtnConfig.select && 'editor-header-center-select-disable',
            selected && 'active'
          )}
          onClick={() => {
            if (!headerBtnConfig.select) return;
            selectHandle();
            trackEvent('ai_modification_top_click', {
              subfunction:
                TaskMap[editorStore.currentFeature || TaskCategory.inpaint],
              click_type: 'select'
            });
          }}
        >
          <ToolSelectBold fill={!headerBtnConfig.select ? '#48484d' : '#fff'} />{' '}
          &nbsp;选择
        </div>
      </HotKeyTooltip>
      {/* 移动画板模式---start */}
      <HotKeyTooltip title={'移动 按住space拖动'}>
        <div
          className={classNames(
            'editor-header-center-select',
            !headerBtnConfig.moveStage && 'editor-header-center-select-disable',
            selectedMove && 'active'
          )}
          onClick={selectMoveHandler}
        >
          <HandBold fill={!headerBtnConfig.moveStage ? '#48484d' : '#fff'} />{' '}
          &nbsp;移动
        </div>
      </HotKeyTooltip>
      {/* 移动画板模式---end */}

      <div className="editor-header-center-upload">
        {!headerBtnConfig.addImg ? (
          <div
            className={
              !headerBtnConfig.addImg
                ? 'editor-header-center-add editor-header-center-add-disable'
                : 'editor-header-center-add'
            }
          >
            <PictureAddBold
              fill={!headerBtnConfig.addImg ? '#48484d' : '#fff'}
            />{' '}
            &nbsp;加图
          </div>
        ) : (
          <CommonUpload
            className="editor-header-center-upload-btn"
            customUploadedToast={({ successTotal, reviewErrorTotal }) => {
              if (successTotal) return '';

              if (reviewErrorTotal) return '请重新上传合规的图片';
            }}
            onClick={() => {
              trackEvent('whee_upload_image_click', {
                function: AppModuleParam.ImageEditor
              });
              trackEvent('ai_modification_top_click', {
                subfunction:
                  TaskMap[editorStore.currentFeature || TaskCategory.inpaint],
                click_type: 'pic_add'
              });
            }}
            accept={accept}
            supports={[
              'image/jpeg',
              'image/png',
              'image/jpg',
              'image/bmp',
              'image/webp'
            ]}
            limit={30}
            onFinish={async (v) => {
              editorStore.editor?.selectorPlugin.selector?.cancelSelect();
              if (!v[0]?.previewUrl) return;
              trackEvent('whee_upload_image_success', {
                function: AppModuleParam.ImageEditor
              });

              // 如果当前功能是改图 上传完成后 询问用户是否退出改图
              if (editorStore.currentFeature === TaskCategory.inpaint) {
                const exitChangePicture =
                  await editorStore.confirmExitChangePictureHandler();
                if (!exitChangePicture) {
                  return;
                }
              }

              const image = document.createElement('img');

              // 如果是webp格式的图片，转换成jpg格式
              // 文档：https://cf.meitu.com/confluence/pages/viewpage.action?pageId=257462561

              const path = v[0]?.previewUrl.split('?')?.[0];
              const query = v[0]?.previewUrl.split('?')?.[1];
              if (path.endsWith('.webp')) {
                v[0].previewUrl = path + '?imageMogr2/format/jpg&' + query;
              }
              image.src = optimizeImage(v[0]?.previewUrl || '', {
                width: editorStore.maxEditorWidth,
                height: editorStore.maxEditorHeight
              });
              image.crossOrigin = 'Anonymous';
              image.onload = () => {
                const { width, height } =
                  editorStore.editor.wheeEditorPlugin.plugin?.getDrawAreaDetail() || {
                    width: 0,
                    height: 0
                  };
                const { width: maxWidth, height: maxHeight } = fitImageToFrame(
                  width,
                  height,
                  image.width,
                  image.height
                );
                editorStore.editor.app.setTool(editorStore.editor.imageTooler);
                editorStore.editor?.imageTooler.insertImage(
                  image,
                  image.src,
                  {
                    width: maxWidth,
                    height: maxHeight
                  },
                  {
                    x: (width - maxWidth) / 2 + maxWidth / 2,
                    y: (height - maxHeight) / 2 + maxHeight / 2
                  }
                );
              };
            }}
          >
            <div
              className={
                !headerBtnConfig.addImg
                  ? 'editor-header-center-add editor-header-center-add-disable'
                  : 'editor-header-center-add'
              }
            >
              <PictureAddBold
                fill={!headerBtnConfig.addImg ? '#48484d' : '#fff'}
              />{' '}
              &nbsp;加图
            </div>
          </CommonUpload>
        )}
      </div>

      {/* </div> */}
      <div style={{ color: 'white', width: 'fit-content', display: 'none' }}>
        {editorStore.projectInfo.width} x {editorStore.projectInfo.height}
      </div>
    </div>
  );
});

const HeaderRight = observer(() => {
  let { exportCoverImage, disableDownload, currentFeature, headerBtnConfig } =
    useStore('EditorStore');
  return (
    <div className="editor-header-right">
      <div className="editor-header-stage-scale">
        <StageScale disabled={!(headerBtnConfig?.scaleStage || null)} />
      </div>
      <Button
        className="editor-header-export"
        disabled={disableDownload}
        onClick={async () => {
          // 名称为当前时间戳格式为年月日时分秒
          let name = new Date().toLocaleString().replace(/\/|:| /g, '');
          // uploaderFunc((await exportCoverImage()), '.png');
          downloadFile(
            await exportCoverImage(),
            'whee_inpaint_' + name + '.png'
          );
          trackEvent('ai_modification_edit_page_download_click', {
            subfunction:
              currentFeature === TaskCategory.compound
                ? 'compound'
                : currentFeature === TaskCategory.graffiti
                ? 'sketch'
                : 'modification'
          });
        }}
      >
        <DownloadBlack />
        下载
      </Button>
      <div className="editor-header-account">
        <AccountAvatar />
      </div>
    </div>
  );
});

const Header = observer(() => {
  return (
    <div className="editor-header" id="editor-header">
      <HeaderLeft />
      <HeaderCenter />
      <HeaderRight />
    </div>
  );
});

export default Header;
