.editor-header {
  border-bottom: 1px solid var(--stroke-editor<PERSON><PERSON><PERSON><PERSON>, #29292e);
  background: var(--background-editor<PERSON><PERSON><PERSON><PERSON><PERSON>, #17171a);
  height: 56px;
  width: 100%;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .ant-btn.designer-btn.ant-btn:not(.ant-btn-sm):not(.ant-btn-circle) {
    border-radius: 4px;
    padding: 0;
    height: 36px;
  }
  .ant-btn.designer-btn.ant-btn-primary:disabled {
    background: #1e2353;
    outline: none;
    border: none;
    color: #515153;
  }

  &-export {
    margin-right: 16px;
    width: 80px;
    :global(.ant-btn) {
      padding: 0 !important;
    }
  }

  &-logo {
    display: flex;
    align-items: center;
    color: #fff;
    font-family: 'PingFang SC';
    font-size: 17px;
    font-style: normal;
    font-weight: 600;
    margin-left: 6px;
    line-height: normal;
    width: 300px;

    a {
      color: #fff !important;
    }

    &-icon {
      z-index: 61;
      display: flex;
      align-items: center;
      color: #fff;
      font-family: 'PingFang SC';
      font-size: 17px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      cursor: pointer;
      white-space: nowrap;
      .logo {
        width: 36px;
        height: 36px;

        svg {
          width: 100%;
          height: 100%;
        }
      }

      .whee {
        width: 58px;
        height: 14px;
        margin-left: 6px;
        margin-right: 10px;

        svg {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  &-account {
    z-index: 61;
    width: 36px;

    .ant-avatar {
      border: none;
    }
  }

  &-save {
    color: var(--content-editorButtonSecondary, #dcdde5);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-left: 26px;
    white-space: nowrap;

    .save-icon {
      margin-right: 4px;
    }
  }

  &-stage {
    &-scale {
      margin-right: 8px;
    }
  }

  &-center {
    // width: 241px;
    height: 36px;
    display: flex;
    align-items: center;
    color: var(--content-editorButtonSecondary, #dcdde5);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;

    &-separator {
      width: 1px;
      height: 24px;
      background: var(--stroke-editorSeparatorPrimary, #29292e);
      margin-left: 8px;
      margin-right: 8px;
    }

    &-undo {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      border-radius: 4px;
      cursor: pointer;

      &-disable {
        cursor: not-allowed;
        color: var(--content-editorButtonDisabled, #dcdde5);
      }

      &-disable:hover {
        background-color: #17171a !important;
      }
    }

    &-undo:hover {
      background-color: var(
        --background-editorHoverOverlay,
        rgba(235, 238, 255, 0.1)
      );
    }

    &-redo {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      cursor: pointer;

      &-disable {
        cursor: not-allowed;
        color: var(--content-editorButtonDisabled, #dcdde5);
      }

      &-disable:hover {
        background-color: #17171a !important;
      }
    }

    &-redo:hover {
      background-color: var(
        --background-editorHoverOverlay,
        rgba(235, 238, 255, 0.1)
      );
    }

    &-static {
      width: fit-content;
      height: 36px;
      margin-right: 8px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }

    &-select {
      width: 68px;
      height: 36px;
      // margin-left: 8px;
      margin-right: 8px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      &-disable {
        cursor: not-allowed;
        color: #48484d;
      }

      &-disable:hover {
        background-color: #17171a !important;
      }

      &.active {
        background-color: var(
          --background-editorHoverOverlay,
          rgba(235, 238, 255, 0.1)
        );
      }
    }

    &-select:hover {
      background-color: var(
        --background-editorHoverOverlay,
        rgba(235, 238, 255, 0.1)
      );
    }

    &-upload {
      width: 68px;
      height: 36px;
      border-radius: 4px;

      &-disable {
        cursor: not-allowed;
      }

      &-disable:hover {
        background-color: #17171a !important;
      }

      .ant-upload {
        padding: 0;
      }

      .ant-upload-wrapper .ant-upload-drag .ant-upload {
        padding: 0;
      }

      .ant-upload-wrapper {
        border: none;
        width: 68px;
        height: 36px;

        .ant-upload-drag {
          border: none;
        }
      }

      .ant-upload-list {
        display: none;
      }
    }

    &-upload:hover {
      background-color: var(
        --background-editorHoverOverlay,
        rgba(235, 238, 255, 0.1)
      );
    }

    &-add {
      color: var(--content-editorButtonSecondary, #dcdde5);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      width: 68px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 4px;

      &-disable {
        cursor: not-allowed;
        color: #48484d;
      }

      &-disable:hover {
        background-color: #17171a !important;
      }
    }

    &-add:hover {
    }
  }

  &-right {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
