.HotKeyMask {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  right: 0;
  z-index: 900;
  background-color: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s ease-in-out;

  .HotKeyMaskCenter {
    width: 881px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #1c1c1f;
    border-radius: 8px;
    padding-bottom: 40px;

    .HotKeyHeader {
      color: #e1e3eb;
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      padding-top: 17px;
      padding-bottom: 28px;
    }

    .HotKeyClose {
      font-size: 16px;
      color: rgba(99, 99, 112, 1);
      position: absolute;
      right: 20px;
      top: 18px;
      cursor: pointer;
      &:hover {
        color: rgba(165, 167, 184, 1);
      }
    }

    .HotKeyMaskBody {
      position: relative;

      .HotKeyMaskDivider {
        height: calc(~'100% - 14px');
        width: 0;
        border-right: 1px solid rgba(240, 242, 255, 0.08);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        margin-top: -14px;
      }

      .HotKeyMap {
        list-style: none;
        padding-left: 0;
        margin-bottom: 0;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .HotKeyMapItem {
          padding: 0 40px;
          margin-bottom: 24px;
          width: 50%;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .HotKeyMapItemName {
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            color: rgba(129, 131, 140, 1);
          }

          .HotKeyMapItemKey {
            span {
              color: #e1e3eb;
              border-radius: 4px;
              background: rgba(235, 238, 255, 0.08);
              line-height: 30px;
              height: 30px;
              min-width: 30px;
              text-align: center;
              padding: 0 8px;
              display: inline-block;
              font-size: 14px;
              font-weight: 500;
              margin-left: 6px;
            }

            em {
              color: rgba(129, 131, 140, 1);
              font-size: 14px;
              font-style: normal;
              // padding: 0 6px;
              line-height: 30px;
              margin-left: 6px;
            }
          }
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
