import { useEffect, useState } from 'react';
import { observer } from 'mobx-react';

import { CrossBlack } from '@meitu/candy-icons';
import styles from './index.module.less';
type configType = {
  name: string;
  hotKey: JSX.Element;
};

const HotKeyMask = observer(
  ({ close, show }: { close: () => void; show: boolean }) => {
    const [isMac, setIsMac] = useState<string>('');
    useEffect(() => {
      info();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const info = () => {
      const isInMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
      setIsMac(isInMac ? '⌘' : 'Ctrl');
    };

    const hotKeyConfig = [
      {
        name: '复制',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>{isMac}</span>
            <em>+</em>
            <span>C</span>
          </div>
        )
      },
      {
        name: '选择',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>V</span>
          </div>
        )
      },
      {
        name: '粘贴',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>{isMac}</span>
            <em>+</em>
            <span>V</span>
          </div>
        )
      },
      {
        name: '移动',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>按住空格Space并拖动</span>
          </div>
        )
      },
      {
        name: '剪切',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>{isMac}</span>
            <em>+</em>
            <span>X</span>
          </div>
        )
      },
      {
        name: '放大',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>{isMac}</span>
            <em>+</em>
            <span>+</span>
          </div>
        )
      },
      {
        name: '删除',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span style={{ transform: 'rotate(180deg)' }}>⌦</span>
            <em>或</em>
            <span>Delete</span>
          </div>
        )
      },
      {
        name: '缩小',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>{isMac}</span>
            <em>+</em>
            <span>-</span>
          </div>
        )
      },
      {
        name: '撤销',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>{isMac}</span>
            <em>+</em>
            <span>Z</span>
          </div>
        )
      },
      {
        name: '适应屏幕',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>{isMac}</span>
            <em>+</em>
            <span>0</span>
          </div>
        )
      },
      {
        name: '重做',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>{isMac}</span>
            <em>+</em>
            <span>Y</span>
          </div>
        )
      },
      {
        name: '缩放到100%',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>⇧ Shift</span>
            <em>+</em>
            <span>1</span>
          </div>
        )
      },
      {
        name: '移动+1',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>↑</span>
            <span>↓</span>
            <span>←</span>
            <span>→</span>
          </div>
        )
      },
      {
        name: '缩放',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>{isMac === 'Ctrl' ? 'Ctrl' : 'Control'}</span>
            <em>+</em>
            <span>滚轮上下</span>
          </div>
        )
      },
      {
        name: '移动+10',
        hotKey: (
          <div className={styles.HotKeyMapItemKey}>
            <span>⇧ Shift</span>
            <em>+</em>
            <span>↑</span>
            <span>↓</span>
            <span>←</span>
            <span>→</span>
          </div>
        )
      }
    ] as configType[];

    const closeHandle = () => {
      close();
    };

    if (!show) return null;
    return (
      <div className={styles.HotKeyMask} id="HotKeyMask">
        <div className={styles.HotKeyMaskCenter}>
          <div className={styles.HotKeyHeader}>快捷键</div>
          <div className={styles.HotKeyClose} onClick={closeHandle}>
            <CrossBlack />
          </div>
          <div className={styles.HotKeyMaskBody}>
            <div className={styles.HotKeyMaskDivider}></div>
            <ul className={styles.HotKeyMap}>
              {hotKeyConfig.map((config, key) => (
                <li key={key} className={styles.HotKeyMapItem}>
                  <span className={styles.HotKeyMapItemName}>
                    {config.name}
                  </span>
                  {config.hotKey}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    );
  }
);

export default HotKeyMask;
