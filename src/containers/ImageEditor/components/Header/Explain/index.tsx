import React, { useEffect, useState, useRef } from 'react';
import { observer } from 'mobx-react';
import classNames from 'classnames';
import {
  QuestionMarkCircleBold,
  KeyboardBoldOutlined
} from '@meitu/candy-icons';
import styles from './index.module.less';
import { Dropdown } from 'antd';
import HotKeyMask from '../HotKeyMask';
type configType = {
  icon: JSX.Element;
  title: string;
  key: string;
};
const dropdownListConfig = [
  {
    icon: <KeyboardBoldOutlined />,
    title: '快捷键',
    key: 'hotKeys'
  }
] as configType[];
const Explain = observer(() => {
  const [activeElement, setActiveElement] = useState<string | null>(null);
  const [openMenu, setOpenMenu] = useState<boolean>(false);
  const questionCircle = useRef<HTMLElement>(null);

  const contentStyle: React.CSSProperties = {
    backgroundColor: 'rgb(37,37,41)',
    borderRadius: '4px',
    boxShadow: '0px 12px 36px 0px rgba(0, 0, 0, 0.25)'
  };

  useEffect(() => {
    info();
    document.addEventListener('click', questionCircleListener);
    return () => {
      document.removeEventListener('click', questionCircleListener);
    };
  }, []);
  const info = () => {};
  const questionCircleListener = (event: MouseEvent) => {
    if (!questionCircle.current) return;
    // 检查鼠标是否在点到icon上
    if (
      event.target &&
      !questionCircle.current.contains(event.target as Node)
    ) {
      setOpenMenu(false);
      return;
    }
    setOpenMenu(true);
  };

  const dropdownClickHandel = (key: string | null) => {
    // let element = null
    // switch (key) {
    //   case 'hotKeys':
    //     element = <HotKeyMask />
    //     break;

    //   default:
    //     break;
    // }
    setActiveElement(key);
  };

  const dropdownList = () => {
    return (
      <ul className={'dropdown-list-box'}>
        {dropdownListConfig.map((config: configType) => {
          const { icon, title, key } = config;
          return (
            <li
              key={key}
              className={
                classNames()
                // isSelect ? 'active' : '',
              }
              onClick={() => {
                dropdownClickHandel(key);
              }}
            >
              <div className="icon">{icon}</div>
              <div className="check-center">{title}</div>
            </li>
          );
        })}
      </ul>
    );
  };

  return (
    <>
      <Dropdown
        placement="bottom"
        menu={{ items: [] }}
        overlayClassName={'explain-dropdown-base'}
        trigger={['click']}
        dropdownRender={(menu) => (
          <div style={contentStyle}>{dropdownList()}</div>
        )}
        getPopupContainer={(triggerNode) =>
          triggerNode.parentElement || document.body
        }
        open={openMenu}
      >
        <QuestionMarkCircleBold
          ref={questionCircle}
          className={styles.questionIcon}
        />
      </Dropdown>

      <HotKeyMask
        show={activeElement === 'hotKeys'}
        close={() => {
          setActiveElement(null);
        }}
      />
    </>
  );
});

export default Explain;
