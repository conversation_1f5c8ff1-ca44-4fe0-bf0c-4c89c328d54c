@bg-color: #252529;
@bg-color-hover: #ebeeff1a;
@content-color: #dcdde5;
@fs-color: #e1e3eb;
.questionIcon {
  font-size: 14px;
  color: rgba(99, 99, 112, 1);
  margin-left: 4px;
  &:hover {
    color: rgba(165, 167, 184, 1);
  }
}
.stage-scale-box {
  padding: 8px;
  border-radius: 4px;
  display: flex;
  min-width: 72px;
  height: 36px;
  white-space: nowrap;
  color: @fs-color;
  font-family: 'PingFang SC';
  // background-color: rgba(235, 238, 255, 0.1);

  &:hover {
    background-color: rgba(235, 238, 255, 0.1);
  }
}

:global {
  .explain-dropdown-base {
    // background:red;
    // transform: translateY(10px);
    padding-top: 12px !important;

    .dropdown-list-box {
      color: #e1e3eb;
      list-style: none;
      padding-left: 0;
      margin-bottom: 0;
      padding-top: 5px;
      padding-bottom: 6px;

      li {
        display: flex;
        padding-left: 16px;
        padding-right: 16px;
        height: 34px;
        line-height: 34px;
        cursor: pointer;
        &:hover {
          background: rgba(235, 238, 255, 0.1);
        }

        &.active {
          background: rgba(235, 238, 255, 0.1);
        }

        .check-icon {
          min-width: 14px;
        }

        .check-center {
          min-width: 66px;
          margin-left: 8px;
        }
      }
    }
  }

  .stage-scale-box-dropdown-content {
    background: rgb(37, 37, 41);
    border-radius: 4px;
    box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.25);
  }
}
