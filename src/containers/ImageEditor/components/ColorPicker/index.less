@import '~@/styles/variables.less';

.color-picker-container {
  display: flex;
  flex-wrap: wrap;

  .color-item {
    width: 26px;
    height: 26px;
    border-radius: 2px;
    margin: 2px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;

    &:first-child {
      border: 1px solid #323232;
    }

    &.active {
      box-shadow: 0 0 0 2px #3549ff, 0 0 0 2px #0d0e0f inset;
    }

    .selected-icon {
      width: 14px;
      height: 14px;
      color: #fff;
    }
  }
}
