import { useEffect, useState } from 'react';
import './index.less';
import { CheckBlack } from '@/icons';
import classNames from 'classnames';

export interface ColorItem {
  rgb: string;
}

interface ColorPaletteProps {
  colorList?: ColorItem[];
  onChange?: (item: ColorItem) => void;
  defaultValue?: string;
}

const defaultColorList = [
  { rgb: 'rgba(0, 0, 0, 1)' },
  { rgb: 'rgba(255, 255, 255, 1)' },
  { rgb: 'rgba(166, 166, 166, 1)' },
  { rgb: 'rgba(89, 89, 89, 1)' },
  { rgb: 'rgba(156, 105, 83, 1)' },
  { rgb: 'rgba(250, 74, 74, 1)' },
  { rgb: 'rgba(249, 142, 64, 1)' },
  { rgb: 'rgba(255, 237, 76, 1)' },
  { rgb: 'rgba(157, 213, 60, 1)' },
  { rgb: 'rgba(41, 207, 117, 1)' },
  { rgb: 'rgba(50, 203, 252, 1)' },
  { rgb: 'rgba(60, 123, 245, 1)' },
  { rgb: 'rgba(137, 67, 252, 1)' },
  { rgb: 'rgba(238, 96, 232, 1)' },
  { rgb: 'rgba(251, 218, 194, 1)' },
  { rgb: 'rgba(248, 159, 181, 1)' }
];

const ColorPicker = (props: ColorPaletteProps) => {
  const { colorList = defaultColorList, onChange, defaultValue } = props;
  const [selected, setSelected] = useState<ColorItem | null>(null);

  useEffect(() => {
    defaultValue && setSelected({ rgb: defaultValue });
  }, [defaultValue]);
  const handleClick = (item: ColorItem) => {
    onChange?.(item);
    setSelected(item);
  };

  return (
    <div className="color-picker-container">
      {colorList.map((v) => (
        <div
          key={v.rgb}
          className={classNames(
            'color-item',
            selected?.rgb === v.rgb && 'active'
          )}
          onClick={() => handleClick(v)}
          style={{ backgroundColor: v.rgb }}
        >
          {selected?.rgb === v.rgb && <CheckBlack className="selected-icon" />}
        </div>
      ))}
    </div>
  );
};

export default ColorPicker;
