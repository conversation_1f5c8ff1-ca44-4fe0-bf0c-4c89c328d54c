import { Form } from 'antd';
import { FormInstance } from 'antd/lib';

import './index.less';
import GenerateRatio from '../../GenerateRatio';
import Size from '../size';
import { ratioOptions } from '../config';
import { useCallback, useEffect } from 'react';

export default function DrawAreaForm({
  form,
  defaultWidth,
  defaultHeight
}: {
  form: FormInstance<any>;
  defaultWidth: number;
  defaultHeight: number;
}) {
  const ratio = Form.useWatch('imageRatio', form);
  const maxDefaultEdge = Math.max(defaultWidth, defaultHeight);
  const sizeHandler = useCallback(() => {
    let canvasSize = {
      width: maxDefaultEdge,
      height: maxDefaultEdge
    };
    if (!ratio) return;
    if (ratio === 'free') {
      return;
    }
    const [width, height] = ratio.split(':').map(Number);
    if (width > height) {
      canvasSize = {
        width: maxDefaultEdge,
        height: maxDefaultEdge / (width / height)
      };
    } else {
      canvasSize = {
        width: maxDefaultEdge * (width / height),
        height: maxDefaultEdge
      };
    }
    // console.log(canvasSize);
    form.setFieldsValue({
      canvasSize: {
        width: Math.ceil(canvasSize.width),
        height: Math.ceil(canvasSize.height)
      }
    });
  }, [form, maxDefaultEdge, ratio]);

  useEffect(() => {
    sizeHandler();
  }, [ratio, sizeHandler]);
  return (
    <>
      <div className="ratio-title">画布比例</div>
      <Form
        initialValues={{
          imageRatio: 'free',
          canvasSize: {
            width: defaultWidth,
            height: defaultHeight
          }
        }}
        form={form}
      >
        <Form.Item name="imageRatio">
          <GenerateRatio options={ratioOptions}></GenerateRatio>
        </Form.Item>
        <Form.Item name="canvasSize">
          <Size ratio={ratio} form={form}></Size>
        </Form.Item>
      </Form>
    </>
  );
}
