.modifyDrawAreaModal {
  .ant-modal-close {
    right: 20px !important;
    top: 20px !important;
    color: #636370 !important;
  }
  .ant-modal-content {
    padding: 0 !important;
    border-radius: 8px !important;
    overflow: hidden;
  }
}
.container {
  width: 100%;
  height: 100%;
  display: flex;
  background: var(--background-editor<PERSON>ainPanel, #1c1c1f);

  .options {
    width: 300px;
    height: 560px;
    padding: 16px;
    flex-shrink: 0;
    border-right: 1px solid var(--stroke-editorSeparatorPrimary, #2e2e33);
    background: var(--background-editorMainPanel, #1c1c1f);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .cancelButton:hover {
      background: var(
        --background-editorHover<PERSON><PERSON><PERSON>,
        rgba(235, 238, 255, 0.12)
      ) !important;
    }
    .cancelButton {
      color: var(--content-editorButtonSecondary, #e1e3eb) !important;
      background: var(
        --background-editorButtonSecondary,
        rgba(235, 238, 255, 0.1)
      ) !important;
    }
    .ant-btn {
      font-size: 14px !important;
      border: none !important;
      outline: none !important;
      box-shadow: none !important;
    }
  }
  .options--no-preview {
    border-right: none;
  }
  .preview {
    display: flex;
    flex: 1;
    height: 560px;
    padding: 48px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    background: #26262a;
    .drawArea {
      height: 100%;
      width: 100%;
      position: relative;
      .canvasContainer {
        position: absolute;
        background-repeat: repeat;
        background-size: 20px 20px;
        opacity: 0.3;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
      .cover {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
        z-index: 1;
        transform: translate(-50%, -50%);
        background-size: 100% 100%;
      }
    }
  }
}
:global {
  .modifyDrawAreaModal {
    .ant-modal-content {
      padding: 0;
    }
  }
}
