import useStore from '@/hooks/useStore';
import { Modal, ConfigProvider, Flex, Form, FormInstance } from 'antd';
import { Button } from '@/components/Button';
import { observer } from 'mobx-react';

import Title from './title';
import './index.less';
import DrawAreaForm from './form';
import { theme } from './config';
import { CrossBlack } from '@meitu/candy-icons';

import WheeEditorBackground from '@/containers/ImageEditor/assets/wheeEditor/bg.png';
import { useCallback, useEffect, useRef, useState } from 'react';
import classNames from 'classnames';

const ResizeDrawArea = observer(
  ({
    form,
    defaultWidth,
    defaultHeight
  }: {
    form: FormInstance<any>;
    defaultWidth: number;
    defaultHeight: number;
  }) => {
    const store = useStore('EditorStore');
    const canvasContainerRef = useRef<HTMLDivElement>(null);
    const [size, setSize] = useState({ width: 1080, height: 1080 });
    const [coverSize, setCoverSize] = useState({ width: 0, height: 0 });

    const canvasSize = Form.useWatch('canvasSize', form);
    const resizeHandler = useCallback(() => {
      if (!canvasContainerRef.current) return;
      if (!canvasSize) return;
      const { width: ContainerWidth, height: ContainerHeight } =
        canvasContainerRef.current.getBoundingClientRect();
      const { width: OriginWidth, height: OriginHeight } = canvasSize;
      const originRatio = OriginWidth / OriginHeight;
      const containerRatio = ContainerWidth / ContainerHeight;
      let width = 0;
      let height = 0;
      if (originRatio > containerRatio) {
        width = ContainerWidth;
        height = width / originRatio;
      } else {
        height = ContainerHeight;
        width = height * originRatio;
      }
      setSize({
        width: Math.ceil(width),
        height: Math.ceil(height)
      });
      if (store.projectInfo.picUrl) {
        const projectWidth = store.projectInfo.width;
        const projectHeight = store.projectInfo.height;
        const coverRatio = projectWidth / projectHeight;
        let coverWidth = 0;
        let coverHeight = 0;
        if (coverRatio > originRatio) {
          coverWidth = width;
          coverHeight = coverWidth / coverRatio;
        } else {
          coverHeight = height;
          coverWidth = coverHeight * coverRatio;
        }
        setCoverSize({
          width: coverWidth,
          height: coverHeight
        });
      }
    }, [
      canvasSize,
      store.projectInfo.height,
      store.projectInfo.picUrl,
      store.projectInfo.width
    ]);

    useEffect(() => {
      resizeHandler();
    }, [canvasSize, resizeHandler]);

    const onCancel = () => {
      form.resetFields();
      store.setShowModifyCanvasDialog(false);
    };

    const submitHandler = () => {
      store.setShowModifyCanvasDialog(false);
      const { canvasSize, imageRatio } = form.getFieldsValue([
        'canvasSize',
        'imageRatio'
      ]) as {
        canvasSize: { width: number; height: number };
        imageRatio: string;
      };
      // console.log(canvasSize.width, canvasSize.height, imageRatio);
      store.setCanvasSizeHandler(
        canvasSize.width,
        canvasSize.height,
        imageRatio
      );
      form.resetFields();
    };
    return (
      <ConfigProvider theme={theme}>
        <Modal
          open={store.showModifyCanvasDialog}
          width={!store.projectInfo.picUrl ? 880 : 300}
          centered
          footer={null}
          closeIcon={
            <CrossBlack width={16} height={16} color="#636370"></CrossBlack>
          }
          rootClassName="modifyDrawAreaModal"
          onCancel={onCancel}
        >
          <div className="container">
            <div
              className={classNames('options', {
                'options--no-preview': store.projectInfo.picUrl
              })}
            >
              <Flex vertical>
                <Title
                  title={!store.projectInfo.picUrl ? '新建画布' : '修改画布'}
                ></Title>
                <DrawAreaForm
                  form={form}
                  defaultWidth={defaultWidth}
                  defaultHeight={defaultHeight}
                ></DrawAreaForm>
              </Flex>
              <Flex justify="space-between" gap={16}>
                <Button
                  className="cancelButton"
                  type="default"
                  size="large"
                  block
                  onClick={onCancel}
                >
                  取消
                </Button>
                <Button size="large" block onClick={submitHandler}>
                  确认
                </Button>
              </Flex>
            </div>
            {!store.projectInfo.picUrl && (
              <div className="preview">
                <div className="drawArea" ref={canvasContainerRef}>
                  <div
                    className="canvasContainer"
                    style={{
                      backgroundImage: `url(${WheeEditorBackground})`,
                      width: `${size.width}px`,
                      height: `${size.height}px`
                    }}
                  ></div>
                  {store.projectInfo.picUrl && (
                    <div
                      className="cover"
                      style={{
                        backgroundImage: `url(${store.projectInfo.picUrl})`,
                        width: `${coverSize.width}px`,
                        height: `${coverSize.height}px`
                      }}
                    ></div>
                  )}
                </div>
              </div>
            )}
          </div>
        </Modal>
      </ConfigProvider>
    );
  }
);

export function useResizeDrawArea(params?: {
  modifyCanvasSizeHandler?: (
    width: number,
    height: number,
    ratio: string
  ) => void;
  canvasSize?: { width: number; height: number };
}) {
  const { modifyCanvasSizeHandler } = params || {};
  const store = useStore('EditorStore');
  const setShowModifyCanvasDialog = () => {
    store.setShowModifyCanvasDialog(true);
  };
  const setHideModifyCanvasDialog = () => {
    store.setShowModifyCanvasDialog(false);
  };
  if (modifyCanvasSizeHandler) {
    store.changeSetCanvasSizeHandler(modifyCanvasSizeHandler);
  }
  return {
    Component: ResizeDrawArea,
    setShowModifyCanvasDialog,
    setHideModifyCanvasDialog
  };
}
