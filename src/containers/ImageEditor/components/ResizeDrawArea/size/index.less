.sizeContainer {
  width: 100%;
  height: 36px;
  display: flex;
  gap: 8px;
  .ant-input-number-input {
    height: 36px !important;
    line-height: 36px !important;
  }
  .iconContainer {
    width: 36px;
    height: 36px;
    background: var(--background-editor<PERSON><PERSON><PERSON><PERSON><PERSON>, rgba(235, 238, 255, 0.1));
    border-radius: 4px;
    display: flex;
    height: 36px;
    padding: 0px 10px;
    justify-content: center;
    align-items: center;
    color: #e1e3eb;
  }
  .delink {
    background: none;
    pointer-events: none;
  }
  .link {
    background: var(--background-editorHoverOverlay, rgba(235, 238, 255, 0.1));
    pointer-events: all;
    cursor: pointer;
  }
  .unit {
    color: var(--content-editorSecondary, #81838c);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    // 禁止换行
    white-space: nowrap;
    line-height: 36px;
  }
}
