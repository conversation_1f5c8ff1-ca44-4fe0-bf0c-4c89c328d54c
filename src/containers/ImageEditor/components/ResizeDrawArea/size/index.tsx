import { InputNumber, FormInstance } from 'antd';
import './index.less';
import { DelinkBold, LinkBold } from '@meitu/candy-icons';
import classNames from 'classnames';
import { maxEdge, minEdge } from '../config';
interface Value {
  width: number;
  height: number;
}
export default function Size({
  value,
  onChange,
  ratio = 'free',
  form
}: {
  value?: Value;
  onChange?: (val: Value) => void;
  ratio?: string;
  form: FormInstance<any>;
}) {
  const linkHandler = () => {
    const { canvasSize } = form.getFieldsValue(['canvasSize']);
    form.setFieldsValue({
      imageRatio: 'free'
    });
    requestAnimationFrame(() => {
      form.setFieldsValue({
        canvasSize: {
          width: canvasSize.width,
          height: canvasSize.height
        }
      });
    });
  };

  const canvasSizeRatioHandler = (
    width: number,
    height: number,
    isChangeWidth: boolean
  ) => {
    const { imageRatio } = form.getFieldsValue(['imageRatio']);
    if (imageRatio === 'free') {
      return {
        width,
        height
      };
    }
    const [widthRatio, heightRatio] = imageRatio.split(':').map(Number);
    let maxWidth;
    let maxHeight;
    let minWidthEdge;
    let minHeightEdge;
    // 限制长边最大为2160
    if (widthRatio > heightRatio) {
      maxWidth = 2160;
      maxHeight = (maxWidth / widthRatio) * heightRatio;
      minHeightEdge = 50;
      minWidthEdge = 50 * (widthRatio / heightRatio);
    } else {
      maxHeight = 2160;
      maxWidth = (maxHeight / heightRatio) * widthRatio;
      minWidthEdge = 50;
      minHeightEdge = 50 * (heightRatio / widthRatio);
    }
    // 通过宽高比计算宽高 并且不能超过最大值
    if (isChangeWidth) {
      width = Math.max(Math.min(width, maxWidth), minWidthEdge);
      height = (width / widthRatio) * heightRatio;
    } else {
      height = Math.max(Math.min(height, maxHeight), minHeightEdge);
      width = (height / heightRatio) * widthRatio;
    }
    return {
      width: Math.ceil(width),
      height: Math.ceil(height)
    };
  };

  const changeWidthHandler = (val: number | null) => {
    const { width, height } = canvasSizeRatioHandler(
      val || 0,
      value?.height || 0,
      true
    );
    onChange?.({
      width,
      height
    });
  };
  const changeHeightHandler = (val: number | null) => {
    const { width, height } = canvasSizeRatioHandler(
      value?.width || 0,
      val || 0,
      false
    );
    onChange?.({
      width,
      height
    });
  };
  return (
    <div className="sizeContainer">
      <InputNumber
        value={value?.width}
        controls={false}
        max={maxEdge}
        min={minEdge}
        onChange={changeWidthHandler}
      ></InputNumber>
      <div
        className={classNames(
          'iconContainer',
          ratio !== 'free' ? 'link' : 'delink'
        )}
        onClick={linkHandler}
      >
        {ratio !== 'free' ? (
          <LinkBold color="#E1E3EB"></LinkBold>
        ) : (
          <DelinkBold color="#E1E3EB"></DelinkBold>
        )}
      </div>
      <InputNumber
        value={value?.height}
        controls={false}
        max={2160}
        min={50}
        onChange={changeHeightHandler}
      ></InputNumber>
      <span className="unit">像素</span>
    </div>
  );
}
