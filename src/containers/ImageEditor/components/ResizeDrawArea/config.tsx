import {
  <PERSON>io11<PERSON><PERSON>,
  <PERSON>io169B<PERSON>,
  <PERSON><PERSON>23<PERSON><PERSON>,
  Ratio32B<PERSON>,
  Ratio34<PERSON><PERSON>,
  Ratio43Bold,
  Ratio916Bold,
  RatioFreeBold
} from '@meitu/candy-icons';

export const theme = {
  components: {
    Modal: {
      contentBg: 'var(--background-editorMainPanel, #1C1C1F)'
    },
    InputNumber: {
      controlWidth: 90,
      paddingInline: 10,
      activeBg: 'var(--background-editorInput, #0D0E0F)',
      hoverBorderColor: 'var(--stroke-editorSeparatorPrimary, #2E2E33)',
      activeBorderColor: 'var(--stroke-editorSeparatorPrimary, #2E2E33)',
      colorText: 'var(--content-editorPrimary, #E1E3EB)',
      activeShadow: 'none'
    },
    Button: {
      defaultBg:
        'var(--background-editorButtonSecondary, rgba(235, 238, 255, 0.12))',
      defaultColor: 'var(--content-editorButtonSecondary, #E1E3EB)',
      primaryShadow: 'none',
      defaultHoverBorderColor: 'none',
      defaultHoverColor: 'var(--content-editorButtonSecondary, #E1E3EB)'
    }
  },
  token: {
    padding: 0,
    colorBgContainer: 'var(--background-editorInput, #0D0E0F)',
    colorBorder: 'var(--stroke-editorSeparatorPrimary, #2E2E33)',
    borderRadius: 4
  }
};

export const ratioOptions = [
  {
    label: '自定义',
    image_ratio: 'free',
    icon: <RatioFreeBold />
  },
  {
    label: '1:1',
    image_ratio: '1:1',
    icon: <Ratio11Bold />
  },
  {
    label: '2:3',
    image_ratio: '2:3',
    icon: <Ratio23Bold />
  },
  {
    label: '3:4',
    image_ratio: '3:4',
    icon: <Ratio34Bold />
  },
  {
    label: '9:16',
    image_ratio: '9:16',
    icon: <Ratio916Bold />
  },
  {
    label: '3:2',
    image_ratio: '3:2',
    icon: <Ratio32Bold />
  },
  {
    label: '4:3',
    image_ratio: '4:3',
    icon: <Ratio43Bold />
  },
  {
    label: '16:9',
    image_ratio: '16:9',
    icon: <Ratio169Bold />
  }
];

// 通过弹框设置的画布尺寸默认长边值
export const maxDefaultEdge = 1080;
// 通过弹框设置的画布尺寸长边的最大值
export const maxEdge = 2160;
// 通过弹框设置的画布尺寸最小值
export const minEdge = 50;
