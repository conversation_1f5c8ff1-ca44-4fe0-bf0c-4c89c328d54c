import { CrossCircleBold } from '@meitu/candy-icons';

import styles from './index.module.less';
import classNames from 'classnames';

export type SelectionControlsProps = {
  onInvert?: () => void;
  onClearSelection?: () => void;
  className?: string;
};

export default function SelectionControls({
  // onInvert,
  onClearSelection,
  className
}: SelectionControlsProps) {
  return (
    <div className={classNames(styles.selectionControls, className)}>
      {/* <div className="selection-controls-btn" onClick={onInvert}>
        <span className="selection-controls-btn-icon">
          <InverseBold />
        </span>
        <span className="selection-controls-btn-label">反选</span>
      </div> */}

      <div className="selection-controls-btn" onClick={onClearSelection}>
        <span className="selection-controls-btn-icon">
          <CrossCircleBold />
        </span>
        <span className="selection-controls-btn-label">取消选择</span>
      </div>
    </div>
  );
}
