@bg-color: #252529;
@bg-color-hover: #ebeeff1a;
@content-color: #dcdde5;

.selection-controls {
  background-color: @bg-color;
  color: @content-color;
  display: flex;
  border-radius: 6px;
  padding: 6px;

  position: fixed;
  left: 50%;
  transform: translate(-50%);
  bottom: 16px;
  z-index: 999;
  box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.25);
  :global {
    .selection-controls-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 10px;
      height: 36px;
      cursor: pointer;
      &:hover {
        background-color: @bg-color-hover;
      }

      &:not(:first-child) {
        margin-left: 6px;
      }
      &-icon {
        margin-right: 4px;
        font-size: 16px;
      }

      &-label {
        font-size: 14px;
      }
    }
  }
}
