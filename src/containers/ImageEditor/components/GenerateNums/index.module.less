@import '~@/styles/variables.less';

@border-color-selected: #4053ff;
@bg-color: rgba(235, 238, 255, 0.08);
@content-color: #dcdde5;

.generate-nums {
  &:global(.@{ant-prefix}-radio-group) {
    display: flex;
    width: calc(100% - 4px);
    justify-content: space-between;
    margin: 0 2px;
    :global {
      .generate-nums-item {
        flex: 0 0 auto;
        box-sizing: border-box;
        width: 61px;
        padding: 2px;
        height: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background: @bg-color;
        color: @content-color;
        border: none;
        &::before {
          display: none;
        }

        &.generate-nums-item-selected {
          box-shadow: 0 0 0 2px @border-color-selected;
          color: @content-color;
        }
      }
    }
  }
}
