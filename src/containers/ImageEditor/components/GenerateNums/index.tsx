import classNames from 'classnames';
import { Radio } from 'antd';

import styles from './index.module.less';

type GenerateNumsProps = {
  value?: number;
  onChange?(newValue: number): void;

  options?: number[];
};

export default function GenerateNums({
  value,
  onChange,
  options = [1, 2, 3, 4]
}: GenerateNumsProps) {
  return (
    <Radio.Group
      value={value}
      onChange={(e) => onChange?.(Number(e.target.value))}
      className={styles.generateNums}
    >
      {options.map((n) => {
        return (
          <Radio.Button
            className={classNames(
              'generate-nums-item',
              value === Number(n) && 'generate-nums-item-selected'
            )}
            key={n}
            value={n}
          >
            {n}
          </Radio.Button>
        );
      })}
    </Radio.Group>
  );
}
