import {
  MinusCircleBold,
  MinusCircleBoldFill,
  PlusCircleBold,
  PlusCircleBoldFill
} from '@meitu/candy-icons';
import styles from './index.module.less';
import classNames from 'classnames';

export enum AreaSelectionMode {
  // 增加选区
  Add = 'add',
  // 减少选区
  Remove = 'remove',
  //自动识别
  Auto = 'auto'
}

type ModeSelectionProps = {
  currentMode?: AreaSelectionMode | null;
  onModeChange?(mode: AreaSelectionMode): void;
  addText?: string;
  removeText?: string;
  options?: any;
  items?: AreaSelectionMode[];
};
type ModeOption = {
  label: string;
  icon: React.ReactNode;
  activeIcon: React.ReactNode;
};
export type OptionsType = Record<AreaSelectionMode, ModeOption>;
const defaultOptions = {
  [AreaSelectionMode.Add]: {
    label: '增加选区',
    icon: <PlusCircleBold />,
    activeIcon: <PlusCircleBoldFill />
  },
  [AreaSelectionMode.Remove]: {
    label: '减少选区',
    icon: <MinusCircleBold />,
    activeIcon: <MinusCircleBoldFill />
  }
} as OptionsType;

const defaultItems = [AreaSelectionMode.Add, AreaSelectionMode.Remove];

export function ModeSelection({
  currentMode,
  onModeChange,
  addText = '',
  removeText = '',
  options = defaultOptions,
  items = defaultItems
}: ModeSelectionProps) {
  const renderLabel = (key: AreaSelectionMode) => {
    // 如果有传入文字，则直接返回
    if (key === AreaSelectionMode.Add) {
      return addText ? addText : options[key].label;
    } else {
      return removeText ? removeText : options[key].label;
    }
  };
  return (
    <div className={styles.modeSelection}>
      {items.map((key) => {
        return (
          <div
            key={key}
            className={classNames(
              'mode-selection-item',
              `mode-selection-item-${key}`,
              key === currentMode && 'active'
            )}
            onClick={() => onModeChange?.(key)}
          >
            <div className="mode-selection-item-icon">
              {options[key][key === currentMode ? 'activeIcon' : 'icon']}
            </div>
            <div className="mode-selection-item-label">
              {/* {options[key].label} */}
              {renderLabel(key)}
            </div>
          </div>
        );
      })}
    </div>
  );
}
