@accent-primary: #4053ff;
@warning-primary: #fa395a;
@content-secondary: #7f818a;
@content-primary: #dcdde5;

@content-hover: rgba(139, 139, 158, 1);
.mode-selection {
  display: flex;
  width: 100%;
  :global {
    .mode-selection-item {
      display: flex;
      flex: 1 1 0;
      height: 64px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding-top: 4px;
      padding-bottom: 4px;

      color: @content-secondary;

      &-icon {
        font-size: 18px;
        line-height: 1em;
      }

      &-label {
        font-size: 12px;
        margin-top: 4px;
      }

      &:hover {
        cursor: pointer;
        color: @content-hover;
      }

      &.active {
        .mode-selection-item-label {
          color: @content-primary;
        }
      }

      &.mode-selection-item-add {
        &.active {
          .mode-selection-item-icon {
            color: @accent-primary;
          }
        }
      }

      &.mode-selection-item-remove {
        &.active {
          .mode-selection-item-icon {
            color: @warning-primary;
          }
        }
      }
      &.mode-selection-item-auto {
        &.active {
          .mode-selection-item-icon {
            color: @accent-primary;
          }
        }
      }
    }
  }
}
