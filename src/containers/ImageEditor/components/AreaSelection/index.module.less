@content-primary: #dcdde5;
@content-secondary: #7f818a;
@accent-primary: #4053ff;

@params-bg-color: #000;
@content-hover: rgba(139, 139, 158, 1);

.area-selection {
  --item-nums: 1;
  --current-item: 1;

  margin-top: 12px;

  :global {
    .area-selection-way {
      display: flex;
      justify-content: space-between;
      &-item {
        color: @content-secondary;
        display: flex;
        flex: 1 1 0px;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        &:hover {
          cursor: pointer;
          color: @content-hover;
        }
        &-icon {
          line-height: 1em;
          font-size: 24px;
        }
        &-label {
          margin-top: 8px;
          font-size: 12px;
        }

        &.active {
          .area-selection-way-item-icon {
            color: @accent-primary;
          }
          .area-selection-way-item-label {
            color: @content-primary;
          }
        }
      }
    }

    .area-selection-params {
      margin-top: 8px;
      padding-top: 7px;
      position: relative;
      &-cursor {
        position: absolute;
        top: 0;
        // left: calc(
        //   100% / calc(var(--item-nums) * 2) * (2 * var(--current-item) - 1)
        // );
        transform: translate(-50%);
        transition: left 0.2s ease-in-out;
      }

      &-content {
        background-color: @params-bg-color;
        overflow: hidden;
        border-radius: 2px;
      }
    }
  }
}
