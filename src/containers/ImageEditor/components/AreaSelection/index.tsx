import classNames from 'classnames';
import styles from './index.module.less';
import { useRef, useEffect } from 'react';

// 区域选择的方式
export enum SelectionWay {
  // 智能选择
  Smart = 'smart',
  // 框选
  Box = 'box',
  // 圈选
  Lasso = 'lasso',
  // 涂抹
  Paint = 'paint',
  //圆形
  Ellipse = 'ellipse',
  //橡皮擦
  Eraser = 'eraser',
  //手动修补
  Repair = 'repair'
}

type AreaSelectionType<T extends SelectionWay> = {
  key: T;
  icon: React.ReactNode;
  label: React.ReactNode;
  params: React.ReactNode;
};

type AreaSelectionProps = {
  items: Array<AreaSelectionType<SelectionWay>>;
  currentSelectionWay: SelectionWay | null;
  onSelectionWayChange(way: SelectionWay): void;
};

const selectionWidth = 267;
export default function AreaSelection({
  items,
  currentSelectionWay,
  onSelectionWayChange
}: AreaSelectionProps) {
  const currentIndex = items.findIndex(
    (item) => item.key === currentSelectionWay
  );
  const selectionContent = useRef<HTMLDivElement>(null);
  // const [selectionWidth, setSelectionWidth] = useState(267);
  const current = currentIndex >= 0 ? items[currentIndex] : null;
  useEffect(() => {
    // const selectionDom = selectionContent.current;
    // if (!selectionDom) return;
    // const style = window.getComputedStyle(selectionDom);
    // console.log(style.width, 'selectionWidth----')
    // const elementWidth = parseFloat(style.width);
    // setSelectionWidth(selectionDom.offsetWidth);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.areaSelection}>
      <div className="area-selection-way">
        {items.map((item) => {
          return (
            <div
              key={item.key}
              className={classNames(
                'area-selection-way-item',
                currentSelectionWay === item.key && 'active'
              )}
              onClick={() => onSelectionWayChange(item.key)}
            >
              <div className="area-selection-way-item-icon">{item.icon}</div>
              <div className="area-selection-way-item-label">{item.label}</div>
            </div>
          );
        })}
      </div>

      <div
        className="area-selection-params"
        style={
          {
            // '--item-nums': items.length,
            // '--current-item': currentIndex + 1
          } as any
        }
      >
        {current !== null && (
          <svg
            className="area-selection-params-cursor"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              left:
                (selectionWidth / items.length) * currentIndex +
                selectionWidth / items.length / 2
            }}
            width="14"
            height="7"
            viewBox="0 0 14 7"
            fill="none"
          >
            <path
              d="M5.49485 0.720173C6.29167 -0.190481 7.70833 -0.190479 8.50515 0.720175L14 7H0L5.49485 0.720173Z"
              fill="black"
            />
          </svg>
        )}
        <div className="area-selection-params-content" ref={selectionContent}>
          {current && current.params}
        </div>
      </div>
    </div>
  );
}
