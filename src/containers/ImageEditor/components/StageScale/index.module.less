@bg-color: #252529;
@bg-color-hover: #ebeeff1a;
@content-color: #dcdde5;
@fs-color: #e1e3eb;

.stage-scale-box {
  padding: 8px 10px;
  border-radius: 4px;
  display: flex;
  min-width: 72px;
  height: 36px;
  white-space: nowrap;
  color: @fs-color;
  font-family: 'PingFang SC';
  // background-color: rgba(235, 238, 255, 0.1);

  &:hover {
    background-color: rgba(235, 238, 255, 0.1);
  }

  &-left {
    white-space: nowrap;
    font-size: 14px;
    text-align: right;
    // min-width: 39px;
    min-width: 37px;
    font-weight: 400;
    margin-right: 4px;
  }

  &-right {
    font-size: 12px;

    transition: all 0.3s ease;
    transform: rotate(0deg);
    transform-origin: 50% 50%;
    span {
      vertical-align: middle;
      // top: -2px;
      // position: relative;
    }
  }
  .stage-scale-box-right-open {
    transform: rotate(180deg);
    span {
      top: -2px;
      position: relative;
    }
  }
  &.stage-scale-box-disable {
    cursor: not-allowed;
    color: #48484d;
  }
}

:global {
  /* 覆盖 Dropdown 动画 */
  @keyframes antSlideUpIn {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }

    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes antSlideUpOut {
    0% {
      opacity: 1;
      transform: translateY(0);
    }

    100% {
      opacity: 0;
      transform: translateY(10px);
    }
  }

  .unselectable-text {
    -webkit-user-select: none;
    /* Safari */
    -moz-user-select: none;
    /* Firefox */
    -ms-user-select: none;
    /* IE10+/Edge */
    user-select: none;
    /* Standard syntax */
  }

  .stage-scale-dropdown-base {
    // background:red;
    // transform: translateY(10px);
    padding-top: 3px !important;

    &.ant-dropdown {
      animation-duration: 0.2s !important;
      /* 调整此处以控制动画速度 */
    }

    &.ant-slide-up-enter.ant-slide-up-enter-active.ant-dropdown-placement-bottomLeft,
    &.ant-slide-up-enter.ant-slide-up-enter-active.ant-dropdown-placement-bottomCenter,
    &.ant-slide-up-enter.ant-slide-up-enter-active.ant-dropdown-placement-bottomRight,
    &.ant-slide-up-appear.ant-slide-up-appear-active.ant-dropdown-placement-bottomLeft,
    &.ant-slide-up-appear.ant-slide-up-appear-active.ant-dropdown-placement-bottomCenter,
    &.ant-slide-up-appear.ant-slide-up-appear-active.ant-dropdown-placement-bottomRight {
      animation: antSlideUpIn 0.2s ease-out !important;
    }

    &.ant-slide-up-leave.ant-slide-up-leave-active.ant-dropdown-placement-bottomLeft,
    &.ant-slide-up-leave.ant-slide-up-leave-active.ant-dropdown-placement-bottomCenter,
    &.ant-slide-up-leave.ant-slide-up-leave-active.ant-dropdown-placement-bottomRight {
      animation: antSlideUpOut 0.2s ease-in !important;
    }

    .dropdown-list-box {
      color: #e1e3eb;
      list-style: none;
      padding-left: 0;
      margin-bottom: 0;
      padding-top: 5px;
      padding-bottom: 6px;

      .check-divider {
        width: 100%;
        height: 0;
        border-bottom: 1px solid rgba(240, 242, 255, 0.08);
      }

      li {
        display: flex;
        padding-left: 16px;
        padding-right: 16px;
        height: 34px;
        line-height: 34px;
        cursor: pointer;

        &:hover {
          background: rgba(235, 238, 255, 0.1);
        }

        &.no-hover {
          cursor: auto;

          &:hover {
            background: none;
          }
        }

        &.active {
          background: rgba(235, 238, 255, 0.1);
        }

        .check-icon {
          color: rgba(64, 83, 255, 1);
          min-width: 14px;
        }

        .check-center {
          min-width: 58px;
          margin-left: 8px;
          margin-right: 8px;
        }

        .check-hot-key {
          color: #636370;
          min-width: 39px;
          text-align: right;
        }
      }
    }
  }

  .stage-scale-box-dropdown-content {
    background: rgb(37, 37, 41);
    border-radius: 4px;
    box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.25);
  }
}
