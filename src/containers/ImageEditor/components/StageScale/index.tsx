import React, { useEffect, useState, useRef } from 'react';
import { observer } from 'mobx-react';
import classNames from 'classnames';
import { CanvasZoomEventName } from '@/editor/core/src';
import { ChevronDownBlack, CheckBlack } from '@meitu/candy-icons';
import styles from './index.module.less';
import { Dropdown } from 'antd';
import useStore from '@/hooks/useStore';
import Konva from 'konva';
const dropdownListConfig = [
  {
    key: 'auto',
    hotKey: '0',
    value: 'auto',
    title: '适应屏幕',
    isSelect: true,
    activeKey: 'Ctrl'
  },
  {
    key: '50',
    hotKey: '',
    value: '0.5',
    title: '50%',
    isSelect: true,
    activeKey: 'Ctrl'
  },
  {
    key: '100',
    hotKey: '1',
    value: '1',
    title: '100%',
    isSelect: true,
    activeKey: '⇧'
  },
  {
    key: '200',
    hotKey: '',
    value: '2',
    title: '200%',
    isSelect: true,
    activeKey: 'Ctrl'
  },
  {
    key: 'divider'
  },
  {
    key: 'max',
    hotKey: '+',
    value: 'add',
    title: '放大',
    activeKey: 'Ctrl'
  },
  {
    key: 'min',
    hotKey: '-',
    value: 'sub',
    title: '缩小',
    activeKey: 'Ctrl'
  }
];
type StageScaleType = {
  disabled: boolean;
};
const StageScale = observer(({ disabled }: StageScaleType) => {
  const [isMac, setIsMac] = useState<boolean>(false);
  const [isOpenMenu, setIsOpenMenu] = useState<boolean>(false);
  const [syncScale, setSyncScale] = useState<number>(0);
  const disabledRef = useRef(disabled);
  let { headerMainConfig } = useStore('EditorStore');
  const { app, editor } = headerMainConfig || {};
  const contentStyle: React.CSSProperties = {
    backgroundColor: 'rgb(37,37,41)',
    borderRadius: '4px',
    boxShadow: '0px 12px 36px 0px rgba(0, 0, 0, 0.25)'
  };
  useEffect(() => {
    disabledRef.current = disabled;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [disabled]);

  useEffect(() => {
    if (!app) return;
    app.on(CanvasZoomEventName.end, zoomHandle);
    setSyncScale(
      parseInt((parseFloat(app.scale().toFixed(2)) * 100).toString())
    );
    app.keyCommand.register('ctrl+0,command+0', (event: Event) => {
      event.preventDefault();
      dropdownClickHandel('auto');
    });
    app.keyCommand.register('shift+1', (event: Event) => {
      event.preventDefault();
      dropdownClickHandel('1');
    });
    // app.keyCommand.register('ctrl+2,command+2', (event: Event) => {
    //   event.preventDefault();
    //   dropdownClickHandel('2');
    // });

    app.keyCommand.register(
      'command+=,ctrl+=,ctrl+num_add,command+num_add',
      (event) => {
        event.preventDefault();
        setScaleTo(parseFloat(app.scale().toFixed(2)) + 0.1);
      }
    );
    app.keyCommand.register(
      'command+-,ctrl+-,ctrl+num_subtract,command+num_subtract',
      (event) => {
        event.preventDefault();
        setScaleTo(parseFloat(app.scale().toFixed(2)) - 0.1);
      }
    );
    return () => {
      app.off(CanvasZoomEventName.end, zoomHandle);
      app.keyCommand.unbind('ctrl+0,command+0');
      app.keyCommand.unbind('shift+1');
      // app.keyCommand.unbind('ctrl+2,command+2');
      app.keyCommand.unbind('command+=,ctrl+=,ctrl+num_add,command+num_add');
      app.keyCommand.unbind(
        'command+-,ctrl+-,ctrl+num_subtract,command+num_subtract'
      );
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [app, editor]);

  useEffect(() => {
    info();
  }, []);

  const info = () => {
    const isInMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    setIsMac(isInMac);
  };

  const zoomHandle = ({ scale }: { scale: number }) => {
    if (disabledRef.current) return;
    setSyncScale(parseInt((parseFloat(scale.toFixed(2)) * 100).toString()));
  };

  const dropdownClickHandel = (key: string | null) => {
    if (!app) return;
    const config = checkTitleFun(key);
    if (!config) return;
    const { value } = config;
    const oldScale = parseFloat(app.scale().toFixed(2));
    switch (value) {
      case 'auto':
        editor?.plugin?.initAllPosition();
        break;
      case 'add':
        setScaleTo(oldScale + 0.1);
        break;
      case 'sub':
        setScaleTo(oldScale - 0.1);
        break;
      default:
        value && setScaleTo(value);
        break;
    }
  };

  const setScaleTo = (value: string | number) => {
    if (disabledRef.current) return;
    if (!(app && value)) return;
    value = value.toString();
    const layer = app.getLayerByName('meitu:main:layer') as Konva.Layer;
    if (!layer) return;
    const group = layer?.children?.find(
      (children) => children.getAttr('name') === 'meitu:editor:group'
    ) as Konva.Group | null;
    if (!group) return;
    const scale = parseFloat(value);
    // 缩放前 Group 中心点到屏幕左上角的距离
    const beforeZoom = getGroupCenterScreenCoords(group);
    // 缩放
    app?.scaleTo(scale, {
      x: group.x() + group.width() / 2,
      y: group.y() + group.height() / 2
    });
    // 缩放后 Group 中心点到屏幕左上角的距离
    const afterZoom = getGroupCenterScreenCoords(group);
    if (!afterZoom) return;
    if (!beforeZoom) return;
    // 计算位置差
    const dx = beforeZoom.x - afterZoom.x;
    const dy = beforeZoom.y - afterZoom.y;
    // // 调整舞台位置
    app?.stage.position({
      x: app?.stage.x() + dx,
      y: app?.stage.y() + dy
    });
    const newScale = app?.scale();
    newScale && editor?.plugin?.zoomEffect({ scale: newScale });
    app?.stage.batchDraw();
  };

  // 获取 Group 中心点在舞台上的坐标
  const getGroupCenter = (group: Konva.Group) => {
    const groupRect = group.getClientRect();
    const centerX = groupRect.x + groupRect.width / 2;
    const centerY = groupRect.y + groupRect.height / 2;
    return { x: centerX, y: centerY };
  };

  // 获取 Group 中心点的屏幕坐标
  const getGroupCenterScreenCoords = (group: Konva.Group) => {
    if (!app) return;
    const center = getGroupCenter(group);
    const stageContainer = app.stage.container();
    const boundingRect = stageContainer.getBoundingClientRect();

    // 将舞台坐标转换为屏幕坐标
    const screenX = center.x + boundingRect.left;
    const screenY = center.y + boundingRect.top;
    return { x: screenX, y: screenY };
  };

  const dropdownList = () => {
    return (
      <ul className={'dropdown-list-box'}>
        {dropdownListConfig.map((config) => {
          const { key, hotKey, value, title, activeKey } = config;
          const isSelect = (syncScale / 100).toString() === value;
          if (key === 'divider')
            return <li key={config.key} className="check-divider"></li>;
          const activeKeyShow =
            activeKey === 'Ctrl' ? (isMac ? '⌘' : 'Ctrl') : activeKey;
          return (
            <li
              key={config.key}
              className={classNames(
                'unselectable-text',
                isSelect ? 'active' : ''
                // !configSelect ? 'no-hover' : ''
              )}
              onClick={() => {
                dropdownClickHandel(value || null);
              }}
            >
              <div className="check-icon">{isSelect && <CheckBlack />}</div>
              <div className="check-center unselectable-text">{title}</div>
              {hotKey && (
                <div className="check-hot-key">
                  {activeKeyShow} {hotKey}
                </div>
              )}
            </li>
          );
        })}
      </ul>
    );
  };

  const checkTitleFun = (key: string | null) => {
    return dropdownListConfig.find((item) => item.value === key);
  };
  const checkTitle = syncScale && syncScale + '%';
  return (
    <Dropdown
      placement="bottom"
      disabled={disabled}
      menu={{ items: [] }}
      overlayClassName={'stage-scale-dropdown-base'}
      trigger={['click']}
      dropdownRender={(menu) => (
        <div style={contentStyle}>{dropdownList()}</div>
      )}
      getPopupContainer={(triggerNode) =>
        triggerNode.parentElement || document.body
      }
      onOpenChange={(open) => {
        setIsOpenMenu(open);
      }}
    >
      <div
        className={`${styles['stage-scale-box']} ${
          disabled ? styles['stage-scale-box-disable'] : null
        }`}
      >
        <div className={styles['stage-scale-box-left']}>{checkTitle}</div>
        <div
          className={
            !isOpenMenu
              ? styles['stage-scale-box-right']
              : `${styles['stage-scale-box-right']} ${styles['stage-scale-box-right-open']}`
          }
        >
          <ChevronDownBlack />
        </div>
      </div>
    </Dropdown>
  );
});

export default StageScale;
