import { CSSProperties, useEffect, useRef, useState } from 'react';
import { CanvasMouseEventName, CanvasNodeEventName } from '@/editor/core/src';
import Konva from 'konva';
import useStore from '@/hooks/useStore';
import { observer } from 'mobx-react';
import { TaskCategory } from '@/api/types/imageEditor';
import { useLocation } from 'react-router-dom';
import { AppModule, getAppModulePath } from '@/services';
import { layerEditorType } from '../LayerEditLayout/type';
import './index.less';
const mouseStyleList = [
  'cursorStyle_smart_add',
  'cursorStyle_smart_remove',
  'cursorStyle_box_add',
  'cursorStyle_box_remove',
  'cursorStyle_lasso',
  'cursorStyle_none',
  'cursorStyle_selector',
  'cursorStyle_selector_move',
  'cursorStyle_rotates',
  'cursorStyle_default',
  'cursorStyle_grab',
  'cursorStyle_grabbing',
  'cursorStyle_lasso_add',
  'cursorStyle_lasso_remove'
];

const cursorCircularType = ['paint', 'smart', 'eraser', 'repair'];
const CustomCursor = () => {
  const editorStore = useStore('EditorStore');
  const { editor, mouseStyleType, mainEditorKey, layerEditorKey } = editorStore;
  const editorApp = useRef(editor.app);
  const [style, setStyle] = useState<CSSProperties>({
    top: 0,
    left: 0,
    opacity: 0,
    width: 24,
    height: 24,
    backgroundColor: 'rgba(64, 83, 255, 0.5)'
  });
  const mouseConfig = useRef<{
    selectionParams: string | null;
    selectionWay: string | null;
    paintWidth?: number | null | undefined;
    content: HTMLElement;
    scale?: number | null;
    fillColor?: string;
  }>({
    selectionParams: 'selector',
    selectionWay: null,
    paintWidth: undefined,
    content: editor.app.stage?.content
  });

  const historyConfig = useRef<{
    selectionParams: string | null;
    selectionWay: string | null;
    paintWidth?: number | null | undefined;
    scale?: number | null;
  }>({
    selectionParams: 'selector',
    selectionWay: null,
    paintWidth: undefined
  });

  const mouseStateInOut = useRef({ state: 'in' });
  const mouseUpOutStageTimer = useRef<any | null>(null);
  const stopMouseState = useRef<any | null>(false);
  const keyCommandState = useRef<any | null>({
    isActive: true,
    layoutNode: null
  });

  const [isActiveHotKey, setIsActiveHotKey] = useState(true);

  const location = useLocation();
  useEffect(() => {
    return () => {
      clearMouseStyle();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  //判断当前路由是否为 编辑器路由
  useEffect(() => {
    if (location.pathname !== getAppModulePath(AppModule.ImageEditor)) {
      clearMouseStyle();
      editorApp.current.keyCommand.stop();
      stopMouseState.current = true;
    } else {
      stopMouseState.current = false;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);

  useEffect(() => {
    editorApp.current?.on(CanvasMouseEventName.move, canvasMouseMove);
    editorApp.current?.on(CanvasMouseEventName.moveOutStage, moveOutStage);
    editorApp.current?.on(CanvasMouseEventName.moveInStage, domMouseOut);

    document.addEventListener('mouseup', onMouseUpInOutStage);

    var transformerRotater = editorApp.current.stage.findOne('.rotater');
    transformerRotater?.on('mousemove', transformerRotaterMousemove);
    transformerRotater?.on('mouseout', transformerRotaterMouseout);
    const header = document.getElementById('editor-header');
    const layoutNode = header?.parentNode;
    keyCommandState.current.layoutNode = layoutNode;
    document.addEventListener('mousemove', layerEle);

    const updateAfter = () => {
      setOutLineShow(false);
    };
    const updateBefore = () => {
      setOutLineShow(true);
    };
    editorApp.current?.on(CanvasNodeEventName.updateBefore, updateBefore);
    editorApp.current?.on(CanvasNodeEventName.updateAfter, updateAfter);
    //舞台容器 禁用右键
    const stageContent = editorApp?.current.stage.content;
    stageContent?.addEventListener('contextmenu', handleContextMenu);
    editorApp.current.keyCommand.restore();

    return () => {
      editorApp.current?.off(CanvasMouseEventName.move, canvasMouseMove);
      editorApp.current?.off(CanvasMouseEventName.moveOutStage, moveOutStage);
      editorApp.current?.off(CanvasMouseEventName.moveInStage, domMouseOut);
      transformerRotater?.off('mousemove', transformerRotaterMousemove);
      transformerRotater?.off('mouseout', transformerRotaterMouseout);
      editorApp.current.keyCommand.stop();
      document.removeEventListener('mousemove', layerEle);
      editorApp.current.off(CanvasNodeEventName.updateBefore, updateBefore);
      editorApp.current.off(CanvasNodeEventName.updateAfter, updateAfter);
      document.removeEventListener('mouseup', onMouseUpInOutStage);
      stageContent?.removeEventListener('contextmenu', handleContextMenu);
      if (mouseUpOutStageTimer.current)
        clearTimeout(mouseUpOutStageTimer.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editorApp.current]);
  /**
   * 监听鼠标样式变化
   */
  useEffect(() => {
    historyConfig.current = { ...mouseConfig.current };
    //更新 当前操作的app
    if (mouseStyleType.app) {
      editorApp.current = mouseStyleType.app;
      delete mouseStyleType.app;
    }
    mouseConfig.current = {
      ...mouseStyleType,
      content: mouseStyleType?.content || editorApp.current.stage?.content
    };

    setMouseStyle(mouseStyleType.selectionWay, mouseStyleType.selectionParams);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mouseStyleType]);

  const handleContextMenu = (event: MouseEvent) => {
    event.preventDefault();
  };

  const onMouseUpInOutStage = (event: MouseEvent) => {
    const isInStage = (event: MouseEvent) => {
      const stage = editorApp.current.stage;
      const targetElement = stage.content as HTMLElement; // 要检查的目标元素
      // 检查鼠标是否在canvas上
      if (event.target && !targetElement.contains(event.target as Node)) {
        return;
      }
      moveOutStage();
    };
    if (mouseUpOutStageTimer.current)
      clearTimeout(mouseUpOutStageTimer.current);
    mouseUpOutStageTimer.current = setTimeout(() => {
      isInStage(event);
    }, 200);
  };

  /***
   * 鼠标移动到 旋转工具上的时候 设置为旋转 移出时 设置为之前的样式
   */
  const transformerRotaterMousemove = () => {
    setMouseStyle('selector_rotates', '');
  };

  const transformerRotaterMouseout = () => {
    setMouseStyle(mouseStyleType.selectionWay, mouseStyleType.selectionParams);
  };

  const setOutLineShow = (isShow: boolean) => {
    const editorPlugin = editorStore.editor.wheeEditorPlugin.plugin;
    if (editorPlugin) {
      //在选中按钮激活的状态下 可以设置
      if (editorStore.headerBtnState.selected) {
        editorPlugin.disableHoverOutline = isShow;
      } else {
        editorPlugin.disableHoverOutline = true;
      }
    }
  };
  //移入到 targetSelectors 内则可以使用自定义快捷键
  const layerEle = (event: MouseEvent) => {
    keyCommandActiveHandle(event);
    // 目标元素的选择器数组
    const targetSelectors = [
      document.getElementById('layer-drawer-box'),
      editorApp.current.stage.content,
      document.getElementById('editorEventReceiver')
    ];
    let isTargetInside = false;
    // 遍历选择器数组，检查 event.target 是否在其中任何一个元素内
    for (const selector of targetSelectors) {
      const targetElement = selector as HTMLElement | null;
      if (targetElement && targetElement.contains(event.target as Node)) {
        isTargetInside = true;
        break; // 如果找到了目标元素，直接退出循环
      }
    }

    // 根据 isTargetInside 状态设置 mouseStateInOut.current.state
    if (!isTargetInside) {
      if (mouseStateInOut.current.state === 'in') {
        mouseStateInOut.current.state = 'out';
      }
      return;
    } else if (mouseStateInOut.current.state === 'out') {
      mouseStateInOut.current.state = 'in';
    }
  };
  const mainEditorKeyRef = useRef({
    mainEditorKey,
    layerEditorKey
  });
  useEffect(() => {
    mainEditorKeyRef.current.mainEditorKey = mainEditorKey;
    mainEditorKeyRef.current.layerEditorKey = layerEditorKey;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mainEditorKey, layerEditorKey]);
  /**
   * 快捷键激活和停止规则
   * @param event
   */
  const keyCommandActiveHandle = (event: MouseEvent) => {
    if (mainEditorKeyRef.current.mainEditorKey === TaskCategory.extend) return;
    if (mainEditorKeyRef.current.layerEditorKey === layerEditorType.Extend)
      return;

    const layoutNode = keyCommandState.current.layoutNode;
    //特殊处理 Confirm 组件 已经不在监听鼠标移动事件自己维护
    let isTargetInside = false;
    if (!event.target) return;
    const classList = (event.target as HTMLElement).classList;
    const isHaveInput = classList.contains('ant-input');
    let isAntTooltip = false;
    const tooltips = document.getElementsByClassName('ant-tooltip');
    for (const key in tooltips) {
      if (Object.prototype.hasOwnProperty.call(tooltips, key)) {
        const element = tooltips[key];
        if (element && element.contains(event.target as Node)) {
          isAntTooltip = true;
        }
      }
    }
    const hotKeyMaskDom = document.getElementById('HotKeyMask');
    if (isAntTooltip) {
      isTargetInside = true;
    } else if (isHaveInput) {
      isTargetInside = false;
    } else if (hotKeyMaskDom && hotKeyMaskDom.contains(event.target as Node)) {
      isTargetInside = false;
    } else if (layoutNode && layoutNode.contains(event.target as Node)) {
      isTargetInside = true;
    }
    //状态锁
    if (isTargetInside === editorApp.current.keyCommand.isActive) return;
    setIsActiveHotKey(isTargetInside);
    keyCommandState.current.isActive = isTargetInside;
  };

  useEffect(() => {
    if (isActiveHotKey) {
      editorApp.current.keyCommand.restore();
    } else {
      editorApp.current.keyCommand.stop();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isActiveHotKey]);

  //移出舞台后将 隐藏画笔的小圆片
  const moveOutStage = () => {
    const { selectionWay } = mouseConfig.current;
    if (selectionWay && cursorCircularType.includes(selectionWay))
      setStyle({
        ...style,
        opacity: 0
      });
  };

  const domMouseOut = () => {};
  /**
   * 更新画笔样式和大小
   * @param event
   * @returns
   */
  const canvasMouseMove = (event: {
    event: Konva.KonvaEventObject<MouseEvent>;
  }) => {
    const { paintWidth, selectionWay, selectionParams, fillColor } =
      mouseConfig.current;
    if (!selectionWay || !cursorCircularType.includes(selectionWay)) {
      setStyle({
        ...style,
        opacity: 0
      });
      return;
    }
    //canvas 移动要去掉到页面顶点的距离
    const pageTop = editorApp.current.stage.content.getBoundingClientRect().top; //60
    const { x, y } = event.event.evt;
    setStyle({
      ...style,
      top: y - pageTop,
      opacity: 1,
      left: x,
      width: paintWidth as number,
      height: paintWidth as number,
      backgroundColor:
        fillColor ||
        (selectionParams === 'add'
          ? 'rgba(64, 83, 255, 0.5)'
          : 'rgba(250, 57, 90, 0.5)')
    });
  };
  /**
   * 设置鼠标样式
   */
  const setMouseStyle = (type: string | null, typeSecond: string | null) => {
    clearMouseStyle();
    const appContent = mouseConfig.current.content;
    if (!appContent) return;
    if (stopMouseState.current) return;
    let cssName = '';
    const isAdd = typeSecond === 'add';
    switch (type) {
      case 'smart':
        cssName = isAdd ? 'cursorStyle_smart_add' : 'cursorStyle_smart_remove';
        break;
      case 'box':
        cssName = isAdd ? 'cursorStyle_box_add' : 'cursorStyle_box_remove';
        break;
      case 'lasso':
        cssName = isAdd ? 'cursorStyle_lasso_add' : 'cursorStyle_lasso_remove';
        break;
      case 'paint':
        cssName = 'cursorStyle_none';
        break;
      case 'grab': //小手
        cssName = 'cursorStyle_grab';
        break;
      case 'grabbing': //抓手
        cssName = 'cursorStyle_grabbing';
        break;
      case 'selector':
        cssName = 'cursorStyle_selector';
        break;
      case 'selector_move':
        cssName = 'cursorStyle_selector_move';
        break;
      case 'selector_rotates':
        cssName = 'cursorStyle_rotates';
        break;
      case 'ellipse':
        cssName = 'cursorStyle_box_add';
        break;
      case 'eraser':
        cssName = 'cursorStyle_none';
        break;
      default:
        cssName = 'cursorStyle_default';
        break;
    }
    cssName && appContent?.classList?.add(cssName);
  };
  /***
   * 清理添加的鼠标样式
   */
  const clearMouseStyle = () => {
    const appContent = mouseConfig.current.content;
    if (!appContent) return;
    const haveStyle = mouseStyleList.filter((item) =>
      appContent.classList.contains(item)
    );
    if (haveStyle.length > 0) {
      haveStyle.forEach((name) => {
        appContent.classList.remove(name);
      });
    }
    setStyle({
      ...style,
      opacity: 0
    });
  };

  return (
    <div
      className="customCursor"
      style={{
        ...style,
        position: 'absolute',
        pointerEvents: 'none',
        zIndex: 900,
        borderRadius: '50%',
        border: '1px solid #fff',
        transform: 'translate3d(-50%, -50%, 0)',
        willChange: 'transform',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}
    ></div>
  );
};

export default observer(CustomCursor);
