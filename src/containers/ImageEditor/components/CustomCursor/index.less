.cursorStyle_smart_add {
  cursor: url('../../assets/editorMouse/smart_add.svg') 5 5, crosshair !important;
}

.cursorStyle_smart_remove {
  cursor: url('../../assets/editorMouse/smart_remove.svg') 5 5, crosshair !important;
}

.cursorStyle_box_add {
  cursor: url('../../assets/editorMouse/box_add.svg') 13 13, crosshair !important;
}

.cursorStyle_box_remove {
  cursor: url('../../assets/editorMouse/box_remove.svg') 13 13, crosshair !important;
}

.cursorStyle_lasso {
  cursor: url('../../assets/editorMouse/lasso.svg') 5 5, crosshair !important;
}

.cursorStyle_lasso_add {
  cursor: url('../../assets/editorMouse/lasso_add.svg') 5 5, crosshair !important;
}

.cursorStyle_lasso_remove {
  cursor: url('../../assets/editorMouse/lasso_remove.svg') 5 5, crosshair !important;
}

.cursorStyle_selector {
  cursor: url('../../assets/editorMouse/select.svg') 5 5, crosshair !important;
}

.cursorStyle_rotates {
  cursor: url('../../assets/editorMouse/rotate.svg') 10 10, crosshair !important;
}

.cursorStyle_grab {
  cursor: grab;
}

.cursorStyle_grabbing {
  cursor: grabbing;
}

.cursorStyle_selector_move {
  cursor: move !important;
}

.cursorStyle_default {
  cursor: default;
}

.cursorStyle_none {
  cursor: none !important;
}
