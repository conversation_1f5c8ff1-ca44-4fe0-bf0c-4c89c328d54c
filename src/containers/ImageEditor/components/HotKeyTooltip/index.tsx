import React from 'react';
import { Tooltip } from 'antd';
type HotKeyTooltipType = {
  title: string;
  children: React.ReactNode;
};
export const HotKeyTooltip = (props: HotKeyTooltipType) => {
  const { title, children } = props;
  return (
    <Tooltip
      placement="bottom"
      color={'#F1F2F6'}
      overlayInnerStyle={{
        color: '#17171A',
        fontSize: '12px',
        padding: '0px 8px',
        lineHeight: '25px',
        borderRadius: ' 4px',
        minHeight: '25px'
      }}
      overlayClassName={'tooltip-overlay'}
      title={<div>{title}</div>}
    >
      {children}
    </Tooltip>
  );
};
