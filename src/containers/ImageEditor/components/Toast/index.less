@keyframes slideInFromTop {
  0% {
    transform: translateX(-50%) translateY(-120px);
  }

  100% {
    transform: translateX(-50%) translateY(0);
  }
}

.toast_box {
  position: fixed;
  top: 76px;
  left: 0;
  z-index: 1010;
  width: 100%;
  pointer-events: none;
}

.ImageEdit-toast-box {
  display: inline-block;
  margin: 0 auto 30px;

  transform: translateX(-50%);
  margin-left: 50%;
  animation: slideInFromTop 0.3s ease;
  pointer-events: auto;
}

.ImageEdit-toast {
  // position: fixed;
  // top: 76px;
  // left: 50%;
  // transform: translateX(-50%);

  background-color: #f1f2f6;
  color: #fff;
  height: 44px;
  line-height: 44px;
  padding: 0px 16px;
  border-radius: 100px;
  box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.25);
  display: flex;
  gap: 6px;
  justify-content: center;
  align-items: center;
  // max-width: 300px;
  // z-index: 1010;

  color: #17171a;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;

  // justify-content: flex-start;
  // /* 子元素按内容大小自适应 */
  // align-items: flex-start;
  // /* 可选：如果你希望子元素垂直方向也按内容大小自适应 */

  & > div {
    line-height: 44px;
  }

  /* 定义一个名为rotate的动画 */
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  /* 应用动画到类名为rotate-image的元素上 */
  .rotate-image {
    animation: rotate 1s linear infinite;
  }

  .toast-Icon {
    img {
      width: 18px;
      height: auto;
      vertical-align: sub;
    }
  }

  .toast-content {
    // flex: 1;
    white-space: nowrap;
  }

  .toast-close {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    font-size: 18px;
  }

  .toast-btn {
    color: #4053ff;
    cursor: pointer;
    padding-left: 10px;
  }

  .toast-percent {
    min-width: 34px;
    text-align: right;
  }

  .toast-canvas {
    display: inline-block;
    line-height: 44px;
    vertical-align: sub;
    transform: rotate(-90deg);
  }
}

.ImageEdit-toast-mark {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}
