import React, { useState, useEffect, useRef } from 'react';
import { createRoot } from 'react-dom/client';
import './index.less';

interface ToastProps {
  type: 'info' | 'loading' | 'error' | 'percent' | 'success'; //显示 toast 类型
  content: string; //展示核心内容
  percent?: number; //显示百分比进度
  updatePercent?: (fun: (percent: number) => void) => void; //自动更新百分比回调 需要将更新后的百分传回函数中
  duration?: number; //定时自动消除
  onClose?: () => void; //关闭后到回调
  btnTitle?: string; //尾部按钮名称
  btnClickFun?: () => void; //点击尾部按钮后到回调
  isShowMask?: boolean; //是否展示遮罩层
  customNode?: any; //末尾添加任何节点
}
export interface ToastBack {
  destroy: () => void; //手动关闭销毁 这个Toast
}

const disableScroll = () => {
  var scrollTopVal =
    document.documentElement.scrollTop || document.body.scrollTop;
  // 禁止滑动
  const selectDom = document.body;
  if (selectDom && selectDom.style.position !== 'fixed') {
    selectDom.style.position = 'fixed';
    selectDom.style.top = '-' + scrollTopVal + 'px';
    selectDom.style.width = '100%';
    selectDom.style.overflow = 'hidden';
  }
};

const enableScroll = () => {
  /** *取消滑动限制***/
  const selectDom = document.body;
  if (selectDom && selectDom.style.position === 'fixed') {
    var scrollVal = Math.abs(parseFloat(selectDom.style.top));
    selectDom.style.position = '';
    selectDom.style.overflow = '';
    selectDom.style.top = '';
    if (document.body) {
      document.body.scrollTop = scrollVal;
    }
    if (document.documentElement) {
      document.documentElement.scrollTop = scrollVal;
    }
  }
};
/**
 * toast 组件 应用于编辑器
 * @param param0
 * @returns
 */
const Toast: React.FC<ToastProps> = ({
  type,
  content,
  duration,
  onClose,
  percent,
  updatePercent,
  btnTitle,
  btnClickFun,
  isShowMask,
  customNode
}) => {
  const [visible, setVisible] = useState(true);
  const [showPercent, setShowPercent] = useState(percent);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationIdRef = useRef<number | null>(null);
  const [currentPercentage, setCurrentPercentage] = useState(0);
  useEffect(() => {
    updatePercent &&
      updatePercent((number) => {
        if (number < 0) number = 0;
        if (number >= 100) number = 100;
        setShowPercent(number);
      });
    return () => {
      if (isShowMask) enableScroll();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    const percentage = showPercent || 0;
    const animationStartTime = performance.now();
    const animationDuration = 100;

    const animate = (timestamp: number) => {
      const progress = Math.min(
        1,
        (timestamp - animationStartTime) / animationDuration
      );
      const interpolatedPercentage =
        currentPercentage + (percentage - currentPercentage) * progress;

      drawPieChart(ctx, interpolatedPercentage);

      if (progress < 1) {
        animationIdRef.current = requestAnimationFrame(animate);
      } else {
        setCurrentPercentage(percentage);
      }
    };

    animationIdRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showPercent]);

  const drawPieChart = (ctx: CanvasRenderingContext2D, percentage: number) => {
    const canvas = ctx.canvas;
    var dpr = window.devicePixelRatio || 1;
    var rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    canvas.style.width = rect.width + 'px';
    canvas.style.height = rect.height + 'px';

    const centerX = ctx.canvas.width / 2;
    const centerY = ctx.canvas.height / 2;
    const radius = Math.min(centerX, centerY);
    const startAngle = 0;
    const endAngle = (percentage / 100) * 2 * Math.PI;

    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    ctx.fillStyle = '#17171A';
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.fill();

    ctx.fillStyle = '#F1F2F6';
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius - 1, 0, 2 * Math.PI);
    ctx.fill();

    ctx.fillStyle = '#17171A';
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
    ctx.closePath();
    ctx.fill();
  };

  useEffect(() => {
    let dismissTimer: NodeJS.Timeout;
    if (!duration) return;
    dismissTimer = setTimeout(() => {
      setVisible(false);
      if (onClose) {
        onClose();
      }
    }, duration);
    return () => {
      clearTimeout(dismissTimer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [duration, onClose]);

  // const handleClose = () => {
  //   setVisible(false);
  //   if (onClose) {
  //     onClose();
  //   }
  // };

  const getImage = (type: string) => {
    let node = null;
    switch (type) {
      case 'info':
        node = <img src={require('./icon/info.png')} alt="" />;
        break;
      case 'success':
        node = <img src={require('./icon/success.png')} alt="" />;
        break;
      case 'error':
        node = <img src={require('./icon/error.png')} alt="" />;
        break;
      case 'loading':
        node = (
          <img
            className="rotate-image"
            src={require('./icon/loading.png')}
            alt=""
          />
        );
        break;
      case 'percent':
        node = (
          <canvas
            className="toast-canvas"
            ref={canvasRef}
            width={18}
            height={18}
          ></canvas>
        );
        break;
      default:
        break;
    }
    return node;
  };

  return visible ? (
    <>
      {/* {isShowMask ? <div className="ImageEdit-toast-mark"></div> : null} */}
      <div className="ImageEdit-toast-box">
        <div className="ImageEdit-toast">
          <div className="toast-Icon">{getImage(type)}</div>
          {typeof percent === 'number' ? (
            <div className="toast-percent">{showPercent}%</div>
          ) : null}
          <div className="toast-content">{content}</div>
          {btnTitle ? (
            <div
              className="toast-btn"
              onClick={() => {
                btnClickFun && btnClickFun();
              }}
            >
              {btnTitle}
            </div>
          ) : null}
          {customNode || null}
        </div>
      </div>
    </>
  ) : null;
};
const messageBoxId = 'ToastBox';
const message = (props: ToastProps): ToastBack => {
  const { type = 'info', onClose, isShowMask } = props;
  let messageBox = document.getElementById(messageBoxId);
  let toastContainer = null as HTMLElement | null;
  if (!messageBox) {
    messageBox = document.createElement('div');
    messageBox.id = messageBoxId;
    messageBox.classList.add('toast_box');
    document.body.appendChild(messageBox);
  }
  toastContainer = document.createElement('div');

  // ImageEdit-toast-box
  if (messageBox.firstChild) {
    messageBox.insertBefore(toastContainer, messageBox.firstChild);
  } else {
    messageBox.appendChild(toastContainer);
  }

  const root = createRoot(toastContainer);
  const closeToast = () => {
    setTimeout(() => {
      //防止状态争抢 确保在挂载后再进行销毁
      root.unmount();
    }, 0);
    toastContainer?.remove();
    enableScroll();
    hiddenMask();
    if (onClose) {
      onClose();
    }
  };
  let maskDom = null as HTMLElement | null;
  const showMask = () => {
    maskDom = document.getElementById('ImageEdit-toast-mark');
    if (maskDom) return;
    maskDom = document.createElement('div');
    maskDom.id = 'ImageEdit-toast-mark';
    maskDom.classList.add('ImageEdit-toast-mark');
    document.body.appendChild(maskDom);
    disableScroll();
  };

  const hiddenMask = () => {
    maskDom?.remove();
    enableScroll();
  };
  const setIsShowMask = (isShow: boolean) => {
    if (isShow) {
      showMask();
    } else {
      hiddenMask();
    }
  };
  if (isShowMask) setIsShowMask(isShowMask);

  root.render(<Toast {...props} type={type} onClose={closeToast} />);
  return {
    destroy: closeToast
  };
};

export default message;
