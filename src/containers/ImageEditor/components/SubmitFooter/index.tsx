import classNames from 'classnames';
import {
  useState,
  useEffect,
  MouseEvent,
  useRef,
  useImperativeHandle,
  forwardRef
} from 'react';
import { Col, Popover, Row, Space, Tooltip, Switch, Popconfirm } from 'antd';

import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useImageModifyAutoPay } from '@/hooks';
import { useMembershipDesc } from '@/hooks/useMember';
import { useOpenSubscribePopupAIEditor } from '@/hooks/useSubscribe';
import message from '../Toast';
import { Button } from '@/components';

import { FunctionCode, MeiDouFetchPriceDescResponse } from '@/api/types/meidou';
import { fetchPriceDesc } from '@/api/meidou';
import { updatePersonalSetting } from '@/api/account';
import { toSnakeCase } from '@meitu/util';
import { ExclamationMarkCircleBold } from '@/icons';
import { MemberGroupCategory } from '@/types';
import styles from './index.module.less';
import { trackEvent } from '@/services';
import { useAccountProfile } from '@/hooks';

const CODE_MAP = {
  [FunctionCode.inpaint]: 'whee.aiedit.tst.v2',
  [FunctionCode.extend]: 'whee.aienlarge.tst.v2',
  [FunctionCode.aiEraser]: 'whee.airemove.tst.v2',
  [FunctionCode.compound]: 'whee.aisynthesis.tst.v2'
};

const AI_EDITOR_MEIDOU_EXIT_ONCE_MODAL_KEY =
  'AI_EDITOR_MEIDOU_EXIT_ONCE_MODAL_KEY';

type SubmitFooterProps = {
  className?: string;

  loading?: boolean;
  buttonLabel?: string;
  onClick?: (price: any) => void;
  freeCounts: number;
  disabled?: boolean;
  code?: FunctionCode;
  getFormValues?: () => any;
  isActivated?: boolean;
  genNums?: number;
  getFunctionBody?: () => any;
  onSubmit?: () => any;
};

const SubmitFooter = forwardRef(
  (
    {
      className,
      buttonLabel,
      onClick,
      loading,
      freeCounts = 20,
      disabled,
      // 是否是活跃态
      isActivated = true,
      code,
      genNums = 4,
      getFunctionBody,
      onSubmit
    }: SubmitFooterProps,
    ref
  ) => {
    const [isExitConfirmOpen, setIsExitConfirmOpen] = useState(false);
    const { personalSetting, setPersonalSetting } = useImageModifyAutoPay();
    const { availableAmount } = useMeiDouBalance();
    const openSubscribePopup = useOpenSubscribePopupAIEditor();
    const { refreshMembershipDesc, isVipCurrent } = useMembershipDesc();
    const { accountProfile } = useAccountProfile();
    const userId = accountProfile?.id ?? '';
    const messageRef = useRef<any>(null);
    const clickNumRef = useRef('0');

    const [price, setPrice] = useState<MeiDouFetchPriceDescResponse>();

    const deficit = (availableAmount ?? 0) < (price?.amount ?? 0);

    const buttonText = deficit && code ? '余额不足' : buttonLabel ?? '立即生成';

    // 新增变量 美豆yu额小于100
    const availableAmountLessThan100 = (availableAmount ?? 0) < 100;

    // console.log(availableAmount, 'availableAmount', availableAmountLessThan100);

    const hasFreeTimes = (price?.totalFreeNum ?? 0) > 0;

    const handleClickExitModalCancel = () => {
      setIsExitConfirmOpen(false);
      trackEvent('meidou_auto_consume_popup_click', {
        click_type: 'cancel'
      });
      // onClick?.(price);
    };
    const handleClickExitModalOk = () => {
      setIsExitConfirmOpen(false);
      trackEvent('meidou_auto_consume_popup_click', {
        click_type: 'open'
      });
      changeAutoPay(true);
      // onClick?.(price);
    };

    useImperativeHandle(ref, () => ({
      getPrice
    }));
    useEffect(() => {
      // 如果code 不存在直接return

      if (isActivated) {
        if (!code) return;
        if (availableAmount === undefined) return;
        if (!personalSetting) return;
        getPrice();
        // 如果用户开启美豆自动消耗 且 用户美豆或者美叶余额大于100，不在弹出美豆自动消耗提示
        if (!availableAmountLessThan100) {
          if (personalSetting.imageModifyAutoPay) {
            localStorage.setItem(userId + ':hasEditorSubmitClick', '10');
            localStorage.setItem(
              userId + ':' + AI_EDITOR_MEIDOU_EXIT_ONCE_MODAL_KEY,
              true.toString()
            );
          }
        }
      }
      clickNumRef.current =
        localStorage.getItem(userId + ':hasEditorSubmitClick') ?? '0';

      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [code, genNums, isActivated, availableAmount, personalSetting]);

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const getPrice = async () => {
      if (!code) return;
      const fixtureParams = {
        functionCode: code,
        functionBody: JSON.stringify(toSnakeCase(getFunctionBody?.())),
        isProject: true
      };
      try {
        const res = await fetchPriceDesc(fixtureParams);
        // console.log(res);
        // 如果用户开启美豆自动消耗 且 用户美豆或者美叶余额小于100，弹出提示 并关闭自动消耗模式
        // console.log(personalSetting);
        if (personalSetting.imageModifyAutoPay) {
          // 如果是undefined 说明用户没有开启美豆自动消耗
          //
          if (typeof availableAmount === 'number' && availableAmount <= 100) {
            if (messageRef.current) {
              messageRef.current.destroy();
              messageRef.current = null;
            }
            messageRef.current = message({
              type: 'info',
              content: '当前美豆余额不足100，无法开启美豆自动消耗~',
              duration: 3000
            });
            await updatePersonalSetting({
              imageModifyAutoPay: false
            });
            trackEvent('meidou_auto_consume_switch_click', {
              click_type: false ? 'open' : 'close'
            });
            setPersonalSetting({
              imageModifyAutoPay: false
            });
          }
        }
        setPrice(res);

        // 会员状态变化时刷新会员详情
        if (isVipCurrent !== res.isVip) {
          refreshMembershipDesc();
          // 重新拉取定价接口
          getPrice();
        }
      } catch (error) {
        // defaultErrorHandler(error);
      } finally {
        // setLoading(false);
      }
    };
    const changeAutoPay = async (value: any) => {
      if (availableAmountLessThan100) {
        if (messageRef.current) {
          messageRef.current.destroy();
          messageRef.current = null;
        }
        messageRef.current = message({
          type: 'info',
          content: '当前美豆余额不足100，无法开启美豆自动消耗~',
          duration: 3000
        });
        return;
      }
      await updatePersonalSetting({
        imageModifyAutoPay: value
      });
      trackEvent('meidou_auto_consume_switch_click', {
        click_type: value ? 'open' : 'close'
      });
      setPersonalSetting({
        imageModifyAutoPay: value
      });
    };
    const onClickFunc = async (
      e: MouseEvent<HTMLAnchorElement & HTMLButtonElement>
    ) => {
      if (loading) {
        e.preventDefault();
        return;
      }

      if (deficit) {
        e.preventDefault();
        // 余额不足时，调用美豆充值弹窗
        // 项目接入ABTest， 当abTestCode命中，跳转到会员订阅页面
        if (!isVipCurrent) {
          openSubscribePopup(
            MemberGroupCategory.Member,
            CODE_MAP[code as keyof typeof CODE_MAP]
          );
        } else {
          openSubscribePopup(
            MemberGroupCategory.Meidou,
            CODE_MAP[code as keyof typeof CODE_MAP]
          );
        }

        return;
      }
      // 是否触发过click 弹框 存到localStorage

      // 如果用美豆余额小于100 直接调用 onclick  不弹框
      if (availableAmountLessThan100) {
        onClick?.(price);
        return;
      } else {
        let num = clickNumRef.current;
        num = (+num + 1).toString();
        clickNumRef.current = num;
        localStorage.setItem(userId + ':hasEditorSubmitClick', num);
      }

      onClick?.(price);
    };
    // 监听当前页面可视状态切换时，重新请求价格
    useEffect(() => {
      const visibilityChangeHandler = () => {
        if (document.visibilityState === 'visible') {
          getPrice();
        }
      };
      document.addEventListener('visibilitychange', visibilityChangeHandler);
      return () => {
        document.removeEventListener(
          'visibilitychange',
          visibilityChangeHandler
        );
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    const handleOpenChange = () => {
      if (
        +clickNumRef.current === 4 &&
        isActivated &&
        !availableAmountLessThan100
      ) {
        setIsExitConfirmOpen(true);
        setTimeout(() => {
          setIsExitConfirmOpen(false);
        }, 8 * 1000);
      } else {
        setIsExitConfirmOpen(false);
      }
    };
    // console.log(isActivated, isExitConfirmOpen, num, 'isActivated')

    return (
      <>
        <div className={classNames(styles.submitFooter, className)}>
          <div className={classNames(styles.meiDouEditor)}>
            <Row justify="space-between" align="middle">
              <>
                {personalSetting && !personalSetting.imageModifyAutoPay ? (
                  <Col>
                    <Space size={4}>
                      <span className={styles.count}>
                        {price?.costPriceText ?? ''}
                      </span>
                      {hasFreeTimes ? null : (
                        <>
                          {price?.costPriceTextOrigin && (
                            <span className={styles.beforeDiscount}>
                              {price.costPriceTextOrigin}
                            </span>
                          )}
                        </>
                      )}

                      {price?.costPriceTips && (
                        <Tooltip
                          overlayClassName={styles.tooltip}
                          title={
                            <span
                              dangerouslySetInnerHTML={{
                                __html: price.costPriceTips
                              }}
                            />
                          }
                        >
                          <ExclamationMarkCircleBold className={styles.icon2} />
                        </Tooltip>
                      )}
                    </Space>
                  </Col>
                ) : (
                  <Col> </Col>
                )}

                {price?.priceDetail && (
                  <Col>
                    <Popover
                      overlayClassName={styles.detailsPopover}
                      title="明细"
                      arrow={false}
                      placement="topLeft"
                      content={
                        <>
                          {price.priceDetail.map((detail) => (
                            <div
                              key={detail.itemName}
                              className={styles.details}
                            >
                              <Space size={4}>
                                <span>{detail.itemName}</span>
                                <span>{detail.itemCount}</span>
                              </Space>
                              <Space>
                                {detail.priceOrigin !== detail.priceNow && (
                                  <span className={styles.beforeDiscount}>
                                    {detail.priceOrigin}
                                  </span>
                                )}

                                <span>{detail.priceNow}</span>
                              </Space>
                            </div>
                          ))}
                          <div className={styles.autoOpen}>
                            <div className={styles.autoOpenText}>
                              美豆自动消耗{' '}
                            </div>
                            <Switch
                              checked={personalSetting.imageModifyAutoPay}
                              onChange={changeAutoPay}
                            />
                          </div>
                        </>
                      }
                    >
                      <Space
                        className={styles.detailsTrigger}
                        size={4}
                        align="center"
                      >
                        明细
                        {/* <ChevronDownBold className={styles.icon2} /> */}
                      </Space>
                    </Popover>
                  </Col>
                )}
              </>
            </Row>
          </div>
          <Popconfirm
            title="美豆自动消耗提示"
            overlayClassName={styles.confirmModalRecharge}
            description="AI改图中美豆消耗较为频繁，即将为您开启美豆隐藏模式（您可在明细中关闭此设定）。"
            open={isExitConfirmOpen && isActivated}
            // open={true}
            placement="right"
            onOpenChange={handleOpenChange}
            onConfirm={handleClickExitModalOk}
            onCancel={handleClickExitModalCancel}
            okText="确认"
            cancelText="取消"
          >
            <Button
              className="submit-footer-button"
              onClick={
                onSubmit ?? onClickFunc
                // freeCounts > 0
                //   ? onClick
                //   : () => {
                //       message({
                //         type: 'error',
                //         content: '今日使用次数已达上限，请明日再来～',
                //         duration: 3000
                //       });
                //     }
              }
              loading={loading}
              disabled={disabled}
            >
              {buttonText}
            </Button>
          </Popconfirm>
        </div>

        {/* <Confirm
          open={isExitConfirmOpen && isActivated}
          okText="开启"
          onClickClose={handleClickExitModalCancel}
          onClickCancel={handleClickExitModalCancel}
          onClickOk={handleClickExitModalOk}
          contentClassName={styles.confirmModalRecharge}
          centered
          okTitle="开启"
          width={340}
        >
          <div className="confirm-modal-recharge-main">美豆自动消耗提示</div>
          <div className="confirm-modal-recharge-secondary">
            AI改图中美豆消耗较为频繁，即将为您开启美豆隐藏模式（您可在明细中关闭此设定）。
          </div>
        </Confirm> */}
      </>
    );
  }
);
export default SubmitFooter;
