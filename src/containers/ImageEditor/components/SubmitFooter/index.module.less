@bg-color: #17171a;

@content-primary: #dcdde5;
@content-tertiary: #5e5e6b;
@button-bg-color: #3549ff;
@button-color: #fff;

@content-width: 268px;

.submit-footer {
  position: absolute;
  width: 100%;
  height: 100px;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-top: 1px solid #29292e;
  background-color: @bg-color;
  z-index: 1;

  :global {
    .submit-footer-tips {
      display: flex;
      margin-top: 12px;
      font-size: 12px;
      width: @content-width;
      justify-content: space-between;
      &-payout {
        display: flex;
        align-self: flex-start;

        span {
          &:first-child {
            color: @content-tertiary;
          }

          &:last-child {
            color: @content-primary;
            margin-left: 4px;
          }
        }
      }

      &-detail {
        align-self: flex-end;
        color: @content-tertiary;
      }
    }

    .ant-btn.designer-btn.ant-btn-primary:disabled {
      // background: #1e2353;
      // background: #3549ff;
      background: #1e2353;
      outline: none;
      border: none;
      // color: #515153;
      color: #fff;
    }

    .submit-footer-button {
      width: @content-width;
      height: 40px;
      border-radius: 4px !important;
      background-color: @button-bg-color;
      color: @button-color;
      border: none;
      font-size: 14px;
      &::before,
      &::after {
        border-radius: 4px !important;
      }
    }
  }
}
@modal-main-color: #e1e3eb;
@modal-secondary-color: #81838c;
@modal-checked-color: #3549ff;
.confirm-modal-recharge {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 300px;
  :global {
    .ant-popover-content .ant-popconfirm-buttons {
      display: flex;
      align-items: center;
      justify-content: space-between;
      button {
        width: 130px;
        height: 36px;
        margin-left: 0;
      }
      .ant-btn-default {
        background: var(
          --background-editorButtonSecondaryReverse,
          rgba(20, 31, 51, 0.12)
        );
      }
      .ant-btn-default:not(:disabled):not(.ant-btn-disabled):hover {
        border-color: #d0d2d6;
        color: #1c1d1f;
      }
      .ant-btn-primary {
        background: var(--background-editorButtonPrimaryAccent, #3549ff);
      }
      .ant-btn-primary:not(:disabled):hover,
      .ant-btn.designer-btn.ant-btn-primary:not(:disabled):active {
        background: #3042e5;
      }
      .ant-btn-primary:hover::after {
        opacity: 1;
      }
      .ant-btn-primary:active::after {
        opacity: 0.33;
      }
    }
    .ant-popconfirm-message-icon {
      display: none;
    }
    .ant-popover-inner {
      padding: 16px 16px 20px 16px !important;
    }
    .ant-popconfirm-title {
      color: var(--content-editorPrimaryReverse, #17171a);
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
    }
    .ant-popconfirm-description {
      color: var(--content-editorPrimaryReverse, #17171a);
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
  :global {
    .confirm-modal-recharge-main {
      font-size: 16px;
      font-weight: 600;
      color: @modal-main-color;
      margin-bottom: 12px;
    }

    .confirm-modal-recharge-secondary {
      font-size: 14px;
      color: @modal-secondary-color;
      text-align: left;
      margin-bottom: 16px;
    }

    .confirm-modal-recharge-check {
      font-size: 14px;
      line-height: 1;
      display: flex;
      align-items: center;
      color: @modal-main-color;
      user-select: none;
      cursor: pointer;
      margin-bottom: 12px;

      input {
        display: none;
      }

      &-box {
        display: inline-block;
        width: 12px;
        height: 12px;
        padding: 3px;
        box-sizing: content-box;
        border-radius: 4px;
        background-color: #fff;
        color: #fff;
        font-size: 12px;
        line-height: 1;
        margin-right: 8px;

        &.checked {
          background-color: @modal-checked-color;
        }
      }
    }
  }
}
@import '~@/styles/variables.less';

.mei-dou-editor {
  // padding: 12px 0px 10px 0px;
  height: 44px;

  z-index: calc(@z-index-popup-base - 128);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: @content-width;

  &.has-border {
    bottom: 0;
    border-radius: 0;
    box-shadow: @separator;
  }

  :global(.ant-row) {
    width: 100%;
  }

  .count {
    color: var(--content-editorPrimary, #e1e3eb);
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .before-discount {
    color: @content-system-quaternary;
    font-size: @text-12;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    text-decoration: line-through;
  }

  .icon {
    font-size: 15px;
  }

  .icon2 {
    font-size: 13px;
    color: @content-system-quaternary;
    cursor: pointer;
  }

  .details-trigger {
    color: @content-system-quaternary;
    font-size: @text-12;
    font-style: normal;
    font-weight: 400;
    line-height: 44px;
    height: 44px;
    cursor: pointer;
  }

  :global(.ant-btn) {
    margin: 7px 0 6px 0;
    border: none;
  }

  .skeleton {
    height: 22px;

    h3 {
      margin-bottom: 0 !important;
    }
  }
}

.details-popover :global(.ant-popover) {
  &-title {
    margin-bottom: 12px !important;
    color: var(--content-editorPrimary, #e1e3eb) !important;
  }

  &-inner-content {
    color: var(--content-editorPrimary, #e1e3eb) !important;
    .details {
      display: flex;
      justify-content: space-between;

      span {
        color: var(--content-editorPrimary, #e1e3eb) !important;
        font-size: @text-14;
        font-weight: 400;
        line-height: 16px;

        &.before-discount {
          color: var(--content-editorTertiary, #636370) !important;
          text-decoration: line-through;
        }
      }
    }
    .auto-open {
      :global(.ant-switch) {
        background-color: rgba(235, 238, 255, 0.1) !important;
      }
      :global(.ant-switch-checked) {
        background: #3549ff !important;
      }
      margin-top: 16px;
      padding-top: 16px;
      display: flex;
      font-size: @text-14;
      align-items: center;
      justify-content: space-between;
      flex-direction: row;
      border-top: 1px solid
        var(--stroke-editorSeparatorOverlay, rgba(240, 242, 255, 0.08));
    }
  }
}

.tooltip :global(.ant-tooltip-inner) {
  font-size: @text-12 !important;
}
.details-popover {
  width: 260px;
  height: 139px;
  border-radius: 6px;
  background: var(--background-editorPopup, #252529) !important;
  box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.25);
  .ant-popover-content {
    background: var(--background-editorPopup, #252529) !important;
  }
  :global(.ant-popover-inner) {
    width: 260px;
    // height: 139px;

    border-radius: 6px;
    background: var(--background-editorPopup, #252529) !important;
    box-shadow: 0px 12px 36px 0px rgba(0, 0, 0, 0.25);
    .ant-popover-title {
    }
  }
}
