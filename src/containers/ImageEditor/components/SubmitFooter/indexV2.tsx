import classNames from 'classnames';
import styles from './index.module.less';
import { Button } from '@/components';
import message from '../Toast';

type SubmitFooterProps = {
  className?: string;

  loading?: boolean;
  buttonLabel?: string;
  onClick?(): void;
  freeCounts: number;
  disabled?: boolean;
};

export default function SubmitFooter({
  className,
  buttonLabel,
  onClick,
  loading,
  freeCounts = 20,
  disabled
}: SubmitFooterProps) {
  return (
    <div className={classNames(styles.submitFooter, className)}>
      <div className="submit-footer-tips">
        <div className="submit-footer-tips-payout">
          <span>剩余免费次数{'  '}</span>
          <span>{freeCounts}</span>
        </div>
        {/* <div className="submit-footer-tips-detail">明细</div> */}
      </div>

      <Button
        className="submit-footer-button"
        onClick={
          freeCounts > 0
            ? onClick
            : () => {
                message({
                  type: 'error',
                  content: '今日使用次数已达上限，请明日再来～',
                  duration: 3000
                });
              }
        }
        loading={loading}
        disabled={disabled}
      >
        {buttonLabel}
      </Button>
    </div>
  );
}
