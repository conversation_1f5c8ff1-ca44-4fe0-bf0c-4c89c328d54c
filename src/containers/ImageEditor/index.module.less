@import '~@/styles/variables.less';

.image-editor {
  width: 100%;

  :global {
    .designer-layout-main {
      padding: 0 !important;
      position: relative;
      background: #000;
      height: calc(100vh - 56px);
    }

    .designer-layout-sider-scroll {
      height: calc(100vh - 56px - @size-lg) !important;
      visibility: hidden;
    }
  }

  user-select: none;

  :global {
    .editor-layer-upload {
      height: 100%;
      position: relative;

      .ant-upload-drag-container {
        height: 100%;
      }

      .ant-upload-wrapper .ant-upload-drag .ant-upload {
        padding: 0 !important;
      }

      .ant-upload-wrapper .ant-upload-drag {
        border: none !important;
        padding: 0;
      }

      .editor-container {
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 1;
      }

      .extend-container {
        width: calc(100% - 380px);
        height: 100%;
        position: absolute;
        left: 380px;
        z-index: 7;
      }
    }
  }
}

.test-button-1 {
  position: fixed;
  left: 0;
  bottom: 60px;
  background: #ccc;
  z-index: 200;
}

.text01 {
  position: fixed;
  left: 0;
  bottom: 300px;
  background: #ccc;
  font-size: 20px;
  padding: 10px;
  z-index: 500;
}

.text02 {
  position: fixed;
  left: 0;
  bottom: 100px;
  background: #ccc;
  font-size: 20px;
  padding: 10px;
  z-index: 500;
}
