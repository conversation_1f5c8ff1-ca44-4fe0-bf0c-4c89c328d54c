import { processor } from './processImageWithWebGL';
import { filterImageWithMaskShader } from './shaders/filterImageWithMask';
import { filterMaskWithImageAlphaShader } from './shaders/filterMaskWithImage';
import { replaceAlphaShader } from './shaders/replaceAlpha';

enum ProgramKey {
  ClipImageWithMask = 'clipImageWithMask',
  ClipMaskWithImageAlpha = 'clipImageWithImageAlpha',
  ReplaceAlphaShader = 'replaceAlphaShader'
}

processor?.registerProgram(
  ProgramKey.ClipImageWithMask,
  filterImageWithMaskShader
);
processor?.registerProgram(
  ProgramKey.ClipMaskWithImageAlpha,
  filterMaskWithImageAlphaShader
);
processor?.registerProgram(ProgramKey.ReplaceAlphaShader, replaceAlphaShader);

export function filterImageWithMask(
  origin: HTMLImageElement,
  mask: HTMLImageElement
) {
  processor?.run(ProgramKey.ClipImageWithMask, { image: origin, image2: mask });

  return processor?.canvas;
}

export function filterMaskWithImageAlpha(
  image: HTMLImageElement,
  mask: HTMLImageElement
) {
  processor?.run(ProgramKey.ClipMaskWithImageAlpha, { image, image2: mask });

  return processor?.canvas;
}

export function replaceAlpha(
  hdImage: HTMLImageElement,
  originImage: HTMLImageElement
) {
  processor?.run(ProgramKey.ReplaceAlphaShader, {
    image: hdImage,
    image2: {
      source: originImage,
      magFilter: WebGLRenderingContext.LINEAR
    }
  });

  return processor?.canvas;
}
