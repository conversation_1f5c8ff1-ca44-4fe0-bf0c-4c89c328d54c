/**
 * 处理axios请求错误
 */

import { AxiosError } from 'axios';
import message from '../components/Toast';

export const handleRequestError = (error: AxiosError, content: string = '') => {
  if (error.code === 'ERR_NETWORK') {
    message({
      type: 'error',
      content: content ?? '操作失败, 请检查网络设置',
      duration: 5000
    });
  } else {
    message({
      type: 'error',
      content: error?.message ?? '生成失败，请重试',
      duration: 5000
    });
  }
};

let closeInvalidTokenToast = null as null | (() => void);
export function handleInvalidTokenError(
  error: AxiosError,
  content = '登陆失效，请重新登录',
  btnTitle = '刷新页面'
) {
  if (error.code === '10022' || error.code === '10005') {
    closeInvalidTokenToast?.();
    const handler = message({
      type: 'error',
      content,
      btnTitle,
      btnClickFun() {
        window.location.reload();
      },
      onClose() {
        closeInvalidTokenToast = null;
      }
    });
    closeInvalidTokenToast = handler.destroy.bind(handler);

    return true;
  }

  return false;
}
