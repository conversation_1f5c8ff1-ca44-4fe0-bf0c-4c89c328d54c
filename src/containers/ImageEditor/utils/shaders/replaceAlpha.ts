/**
 * 使用image的透明通道过滤mask图
 */
export const replaceAlphaShader = `
precision mediump float;

// our texture
uniform sampler2D u_image;  // 超清后图片
uniform sampler2D u_image2; // 原图

// the texCoords passed in from the vertex shader.
varying vec2 v_texCoord;

void main() {
  vec4 hd_color = texture2D(u_image, v_texCoord);
  vec4 origin_color = texture2D(u_image2, v_texCoord);

  gl_FragColor = vec4(hd_color.rgb, origin_color.a);
}
`;
