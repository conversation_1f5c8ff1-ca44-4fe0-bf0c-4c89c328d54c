export const filterImageWithMaskShader = `
precision mediump float;

// our texture
uniform sampler2D u_image;  // origin
uniform sampler2D u_image2; // mask

// the texCoords passed in from the vertex shader.
varying vec2 v_texCoord;

void main() {
  vec4 origin_color = texture2D(u_image, v_texCoord);
  vec4 mask_color = texture2D(u_image2, v_texCoord);

  gl_FragColor = vec4(origin_color.rgba * mask_color.r);
}
`;
