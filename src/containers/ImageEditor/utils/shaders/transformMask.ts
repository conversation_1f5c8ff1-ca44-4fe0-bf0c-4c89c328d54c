/**
 * 将黑白mask图转换为目标颜色
 */
export function getTransformMaskShader(
  destinationColor: readonly [number, number, number, number],
  normalized = false
) {
  const numberColor = normalized
    ? destinationColor
    : destinationColor.map((c) => c / 255);
  const dColor = numberColor.map((n) => n.toFixed(3));

  return `
    precision mediump float;

    // our texture
    uniform sampler2D u_image;

    // the texCoords passed in from the vertex shader.
    varying vec2 v_texCoord;

    void main() {
      vec4 src_color = texture2D(u_image, v_texCoord);
      vec4 des_color = vec4(${dColor[0]}, ${dColor[1]}, ${dColor[2]}, ${dColor[3]});

      gl_FragColor = vec4(des_color.rgb * des_color.a * src_color.r, des_color.a * src_color.r);
    }
  `;
}

/**
 * 将涂抹区转换为灰度图
 */
export function getTransformGrayMaskShader(grayFactor = 1) {
  return `
    precision mediump float;

    // our texture
    uniform sampler2D u_image;

    // the texCoords passed in from the vertex shader.
    varying vec2 v_texCoord;

    void main() {
      vec4 src_color = texture2D(u_image, v_texCoord);

      float gray = (src_color.r + src_color.g + src_color.b) / 3.0 * ${grayFactor.toFixed(
        3
      )};
      

      gl_FragColor = vec4(gray, gray, gray, 1.0);
    }
  `;
}

/**
 * 将涂抹区转换为二值图
 */
export function getTransformBinaryValueMaskShader(grayThreshold = 0.1) {
  return `
    precision mediump float;

    // our texture
    uniform sampler2D u_image;

    // the texCoords passed in from the vertex shader.
    varying vec2 v_texCoord;

    void main() {
      vec4 src_color = texture2D(u_image, v_texCoord);

      float gray = (src_color.r + src_color.g + src_color.b) / 3.0;
      float threshold = ${grayThreshold.toFixed(3)};
      
      float value = gray > threshold ? 1.0 : 0.0;

      gl_FragColor = vec4(value, value, value, 1.0);
    }
  `;
}

/**
 * 将涂抹区转换为pre-mask
 */
export function getTransformPreMaskShader(grayFactor = 1) {
  return `
    precision mediump float;

    // our texture
    uniform sampler2D u_image;

    // the texCoords passed in from the vertex shader.
    varying vec2 v_texCoord;

    void main() {
      vec4 src_color = texture2D(u_image, v_texCoord);
      float gray = src_color.a * ${grayFactor.toFixed(3)};
      gl_FragColor = vec4(gray, gray, gray, 1.0);
    }
  `;
}
