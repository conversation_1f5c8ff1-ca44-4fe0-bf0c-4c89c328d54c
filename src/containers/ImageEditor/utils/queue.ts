import { sleep } from '@meitu/util';
import { autorun } from 'mobx';

export class RequestQueue {
  private queue: Array<Function> = [];
  private isRunning = false;

  public push = (fn: Function) => {
    this.queue.push(fn);
    if (!this.isRunning) {
      this.run();
    }
  };

  private run = async () => {
    if (this.queue.length === 0) {
      this.isRunning = false;
      return;
    }
    const request = this.queue.shift();
    if (request) {
      this.isRunning = true;
      autorun(async () => {
        await request();
        await sleep(1000);
        this.run();
      });
    }
  };
}
