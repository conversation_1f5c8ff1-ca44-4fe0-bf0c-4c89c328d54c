import { isDebugMode } from '@meitu/util';
import type { UploadResult, UploaderOptions } from '@meitu/upload';
import { UploadEnv, Uploader } from '@meitu/upload';
import { isBeta, isRelease } from '@/constants/environment';
import { defaultUploadParam } from '@/constants/uploadParams';
import { getAccountAccessToken } from '@/services';
import { fetchImageUploadSign } from '@/api';

const defaultEnv = isRelease || isBeta ? UploadEnv.Release : UploadEnv.Test;

/**
 * 生成上传实例
 * TODO: 糟糕的实现，后续再优化
 * @returns {Uploader}
 */
let accessToken: string | undefined = '';

export function getUploader(option?: UploaderOptions) {
  const {
    env = defaultEnv,
    debug = isDebugMode(),
    ...restOption
  } = option || {};

  if (!accessToken) {
    accessToken = getAccountAccessToken();
  }

  const uploader = new Uploader({
    env,
    debug,
    accessToken,
    async sign(params) {
      const { type, suffix } = params;
      const signedParams = await fetchImageUploadSign({ type, suffix }); // 请求业务服务端提供的参数签名接口
      return signedParams; // 返回签名后的参数，相当于 params + 3 个新增参数 { sig: 'xxx', sigTime: 'xxx', sigVersion: 'xxxx' }
    },
    ...restOption
  });

  return uploader;
}

export class TimeoutError extends Error {
  public isTimeout: boolean;

  constructor() {
    super('Timeout Error');
    this.isTimeout = true;
  }
}

function uploaderFunc(
  file: File | Blob | string | FileList | Blob[] | string[] | FileList[],
  type?: string,
  timeout?: number,
  app?: string,
  directory?: string
) {
  let uploader = getUploader();
  const suffix = type || 'png';

  return new Promise<UploadResult>((resolve, reject) => {
    uploader
      .upload(file, {
        ...defaultUploadParam,
        app: app || defaultUploadParam.app,
        type: directory || defaultUploadParam.type,
        suffix
      })
      .then(resolve)
      .catch(reject);

    if (typeof timeout === 'number') {
      if (timeout > 0) {
        setTimeout(() => {
          reject(new TimeoutError());
        }, timeout);
      } else {
        reject(new TimeoutError());
      }
    }
  });
}

function fileToArrayBuffer(file: File) {
  return new Promise<ArrayBuffer>((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      resolve(e.target!.result as ArrayBuffer);
    };
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
}

function isWebPAnimated(arrayBuffer: ArrayBuffer) {
  const bytes = new Uint8Array(arrayBuffer);

  // Check if it's a valid WebP file
  if (
    bytes[0] !== 0x52 ||
    bytes[1] !== 0x49 ||
    bytes[2] !== 0x46 ||
    bytes[3] !== 0x46 ||
    bytes[8] !== 0x57 ||
    bytes[9] !== 0x45 ||
    bytes[10] !== 0x42 ||
    bytes[11] !== 0x50
  ) {
    return false;
  }

  // Search for 'ANIM' chunk, which indicates an animated WebP
  for (let i = 12; i < bytes.length - 3; i++) {
    if (
      bytes[i] === 0x41 &&
      bytes[i + 1] === 0x4e &&
      bytes[i + 2] === 0x49 &&
      bytes[i + 3] === 0x4d
    ) {
      return true;
    }
  }
  return false;
}

export { uploaderFunc, isWebPAnimated, fileToArrayBuffer };
