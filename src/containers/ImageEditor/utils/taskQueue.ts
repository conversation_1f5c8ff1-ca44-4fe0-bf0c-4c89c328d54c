import { fetchHistoryList } from '@/api/imageEditor';
import {
  ImageLayer,
  UnknownLayer
} from '@/editor/wukong-whee-editor-plugin/src/types';
import { sleep } from '@meitu/util';

type Task = {
  getData: () => Array<ImageLayer | UnknownLayer>;
  removeData: (...layerIds: string[]) => void;
  hooks: {
    successfully: (result: ResultSync) => void;
    failed: (result: ResultSync) => void;
  };
};

class Result {
  constructor(
    public layerId: string,
    public result: string[],
    public msgId: string
  ) {}
}

export class ResultSync {
  constructor(private clearHandler: (result: Result[]) => void) {}
  public result: Result[] = [];
  public reason: Result[] = [];

  public clear() {
    this.clearHandler(this.result);
    this.result = [];
  }
  public push(value: Result) {
    this.result.push(value);
  }
}

export class TaskQueue {
  private tasks: Map<
    string,
    {
      taskType: string;
      msgId: string;
    }
  > = new Map();
  private _clearHandler = (result: Result[]) => {
    result.forEach((item) => {
      this.params.removeData(item.layerId);
    });
  };
  private result: ResultSync = new ResultSync(this._clearHandler);
  private _isRunning = false;
  constructor(private params: Task) {
    this.getTasks();
  }

  private getTasks() {
    this.tasks.clear();
    this.params
      .getData()
      .filter((layer) => layer.layerType === 'unknown' && layer.syncTask)
      .forEach((layer) => {
        const l = layer as UnknownLayer;
        this.tasks.set(layer.id, {
          taskType: l.taskType,
          msgId: l.msgId
        });
      });
  }

  public cancelTask(...taskIds: string[]) {
    if (taskIds.length === 0) {
      this.tasks.clear();
    } else {
      taskIds.forEach((taskId) => {
        this.tasks.delete(taskId);
      });
    }
  }

  public _initRun = async () => {
    if (this._isRunning) return;
    this.getTasks();
    if (this.tasks.size <= 0) return;
    this._isRunning = true;
    await this.run();
    this.params.hooks.successfully(this.result);
    this.params.hooks.failed(this.result);

    if (this.tasks.size > 0) {
      this._isRunning = false;
      await sleep(5000);
      await this._initRun();
    }
  };

  public run = async () => {
    // 根据map取出所有的task
    const sendTasks: Record<string, string[]> = {};
    const values = Array.from(this.tasks.values());
    values.forEach((item) => {
      if (Object.prototype.hasOwnProperty.call(sendTasks, item.taskType)) {
        sendTasks[item.taskType].push(item.msgId);
      } else {
        sendTasks[item.taskType] = [item.msgId];
      }
    });
    const body: Record<string, string> = {};
    Object.keys(sendTasks).forEach((key: string) => {
      body[key] = sendTasks[key].join(',');
    });
    // 发送请求并处理body
    const { list } = await fetchHistoryList({
      typeIds: JSON.stringify(body)
    });
    const result = list
      .filter(
        (item) =>
          item.resultImages &&
          item.resultImages.filter((r) => r.status === 1).length > 0
      )
      .map((item) => {
        return {
          msgId: item.id,
          images:
            item.resultImages
              ?.filter((r) => r.status === 1)
              .map((r) => r.url) ?? []
        };
      });
    result.forEach((item) => {
      const layerId = Array.from(this.tasks.entries()).find(
        ([_, value]) => value.msgId === item.msgId
      )?.[0];
      if (layerId) {
        this.result.push(new Result(layerId, item.images, item.msgId));
      }
    });

    const reason = list
      .filter(
        (item) =>
          item.resultImages &&
          item.resultImages.filter((r) => r.status === 2).length > 0
      )
      .map((item) => {
        return {
          msgId: item.id,
          images:
            item.resultImages
              ?.filter((r) => r.status === 2)
              .map((r) => r.url) ?? []
        };
      });

    reason.forEach((item) => {
      const layerId = Array.from(this.tasks.entries()).find(
        ([_, value]) => value.msgId === item.msgId
      )?.[0];
      if (layerId) {
        this.result.reason.push(new Result(layerId, item.images, item.msgId));
      }
    });
  };
}
