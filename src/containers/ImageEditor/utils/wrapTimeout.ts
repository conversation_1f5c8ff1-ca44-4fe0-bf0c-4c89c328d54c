import { TimeoutError } from './upload';

export function wrapTimeout<
  T extends (...args: any[]) => Promise<any>,
  PromiseRet = T extends (...args: any[]) => Promise<infer R> ? R : never
>(fn: T, timeout: number) {
  return function (...args: Parameters<T>) {
    return new Promise<PromiseRet>((resolve, reject) => {
      fn(...args)
        .then(resolve)
        .catch(reject);

      if (typeof timeout === 'number') {
        if (timeout > 0) {
          setTimeout(() => {
            reject(new TimeoutError());
          }, timeout);
        } else {
          reject(new TimeoutError());
        }
      }
    });
  };
}
