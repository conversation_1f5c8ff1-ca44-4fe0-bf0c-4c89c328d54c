import { DefferType, deffer } from './deffer';

type SaveTaskType<T> = () => Promise<T>;

export class SaveQueue<SaveResponse extends unknown> {
  private saveTasks = [] as Array<SaveTaskType<SaveResponse>>;
  private runningDeffer = null as null | DefferType<SaveResponse>;

  get isRunning() {
    return !!this.runningDeffer;
  }

  get runningPromise() {
    return this.runningDeffer?.promise;
  }

  public runSaveTask(task: () => Promise<SaveResponse>) {
    this.saveTasks.push(task);

    if (!this.isRunning) {
      this.runningDeffer = deffer();
      this.flushTask();
    }

    return this.runningDeffer!.promise;
  }

  private async flushTask() {
    const lengthBeforeRunTask = this.saveTasks.length;
    if (!lengthBeforeRunTask) {
      return;
    }

    const lastTask = this.saveTasks[lengthBeforeRunTask - 1];
    let res = null as null | SaveResponse;
    let err = null as any;
    try {
      res = await lastTask();
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
        console.error(e);
      }
      err = e;
    } finally {
      const lengthAfterRunTask = this.saveTasks.length;
      // 执行前的长度 === 执行后的长度，表示最后一个任务执行完毕
      if (lengthAfterRunTask === lengthBeforeRunTask) {
        if (err) {
          this.runningDeffer!.reject(err);
        } else {
          this.runningDeffer!.resolve(res);
        }

        this.runningDeffer = null;
        this.saveTasks.length = 0;
      } else {
        this.flushTask();
      }
    }
  }
}

// (function () {
//   const queue = new SaveQueue();

//   function createTask(timeout: number, id: number) {
//     return  function () {

//       return new Promise((resolve, reject) => {
//         setTimeout(() => {
//           console.log(`执行完成: ${id}`);
//           resolve(void 0);
//         }, timeout)
//       })
//     }
//   }

//   const tasks = new Array(10).fill(0).map((_, i) => createTask(1000, i));
//   const rs = tasks.map((t, i) => {
//     console.log(`开始执行: ${i}`);
//     return queue.runSaveTask(t)
//   });
//   console.log(rs);

//   const s = new Set(rs);
//   console.assert(s.size === 1, `应该只有一个promise，但是结果有[${s.size}]个`);

//   rs[0].then(() => console.log('已清空'));

//   console.log(queue);
// })()
