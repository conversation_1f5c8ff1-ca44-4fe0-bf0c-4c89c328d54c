const ImageSizeRex = /imageView2\/2\/w\/(\d+)\/h\/(\d+)/;
/**
 * 通过华为obs设置图片包围框
 * @param url { string } 图片地址
 * @param size { width: number, height: number } 图片包括框大小
 */
export function optimizeImage(
  url: string,
  size: { width: number; height: number }
): string {
  return url;
  // 图片处理云函数会影响图片色彩，暂时弃用

  // if (!url) return url;
  // // 如果是base64图片，直接返回
  // if (url.startsWith('data:image')) return url;
  // const { width, height } = size;
  // let [urlPrefix, urlSuffix] = url.split('?');
  // if (urlPrefix.endsWith('.svg')) return url;
  // if (!urlSuffix) return url;
  // const imageSizeMatch = urlSuffix.match(ImageSizeRex);
  // if (imageSizeMatch) {
  //   urlSuffix = urlSuffix.replace(
  //     `${imageSizeMatch[0]}&`,
  //     `imageView2/2/w/${width}/h/${height}`
  //   );
  // } else {
  //   urlSuffix = `imageView2/2/w/${width}/h/${height}&${urlSuffix}`;
  // }
  // return `${urlPrefix}?${urlSuffix}`;
}

/**
 * 重置图片地址
 * @param url
 * @returns
 */
export function resetImageUrl(url: string): string {
  if (!url) return url;
  // 如果是base64图片，直接返回
  if (url.startsWith('data:image')) return url;
  let [urlPrefix, urlSuffix] = url.split('?');
  if (!urlSuffix) return url;
  const imageSizeMatch = urlSuffix.match(ImageSizeRex);
  if (imageSizeMatch) {
    urlSuffix = urlSuffix
      .replace(`${imageSizeMatch[0]}`, '')
      .replace(/^&|&$/g, '');
  }
  return `${urlPrefix}?${urlSuffix}`;
}

export const fitImage = (
  frameHeight: number,
  imageWidth: number,
  imageHeight: number
) => {
  // 修改图片是根据图片设置为容器的高度，宽度自适应
  const ratio = frameHeight / imageHeight;
  const height = frameHeight;
  const width = imageWidth * ratio;
  return {
    width,
    height
  };
};
/**
 * 加载图片
 * @param urls
 */
export async function loadImage(...urls: string[]) {
  return await Promise.all(
    urls.map((url) => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.src = url;
        img.onload = () => {
          resolve(img);
        };
        img.onerror = (e) => {
          reject(e);
        };
      });
    })
  );
}
