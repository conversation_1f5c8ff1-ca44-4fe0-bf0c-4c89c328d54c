import { merge, omitBy } from 'lodash';

const vertexShaderSource = `
attribute vec2 a_position;
attribute vec2 a_texCoord;

uniform vec2 u_resolution;

varying vec2 v_texCoord;

void main() {
   // convert the rectangle from pixels to 0.0 to 1.0
   vec2 zeroToOne = a_position / u_resolution;

   // convert from 0->1 to 0->2
   vec2 zeroToTwo = zeroToOne * 2.0;

   // convert from 0->2 to -1->+1 (clipspace)
   vec2 clipSpace = zeroToTwo - 1.0;

   gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);

   // pass the texCoord to the fragment shader
   // The GPU will interpolate this value between points.
   v_texCoord = a_texCoord;
}
`;

const renderShader = `
precision mediump float;

// our texture
uniform sampler2D u_image;

// the texCoords passed in from the vertex shader.
varying vec2 v_texCoord;

void main() {
   gl_FragColor = texture2D(u_image, v_texCoord);
}
`;

type ProcessorOptions = {
  debug?: boolean;
};

type ImageSource = HTMLImageElement | HTMLCanvasElement;
function isImageSource(maybeSource: any) {
  return (
    typeof maybeSource === 'object' &&
    (maybeSource instanceof HTMLCanvasElement ||
      maybeSource instanceof HTMLImageElement)
  );
}

type ImageOption = {
  source: ImageSource;
  magFilter?:
    | WebGLRenderingContext['NEAREST']
    | WebGLRenderingContext['LINEAR'];
};

function mergeImageOptions(options: ImageOption | ImageSource) {
  const opt = isImageSource(options)
    ? {
        source: options
      }
    : options;

  const defaultOptions = {
    magFilter: WebGLRenderingContext.NEAREST
  };

  return merge(
    defaultOptions,
    omitBy(opt, (v) => typeof v === 'undefined')
  ) as Required<ImageOption>;
}

type RunParamsType = {
  image: HTMLImageElement | HTMLCanvasElement | ImageOption;
  image2?: HTMLImageElement | HTMLCanvasElement | ImageOption;
  uniforms?: Record<string, any>;
};

function createProcessor({ debug }: ProcessorOptions) {
  const canvas = document.createElement('canvas');

  if (debug) {
    document.body.appendChild(canvas);
  }

  const gl = canvas.getContext('webgl') as WebGLRenderingContext;
  if (!gl) {
    debug && console.error(`cant't get webgl context`);
    return;
  }

  const programMap = new Map<string, WebGLProgram>();

  function registerProgram(key: string, fShaderSource: string = renderShader) {
    const program = compileProgram(gl, vertexShaderSource, fShaderSource);
    program && programMap.set(key, program);
  }

  const positionBuffer = gl.createBuffer();
  const texcoordBuffer = gl.createBuffer();
  const texture = gl.createTexture();
  const texture2 = gl.createTexture();

  function run(key: string, params: RunParamsType) {
    const program = programMap.get(key);
    if (!program) {
      return;
    }

    const imageOptions = mergeImageOptions(params.image);
    const image2Options = params.image2 && mergeImageOptions(params.image2);

    const image = imageOptions.source;

    gl!.useProgram(program);

    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const texcoordLocation = gl.getAttribLocation(program, 'a_texCoord');

    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    setRectangle(gl, 0, 0, image.width, image.height);

    gl.bindBuffer(gl.ARRAY_BUFFER, texcoordBuffer);
    gl.bufferData(
      gl.ARRAY_BUFFER,
      new Float32Array([
        0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 0.0, 1.0, 1.0
      ]),
      gl.STATIC_DRAW
    );

    texture && utilizeTexture(gl, texture, imageOptions, 0, program, 'u_image');
    texture2 &&
      image2Options &&
      utilizeTexture(gl, texture2, image2Options, 1, program, 'u_image2');

    const resolutionLocation = gl.getUniformLocation(program, 'u_resolution');

    gl.canvas.width = image.width;
    gl.canvas.height = image.height;

    gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);

    gl.clearColor(0, 0, 0, 0);
    gl.clear(gl.COLOR_BUFFER_BIT);

    gl.enableVertexAttribArray(positionLocation);
    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);

    {
      const size = 2;
      const type = gl.FLOAT;
      const normalize = false;
      const stride = 0;
      const offset = 0;
      gl.vertexAttribPointer(
        positionLocation,
        size,
        type,
        normalize,
        stride,
        offset
      );
    }

    gl.enableVertexAttribArray(texcoordLocation);

    gl.bindBuffer(gl.ARRAY_BUFFER, texcoordBuffer);

    {
      const size = 2;
      const type = gl.FLOAT;
      const normalize = false;
      const stride = 0;
      const offset = 0;
      gl.vertexAttribPointer(
        texcoordLocation,
        size,
        type,
        normalize,
        stride,
        offset
      );
    }

    gl.uniform2f(resolutionLocation, gl.canvas.width, gl.canvas.height);

    {
      const primitiveType = gl.TRIANGLES;
      const offset = 0;
      const count = 6;
      gl.drawArrays(primitiveType, offset, count);
    }
  }

  return {
    canvas,
    registerProgram,
    run
  };
}

function utilizeTexture(
  gl: WebGLRenderingContext,
  texture: WebGLTexture,
  image: Required<ImageOption>,
  channelIndex: number,
  program: WebGLProgram,
  varName = 'u_image'
) {
  gl.activeTexture(gl.TEXTURE0 + channelIndex);
  gl.bindTexture(gl.TEXTURE_2D, texture);

  gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
  gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
  gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
  gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, image.magFilter);

  gl.texImage2D(
    gl.TEXTURE_2D,
    0,
    gl.RGBA,
    gl.RGBA,
    gl.UNSIGNED_BYTE,
    image.source
  );

  const location = gl.getUniformLocation(program, varName);
  gl.uniform1i(location, channelIndex);
}

export const processor = createProcessor({
  debug: false
});

// 使用WebGL处理图片
export function processImageWithWebGL(
  image: HTMLImageElement,
  fragmentShaderSource: string = renderShader
) {
  // 获取Canvas元素和WebGL上下文
  const canvas = document.createElement('canvas');
  canvas.width = image.naturalWidth;
  canvas.height = image.naturalHeight;
  const gl = canvas.getContext('webgl', {});

  if (!gl) {
    return;
  }

  const vertexShader = gl.createShader(gl.VERTEX_SHADER);
  if (!vertexShader) {
    return;
  }
  gl.shaderSource(vertexShader, vertexShaderSource);
  gl.compileShader(vertexShader);

  const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
  if (!fragmentShader) {
    return;
  }

  gl.shaderSource(fragmentShader, fragmentShaderSource);
  gl.compileShader(fragmentShader);

  const program = gl.createProgram();
  if (!program) {
    return;
  }

  gl.attachShader(program, vertexShader);
  gl.attachShader(program, fragmentShader);
  gl.linkProgram(program);
  gl.useProgram(program);

  const positionLocation = gl.getAttribLocation(program, 'a_position');
  const texcoordLocation = gl.getAttribLocation(program, 'a_texCoord');

  const positionBuffer = gl.createBuffer();

  gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
  setRectangle(gl, 0, 0, image.width, image.height);

  const texcoordBuffer = gl.createBuffer();
  gl.bindBuffer(gl.ARRAY_BUFFER, texcoordBuffer);
  gl.bufferData(
    gl.ARRAY_BUFFER,
    new Float32Array([
      0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 0.0, 1.0, 1.0
    ]),
    gl.STATIC_DRAW
  );

  const texture = gl.createTexture();
  gl.bindTexture(gl.TEXTURE_2D, texture);

  gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
  gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
  gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
  gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);

  gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);

  const resolutionLocation = gl.getUniformLocation(program, 'u_resolution');

  gl.canvas.width = image.naturalWidth;
  gl.canvas.height = image.naturalHeight;

  gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);

  gl.clearColor(0, 0, 0, 0);
  gl.clear(gl.COLOR_BUFFER_BIT);

  gl.useProgram(program);

  gl.enableVertexAttribArray(positionLocation);

  gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);

  {
    const size = 2;
    const type = gl.FLOAT;
    const normalize = false;
    const stride = 0;
    const offset = 0;
    gl.vertexAttribPointer(
      positionLocation,
      size,
      type,
      normalize,
      stride,
      offset
    );
  }

  gl.enableVertexAttribArray(texcoordLocation);

  gl.bindBuffer(gl.ARRAY_BUFFER, texcoordBuffer);

  {
    const size = 2;
    const type = gl.FLOAT;
    const normalize = false;
    const stride = 0;
    const offset = 0;
    gl.vertexAttribPointer(
      texcoordLocation,
      size,
      type,
      normalize,
      stride,
      offset
    );
  }

  gl.uniform2f(resolutionLocation, gl.canvas.width, gl.canvas.height);

  {
    const primitiveType = gl.TRIANGLES;
    const offset = 0;
    const count = 6;
    gl.drawArrays(primitiveType, offset, count);
  }

  return canvas;
}

function compileProgram(
  gl: WebGLRenderingContext,
  vShaderSource: string,
  fShaderSource: string
) {
  const vertexShader = gl.createShader(gl.VERTEX_SHADER);
  if (!vertexShader) {
    return;
  }
  gl.shaderSource(vertexShader, vShaderSource);
  gl.compileShader(vertexShader);

  const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
  if (!fragmentShader) {
    return;
  }

  gl.shaderSource(fragmentShader, fShaderSource);
  gl.compileShader(fragmentShader);

  const program = gl.createProgram();
  if (!program) {
    return;
  }

  gl.attachShader(program, vertexShader);
  gl.attachShader(program, fragmentShader);
  gl.linkProgram(program);

  return program;
}

function setRectangle(
  gl: WebGLRenderingContext,
  x: number,
  y: number,
  width: number,
  height: number
) {
  const x1 = x;
  const x2 = x + width;
  const y1 = y;
  const y2 = y + height;
  gl.bufferData(
    gl.ARRAY_BUFFER,
    new Float32Array([x1, y1, x2, y1, x1, y2, x1, y2, x2, y1, x2, y2]),
    gl.STATIC_DRAW
  );
}
