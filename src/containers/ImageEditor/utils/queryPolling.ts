import { fetchHistoryList } from '@/api/imageEditor';

type ResultType = {
  /**
   * 状态说明
   * 1. isCanceled = true, isTimeout = true：因超时而被取消
   * 2. isCanceled = true, isTimeout = false：手动取消
   * 3. isCanceled = false, isTimeout = false: 查询成功
   * 4. 未定义 isCanceled = false, isTimeout = true
   */

  /**
   * 任务被取消
   */
  isCanceled: boolean;
  /**
   * 任务超时
   */
  isTimeout: boolean;
  /**
   * 成功后返回的数据
   */
  data: any;
};

export function queryPolling(
  msgId: string,
  interval = 3000,
  type = 'inpaint',
  timeout?: number
) {
  const deffer: any = {};
  deffer.promise = new Promise<ResultType>((resolve, reject) => {
    deffer.resolve = resolve;
    deffer.reject = reject;
  });

  let isCancel = false;

  function cancelPolling() {
    deffer.resolve({
      isCanceled: true,
      isTimeout: false,
      data: []
    });
    isCancel = true;
  }

  async function polling() {
    if (isCancel) {
      return;
    }

    try {
      const { list } = await fetchHistoryList({
        typeIds: JSON.stringify({
          [type]: msgId
        })
      });

      const status = (list[0] as any)?.status;

      if (status === 2) {
        setTimeout(polling, interval);
      } else if (status === 4) {
        deffer.resolve({
          isCanceled: false,
          isTimeout: false,
          data: list
        });
      } else {
        deffer.reject(new Error('生成失败，请重试'));
      }
    } catch (e) {
      deffer.reject(e);
    }
  }

  polling();

  if (timeout) {
    setTimeout(() => {
      isCancel = true;
      deffer.resolve({
        data: [],
        isCanceled: true,
        isTimeout: true
      });
    }, timeout);
  }

  return {
    result: deffer.promise as Promise<ResultType>,
    cancelPolling
  };
}
