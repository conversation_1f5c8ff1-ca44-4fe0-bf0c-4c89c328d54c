import { createImage } from '@/utils/cropImage';
import { processor } from './processImageWithWebGL';
import {
  getTransformBinaryValueMaskShader,
  getTransformGrayMaskShader,
  getTransformMaskShader,
  getTransformPreMaskShader
} from './shaders/transformMask';
import {
  SelectionColor,
  getSelectionColorRGBATuple
} from '../constant/selection';

enum ProgramKey {
  // 涂抹区转换为灰度图
  PAINT_TO_MASK = 'paint2mask',
  // 灰度图转换为涂抹区
  MASK_TO_PAINT = 'mask2paint',
  // 涂抹区转换为二值图
  PAINT_TO_BIO = 'paint2bio',

  PAINT_TO_PRE_MASK = 'paint2preMask'
}

const webglProcessor = processor;
webglProcessor?.registerProgram(
  ProgramKey.PAINT_TO_MASK,
  getTransformGrayMaskShader(8)
);
webglProcessor?.registerProgram(
  ProgramKey.MASK_TO_PAINT,
  getTransformMaskShader(getSelectionColorRGBATuple(SelectionColor.Positive))
);
webglProcessor?.registerProgram(
  ProgramKey.PAINT_TO_BIO,
  getTransformBinaryValueMaskShader(0.02)
);
webglProcessor?.registerProgram(
  ProgramKey.PAINT_TO_PRE_MASK,
  getTransformPreMaskShader(2)
);

export async function getGrayMaskCanvas(mask: string) {
  const img = await createImage(mask);
  // return processImageWithWebGL(img, getTransformGrayMaskShader(8));
  processor?.run(ProgramKey.PAINT_TO_MASK, { image: img });

  return processor?.canvas;
}

export function getGrayMaskWithCanvas(maskCanvas: HTMLCanvasElement) {
  processor?.run(ProgramKey.PAINT_TO_MASK, { image: maskCanvas });

  return processor?.canvas;
}

export function paintMaskFromGray(
  maskImage: HTMLImageElement,
  targetCanvas?: HTMLCanvasElement
) {
  // return processImageWithWebGL(
  //   maskImage,
  //   getTransformMaskShader(getSelectionColorRGBATuple(SelectionColor.Positive))
  // );

  processor?.run(ProgramKey.MASK_TO_PAINT, { image: maskImage });

  return targetCanvas && processor?.canvas
    ? cloneCanvas(processor.canvas, targetCanvas)
    : processor?.canvas;
}

export function cloneCanvas(
  originalCanvas: HTMLCanvasElement,
  targetCanvas: HTMLCanvasElement = document.createElement('canvas')
) {
  const targetContext = targetCanvas.getContext('2d');

  targetCanvas.width = originalCanvas.width;
  targetCanvas.height = originalCanvas.height;

  targetContext?.drawImage(originalCanvas, 0, 0);

  return targetCanvas;
}

export async function getGrayMaskBlob(mask: string, type = 'image/jpeg') {
  const canvas = await getGrayMaskCanvas(mask);
  if (!canvas) {
    return null;
  }

  return canvasToBlob(canvas, type);
}

export async function getGrayMaskDataURL(mask: string, type = 'image/jpeg') {
  const canvas = await getGrayMaskCanvas(mask);
  if (!canvas) {
    return null;
  }

  return canvas.toDataURL(type, 1);
}

export function canvasToBlob(
  canvas: HTMLCanvasElement,
  type = 'image/jpeg',
  quality = 1
) {
  return new Promise<Blob>((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject('blob is null');
        }
      },
      type,
      quality
    );
  });
}

export function getPreMaskWithCanvas(preMaskCanvas: HTMLCanvasElement) {
  processor?.run(ProgramKey.PAINT_TO_PRE_MASK, { image: preMaskCanvas });

  return processor?.canvas;
}

/**
 * 获取二值mask图
 * @param mask
 * @param type
 */
export async function getBioImageBlob(mask: string, type = 'image/jpeg') {
  const img = await createImage(mask);

  processor?.run(ProgramKey.PAINT_TO_BIO, { image: img });

  const canvas = processor?.canvas;

  if (!canvas) {
    return null;
  }
  // document.body.appendChild(await createImage(canvas.toDataURL()));
  return canvasToBlob(canvas, type);
}
