/** 三方依赖 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Spin } from 'antd';
/** 项目内部依赖 */
import { Layout } from '@/layouts';
import { ErrorBoundary } from '@/components';
import { TaskCategory } from '@/api/types/imageEditor';
import { fitImageToFrame } from '../../utils/imageEditor';
/** 组件依赖 */
import Header from './components/Header';
import InitState from './components/InitState';
import FeatureArea, { useFeatureArea } from './components/FeatureArea';
import SelectionControls from './components/SelectionControls';
import ChangePicture from './components/FeatureArea/ChangePicture';
import ExtendPicture from './components/FeatureArea/ExtendPicture/ExtendPicture';
import GraffitiPicture from './components/FeatureArea/GraffitiPicture';
import CompositePicture from './components/FeatureArea/CompositePicture';
import { CommonUpload } from './components/CommonUpload';
import { LayerDrawer } from './components/LayerDrawer';
import CustomCursor from './components/CustomCursor';
/** hooks */
import useStore from '@/hooks/useStore';
import { useHasPainted } from './hooks/useHasPainted';
import { getProjectInfo } from '@/api/imageEditor';
import { useAuthoringPageEnter, useSearchParams } from '@/hooks';
import { AppModuleParam } from '@/types';
import { useOverviewTutorials } from './tutorials/overview';
import { useInitUploadTutorials } from './tutorials/init';

import LayerEditLayout from './components/LayerEditLayout';
import Synopsis from './components/Synopsis';

/**图标与图片 */
import {
  BecomeBackgroundBold,
  BrushAndBoardBold,
  LosslessAmplificationBold,
  BrushBold
} from '@meitu/candy-icons';
/**样式 */
import styles from './index.module.less';
import { AppModule, generateRouteTo } from '@/services';
import { isChrome } from './utils/equipment';
import { loadImage, optimizeImage, resetImageUrl } from './utils/image';
import { isString } from 'lodash';
import {
  disableAutoResizeTutorials,
  enableAutoResizeTutorials
} from './tutorials/refresh';
const maxLeaveTime = 1000 * 60 * 3;

export const ImageEditor = observer(() => {
  const location = useLocation();
  const navigate = useNavigate();
  const editorStore = useStore('EditorStore');
  const container = useRef<HTMLDivElement>(null);
  const extendContainer = useRef<HTMLDivElement>(null);
  const [extendActive, setExtendActive] = useState<boolean>(false);
  const [initChange, setInitChange] = useState<boolean>(false);
  const [accept, setAccept] = useState<string>('.png,.jpg,.jpeg,.bmp');
  const [leaveTime, setLeaveTime] = useState<number>(0);

  const queryParams = new URLSearchParams(location.search);
  let projectId = queryParams.get('projectId') || queryParams.get('project_id');

  const {
    activeKey,
    editLeftMode,
    onFeatureChangeClick,
    isOpen
    // onOpenChange
  } = useFeatureArea();

  const { wheeEditorPlugin } = editorStore.editor;

  // 页面进入埋点
  useAuthoringPageEnter(AppModuleParam.ImageEditor);

  // 切换功能模块
  const switchProxy = async (key: string) => {
    // 从改图切换到其他功能时 需要用户确认是否清空涂抹区
    if (key && key !== activeKey && activeKey === TaskCategory.inpaint) {
      const exit = await editorStore.confirmExitChangePictureHandler(false);
      if (!exit) {
        return;
      }
    }
    if (key && key !== activeKey && activeKey === TaskCategory.graffiti) {
      const exit = await editorStore.submitGraffitiPicture();
      if (!exit) {
        return;
      }
    }
    // 切换合成，重置鼠标样式
    if (key === TaskCategory.compound) {
      editorStore.setMouseStyleType({
        selectionParams: null,
        selectionWay: null
      });
    }
    onFeatureChangeClick(key);
  };

  const browserVisibleHandler = useCallback(() => {
    if (document.hidden) {
      setLeaveTime(Date.now());
    } else {
      if (Date.now() - leaveTime > maxLeaveTime) {
        // 超过3分钟，刷新页面
        editorStore.editor.wheeEditorPlugin.plugin?.initAllPosition();
      }
    }
  }, [editorStore.editor.wheeEditorPlugin.plugin, leaveTime]);

  useEffect(() => {
    document.addEventListener('visibilitychange', browserVisibleHandler);
    return () => {
      document.removeEventListener('visibilitychange', browserVisibleHandler);
    };
  }, [browserVisibleHandler]);

  useEffect(() => {
    const stopKeyDown = (event: any) => {
      if (
        (event.ctrlKey || event.metaKey) &&
        (event.key === '+' || event.key === '-' || event.key === '=')
      ) {
        event.preventDefault();
      }
    };
    document.addEventListener('keydown', stopKeyDown);
    return () => {
      document.removeEventListener('keydown', stopKeyDown);
    };
  }, []);

  useEffect(() => {
    editorStore.setSwitchFeature(onFeatureChangeClick);
    editorStore.setSetExtendActive(setExtendActive);
    editorStore.setMainEditorKey(activeKey);
    editorStore.editor.app.keyCommand.restore();
    if (activeKey === TaskCategory.extend) {
      editorStore.headerBtnConfig = {
        addImg: false,
        moveStage: false,
        select: false,
        history: false,
        scaleStage: false
      };
      editorStore.setDisableDownload(true);
      // editorStore.setProxyUndo(() => {
      //   editorStore.extendEditor?.historyPlugin.undo();
      // });
      // editorStore.setProxyRedo(() => {
      //   editorStore.extendEditor?.historyPlugin.redo();
      // });

      // const selectNode =
      //   editorStore.editor.selectorPlugin?.selector?.selectedNodes[0];
      // if (selectNode) {
      //   editorStore.setSelectExtendLayerId(selectNode.config.id);
      // } else {
      //   editorStore.setSelectExtendLayerId(undefined);
      // }
      editorStore.setSelectExtendLayerId(undefined);
      setExtendActive(true);
      setTimeout(() => {
        if (extendContainer.current) {
          editorStore.initExtendEditor(extendContainer.current);
          editorStore.editor.app.keyCommand.stop();
        }
      }, 200);
      // // dom挂在完之后，初始化扩图编辑器
      // setTimeout(() => {
      //   editorStore.setProxyHistory(editorStore?.extendEditor!.historyPlugin);
      // }, 260);
    } else if (activeKey === TaskCategory.graffiti) {
      editorStore.setDisableSelect(false);
      setExtendActive(false);
      editorStore.destroyExtendEditor();
      editorStore.setDisableDownload(false);
      editorStore.enableGraffitiEditor();
      editorStore.editor.historyPlugin.disable(); //禁用主编译器历史
      editorStore.headerBtnConfig = {
        addImg: true,
        moveStage: true,
        select: true,
        history: true,
        scaleStage: true
      };
    } else if (activeKey === TaskCategory.inpaint) {
      editorStore.headerBtnConfig = {
        addImg: true,
        moveStage: true,
        select: true,
        history: true,
        scaleStage: true
      };
      setExtendActive(false);
      editorStore.destroyExtendEditor();
      editorStore.setDisableDownload(false);
      editorStore.disableGraffitiEditor();
    } else {
      editorStore.editor.historyPlugin.enable(); //开启主编译器历史
      editorStore.setProxyUndo(() => {
        editorStore.editor.historyPlugin.undo();
      });
      editorStore.setProxyRedo(() => {
        editorStore.editor.historyPlugin.redo();
      });
      editorStore.setProxyHistory(editorStore.editor.historyPlugin);
      editorStore.headerBtnConfig = {
        addImg: true,
        moveStage: true,
        select: true,
        history: true,
        scaleStage: true
      };
      setExtendActive(false);
      editorStore.destroyExtendEditor();
      editorStore.setDisableDownload(false);
      editorStore.disableGraffitiEditor();
      editorStore.editor.historyPlugin.enable();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey, editorStore]);

  useEffect(() => {
    editorStore.editor.app?.mount(container.current!);
    editorStore.editor.wheeEditorPlugin.plugin?._resizeEditor();
    // 初始化undo redo
    editorStore.setProxyUndo(() => {
      editorStore.editor.historyPlugin.undo();
    });
    editorStore.setProxyRedo(() => {
      editorStore.editor.historyPlugin.redo();
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    editorStore,
    editorStore.editor.app,
    editorStore.editor.wheeEditorPlugin.plugin
  ]);

  const searchParams = useSearchParams();

  useEffect(() => {
    if (isChrome()) {
      setAccept((val) => (val += ',.webp'));
    }
    if (projectId) {
      if (initChange) return;
      if (+projectId === 0) return;
      getProjectInfo({ projectId: +projectId, withEditorParams: true })
        .then((res) => {
          // navigate(
          //   generateRouteTo(AppModule.ImageEditor, {
          //     projectId: res?.projectId
          //   }),
          //   {
          //     replace: true
          //   }
          // );
          let editorParams = JSON.parse(res?.editorParams as string);
          let layers;
          if (isString(editorParams.layers)) {
            layers = JSON.parse(editorParams.layers);
          } else {
            layers = editorParams.layers;
          }

          editorStore.setProjectInfo({
            id: res?.projectId,
            width: editorParams.width,
            height: editorParams.height,
            currentVersion: res?.version,
            picUrl: res?.picUrl
          });
          // 更新全局
          if (Array.isArray(layers)) {
            editorStore.initTasksWithLayers(layers);
          }
          loadImage(...layers.map((item: any) => item.url));

          editorStore.editor.wheeEditorPlugin.plugin?.init({
            width: editorParams.width,
            height: editorParams.height,
            layers: layers.map((item: any) => ({
              ...item,
              url: optimizeImage(item.url, {
                width: editorStore.maxEditorWidth,
                height: editorStore.maxEditorHeight
              })
            }))
          });

          editorStore.editor.invertSelectPlugin.setSelectArea({
            width:
              editorStore.editor.wheeEditorPlugin.plugin?.getDrawAreaDetail()
                ?.width || 0,
            height:
              editorStore.editor.wheeEditorPlugin.plugin?.getDrawAreaDetail()
                ?.height || 0,
            x:
              editorStore.editor.wheeEditorPlugin.plugin?.getDrawAreaDetail()
                ?.x || 0,
            y:
              editorStore.editor.wheeEditorPlugin.plugin?.getDrawAreaDetail()
                ?.y || 0
          });
          editorStore.calcScaleWheeEditor({
            width: editorParams.width,
            height: editorParams.height
          });

          editorStore.updateInit(false);

          editorStore.setUploadImagePromise(
            Promise.resolve({
              url: res.picUrl.split('?')[0] ?? '',
              previewUrl: res.picUrl
            })
          );
        })
        .catch((error) => {
          // console.log(error, 'error')
          navigate(generateRouteTo(AppModule.ImageEditor), {
            replace: true
          });
        });
    } else {
      // 从主站跳转到编辑器，会携带editorImageUrl
      if (searchParams?.editorImageUrl) {
        editorStore.initProjectImageLayer(searchParams?.editorImageUrl);
      } else {
        editorStore.updateInit(true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (editorStore.projectInfo.id === 0) return;
    navigate(
      generateRouteTo(AppModule.ImageEditor, {
        projectId: editorStore.projectInfo.id
      }),
      {
        replace: true
      }
    );
    setInitChange(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editorStore.projectInfo.id, navigate]);

  const { hasPainted, promiseRef: hasPaintedPromiseRef } = useHasPainted({
    app: editorStore.editor.app,
    wheeEditorPlugin: editorStore.editor.wheeEditorPlugin,
    enabled: activeKey === TaskCategory.inpaint
  });

  useEffect(() => {
    editorStore.updateSurplus();
  }, [editorStore]);
  // useEffect(() => {
  //   // 设置编辑器 konva 编辑器
  //   Konva.pixelRatio = 1;
  //   return () => {
  //     Konva.pixelRatio = 2;
  //   };
  // }, []);

  const hasNotFinish = editorStore.isInit && !projectId;
  // 初始化上传时的新手引导
  useInitUploadTutorials({
    editorStore,
    initFinish: !hasNotFinish
  });
  // 功能概览引导
  useOverviewTutorials({
    editorStore,
    initFinish: !hasNotFinish,
    onChangeFeature: onFeatureChangeClick,
    hasPaintedPromiseRef
  });
  useEffect(() => {
    enableAutoResizeTutorials();

    return () => {
      disableAutoResizeTutorials();
    };
  }, []);

  return (
    <ErrorBoundary>
      <Spin
        spinning={editorStore.globalLoading}
        size={'large'}
        fullscreen
        style={{ pointerEvents: 'unset' }}
        tip="加载中，请稍后..."
      />
      <Layout
        theme="app"
        className={styles.imageEditor}
        header={<Header />}
        style={{ width: '100%', height: '100%', overflow: 'hidden' }}
      >
        <Layout.Sider width={0} noStyle />
        {/* 单图层编辑器 */}

        <LayerEditLayout
          activeKey={editLeftMode}
          onFeatureChangeClick={onFeatureChangeClick}
        />

        {/* 初始化上传 */}
        {editorStore.isInit && !projectId ? <InitState /> : <></>}
        {/* 左侧参数栏 */}
        <FeatureArea
          items={[
            {
              key: TaskCategory.inpaint,
              icon: <BrushAndBoardBold />,
              label: '局部修改',
              featurePanel: (
                <ChangePicture hasPaintedPromiseRef={hasPaintedPromiseRef} />
              )
            },
            {
              key: TaskCategory.extend,
              icon: <LosslessAmplificationBold />,
              label: '扩图',
              featurePanel: <ExtendPicture />
            },
            {
              key: TaskCategory.compound,
              icon: <BecomeBackgroundBold />,
              label: 'AI合成',
              featurePanel: <CompositePicture />
            },
            {
              key: TaskCategory.graffiti,
              icon: <BrushBold />,
              label: '涂鸦',
              featurePanel: (
                <GraffitiPicture hasPaintedPromiseRef={hasPaintedPromiseRef} />
              )
            }
          ]}
          activeKey={activeKey}
          onFeatureChangeClick={switchProxy}
          isOpen={isOpen}
          // onOpenChange={onOpenChange}
        />
        {/* 中间区域画布 */}
        {/* <div
          style={{
            width: isOpen && activeKey === TaskCategory.extend ? '380px' : '0'
          }}
        /> */}
        <Layout.Content noStyle>
          <CommonUpload
            className="editor-layer-upload"
            openFileDialogOnClick={false}
            customUploadedToast={({ successTotal, reviewErrorTotal }) => {
              if (successTotal) return '';

              if (reviewErrorTotal) return '请重新上传合规的图片';
            }}
            onClick={() => {}}
            accept={accept}
            supports={['image/jpeg', 'image/png', 'image/jpg', 'image/bmp']}
            limit={30}
            onFinish={(v) => {
              editorStore.editor?.selectorPlugin.selector?.cancelSelect();
              if (!v[0]?.previewUrl) return;
              // console.log(v[0]?.previewUrl);
              // onUploaded(v[0]?.previewUrl);
              const image = document.createElement('img');

              // 如果是webp格式的图片，转换成jpg格式
              // 文档：https://cf.meitu.com/confluence/pages/viewpage.action?pageId=257462561

              const path = v[0]?.previewUrl.split('?')?.[0];
              const query = v[0]?.previewUrl.split('?')?.[1];
              if (path.endsWith('.webp')) {
                v[0].previewUrl = path + '?imageMogr2/format/jpg&' + query;
              }
              image.src = resetImageUrl(
                optimizeImage(v[0]?.previewUrl || '', {
                  width: editorStore.maxEditorWidth,
                  height: editorStore.maxEditorHeight
                })
              );
              image.crossOrigin = 'Anonymous';

              image.onload = () => {
                const { width, height } =
                  editorStore.editor.wheeEditorPlugin.plugin?.getDrawAreaDetail() || {
                    width: 0,
                    height: 0
                  };
                const { width: maxWidth, height: maxHeight } = fitImageToFrame(
                  width,
                  height,
                  image.width,
                  image.height
                );

                editorStore.editor.app.setTool(editorStore.editor.imageTooler);
                editorStore.editor?.imageTooler.insertImage(
                  image,
                  image.src,
                  {
                    width: maxWidth,
                    height: maxHeight
                  },
                  {
                    x: (width - maxWidth) / 2 + maxWidth / 2,
                    y: (height - maxHeight) / 2 + maxHeight / 2
                  }
                );
              };
            }}
          >
            <div
              style={{ width: '100%', height: '100%', position: 'relative' }}
            >
              <div ref={container} className="editor-container"></div>
              {extendActive && (
                <div ref={extendContainer} className="extend-container"></div>
              )}
            </div>
          </CommonUpload>
        </Layout.Content>
        {/* 中间区底部操作 */}
        <CustomCursor />
        {editorStore.synopsisConfig && (
          <Synopsis
            x={editorStore.synopsisConfig.x}
            y={editorStore.synopsisConfig.y}
            hidden={editorStore.synopsisConfig.hidden}
            type={editorStore.synopsisConfig.type}
          />
        )}

        {hasPainted && !editorStore.layerEditor && (
          <SelectionControls
            onClearSelection={() => wheeEditorPlugin?.plugin?.clearMask()}
            // onInvert={() => invertSelectPlugin?.invertSelectHandler()}
          />
        )}
        {/* 右侧图层面板 */}
        <LayerDrawer />
      </Layout>
    </ErrorBoundary>
  );
});

export { ImageEditor as Component };
