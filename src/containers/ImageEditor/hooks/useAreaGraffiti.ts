import {
  App,
  BaseShape,
  CanvasNodeEventName,
  CanvasZoomEventName,
  LineCap,
  LineJoin,
  ShapeConfig,
  ShapeType
} from '@/editor/core/src';
import { SelectionWay } from '../components/AreaSelection';
import { useCallback, useEffect, useRef, useState } from 'react';
import { AreaSelectionMode } from '../components/AreaSelection/ModeSelection';
import {
  EraserShape,
  EraserTool,
  LassoTool,
  LineTool,
  RectTool,
  EllipseTool
} from '@/editor/utils/src';
import { SelectorPlugin } from '@/editor/wukong-selector-plugin/src';
import { WheeEditorPlugin } from '@/editor/wukong-whee-editor-plugin/src';
import useStore from '@/hooks/useStore';
import message from '../components/Toast';
import { useAccountProfile } from '@/hooks';
// import { SelectionColor } from '../constant/selection';
import { SmartSelectionToolPlugin } from '../plugins/SmartSelectionToolPlugin';
export type ToolPluginsType = {
  rectTool: RectTool;
  eraserTool: EraserTool;
  lineTool: LineTool;
  lassoTool: LassoTool;
  smartSelectionTool: SmartSelectionToolPlugin;
  ellipseTool: EllipseTool;
};

//#region 选择方式的参数

// 选择模式：是增加选区还是删除选区
type ParamsAreaSelectionMode = {
  paintWidth?: number | null;
  fillColor?: string;
  globalCompositeOperation?: GlobalCompositeOperation;
};

// 涂抹的尺寸
type ParamsPaintWidth = {
  paintWidth: number;
};

export type GraffitiPictureWay =
  | SelectionWay.Ellipse
  | SelectionWay.Eraser
  | SelectionWay.Paint
  | SelectionWay.Box;

export type AreaSelectionParams<T extends GraffitiPictureWay> =
  T extends SelectionWay.Box
    ? ParamsAreaSelectionMode
    : T extends SelectionWay.Lasso
    ? ParamsAreaSelectionMode
    : T extends SelectionWay.Paint
    ? ParamsAreaSelectionMode & ParamsPaintWidth
    : T extends SelectionWay.Smart
    ? ParamsAreaSelectionMode
    : T extends SelectionWay.Ellipse
    ? ParamsAreaSelectionMode
    : T extends SelectionWay.Eraser
    ? ParamsAreaSelectionMode & ParamsPaintWidth
    : never;

//#endregion

type InstallCleanup = () => void;
type SetParamsType = (params: any) => void;
type Installer = (options: {
  app: App;
  toolPlugins: ToolPluginsType;
  wheeEditorPlugin: WheeEditorPlugin;
  hasPaintedPromiseRef: React.MutableRefObject<Promise<boolean>>;
}) => {
  cleanup: InstallCleanup;
  setParams: SetParamsType;
};

const ToolMapping: Record<GraffitiPictureWay, Installer> = {
  [SelectionWay.Box]: ({ app, toolPlugins }) => {
    function setParams(params: AreaSelectionParams<SelectionWay.Box>) {
      toolPlugins.rectTool.registerConfig({
        ...toolPlugins.rectTool.config,
        fill: params.fillColor as string,
        globalCompositeOperation: 'source-over'
      });

      app.setTool(toolPlugins.rectTool);
      app.lockAllShapes();
    }

    function cleanup() {
      app.destroyTool();
    }

    return {
      setParams,
      cleanup
    };
  },
  [SelectionWay.Paint]: ({ app, toolPlugins }) => {
    let currentParams: null | AreaSelectionParams<SelectionWay.Paint> = null;
    function setParams(params: AreaSelectionParams<SelectionWay.Paint>) {
      currentParams = params;
      const lineTool = toolPlugins.lineTool;
      params.fillColor &&
        lineTool.registerConfig({
          ...lineTool.config,
          stroke: params.fillColor
        });
      params.globalCompositeOperation &&
        lineTool.registerConfig({
          ...lineTool.config,
          globalCompositeOperation: params.globalCompositeOperation
        });
      params.paintWidth &&
        lineTool.registerConfig({
          ...lineTool.config,
          strokeWidth: params.paintWidth / app.stage.scaleX()
        });
      app.setTool(lineTool);
      app.lockAllShapes();
    }

    function handleZoomChange() {
      const params = currentParams;
      const lineTool = toolPlugins.lineTool;
      typeof params?.paintWidth === 'number' &&
        lineTool.setStrokeWidth(params.paintWidth / app.stage.scaleX());
    }

    app.on(CanvasZoomEventName.end, handleZoomChange);

    function cleanup() {
      app.destroyTool();
      app.off(CanvasZoomEventName.end, handleZoomChange);
    }

    return {
      setParams,
      cleanup
    };
  },
  [SelectionWay.Ellipse]: ({ app, toolPlugins }) => {
    function setParams(params: AreaSelectionParams<SelectionWay.Ellipse>) {
      const ellipseTool = toolPlugins.ellipseTool;
      params.fillColor &&
        ellipseTool.registerConfig({
          ...ellipseTool.config,
          fill: params.fillColor
        });
      params.globalCompositeOperation &&
        ellipseTool.registerConfig({
          ...ellipseTool.config,
          globalCompositeOperation: 'source-over'
        });
      app.setTool(ellipseTool);
      app.lockAllShapes();
    }

    function cleanup() {
      app.destroyTool();
    }

    return {
      setParams,
      cleanup
    };
  },
  [SelectionWay.Eraser]: ({ app, toolPlugins }) => {
    let currentParams: null | AreaSelectionParams<SelectionWay.Eraser> = null;
    function setParams(params: AreaSelectionParams<SelectionWay.Eraser>) {
      currentParams = params;
      const eraserTool = toolPlugins.eraserTool;
      eraserTool.registerConfig({
        shape: EraserShape.Line,
        globalCompositeOperation: 'destination-out',
        lineCap: LineCap.round,
        lineJoin: LineJoin.round,
        strokeWidth: params.paintWidth / app.stage.scaleX(),
        listening: false
      });

      app.setTool(eraserTool);
      app.lockAllShapes();
    }

    function handleZoomChange() {
      const params = currentParams;
      if (!params) {
        return;
      }
      const eraserTool = toolPlugins.eraserTool;
      eraserTool.registerConfig({
        shape: EraserShape.Line,
        globalCompositeOperation: 'destination-out',
        lineCap: LineCap.round,
        lineJoin: LineJoin.round,
        strokeWidth: params.paintWidth / app.stage.scaleX(),
        listening: false
      });
    }
    app.on(CanvasZoomEventName.end, handleZoomChange);
    function cleanup() {
      app.destroyTool();
      app.off(CanvasZoomEventName.end, handleZoomChange);
    }
    return {
      setParams,
      cleanup
    };
  }
};

const TipsMapping: Record<GraffitiPictureWay, string> = {
  [SelectionWay.Box]: '请框选想要修改的区域',
  [SelectionWay.Paint]: '请框选想要修改的区域',
  [SelectionWay.Ellipse]: '请框选想要修改的区域',
  [SelectionWay.Eraser]: '请框选想要修改的区域'
};

type UseAreaSelectionOptions = {
  app: App;
  toolPlugins: ToolPluginsType;
  selectorPlugin: SelectorPlugin;
  wheeEditorPlugin: WheeEditorPlugin;
  hasPaintedPromiseRef: React.MutableRefObject<Promise<boolean>>;
};

export function useAreaGraffiti({
  app,
  toolPlugins,
  selectorPlugin,
  wheeEditorPlugin,
  hasPaintedPromiseRef
}: UseAreaSelectionOptions) {
  const [selectionWay, setSelectionWay] = useState<GraffitiPictureWay | null>(
    null
  );
  const [selectionParams, setSelectionParams] = useState<
    AreaSelectionParams<GraffitiPictureWay>
  >({});

  const setParamsRef = useRef<null | SetParamsType>(null);
  // console.log('params', selectionParams);

  const editorStore = useStore('EditorStore');
  const { setMouseStyleType } = editorStore;

  useAreaSelectionTips({
    app,
    selectionWay,
    hasPaintedPromiseRef
  });

  useEffect(() => {
    if (!selectionWay) {
      return;
    }

    const install = ToolMapping[selectionWay];
    const { cleanup, setParams } = install({
      app,
      toolPlugins,
      wheeEditorPlugin,
      hasPaintedPromiseRef
    });
    setParamsRef.current = setParams;
    app.lockAllShapes();
    return () => {
      cleanup();
      app.unlockAllShapes();
      setParamsRef.current = null;
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [app, toolPlugins, selectionWay, selectorPlugin, wheeEditorPlugin]);

  // //监听鼠标状态 更新鼠标样式及轮廓光显隐逻辑
  useEffect(() => {
    setMouseStyleType({
      selectionParams: AreaSelectionMode.Add,
      selectionWay,
      paintWidth: selectionParams.paintWidth,
      fillColor:
        selectionWay === SelectionWay.Eraser
          ? 'rgba(255, 255, 255, 0)'
          : selectionParams.fillColor
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectionParams, selectionWay]);

  useEffect(() => {
    setParamsRef.current?.(selectionParams);
  }, [selectionParams]);

  useEffect(() => {
    function reset() {
      setSelectionWay(null);
      app.destroyTool();
    }

    app.on(CanvasNodeEventName.select, reset);

    return () => {
      app.off(CanvasNodeEventName.select, reset);
    };
  }, [app]);

  return {
    selectionWay,
    onSelectionWayChange: setSelectionWay,
    selectionParams: selectionWay && selectionParams,
    onSelectionParamsChange: setSelectionParams
  };
}

type UseAreaSelectionTipsOptions = {
  app: App;
  selectionWay: GraffitiPictureWay | null;
  hasPaintedPromiseRef: React.MutableRefObject<Promise<boolean>>;
};
function useAreaSelectionTips({
  app,
  selectionWay,
  hasPaintedPromiseRef
}: UseAreaSelectionTipsOptions) {
  const { accountProfile } = useAccountProfile();
  const userId = accountProfile?.id ?? '';

  const editorStore = useStore('EditorStore');
  const projectId = editorStore.projectInfo.id;

  const getTipsKey = useCallback(
    () => `${userId}:selections:tips:${selectionWay}`,
    [userId, selectionWay]
  );

  const hasTipped = useCallback(() => {
    const tipKey = getTipsKey();
    return !!localStorage.getItem(tipKey);
  }, [getTipsKey]);

  useEffect(() => {
    if (!selectionWay) {
      return;
    }

    if (hasTipped() || !userId || !projectId) {
      return;
    }

    const tipsContent = TipsMapping[selectionWay];

    let isIgnore = false;
    let closeTips: null | (() => void) = null;
    hasPaintedPromiseRef.current.then((hasPainted) => {
      if (!hasPainted || isIgnore) {
        return;
      }

      const { destroy } = message({
        type: 'info',
        content: tipsContent
      });

      closeTips = destroy;
    });

    // 涂抹之后关闭toast
    function closeTipsWhenPaint(payload: { nodes: BaseShape<ShapeConfig>[] }) {
      if (!closeTips) {
        return;
      }

      const { nodes } = payload;
      for (const n of nodes) {
        if (n.config.type !== ShapeType.Image) {
          closeTips();
          app.off(CanvasNodeEventName.add, closeTipsWhenPaint);
          localStorage.setItem(getTipsKey(), 'true');
        }
      }
    }

    app.on(CanvasNodeEventName.add, closeTipsWhenPaint);

    return () => {
      isIgnore = true;
      closeTips?.();
      app.off(CanvasNodeEventName.add, closeTipsWhenPaint);
    };
  }, [
    selectionWay,
    hasTipped,
    getTipsKey,
    app,
    projectId,
    userId,
    hasPaintedPromiseRef
  ]);
}
