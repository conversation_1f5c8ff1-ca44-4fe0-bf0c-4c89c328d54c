import { useEffect, useRef } from 'react';
import message, { ToastBack } from '../components/Toast';
import { merge } from 'lodash';

/**
 * 在组件卸载时 自动关闭toast
 * @returns
 */
export function useAutoCloseToast() {
  const toastHandles = useRef<Set<ToastBack>>(new Set());

  const wrappedMessage: typeof message = function (props) {
    let handle = null as null | ToastBack;
    const onClose = props.onClose;
    function handleClose() {
      onClose?.();
      handle && toastHandles.current?.delete(handle);
    }

    handle = message(merge(props, { onClose: handleClose }));
    toastHandles && toastHandles.current?.add(handle);
    return handle;
  };
  const clearTost = () => {
    const handles = toastHandles.current;
    handles.forEach((handle) => {
      handle.destroy();
    });
    handles.clear();
  };
  useEffect(() => {
    return () => {
      clearTost();
    };
  }, []);

  return {
    message: wrappedMessage,
    clearTost
  };
}
