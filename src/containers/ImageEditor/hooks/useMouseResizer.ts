import { useCallback, useEffect, useRef } from 'react';

const controllerStyle = `
    position: absolute;
    left: 0;
    cursor: row-resize;
    height: 5px;
    top: 0;
    width: 100%;
`;
export function useMouseResize(
  container: HTMLDivElement | null,
  options: {
    mouseDownHandler: () => void;
    mouseUpHandler: () => void;
    contentBox: HTMLDivElement;
  },
  maxHeight?: number
) {
  const max_h = useRef<number>(maxHeight || 0);
  const y = useRef<number>(0);
  const h = useRef<number>(0);
  const h_val = useRef<number>(0);
  const parentHeight = useRef<number>(0);
  const mouseMoveHandler = useCallback(
    (e: MouseEvent) => {
      if (!container?.parentElement) return;
      const dy = e.clientY - y.current;
      const val = Math.min(max_h.current, h.current + -dy);
      h_val.current = val / parentHeight.current;
      (container as HTMLDivElement).style.height = `${h_val.current * 100}%`;
      const rect = container.getBoundingClientRect();
      container.parentElement.style.paddingBottom = `${rect.height}px`;
      if (options.contentBox) {
        options.contentBox.style.height = `100%`;
      }
    },
    [container, options.contentBox]
  );
  const mouseUpHandler = useCallback(() => {
    options.mouseUpHandler();
    document.removeEventListener('mousemove', mouseMoveHandler);
    document.removeEventListener('mouseup', mouseUpHandler);
  }, [mouseMoveHandler, options]);
  const mouseDownHandler = useCallback(
    (e: MouseEvent) => {
      options.mouseDownHandler();
      y.current = e.clientY;
      const styles = window.getComputedStyle(container as HTMLDivElement);
      h.current = parseInt(styles.height, 10);
      h_val.current = h.current / parentHeight.current;
      document.addEventListener('mousemove', mouseMoveHandler);
      document.addEventListener('mouseup', mouseUpHandler);
    },
    [container, mouseMoveHandler, mouseUpHandler, options]
  );
  const resizeHandler = useCallback(() => {
    if (container?.parentElement) {
      const rect = container.getBoundingClientRect();
      container.parentElement.style.paddingBottom = `${rect.height}px`;
    }
  }, [container]);
  useEffect(() => {
    if (!container) return;
    if (!maxHeight && container.parentElement) {
      max_h.current =
        container.parentElement.getBoundingClientRect().height * (2 / 3);
      parentHeight.current =
        container.parentElement.getBoundingClientRect().height;
    } else {
      return;
    }
    container.style.position = 'absolute';
    container.style.bottom = '0';
    container.style.paddingBottom = '15px';
    container.style.paddingTop = '15px';
    const resize = document.createElement('div');
    resize.style.cssText = controllerStyle;
    container.appendChild(resize);
    resizeHandler();
    resize.addEventListener('mousedown', mouseDownHandler);
    window.addEventListener('resize', resizeHandler);
    return () => {
      container?.removeEventListener('mousedown', mouseDownHandler);
      container?.removeChild(resize);
      window.removeEventListener('resize', resizeHandler);
      if (container!.parentElement) {
        container.parentElement.style.paddingBottom = `${container.style.height}px`;
      }
    };
  }, [container, maxHeight, mouseDownHandler, resizeHandler]);
}
