import {
  App,
  BaseSha<PERSON>,
  CanvasNodeEventName,
  CanvasZoomEventName,
  LineCap,
  LineJoin,
  ShapeConfig,
  ShapeType
} from '@/editor/core/src';
import { SelectionWay } from '../components/AreaSelection';
import { useCallback, useEffect, useRef, useState } from 'react';
import { AreaSelectionMode } from '../components/AreaSelection/ModeSelection';
import {
  EraserShape,
  EraserTool,
  LassoTool,
  LineTool,
  RectTool,
  EllipseTool
} from '@/editor/utils/src';
import { SelectorPlugin } from '@/editor/wukong-selector-plugin/src';
import { WheeEditorPlugin } from '@/editor/wukong-whee-editor-plugin/src';
import useStore from '@/hooks/useStore';
import message from '../components/Toast';
import { useAccountProfile } from '@/hooks';
import { LASSO_TOOL_STROKE_WIDTH, SelectionColor } from '../constant/selection';
import { SmartSelectionToolPlugin } from '../plugins/SmartSelectionToolPlugin';

export type ToolPluginsType = {
  rectTool: RectTool;
  eraserTool: EraserTool;
  lineTool: LineTool;
  lassoTool: LassoTool;
  smartSelectionTool: SmartSelectionToolPlugin;
  ellipseTool: EllipseTool;
};

//#region 选择方式的参数

// 选择模式：是增加选区还是删除选区
type ParamsAreaSelectionMode = {
  selectionMode: AreaSelectionMode | null;
  paintWidth?: number | null;
  fillColor?: string;
};

// 涂抹的尺寸
type ParamsPaintWidth = {
  paintWidth: number;
};

export type ChangePictureSelectionWay =
  | SelectionWay.Smart
  | SelectionWay.Lasso
  | SelectionWay.Paint
  | SelectionWay.Box;

export type AreaSelectionParams<T extends ChangePictureSelectionWay> =
  T extends SelectionWay.Box
    ? ParamsAreaSelectionMode
    : T extends SelectionWay.Lasso
    ? ParamsAreaSelectionMode
    : T extends SelectionWay.Paint
    ? ParamsAreaSelectionMode & ParamsPaintWidth
    : T extends SelectionWay.Smart
    ? ParamsAreaSelectionMode
    : T extends SelectionWay.Ellipse
    ? ParamsAreaSelectionMode
    : T extends SelectionWay.Eraser
    ? ParamsAreaSelectionMode & ParamsPaintWidth
    : never;

//#endregion

type InstallCleanup = () => void;
type SetParamsType = (params: any, preParams: null | any) => void;
type Installer<T extends ChangePictureSelectionWay> = (options: {
  app: App;
  toolPlugins: ToolPluginsType;
  wheeEditorPlugin: WheeEditorPlugin;
  hasPaintedPromiseRef: React.MutableRefObject<Promise<boolean>>;
}) => {
  cleanup: InstallCleanup;
  initParams: AreaSelectionParams<T>;
  setParams: SetParamsType;
};

// 智能选择涂抹宽度
const SMART_PAINT_WIDTH = 20;

// 涂抹的默认宽度
const DEFAULT_PAINT_WIDTH = 50;

const ToolMapping: Record<
  ChangePictureSelectionWay,
  Installer<ChangePictureSelectionWay>
> = {
  [SelectionWay.Box]: ({ app, toolPlugins }) => {
    const initParams: AreaSelectionParams<SelectionWay.Box> = {
      selectionMode: AreaSelectionMode.Add
    };

    function setParams(
      params: AreaSelectionParams<SelectionWay.Box>,
      preParams: AreaSelectionParams<SelectionWay.Box> | null
    ) {
      if (!params.selectionMode) {
        return;
      }
      toolPlugins.rectTool.registerConfig({
        ...toolPlugins.rectTool.config,
        fill: SelectionColor.Positive,
        globalCompositeOperation: 'xor'
      });
      if (!preParams || params.selectionMode !== preParams.selectionMode) {
        switch (params.selectionMode) {
          case AreaSelectionMode.Add: {
            app.setTool(toolPlugins.rectTool);
            break;
          }

          case AreaSelectionMode.Remove: {
            const eraserTool = toolPlugins.eraserTool;
            eraserTool.registerConfig({
              shape: EraserShape.Rect,
              globalCompositeOperation: 'source-over',
              backgroundColor: SelectionColor.Negative,
              lineCap: LineCap.square,
              lineJoin: LineJoin.bevel,
              strokeWidth: 0,
              listening: false
            });
            app.setTool(eraserTool);
            break;
          }
        }
        app.lockAllShapes();
      }
    }

    function cleanup() {
      app.destroyTool();
    }

    return {
      initParams,
      setParams,
      cleanup
    };
  },

  [SelectionWay.Lasso]: ({ app, toolPlugins }) => {
    const initParams: AreaSelectionParams<SelectionWay.Lasso> = {
      selectionMode: AreaSelectionMode.Add
    };

    let currentParams: null | AreaSelectionParams<SelectionWay.Lasso> = null;

    function setParams(
      params: AreaSelectionParams<SelectionWay.Lasso>,
      preParams: AreaSelectionParams<SelectionWay.Lasso>
    ) {
      if (!params.selectionMode) {
        return;
      }

      currentParams = params;

      switch (params.selectionMode) {
        case AreaSelectionMode.Add: {
          if (!preParams || preParams.selectionMode !== AreaSelectionMode.Add) {
            toolPlugins.lassoTool.registerConfig({
              ...toolPlugins.lassoTool.config,
              fill: SelectionColor.Positive,
              stroke: '#0000ff7f'
            });
            app.setTool(toolPlugins.lassoTool);
            app.lockAllShapes();
          }

          toolPlugins.lassoTool.registerConfig({
            ...toolPlugins.lassoTool.config,
            strokeWidth: LASSO_TOOL_STROKE_WIDTH / app.stage.scaleX()
          });
          break;
        }

        case AreaSelectionMode.Remove: {
          const eraserTool = toolPlugins.eraserTool;
          eraserTool.registerConfig({
            shape: EraserShape.Lasso,
            globalCompositeOperation: 'source-over',
            lineCap: LineCap.round,
            lineJoin: LineJoin.round,
            strokeWidth: LASSO_TOOL_STROKE_WIDTH / app.stage.scaleX(),
            strokeColor: SelectionColor.Negative,
            listening: false
          });

          if (
            !preParams ||
            preParams.selectionMode !== AreaSelectionMode.Remove
          ) {
            app.setTool(eraserTool);
            app.lockAllShapes();
          }
          break;
        }
      }
    }

    function handleZoomChange() {
      const params = currentParams;
      if (!params) {
        return;
      }

      switch (params.selectionMode) {
        case AreaSelectionMode.Add: {
          toolPlugins.lassoTool.registerConfig({
            ...toolPlugins.lassoTool.config,
            strokeWidth: LASSO_TOOL_STROKE_WIDTH / app.stage.scaleX()
          });
          break;
        }

        case AreaSelectionMode.Remove: {
          const eraserTool = toolPlugins.eraserTool;
          eraserTool.registerConfig({
            shape: EraserShape.Lasso,
            globalCompositeOperation: 'source-over',
            strokeColor: SelectionColor.Negative,
            lineCap: LineCap.round,
            lineJoin: LineJoin.round,
            strokeWidth: LASSO_TOOL_STROKE_WIDTH / app.stage.scaleX(),
            listening: false
          });
          break;
        }
      }
    }

    app.on(CanvasZoomEventName.end, handleZoomChange);

    function cleanup() {
      app.destroyTool();
      app.off(CanvasZoomEventName.end, handleZoomChange);
    }

    return {
      initParams,
      setParams,
      cleanup
    };
  },

  [SelectionWay.Paint]: ({ app, toolPlugins }) => {
    const initParams: AreaSelectionParams<SelectionWay.Paint> = {
      selectionMode: AreaSelectionMode.Add,
      paintWidth: DEFAULT_PAINT_WIDTH
    };

    let currentParams: null | AreaSelectionParams<SelectionWay.Paint> = null;

    function setParams(
      params: AreaSelectionParams<SelectionWay.Paint>,
      preParams: AreaSelectionParams<SelectionWay.Paint>
    ) {
      if (!params.selectionMode) {
        return;
      }

      currentParams = params;

      switch (params.selectionMode) {
        case AreaSelectionMode.Add: {
          const lineTool = toolPlugins.lineTool;
          lineTool.registerConfig({
            ...lineTool.config,
            stroke: SelectionColor.Positive,
            globalCompositeOperation: 'xor'
          });
          typeof params.paintWidth === 'number' &&
            lineTool.setStrokeWidth(params.paintWidth / app.stage.scaleX());

          if (!preParams || preParams.selectionMode !== AreaSelectionMode.Add) {
            app.setTool(lineTool);
            app.lockAllShapes();
          }
          break;
        }

        case AreaSelectionMode.Remove: {
          const eraserTool = toolPlugins.eraserTool;
          eraserTool.registerConfig({
            shape: EraserShape.Line,
            globalCompositeOperation: 'source-over',
            strokeColor: SelectionColor.Negative,
            lineCap: LineCap.round,
            lineJoin: LineJoin.round,
            strokeWidth: params.paintWidth / app.stage.scaleX(),
            listening: false
          });

          if (
            !preParams ||
            preParams.selectionMode !== AreaSelectionMode.Remove
          ) {
            app.setTool(eraserTool);
            app.lockAllShapes();
          }
          break;
        }
      }
    }

    function handleZoomChange() {
      const params = currentParams;
      if (!params) {
        return;
      }

      switch (params.selectionMode) {
        case AreaSelectionMode.Add: {
          const lineTool = toolPlugins.lineTool;
          typeof params.paintWidth === 'number' &&
            lineTool.setStrokeWidth(params.paintWidth / app.stage.scaleX());
          break;
        }

        case AreaSelectionMode.Remove: {
          const eraserTool = toolPlugins.eraserTool;
          eraserTool.registerConfig({
            shape: EraserShape.Line,
            globalCompositeOperation: 'source-over',
            strokeColor: SelectionColor.Negative,
            lineCap: LineCap.round,
            lineJoin: LineJoin.round,
            strokeWidth: params.paintWidth / app.stage.scaleX(),
            listening: false
          });
          break;
        }
      }
    }

    app.on(CanvasZoomEventName.end, handleZoomChange);

    function cleanup() {
      app.destroyTool();
      app.off(CanvasZoomEventName.end, handleZoomChange);
    }

    return {
      initParams,
      setParams,
      cleanup
    };
  },

  [SelectionWay.Smart]: ({ app, toolPlugins, hasPaintedPromiseRef }) => {
    const initParams: AreaSelectionParams<SelectionWay.Paint> = {
      selectionMode: AreaSelectionMode.Add,
      paintWidth: SMART_PAINT_WIDTH
    };

    let currentParams: null | AreaSelectionParams<SelectionWay.Paint> = null;
    function setParams(
      params: AreaSelectionParams<SelectionWay.Paint>,
      preParams: AreaSelectionParams<SelectionWay.Paint>
    ) {
      if (!params.selectionMode) {
        return;
      }

      currentParams = params;

      if (!preParams || params.selectionMode !== preParams.selectionMode) {
        toolPlugins.smartSelectionTool.registerConfig({
          ...toolPlugins.smartSelectionTool.config,
          paintModeColor: SelectionColor.Positive,
          eraserModeColor: SelectionColor.Negative,
          opacity: 1
        });

        switch (params.selectionMode) {
          case AreaSelectionMode.Add: {
            app.setTool(toolPlugins.smartSelectionTool);
            toolPlugins.smartSelectionTool.isEraserMode = false;
            toolPlugins.smartSelectionTool.setStrokeWidth(
              SMART_PAINT_WIDTH / app.stage.scaleX()
            );
            break;
          }

          case AreaSelectionMode.Remove: {
            app.setTool(toolPlugins.smartSelectionTool);
            toolPlugins.smartSelectionTool.isEraserMode = true;
            toolPlugins.smartSelectionTool.setStrokeWidth(
              SMART_PAINT_WIDTH / app.stage.scaleX()
            );
            break;
          }
        }

        app.lockAllShapes();
      }
    }

    function handleZoomChange() {
      const params = currentParams;
      if (!params) {
        return;
      }
      toolPlugins.smartSelectionTool.setStrokeWidth(
        SMART_PAINT_WIDTH / app.stage.scaleX()
      );
    }

    app.on(CanvasZoomEventName.end, handleZoomChange);

    toolPlugins.smartSelectionTool.injectHasPaintedChecker(
      () => hasPaintedPromiseRef.current
    );

    function cleanup() {
      app.off(CanvasZoomEventName.end, handleZoomChange);
      app.destroyTool();
      toolPlugins.smartSelectionTool.injectHasPaintedChecker(() =>
        Promise.resolve(false)
      );
    }

    return {
      initParams,
      setParams,
      cleanup
    };
  }
};

const TipsMapping: Record<
  ChangePictureSelectionWay,
  Partial<Record<AreaSelectionMode, string>>
> = {
  [SelectionWay.Smart]: {
    [AreaSelectionMode.Add]: '请点击或涂抹想要修改的区域',
    [AreaSelectionMode.Remove]: '请点击或涂抹想要去除的区域'
  },
  [SelectionWay.Box]: {
    [AreaSelectionMode.Add]: '请框选想要修改的区域',
    [AreaSelectionMode.Remove]: '请框选想要去除的区域'
  },
  [SelectionWay.Lasso]: {
    [AreaSelectionMode.Add]: '请圈出想要修改的区域',
    [AreaSelectionMode.Remove]: '请圈出想要去除的区域'
  },
  [SelectionWay.Paint]: {
    [AreaSelectionMode.Add]: '请涂抹想要修改的区域',
    [AreaSelectionMode.Remove]: '请涂抹想要去除的区域'
  }
};

type UseAreaSelectionOptions = {
  app: App;
  toolPlugins: ToolPluginsType;
  selectorPlugin: SelectorPlugin;
  wheeEditorPlugin: WheeEditorPlugin;
  hasPaintedPromiseRef: React.MutableRefObject<Promise<boolean>>;
};

export function useAreaSelection({
  app,
  toolPlugins,
  selectorPlugin,
  wheeEditorPlugin,
  hasPaintedPromiseRef
}: UseAreaSelectionOptions) {
  const [selectionWay, setSelectionWay] =
    useState<ChangePictureSelectionWay | null>(null);
  const [selectionParams, setSelectionParams] = useState<
    AreaSelectionParams<ChangePictureSelectionWay>
  >({ selectionMode: AreaSelectionMode.Add });

  const setParamsRef = useRef<null | SetParamsType>(null);
  // console.log('params', selectionParams);

  const editorStore = useStore('EditorStore');
  const { setMouseStyleType } = editorStore;

  useAreaSelectionTips({
    app,
    selectionWay,
    selectionMode: selectionParams.selectionMode,
    hasPaintedPromiseRef
  });

  useEffect(() => {
    if (!selectionWay) {
      return;
    }

    const install = ToolMapping[selectionWay];
    const { initParams, setParams, cleanup } = install({
      app,
      toolPlugins,
      wheeEditorPlugin,
      hasPaintedPromiseRef
    });
    setSelectionParams(initParams);
    setParamsRef.current = setParams;
    app.lockAllShapes();
    return () => {
      setParamsRef.current = null;
      cleanup();
      app.unlockAllShapes();
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [app, toolPlugins, selectionWay, selectorPlugin, wheeEditorPlugin]);

  //监听鼠标状态 更新鼠标样式及轮廓光显隐逻辑
  useEffect(() => {
    setMouseStyleType({
      selectionParams: selectionParams.selectionMode,
      selectionWay,
      paintWidth:
        selectionWay === SelectionWay.Smart
          ? SMART_PAINT_WIDTH
          : selectionParams.paintWidth
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectionParams, selectionWay, editorStore.currentFeature]);

  const preParams =
    useRef<AreaSelectionParams<ChangePictureSelectionWay> | null>(null);
  useEffect(() => {
    preParams.current = null;
  }, [selectionWay]);
  useEffect(() => {
    setParamsRef.current?.(selectionParams, preParams.current);
    preParams.current = selectionParams;
  }, [selectionParams]);

  useEffect(() => {
    function reset() {
      setSelectionWay(null);
      app.destroyTool();
    }

    app.on(CanvasNodeEventName.select, reset);

    return () => {
      app.off(CanvasNodeEventName.select, reset);
    };
  }, [app]);

  return {
    selectionWay,
    onSelectionWayChange: setSelectionWay,
    selectionParams: selectionWay && selectionParams,
    onSelectionParamsChange: setSelectionParams
  };
}

type UseAreaSelectionTipsOptions = {
  app: App;
  selectionWay: ChangePictureSelectionWay | null;
  selectionMode: AreaSelectionMode | null;
  hasPaintedPromiseRef: React.MutableRefObject<Promise<boolean>>;
};
function useAreaSelectionTips({
  app,
  selectionWay,
  selectionMode,
  hasPaintedPromiseRef
}: UseAreaSelectionTipsOptions) {
  const { accountProfile } = useAccountProfile();
  const userId = accountProfile?.id ?? '';

  const editorStore = useStore('EditorStore');
  const projectId = editorStore.projectInfo.id;

  const getTipsKey = useCallback(
    () => `${userId}:selections:tips:${selectionWay}:${selectionMode}`,
    [userId, selectionWay, selectionMode]
  );

  const hasTipped = useCallback(() => {
    const tipKey = getTipsKey();
    return !!localStorage.getItem(tipKey);
  }, [getTipsKey]);

  useEffect(() => {
    if (!selectionWay || !selectionMode) {
      return;
    }

    if (hasTipped() || !userId || !projectId) {
      return;
    }

    const tipsContent = TipsMapping[selectionWay][selectionMode];

    let isIgnore = false;
    let closeTips: null | (() => void) = null;
    hasPaintedPromiseRef.current.then((hasPainted) => {
      if (
        (!hasPainted && selectionMode === AreaSelectionMode.Remove) ||
        isIgnore
      ) {
        return;
      }

      const { destroy } = message({
        type: 'info',
        content: tipsContent || ''
      });

      closeTips = destroy;
    });

    // 涂抹之后关闭toast
    function closeTipsWhenPaint(payload: { nodes: BaseShape<ShapeConfig>[] }) {
      if (!closeTips) {
        return;
      }

      const { nodes } = payload;
      for (const n of nodes) {
        if (n.config.type !== ShapeType.Image) {
          closeTips();
          app.off(CanvasNodeEventName.add, closeTipsWhenPaint);
          localStorage.setItem(getTipsKey(), 'true');
        }
      }
    }

    app.on(CanvasNodeEventName.add, closeTipsWhenPaint);

    return () => {
      isIgnore = true;
      closeTips?.();
      app.off(CanvasNodeEventName.add, closeTipsWhenPaint);
    };
  }, [
    selectionWay,
    selectionMode,
    hasTipped,
    getTipsKey,
    app,
    projectId,
    userId,
    hasPaintedPromiseRef
  ]);
}
