import { App, CanvasNodeEventName } from '@/editor/core/src';
import { WheeEditorPlugin } from '@/editor/wukong-whee-editor-plugin/src';
import { WheeLayerEditorPlugin } from '@/editor/wukong-whee-layer-editor-plugin/src';
import { useEffect, useRef, useState } from 'react';

type UseHasPaintedOptions = {
  app?: App;
  wheeEditorPlugin?: WheeEditorPlugin | WheeLayerEditorPlugin;
  enabled: boolean;
};

export function useHasPainted({
  app,
  wheeEditorPlugin,
  enabled
}: UseHasPaintedOptions) {
  const [hasPainted, setHasPainted] = useState(false);
  const promiseRef = useRef<Promise<boolean>>(Promise.resolve(hasPainted));

  useEffect(() => {
    async function canvasChangeHandler() {
      if (!enabled) {
        setHasPainted(false);
        promiseRef.current = Promise.resolve(false);
        return;
      }

      const plugin = wheeEditorPlugin?.plugin;
      if (!plugin) {
        setHasPainted(false);
        promiseRef.current = Promise.resolve(false);
        return;
      }
      promiseRef.current =
        wheeEditorPlugin?.plugin?.checkHasPainted() ?? Promise.resolve(false);
      setHasPainted(await promiseRef.current);
    }

    canvasChangeHandler();

    app?.on(CanvasNodeEventName.add, canvasChangeHandler);
    app?.on(CanvasNodeEventName.remove, canvasChangeHandler);

    return () => {
      app?.off(CanvasNodeEventName.add, canvasChangeHandler);
      app?.off(CanvasNodeEventName.remove, canvasChangeHandler);
    };
  }, [app, wheeEditorPlugin, enabled]);

  return {
    hasPainted,
    promiseRef
  };
}
