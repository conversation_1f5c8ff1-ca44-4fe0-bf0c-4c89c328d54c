import {
  App,
  BaseShape,
  CanvasNodeEventName,
  CanvasZoomEventName,
  LineCap,
  LineJoin,
  ShapeConfig,
  ShapeType,
  Custom
} from '@/editor/core/src';
import { SelectionWay } from '../components/AreaSelection';
import { useCallback, useEffect, useRef, useState } from 'react';
import { AreaSelectionMode } from '../components/AreaSelection/ModeSelection';
import {
  EraserShape,
  EraserTool,
  LassoTool,
  LineTool,
  RectTool
} from '@/editor/utils/src';
import { WheeEditorPlugin } from '@/editor/wukong-whee-editor-plugin/src';
import { WheeLayerEditorPlugin } from '@/editor/wukong-whee-layer-editor-plugin/src';
import useStore from '@/hooks/useStore';
import message from '../components/Toast';
import { useAccountProfile } from '@/hooks';
import { SelectionColor } from '../constant/selection';
import { SmartSelectionToolPlugin } from '../plugins/SmartSelectionToolPlugin';
import { paintMaskFromGray } from '../utils/maskImageTools';
import { getClock } from '../utils/clock';
import { autoRecognitionSelection } from '@/api/imageEditor';
import { createImage } from '@/utils/cropImage';
import { v4 as uuidv4 } from 'uuid';
import { loadingToDrawing } from '@/editor/effect/src';
import { handleInvalidTokenError } from '../utils/handleRequestError';
import { MtccFuncCode } from '@/api/types';

// 交互选择区 选择超时时间
const SMART_SELECTION_TIMEOUT = 10 * 1000;

export type ToolPluginsType = {
  rectTool?: RectTool;
  eraserTool?: EraserTool;
  lineTool?: LineTool;
  lassoTool?: LassoTool;
  smartSelectionTool?: SmartSelectionToolPlugin;
};

//#region 选择方式的参数

// 选择模式：是增加选区还是删除选区
type ParamsAreaSelectionMode = {
  selectionMode: AreaSelectionMode | null;
  paintWidth?: number | null;
};

type AiEraserSelectionRef = {
  selectionWay: null | string;
  selectionMode: null | string;
  paintMaskCanvas?: HTMLCanvasElement | null;
  mask?: HTMLElement | null;
};
// 涂抹的尺寸
type ParamsPaintWidth = {
  paintWidth: number;
  hardness: number;
};

export type AICutoutSelectionWay = SelectionWay.Smart | SelectionWay.Paint;

export type AreaSelectionParams<T extends AICutoutSelectionWay> =
  T extends SelectionWay.Paint
    ? ParamsAreaSelectionMode & ParamsPaintWidth
    : T extends SelectionWay.Smart
    ? ParamsAreaSelectionMode
    : never;

//#endregion

type InstallCleanup = () => void;
type SetParamsType = (params: any, preParams: null | any) => void;
type Installer<T extends AICutoutSelectionWay> = (options: {
  app: App;
  toolPlugins: ToolPluginsType;
  wheeEditorPlugin: WheeEditorPlugin | WheeLayerEditorPlugin;
  hasPaintedPromiseRef: React.MutableRefObject<Promise<boolean>>;
}) => {
  cleanup: InstallCleanup;
  initParams: AreaSelectionParams<T>;
  setParams: SetParamsType;
};

// 智能选择涂抹宽度
const SMART_PAINT_WIDTH = 20;

// 涂抹的默认宽度
const DEFAULT_PAINT_WIDTH = 50;

// 硬度默认值
const DEFAULT_HARDNESS_WIDTH = 100;

const ToolMapping: Record<
  AICutoutSelectionWay,
  Installer<AICutoutSelectionWay>
> = {
  [SelectionWay.Paint]: ({ app, toolPlugins }) => {
    const initParams: AreaSelectionParams<SelectionWay.Paint> = {
      selectionMode: AreaSelectionMode.Add,
      paintWidth: DEFAULT_PAINT_WIDTH,
      hardness: DEFAULT_HARDNESS_WIDTH
    };

    let currentParams: null | AreaSelectionParams<SelectionWay.Paint> = null;

    function setParams(
      params: AreaSelectionParams<SelectionWay.Paint>,
      preParams: AreaSelectionParams<SelectionWay.Paint>
    ) {
      if (!params.selectionMode) {
        return;
      }

      currentParams = params;
      if (!toolPlugins.lineTool) return;
      if (!toolPlugins.eraserTool) return;

      switch (params.selectionMode) {
        case AreaSelectionMode.Add: {
          const lineTool = toolPlugins.lineTool;
          typeof params.paintWidth === 'number' &&
            lineTool.setStrokeWidth(params.paintWidth / app.stage.scaleX());
          // lineTool.setLineBlur(params.hardness / app.stage.scaleX());
          // lineTool.setLineBlur(100);
          // lineTool.setFilter("blur", {
          //   filter: Konva.Filters.Blur,
          //   blurRadius: 100
          // })

          if (!preParams || preParams.selectionMode !== AreaSelectionMode.Add) {
            app.setTool(lineTool);
            app.lockAllShapes();
          }
          break;
        }

        case AreaSelectionMode.Remove: {
          const eraserTool = toolPlugins.eraserTool;
          eraserTool.registerConfig({
            shape: EraserShape.Line,
            globalCompositeOperation: 'source-over',
            strokeColor: SelectionColor.Negative,
            lineCap: LineCap.round,
            lineJoin: LineJoin.round,
            strokeWidth: params.paintWidth / app.stage.scaleX(),
            listening: false
          });

          if (
            !preParams ||
            preParams.selectionMode !== AreaSelectionMode.Remove
          ) {
            app.setTool(eraserTool);
            app.lockAllShapes();
          }
          break;
        }
      }
    }

    function handleZoomChange() {
      const params = currentParams;
      if (!params) {
        return;
      }
      if (!toolPlugins.lineTool) return;
      if (!toolPlugins.eraserTool) return;
      switch (params.selectionMode) {
        case AreaSelectionMode.Add: {
          const lineTool = toolPlugins.lineTool;
          typeof params.paintWidth === 'number' &&
            lineTool.setStrokeWidth(params.paintWidth / app.stage.scaleX());
          break;
        }

        case AreaSelectionMode.Remove: {
          const eraserTool = toolPlugins.eraserTool;
          eraserTool.registerConfig({
            shape: EraserShape.Line,
            globalCompositeOperation: 'source-over',
            strokeColor: SelectionColor.Negative,
            lineCap: LineCap.round,
            lineJoin: LineJoin.round,
            strokeWidth: params.paintWidth / app.stage.scaleX(),
            listening: false
          });
          break;
        }
      }
    }

    app.on(CanvasZoomEventName.end, handleZoomChange);

    function cleanup() {
      app.destroyTool();
      app.off(CanvasZoomEventName.end, handleZoomChange);
    }

    return {
      initParams,
      setParams,
      cleanup
    };
  },

  [SelectionWay.Smart]: ({ app, toolPlugins, hasPaintedPromiseRef }) => {
    const initParams: AreaSelectionParams<SelectionWay.Paint> = {
      selectionMode: AreaSelectionMode.Add,
      paintWidth: SMART_PAINT_WIDTH,
      hardness: DEFAULT_HARDNESS_WIDTH
    };

    let currentParams: null | AreaSelectionParams<SelectionWay.Paint> = null;
    function setParams(
      params: AreaSelectionParams<SelectionWay.Paint>,
      preParams: AreaSelectionParams<SelectionWay.Paint>
    ) {
      if (!params.selectionMode) {
        return;
      }

      currentParams = params;

      if (
        toolPlugins?.smartSelectionTool &&
        (!preParams || params.selectionMode !== preParams.selectionMode)
      ) {
        switch (params.selectionMode) {
          case AreaSelectionMode.Add: {
            app.setTool(toolPlugins.smartSelectionTool);
            toolPlugins.smartSelectionTool.isEraserMode = false;
            toolPlugins.smartSelectionTool.setStrokeWidth(
              SMART_PAINT_WIDTH / app.stage.scaleX()
            );
            break;
          }

          case AreaSelectionMode.Remove: {
            app.setTool(toolPlugins.smartSelectionTool);
            toolPlugins.smartSelectionTool.isEraserMode = true;
            toolPlugins.smartSelectionTool.setStrokeWidth(
              SMART_PAINT_WIDTH / app.stage.scaleX()
            );
            break;
          }
          case AreaSelectionMode.Auto: {
            app.destroyTool();
            break;
          }
        }

        app.lockAllShapes();
      }
    }

    function handleZoomChange() {
      const params = currentParams;
      if (!params) {
        return;
      }
      toolPlugins?.smartSelectionTool?.setStrokeWidth(
        SMART_PAINT_WIDTH / app.stage.scaleX()
      );
    }

    app.on(CanvasZoomEventName.end, handleZoomChange);

    toolPlugins.smartSelectionTool?.injectHasPaintedChecker(
      () => hasPaintedPromiseRef.current
    );

    function cleanup() {
      app.off(CanvasZoomEventName.end, handleZoomChange);
      app.destroyTool();
      toolPlugins.smartSelectionTool?.injectHasPaintedChecker(() =>
        Promise.resolve(false)
      );
    }

    return {
      initParams,
      setParams,
      cleanup
    };
  }
};

const TipsMapping: Record<
  AICutoutSelectionWay,
  Partial<Record<AreaSelectionMode, string>>
> = {
  [SelectionWay.Smart]: {
    [AreaSelectionMode.Add]: '请点击或涂抹想要保留的区域',
    [AreaSelectionMode.Remove]: '请涂抹想要去除的选区',
    [AreaSelectionMode.Auto]: ''
  },
  // [SelectionWay.Box]: {
  //   [AreaSelectionMode.Add]: '请框选想要修改的区域',
  //   [AreaSelectionMode.Remove]: '请框选想要去除的区域'
  // },
  // [SelectionWay.Lasso]: {
  //   [AreaSelectionMode.Add]: '请圈出想要修改的区域',
  //   [AreaSelectionMode.Remove]: '请圈出想要去除的区域'
  // },
  [SelectionWay.Paint]: {
    [AreaSelectionMode.Add]: '请点击或涂抹想要保留的区域',
    [AreaSelectionMode.Remove]: '请涂抹想要去除的选区'
  }
};

type UseAreaSelectionOptions = {
  app?: App;
  toolPlugins?: ToolPluginsType;
  wheeEditorPlugin?: WheeEditorPlugin | WheeLayerEditorPlugin;
  hasPaintedPromiseRef?: React.MutableRefObject<Promise<boolean>>;
  hooks?: { autoRecognitionAfter: () => any };
};

export function useAreaSelection({
  app,
  toolPlugins,
  wheeEditorPlugin,
  hasPaintedPromiseRef,
  hooks
}: UseAreaSelectionOptions) {
  const [selectionWay, setSelectionWay] = useState<AICutoutSelectionWay | null>(
    null
  );
  const [selectionParams, setSelectionParams] = useState<
    AreaSelectionParams<AICutoutSelectionWay>
  >({ selectionMode: AreaSelectionMode.Add });

  const setParamsRef = useRef<null | SetParamsType>(null);

  const aiEraserSelectionRef = useRef<AiEraserSelectionRef>({
    selectionWay: null,
    selectionMode: null
  });

  const editorStore = useStore('EditorStore');
  const { setMouseStyleType } = editorStore;
  // if (!(app&& selectionWay &&toolPlugins && wheeEditorPlugin&&hasPaintedPromiseRef)){
  //   return {
  //     selectionWay,
  //     onSelectionWayChange: setSelectionWay,
  //     selectionParams: selectionWay && selectionParams,
  //     onSelectionParamsChange: setSelectionParams
  //   };
  // }

  useAreaSelectionTips({
    app,
    selectionWay,
    selectionMode: selectionParams.selectionMode,
    hasPaintedPromiseRef
  });

  useEffect(() => {
    if (!selectionWay) {
      return;
    }
    if (!(app && toolPlugins && wheeEditorPlugin && hasPaintedPromiseRef))
      return;
    const install = ToolMapping[selectionWay];
    const { initParams, setParams, cleanup } = install({
      app,
      toolPlugins,
      wheeEditorPlugin,
      hasPaintedPromiseRef
    });

    setSelectionParams(initParams);
    setParamsRef.current = setParams;
    app?.lockAllShapes();
    return () => {
      setParamsRef.current = null;
      cleanup();
      app?.unlockAllShapes();
    };
    // eslint-disable-next-line
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [app, toolPlugins, selectionWay, wheeEditorPlugin]);

  const autoRecognition = () => {
    setTimeout(() => {
      autoRecognitionHandle();
    });
  };

  //监听鼠标状态 更新鼠标样式及轮廓光显隐逻辑
  useEffect(() => {
    const isCheckAuto =
      selectionParams.selectionMode === AreaSelectionMode.Auto;
    let selectionData = {
      selectionWay: selectionWay,
      selectionMode: selectionParams.selectionMode
    };
    aiEraserSelectionRef.current = {
      ...aiEraserSelectionRef.current,
      ...selectionData
    };
    if (isCheckAuto) {
      selectionData = {
        selectionWay: null,
        selectionMode: null
      };
    }
    setMouseStyleType({
      selectionParams: selectionData.selectionMode,
      selectionWay: selectionData.selectionWay,
      paintWidth:
        selectionWay === SelectionWay.Smart
          ? SMART_PAINT_WIDTH
          : selectionParams.paintWidth,
      content: app?.stage.content,
      app
    });
    if (
      selectionWay === SelectionWay.Smart &&
      selectionParams.selectionMode === AreaSelectionMode.Auto
    ) {
      autoRecognition();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectionParams, selectionWay, app]);

  const preParams = useRef<AreaSelectionParams<AICutoutSelectionWay> | null>(
    null
  );
  useEffect(() => {
    preParams.current = null;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectionWay]);
  useEffect(() => {
    setParamsRef.current?.(selectionParams, preParams.current);
    preParams.current = selectionParams;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectionParams]);

  useEffect(() => {
    function reset() {
      setSelectionWay(null);
      app?.destroyTool();
    }

    app?.on(CanvasNodeEventName.select, reset);
    // app?.on(CanvasNodeEventName.add, listenerAddOptions);
    const paintMaskCanvas = document.createElement('canvas');
    aiEraserSelectionRef.current.paintMaskCanvas = paintMaskCanvas;
    return () => {
      app?.off(CanvasNodeEventName.select, reset);
      // app?.off(CanvasNodeEventName.add, listenerAddOptions);
      aiEraserSelectionRef.current.paintMaskCanvas = null;
      const mask = aiEraserSelectionRef.current.mask;
      //eslint-disable-next-line react-hooks/exhaustive-deps
      if (mask) {
        document.body.removeChild(mask);
        //eslint-disable-next-line react-hooks/exhaustive-deps
        aiEraserSelectionRef.current.mask = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [app, aiEraserSelectionRef.current]);

  function openMask() {
    const mask = document.createElement('div');
    mask.style.width = '100vw';
    mask.style.height = '100vh';
    mask.style.position = 'fixed';
    mask.style.left = '0';
    mask.style.top = '0';
    mask.style.zIndex = '1000';
    document.body.append(mask);
    aiEraserSelectionRef.current.mask = mask;
    const stopWheel = (event: MouseEvent) => {
      event.preventDefault(); // 阻止默认行为
    };
    // 禁用该元素上的 wheel 事件
    mask.addEventListener('wheel', stopWheel, { passive: false });
    return () => {
      mask.removeEventListener('wheel', stopWheel);
      mask && mask.parentNode?.removeChild(mask);
      aiEraserSelectionRef.current.mask = null;
    };
  }
  const autoRecognitionHandle = async () => {
    if (!wheeEditorPlugin) return;
    const closeMask = openMask();
    const drawAnimation = app && loadingToDrawing(app);
    drawAnimation?.start();
    const { destroy: closeToast } = message({
      content: '自动识别主体中...',
      type: 'loading',
      isShowMask: false
    });
    let node = null as null | Custom | undefined;
    try {
      node = await getNewCustom();
    } catch (e: any) {
      if (handleInvalidTokenError(e)) {
        return;
      }
      message({
        content: '识别失败，请重试',
        type: 'error',
        isShowMask: false,
        duration: 3000
      });
    } finally {
      closeMask();
      closeToast();
      drawAnimation?.stop();
      drawAnimation?.destroy();
      if (node) {
        editorStore.layerEditor?.historyPlugin.enableCache();
        wheeEditorPlugin?.plugin?.clearMask({ autoCommitHistory: false });
        wheeEditorPlugin?.plugin?.addOptions(node);
        if (hooks) {
          await hooks.autoRecognitionAfter();
        }
        editorStore.layerEditor?.historyPlugin.commitCache();
      }
    }
  };

  const getNewCustom = async () => {
    let remainingTime = SMART_SELECTION_TIMEOUT;
    const clock = getClock();
    remainingTime -= clock.getDelta();
    let initImage = '';
    const image = app?.stage.find('Image')[0];
    initImage = image?.getAttr('target').config.source;
    if (!initImage) {
      return;
    }
    const visibleRect = wheeEditorPlugin?.plugin?.getVisibleAreaRect();
    if (!visibleRect) {
      return;
    }
    const response = await autoRecognitionSelection(
      {
        projectId: editorStore.projectInfo.id,
        initImage: initImage,
        functionName: MtccFuncCode.FuncCodeImageCutout
      },
      remainingTime
    );
    const newMask = response.resultImage;
    const maskImage = await createImage(newMask);
    // 用来绘制mask的canvas
    const paintMaskCanvas = aiEraserSelectionRef.current.paintMaskCanvas;
    if (!paintMaskCanvas) return;
    paintMaskFromGray(maskImage, paintMaskCanvas);
    const imageDataURL = paintMaskCanvas.toDataURL('image/png');
    const transformedMaskImage = await createImage(imageDataURL);

    const { x, y } = visibleRect;
    const node = new Custom({
      type: ShapeType.Custom,
      id: uuidv4(),
      x,
      y,
      sceneFunc: (context) => {
        context.drawImage(transformedMaskImage, 0, 0);
      }
    });
    return node;
  };

  return {
    selectionWay,
    onSelectionWayChange: setSelectionWay,
    selectionParams: selectionWay && selectionParams,
    onSelectionParamsChange: setSelectionParams,
    autoRecognitionHandle
  };
}

type UseAreaSelectionTipsOptions = {
  app?: App;
  selectionWay?: AICutoutSelectionWay | null;
  selectionMode?: AreaSelectionMode | null;
  hasPaintedPromiseRef?: React.MutableRefObject<Promise<boolean>>;
};
function useAreaSelectionTips({
  app,
  selectionWay,
  selectionMode,
  hasPaintedPromiseRef
}: UseAreaSelectionTipsOptions) {
  const { accountProfile } = useAccountProfile();
  const userId = accountProfile?.id ?? '';

  const editorStore = useStore('EditorStore');
  const projectId = editorStore.projectInfo.id;

  const getTipsKey = useCallback(
    () => `${userId}:selections:aicutout:tips:${selectionWay}:${selectionMode}`,
    [userId, selectionWay, selectionMode]
  );

  const hasTipped = useCallback(() => {
    const tipKey = getTipsKey();
    return !!localStorage.getItem(tipKey);
  }, [getTipsKey]);

  useEffect(() => {
    if (!selectionWay || !selectionMode) {
      return;
    }

    if (hasTipped() || !userId || !projectId) {
      return;
    }

    const tipsContent = TipsMapping[selectionWay][selectionMode];

    let isIgnore = false;
    let closeTips: null | (() => void) = null;
    hasPaintedPromiseRef?.current.then((hasPainted) => {
      if (
        (!hasPainted && selectionMode === AreaSelectionMode.Remove) ||
        isIgnore
      ) {
        return;
      }
      if (tipsContent) {
        const { destroy } = message({
          type: 'info',
          content: tipsContent || ''
        });

        closeTips = destroy;
      }
    });

    // 涂抹之后关闭toast
    function closeTipsWhenPaint(payload: { nodes: BaseShape<ShapeConfig>[] }) {
      if (!closeTips) {
        return;
      }

      const { nodes } = payload;
      for (const n of nodes) {
        if (n.config.type !== ShapeType.Image) {
          closeTips();
          app?.off(CanvasNodeEventName.add, closeTipsWhenPaint);
          localStorage.setItem(getTipsKey(), 'true');
        }
      }
    }

    app?.on(CanvasNodeEventName.add, closeTipsWhenPaint);

    return () => {
      isIgnore = true;
      closeTips?.();
      app?.off(CanvasNodeEventName.add, closeTipsWhenPaint);
    };
  }, [
    selectionWay,
    selectionMode,
    hasTipped,
    getTipsKey,
    app,
    projectId,
    userId,
    hasPaintedPromiseRef
  ]);
}
