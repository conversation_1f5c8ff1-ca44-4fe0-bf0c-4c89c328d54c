import { App, BaseShape, ShapeConfig } from '@/editor/core/src';
import { HistoryPlugin } from '@/editor/wukong-history-plugin/src';
import { SelectorPlugin } from '@/editor/wukong-selector-plugin/src';
import { InvertSelectPlugin } from '@/editor/wukong-invertSelect-plugin/src';
import { WheeEditorPlugin } from '@/editor/wukong-whee-editor-plugin/src';
import { ImageTool } from '@/editor/utils/src';
import { useRef } from 'react';

export function useEditor(fun: () => void) {
  const app = useRef<App>(
    new App({
      backgroundColor: 'black',
      scale: {
        min: 0.05,
        max: 5
      },
      mouseWheel: {
        enabled: true,
        scaleSpeed: 0.02
      }
    })
  );

  // 引入历史插件
  const historyPlugin = useRef(new HistoryPlugin({ max: 9999 }));
  app.current.use(historyPlugin.current);
  historyPlugin.current.enable();

  const wheeEditorPlugin = useRef<WheeEditorPlugin>(
    new WheeEditorPlugin({
      window: {
        width: 800,
        height: 600
      },
      mask: {
        color: 'black',
        opacity: 0.5
      },
      background: {
        color: '#000',
        opacity: 1
      },
      hooks: {
        initialize: () => {
          fun();
        }
      }
    })
  );
  app.current.use(wheeEditorPlugin.current);

  const invertSelectPlugin = useRef<InvertSelectPlugin>(
    new InvertSelectPlugin({
      window: {
        width: 800,
        height: 600,
        x: 0,
        y: 0
      },
      mask: {
        color: 'black',
        opacity: 0.5
      },
      background: {
        color: 'white',
        opacity: 1
      },
      optionsLayer: wheeEditorPlugin.current.plugin!.optionsLayer,
      addOptions: wheeEditorPlugin.current.plugin!.addOptions
    })
  );
  app.current.use(invertSelectPlugin.current);

  const selectorPlugin = useRef<SelectorPlugin>(
    new SelectorPlugin({
      enable: false,
      multipleSelect: true,
      hostKeyConfig: {
        enable: true,
        MaxPasteOffsetX: 20,
        MaxPasteOffsetY: 20,
        cutHandler: (...nodes: BaseShape<ShapeConfig>[]) => {
          app.current?.remove(...nodes);
        },
        pasteHandler: (...nodes: BaseShape<ShapeConfig>[]) => {
          wheeEditorPlugin.current.plugin?.addEditor(...nodes);
          selectorPlugin.current.selector?.selectNodes(
            nodes.map((shape) => shape?.instance)
          );
        },
        copyHandler: (...nodes: BaseShape<ShapeConfig>[]) => {
          // console.log('copyHandler==', nodes);
        },
        deleteHandler: (...nodes: BaseShape<ShapeConfig>[]) => {
          app.current?.remove(...nodes);
        }
        // moveHandler: (type: string, nodes: BaseShape<ShapeConfig>[]) => {
        //   const step = 10;
        //   const updatePosition = (node: BaseShape<ShapeConfig>) => {
        //     const nodeX = node.instance.x();
        //     const nodeY = node.instance.y();
        //     switch (type) {
        //       case 'up':
        //         node.instance.y(nodeY - step);
        //         break;
        //       case 'down':
        //         node.instance.y(nodeY + step);
        //         break;
        //       case 'left':
        //         node.instance.x(nodeX - step);
        //         break;
        //       case 'right':
        //         node.instance.x(nodeX + step);
        //         break;
        //       default:
        //         break;
        //     }
        //   };
        //   nodes.forEach((node) => {
        //     updatePosition(node);
        //   });
        // }
      }
    })
  );
  app.current.use(selectorPlugin.current);

  const imageTooler = useRef<ImageTool>(
    new ImageTool(wheeEditorPlugin.current.plugin!.addEditor)
  );

  return {
    app: app.current,
    historyPlugin: historyPlugin.current,
    selectorPlugin: selectorPlugin.current,
    invertSelectPlugin: invertSelectPlugin.current,
    wheeEditorPlugin: wheeEditorPlugin.current,
    imageTooler: imageTooler.current
  };
}
