import {
  App,
  <PERSON><PERSON>ha<PERSON>,
  CanvasNodeEventName,
  CanvasZoomEventName,
  LineCap,
  LineJoin,
  ShapeConfig,
  ShapeType,
  Custom,
  Rectangle,
  Line,
  Eraser,
  EraserShapeType
} from '@/editor/core/src';
import { SelectionWay } from '../components/AreaSelection';
import { useCallback, useEffect, useRef, useState } from 'react';
import { AreaSelectionMode } from '../components/AreaSelection/ModeSelection';
import {
  EraserShape,
  EraserTool,
  LassoTool,
  LineTool,
  RectTool
} from '@/editor/utils/src';
import { WheeEditorPlugin } from '@/editor/wukong-whee-editor-plugin/src';
import { WheeLayerEditorPlugin } from '@/editor/wukong-whee-layer-editor-plugin/src';
import useStore from '@/hooks/useStore';
import message from '../components/Toast';
import { useAccountProfile } from '@/hooks';
import { LASSO_TOOL_STROKE_WIDTH, SelectionColor } from '../constant/selection';
import { SmartSelectionToolPlugin } from '../plugins/SmartSelectionToolPlugin';
import { getGrayMaskBlob, paintMaskFromGray } from '../utils/maskImageTools';
import { uploaderFunc } from '@/containers/ImageEditor/utils/upload';
import { getClock } from '../utils/clock';
import { interactSelection } from '@/api/imageEditor';
import { createImage } from '@/utils/cropImage';
import { v4 as uuidv4 } from 'uuid';
import { loadingToDrawing } from '@/editor/effect/src';
import { handleInvalidTokenError } from '../utils/handleRequestError';
import { MtccFuncCode } from '@/api/types';

// 交互选择区 选择超时时间
const SMART_SELECTION_TIMEOUT = 10 * 1000;

export type ToolPluginsType = {
  rectTool?: RectTool;
  eraserTool?: EraserTool;
  lineTool?: LineTool;
  lassoTool?: LassoTool;
  smartSelectionTool?: SmartSelectionToolPlugin;
};

//#region 选择方式的参数

// 选择模式：是增加选区还是删除选区
type ParamsAreaSelectionMode = {
  selectionMode: AreaSelectionMode | null;
  paintWidth?: number | null;
};

type AiEraserSelectionRef = {
  selectionWay: null | string;
  selectionMode: null | string;
  paintMaskCanvas?: HTMLCanvasElement | null;
  mask?: HTMLElement | null;
};
// 涂抹的尺寸
type ParamsPaintWidth = {
  paintWidth: number;
};

export type AIEraserSelectionWay =
  | SelectionWay.Smart
  | SelectionWay.Lasso
  | SelectionWay.Repair
  | SelectionWay.Box;

export type AreaSelectionParams<T extends AIEraserSelectionWay> =
  T extends SelectionWay.Box
    ? ParamsAreaSelectionMode
    : T extends SelectionWay.Lasso
    ? ParamsAreaSelectionMode
    : T extends SelectionWay.Repair
    ? ParamsAreaSelectionMode & ParamsPaintWidth
    : T extends SelectionWay.Smart
    ? ParamsAreaSelectionMode
    : never;

//#endregion

type InstallCleanup = () => void;
type SetParamsType = (params: any, preParams: null | any) => void;
type Installer<T extends AIEraserSelectionWay> = (options: {
  app: App;
  toolPlugins: ToolPluginsType;
  wheeEditorPlugin: WheeEditorPlugin | WheeLayerEditorPlugin;
  hasPaintedPromiseRef: React.MutableRefObject<Promise<boolean>>;
}) => {
  cleanup: InstallCleanup;
  initParams: AreaSelectionParams<T>;
  setParams: SetParamsType;
};

// 智能选择涂抹宽度
const SMART_PAINT_WIDTH = 20;

// 涂抹的默认宽度
export const DEFAULT_PAINT_WIDTH = 30;

const ToolMapping: Record<
  AIEraserSelectionWay,
  Installer<AIEraserSelectionWay>
> = {
  [SelectionWay.Box]: ({ app, toolPlugins }) => {
    const initParams: AreaSelectionParams<SelectionWay.Box> = {
      selectionMode: AreaSelectionMode.Add
    };

    function setParams(
      params: AreaSelectionParams<SelectionWay.Box>,
      preParams: AreaSelectionParams<SelectionWay.Box> | null
    ) {
      if (!params.selectionMode) {
        return;
      }

      if (!preParams || params.selectionMode !== preParams.selectionMode) {
        if (!toolPlugins.rectTool) return;
        if (!toolPlugins.eraserTool) return;
        switch (params.selectionMode) {
          case AreaSelectionMode.Add: {
            app.setTool(toolPlugins.rectTool);
            break;
          }

          case AreaSelectionMode.Remove: {
            const eraserTool = toolPlugins.eraserTool;
            eraserTool.registerConfig({
              shape: EraserShape.Rect,
              globalCompositeOperation: 'source-over',
              backgroundColor: SelectionColor.Negative,
              lineCap: LineCap.square,
              lineJoin: LineJoin.bevel,
              strokeWidth: 0,
              listening: false
            });
            app.setTool(eraserTool);
            break;
          }
        }
        app.lockAllShapes();
      }
    }

    function cleanup() {
      app.destroyTool();
    }

    return {
      initParams,
      setParams,
      cleanup
    };
  },

  [SelectionWay.Lasso]: ({ app, toolPlugins }) => {
    const initParams: AreaSelectionParams<SelectionWay.Lasso> = {
      selectionMode: AreaSelectionMode.Add
    };

    let currentParams: null | AreaSelectionParams<SelectionWay.Lasso> = null;

    function setParams(
      params: AreaSelectionParams<SelectionWay.Lasso>,
      preParams: AreaSelectionParams<SelectionWay.Lasso>
    ) {
      if (!params.selectionMode) {
        return;
      }

      currentParams = params;
      if (!toolPlugins.lassoTool) return;
      if (!toolPlugins.eraserTool) return;
      switch (params.selectionMode) {
        case AreaSelectionMode.Add: {
          if (!preParams || preParams.selectionMode !== AreaSelectionMode.Add) {
            app.setTool(toolPlugins.lassoTool);
            app.lockAllShapes();
          }

          toolPlugins.lassoTool.registerConfig({
            ...toolPlugins.lassoTool.config,
            strokeWidth: LASSO_TOOL_STROKE_WIDTH / app.stage.scaleX()
          });
          break;
        }

        case AreaSelectionMode.Remove: {
          const eraserTool = toolPlugins.eraserTool;
          eraserTool.registerConfig({
            shape: EraserShape.Lasso,
            globalCompositeOperation: 'source-over',
            lineCap: LineCap.round,
            lineJoin: LineJoin.round,
            strokeWidth: LASSO_TOOL_STROKE_WIDTH / app.stage.scaleX(),
            strokeColor: SelectionColor.Negative,
            listening: false
          });

          if (
            !preParams ||
            preParams.selectionMode !== AreaSelectionMode.Remove
          ) {
            app.setTool(eraserTool);
            app.lockAllShapes();
          }
          break;
        }
      }
    }

    function handleZoomChange() {
      const params = currentParams;
      if (!params) {
        return;
      }
      if (!toolPlugins.lassoTool) return;
      if (!toolPlugins.eraserTool) return;
      switch (params.selectionMode) {
        case AreaSelectionMode.Add: {
          toolPlugins.lassoTool.registerConfig({
            ...toolPlugins.lassoTool.config,
            strokeWidth: LASSO_TOOL_STROKE_WIDTH / app.stage.scaleX()
          });
          break;
        }

        case AreaSelectionMode.Remove: {
          const eraserTool = toolPlugins.eraserTool;
          eraserTool.registerConfig({
            shape: EraserShape.Lasso,
            globalCompositeOperation: 'source-over',
            strokeColor: SelectionColor.Negative,
            lineCap: LineCap.round,
            lineJoin: LineJoin.round,
            strokeWidth: LASSO_TOOL_STROKE_WIDTH / app.stage.scaleX(),
            listening: false
          });
          break;
        }
      }
    }

    app.on(CanvasZoomEventName.end, handleZoomChange);

    function cleanup() {
      app.destroyTool();
      app.off(CanvasZoomEventName.end, handleZoomChange);
    }

    return {
      initParams,
      setParams,
      cleanup
    };
  },

  [SelectionWay.Repair]: ({ app, toolPlugins }) => {
    const initParams: AreaSelectionParams<SelectionWay.Repair> = {
      selectionMode: AreaSelectionMode.Add,
      paintWidth: DEFAULT_PAINT_WIDTH
    };

    let currentParams: null | AreaSelectionParams<SelectionWay.Repair> = null;

    function setParams(
      params: AreaSelectionParams<SelectionWay.Repair>,
      preParams: AreaSelectionParams<SelectionWay.Repair>
    ) {
      if (!params.selectionMode) {
        return;
      }

      currentParams = params;
      if (!toolPlugins.lineTool) return;
      if (!toolPlugins.eraserTool) return;

      switch (params.selectionMode) {
        case AreaSelectionMode.Add: {
          const lineTool = toolPlugins.lineTool;
          typeof params.paintWidth === 'number' &&
            lineTool.setStrokeWidth(params.paintWidth / app.stage.scaleX());

          if (!preParams || preParams.selectionMode !== AreaSelectionMode.Add) {
            app.setTool(lineTool);
            app.lockAllShapes();
          }
          break;
        }

        case AreaSelectionMode.Remove: {
          const eraserTool = toolPlugins.eraserTool;
          eraserTool.registerConfig({
            shape: EraserShape.Line,
            globalCompositeOperation: 'source-over',
            strokeColor: SelectionColor.Negative,
            lineCap: LineCap.round,
            lineJoin: LineJoin.round,
            strokeWidth: params.paintWidth / app.stage.scaleX(),
            listening: false
          });

          if (
            !preParams ||
            preParams.selectionMode !== AreaSelectionMode.Remove
          ) {
            app.setTool(eraserTool);
            app.lockAllShapes();
          }
          break;
        }
      }
    }

    function handleZoomChange() {
      const params = currentParams;
      if (!params) {
        return;
      }
      if (!toolPlugins.lineTool) return;
      if (!toolPlugins.eraserTool) return;
      switch (params.selectionMode) {
        case AreaSelectionMode.Add: {
          const lineTool = toolPlugins.lineTool;
          typeof params.paintWidth === 'number' &&
            lineTool.setStrokeWidth(params.paintWidth / app.stage.scaleX());
          break;
        }

        case AreaSelectionMode.Remove: {
          const eraserTool = toolPlugins.eraserTool;
          eraserTool.registerConfig({
            shape: EraserShape.Line,
            globalCompositeOperation: 'source-over',
            strokeColor: SelectionColor.Negative,
            lineCap: LineCap.round,
            lineJoin: LineJoin.round,
            strokeWidth: params.paintWidth / app.stage.scaleX(),
            listening: false
          });
          break;
        }
      }
    }

    app.on(CanvasZoomEventName.end, handleZoomChange);

    function cleanup() {
      app.destroyTool();
      app.off(CanvasZoomEventName.end, handleZoomChange);
    }

    return {
      initParams,
      setParams,
      cleanup
    };
  },

  [SelectionWay.Smart]: ({ app, toolPlugins, hasPaintedPromiseRef }) => {
    const initParams: AreaSelectionParams<SelectionWay.Repair> = {
      selectionMode: AreaSelectionMode.Add,
      paintWidth: SMART_PAINT_WIDTH
    };

    let currentParams: null | AreaSelectionParams<SelectionWay.Repair> = null;
    function setParams(
      params: AreaSelectionParams<SelectionWay.Repair>,
      preParams: AreaSelectionParams<SelectionWay.Repair>
    ) {
      if (!params.selectionMode) {
        return;
      }

      currentParams = params;

      if (
        toolPlugins?.smartSelectionTool &&
        (!preParams || params.selectionMode !== preParams.selectionMode)
      ) {
        switch (params.selectionMode) {
          case AreaSelectionMode.Add: {
            app.setTool(toolPlugins.smartSelectionTool);
            toolPlugins.smartSelectionTool.isEraserMode = false;
            toolPlugins.smartSelectionTool.setStrokeWidth(
              SMART_PAINT_WIDTH / app.stage.scaleX()
            );
            break;
          }

          case AreaSelectionMode.Remove: {
            app.setTool(toolPlugins.smartSelectionTool);
            toolPlugins.smartSelectionTool.isEraserMode = true;
            toolPlugins.smartSelectionTool.setStrokeWidth(
              SMART_PAINT_WIDTH / app.stage.scaleX()
            );
            break;
          }
        }

        app.lockAllShapes();
      }
    }

    function handleZoomChange() {
      const params = currentParams;
      if (!params) {
        return;
      }
      toolPlugins?.smartSelectionTool?.setStrokeWidth(
        SMART_PAINT_WIDTH / app.stage.scaleX()
      );
    }

    app.on(CanvasZoomEventName.end, handleZoomChange);

    toolPlugins.smartSelectionTool?.injectHasPaintedChecker(
      () => hasPaintedPromiseRef.current
    );

    function cleanup() {
      app.off(CanvasZoomEventName.end, handleZoomChange);
      app.destroyTool();
      toolPlugins.smartSelectionTool?.injectHasPaintedChecker(() =>
        Promise.resolve(false)
      );
    }

    return {
      initParams,
      setParams,
      cleanup
    };
  }
};

const TipsMapping: Record<
  AIEraserSelectionWay,
  Partial<Record<AreaSelectionMode, string>>
> = {
  [SelectionWay.Smart]: {
    [AreaSelectionMode.Add]: '请点击或涂抹想要消除的区域',
    [AreaSelectionMode.Remove]: '请点击或涂抹想要去除的区域'
  },
  [SelectionWay.Box]: {
    [AreaSelectionMode.Add]: '请框选想要消除的区域',
    [AreaSelectionMode.Remove]: '请框选想要去除的区域'
  },
  [SelectionWay.Lasso]: {
    [AreaSelectionMode.Add]: '请圈出想要消除的区域',
    [AreaSelectionMode.Remove]: '请圈出想要去除的区域'
  },
  [SelectionWay.Repair]: {
    [AreaSelectionMode.Add]: '请涂抹想要消除的区域',
    [AreaSelectionMode.Remove]: '请涂抹想要去除的区域'
  }
};

type UseAreaSelectionOptions = {
  app?: App;
  toolPlugins?: ToolPluginsType;
  wheeEditorPlugin?: WheeEditorPlugin | WheeLayerEditorPlugin;
  hasPaintedPromiseRef?: React.MutableRefObject<Promise<boolean>>;
};

export function useAreaSelection({
  app,
  toolPlugins,
  wheeEditorPlugin,
  hasPaintedPromiseRef
}: UseAreaSelectionOptions) {
  const [selectionWay, setSelectionWay] = useState<AIEraserSelectionWay | null>(
    null
  );
  const [selectionParams, setSelectionParams] = useState<
    AreaSelectionParams<AIEraserSelectionWay>
  >({ selectionMode: AreaSelectionMode.Add });

  const setParamsRef = useRef<null | SetParamsType>(null);

  const aiEraserSelectionRef = useRef<AiEraserSelectionRef>({
    selectionWay: null,
    selectionMode: null,
    paintMaskCanvas: null
  });

  const editorStore = useStore('EditorStore');
  const { setMouseStyleType } = editorStore;
  const interactMessageErr = useRef<null | (() => void)>(null);
  useAreaSelectionTips({
    app,
    selectionWay,
    selectionMode: selectionParams.selectionMode,
    hasPaintedPromiseRef
  });

  useEffect(() => {
    if (!selectionWay) {
      return;
    }
    if (!(app && toolPlugins && wheeEditorPlugin && hasPaintedPromiseRef))
      return;
    const install = ToolMapping[selectionWay];
    const { initParams, setParams, cleanup } = install({
      app,
      toolPlugins,
      wheeEditorPlugin,
      hasPaintedPromiseRef
    });
    setSelectionParams(initParams);
    setParamsRef.current = setParams;
    app?.lockAllShapes();
    return () => {
      setParamsRef.current = null;
      cleanup();
      app?.unlockAllShapes();
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [app, toolPlugins, selectionWay, wheeEditorPlugin]);

  //监听鼠标状态 更新鼠标样式及轮廓光显隐逻辑
  useEffect(() => {
    const selectionData = {
      selectionWay: selectionWay,
      selectionMode: selectionParams.selectionMode
    };
    aiEraserSelectionRef.current = {
      ...aiEraserSelectionRef.current,
      ...selectionData
    };
    setMouseStyleType({
      selectionParams: selectionParams.selectionMode,
      selectionWay:
        selectionWay === SelectionWay.Repair ? 'paint' : selectionWay,
      paintWidth:
        selectionWay === SelectionWay.Smart
          ? SMART_PAINT_WIDTH
          : selectionParams.paintWidth,
      content: app?.stage.content,
      app
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectionParams, selectionWay]);

  const preParams = useRef<AreaSelectionParams<AIEraserSelectionWay> | null>(
    null
  );
  useEffect(() => {
    preParams.current = null;
  }, [selectionWay]);
  useEffect(() => {
    setParamsRef.current?.(selectionParams, preParams.current);
    preParams.current = selectionParams;
  }, [selectionParams]);

  useEffect(() => {
    function reset() {
      setSelectionWay(null);
      app?.destroyTool();
    }

    app?.on(CanvasNodeEventName.select, reset);
    const paintMaskCanvas = document.createElement('canvas');
    aiEraserSelectionRef.current.paintMaskCanvas = paintMaskCanvas;
    const rectTooler = editorStore?.layerEditor?.rectTooler;
    if (rectTooler) {
      rectTooler.hooks.beforeAdd = beforeAdd;
      rectTooler.hooks.afterAdd = afterAdd;
    }
    const lassoTooler = editorStore?.layerEditor?.lassoTooler;
    if (lassoTooler) {
      lassoTooler.hooks.beforeAdd = beforeAdd;
      lassoTooler.hooks.afterAdd = afterAdd;
    }

    const eraserTooler = editorStore?.layerEditor?.eraserTooler;
    if (eraserTooler) {
      eraserTooler.hooks.beforeAdd = beforeAdd;
      eraserTooler.hooks.afterAdd = afterAdd;
    }

    return () => {
      app?.off(CanvasNodeEventName.select, reset);
      aiEraserSelectionRef.current.paintMaskCanvas = null;
      const mask = aiEraserSelectionRef.current.mask;
      if (mask) {
        document.body.removeChild(mask);
        aiEraserSelectionRef.current.mask = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [app]);
  function isActiveInteract() {
    const isAddType = ['box', 'lasso'];
    if (
      !(
        aiEraserSelectionRef.current.selectionWay &&
        isAddType.includes(aiEraserSelectionRef.current.selectionWay)
      )
    ) {
      return false;
    }
    return true;
  }
  function beforeAdd() {
    if (!isActiveInteract()) return;
    editorStore.layerEditor?.historyPlugin.disable();
  }
  async function afterAdd(node: Rectangle | Line | Eraser<EraserShapeType>) {
    if (!isActiveInteract()) return;
    editorStore.layerEditor?.historyPlugin.enable();
    await interact(node);
  }
  function openMask() {
    const mask = document.createElement('div');
    mask.style.width = '100vw';
    mask.style.height = '100vh';
    mask.style.position = 'fixed';
    mask.style.left = '0';
    mask.style.top = '0';
    mask.style.zIndex = '1000';
    document.body.append(mask);
    aiEraserSelectionRef.current.mask = mask;
    const stopWheel = (event: MouseEvent) => {
      event.preventDefault(); // 阻止默认行为
    };
    // 禁用该元素上的 wheel 事件
    mask.addEventListener('wheel', stopWheel, { passive: false });
    return () => {
      mask.removeEventListener('wheel', stopWheel);
      document.body.removeChild(mask);
      aiEraserSelectionRef.current.mask = null;
    };
  }
  const interact = async (
    oldNode: Rectangle | Line | Eraser<EraserShapeType>
  ) => {
    //画板 没有选区的时候 不执行
    if (!(await hasPaintedPromiseRef?.current)) {
      return;
    }
    if (!wheeEditorPlugin) return;
    if (interactMessageErr.current) interactMessageErr.current();
    const closeMask = openMask();
    const drawAnimation = app && loadingToDrawing(app);
    drawAnimation?.start();
    const { destroy: closeToast } = message({
      content: '识别中，请稍后...',
      type: 'loading',
      isShowMask: false
    });
    setTimeout(async () => {
      let node = null as null | Custom | undefined;
      try {
        node = await getNewCustom();
      } catch (e: any) {
        if (handleInvalidTokenError(e)) {
          return;
        }

        let { destroy: messageDestroy } = message({
          content: '识别失败，请重试',
          type: 'error',
          isShowMask: false,
          duration: 3000
        });
        interactMessageErr.current = messageDestroy;
      } finally {
        closeMask();
        closeToast();
        drawAnimation?.stop();
        drawAnimation?.destroy();
        editorStore.layerEditor?.historyPlugin.disable();
        app?.remove(oldNode);
        editorStore.layerEditor?.historyPlugin.enable();
        if (node) {
          editorStore.layerEditor?.historyPlugin.enableCache();
          wheeEditorPlugin?.plugin?.clearMask({ autoCommitHistory: false });
          wheeEditorPlugin?.plugin?.addOptions(node);
          editorStore.layerEditor?.historyPlugin.commitCache();
        }
      }
    });
  };

  //获得原始图片
  const getInitImage = async (remainingTime: number) => {
    return await uploaderFunc(
      await editorStore.exportLayerCoverImage(),
      '.jpg',
      remainingTime
    );
  };

  const getMaskImage = async (remainingTime: number) => {
    if (!wheeEditorPlugin) return;
    const preMask = await wheeEditorPlugin.plugin?.getOptionsLayer(
      'image/jpeg',
      '#000000'
    );

    if (!preMask) return;
    const preGrayMask = await getGrayMaskBlob(await preMask);
    return (
      preGrayMask && (await uploaderFunc(preGrayMask, 'jpg', remainingTime))
    );
  };

  const getNewCustom = async () => {
    let remainingTime = SMART_SELECTION_TIMEOUT;
    const clock = getClock();
    const preMaskURL = await getMaskImage(remainingTime);
    remainingTime -= clock.getDelta();
    let initImage = '';
    const image = app?.stage.find('Image')[0];
    initImage = image?.getAttr('target').config.source;
    if (!initImage) {
      let initImageBack = await getInitImage(remainingTime);
      remainingTime -= clock.getDelta();
      if (!initImageBack) {
        return;
      }
      initImage = initImageBack?.url;
    }
    const visibleRect = wheeEditorPlugin?.plugin?.getVisibleAreaRect();
    if (!visibleRect) {
      return;
    }
    const response = await interactSelection(
      {
        projectId: editorStore.projectInfo.id,
        initImage: initImage,
        maskImage: preMaskURL && preMaskURL.url,
        interact_type:
          aiEraserSelectionRef.current.selectionWay === 'box' ? '0' : '1',
        functionName: MtccFuncCode.FuncCodeInteract
      },
      remainingTime
    );
    const newMask = response.resultImage;
    const maskImage = await createImage(newMask);
    // 用来绘制mask的canvas
    const paintMaskCanvas = aiEraserSelectionRef.current.paintMaskCanvas;
    if (!paintMaskCanvas) return;
    paintMaskFromGray(maskImage, paintMaskCanvas);
    const imageDataURL = paintMaskCanvas.toDataURL('image/png');
    const transformedMaskImage = await createImage(imageDataURL);
    const { x, y } = visibleRect;
    const node = new Custom({
      type: ShapeType.Custom,
      id: uuidv4(),
      x,
      y,
      sceneFunc: (context) => {
        const groupFirst =
          editorStore.layerEditor?.wheeLayerEditorPlugin.plugin?.optionsLayer.find(
            'Group'
          )[0];
        if (!groupFirst) return;
        context.drawImage(
          transformedMaskImage,
          0,
          0,
          groupFirst.width(),
          groupFirst.height()
        );
      }
    });
    return node;
  };

  return {
    selectionWay,
    onSelectionWayChange: setSelectionWay,
    selectionParams: selectionWay && selectionParams,
    onSelectionParamsChange: setSelectionParams
  };
}

type UseAreaSelectionTipsOptions = {
  app?: App;
  selectionWay?: AIEraserSelectionWay | null;
  selectionMode?: AreaSelectionMode | null;
  hasPaintedPromiseRef?: React.MutableRefObject<Promise<boolean>>;
};
function useAreaSelectionTips({
  app,
  selectionWay,
  selectionMode,
  hasPaintedPromiseRef
}: UseAreaSelectionTipsOptions) {
  const { accountProfile } = useAccountProfile();
  const userId = accountProfile?.id ?? '';

  const editorStore = useStore('EditorStore');
  const projectId = editorStore.projectInfo.id;

  const getTipsKey = useCallback(
    () =>
      `${userId}:layer:aiEraser:selections:tips:${selectionWay}:${selectionMode}`,
    [userId, selectionWay, selectionMode]
  );

  const hasTipped = useCallback(() => {
    const tipKey = getTipsKey();
    return !!localStorage.getItem(tipKey);
  }, [getTipsKey]);

  useEffect(() => {
    if (!selectionWay || !selectionMode) {
      return;
    }

    if (hasTipped() || !userId || !projectId) {
      return;
    }

    const tipsContent = TipsMapping[selectionWay][selectionMode];

    let isIgnore = false;
    let closeTips: null | (() => void) = null;
    hasPaintedPromiseRef?.current.then((hasPainted) => {
      if (
        (!hasPainted && selectionMode === AreaSelectionMode.Remove) ||
        isIgnore
      ) {
        return;
      }

      const { destroy } = message({
        type: 'info',
        content: tipsContent || ''
      });

      closeTips = destroy;
    });

    // 涂抹之后关闭toast
    function closeTipsWhenPaint(payload: { nodes: BaseShape<ShapeConfig>[] }) {
      if (!closeTips) {
        return;
      }

      const { nodes } = payload;
      for (const n of nodes) {
        if (n.config.type !== ShapeType.Image) {
          closeTips();
          app?.off(CanvasNodeEventName.add, closeTipsWhenPaint);
          localStorage.setItem(getTipsKey(), 'true');
        }
      }
    }

    app?.on(CanvasNodeEventName.add, closeTipsWhenPaint);

    return () => {
      isIgnore = true;
      closeTips?.();
      app?.off(CanvasNodeEventName.add, closeTipsWhenPaint);
    };
  }, [
    selectionWay,
    selectionMode,
    hasTipped,
    getTipsKey,
    app,
    projectId,
    userId,
    hasPaintedPromiseRef
  ]);
}
