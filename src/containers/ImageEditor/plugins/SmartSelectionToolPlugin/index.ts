import {
  App,
  Custom,
  Eraser,
  EraserShapeType,
  KonvaMouseEvent,
  Line,
  ShapeType
} from '@/editor/core/src';
import { BaseLineConfig, LineTool } from '@/editor/utils/src';
import { CONFIG_DEFAULT } from '@/editor/utils/src/lineTool/config';
import { HistoryPlugin } from '@/editor/wukong-history-plugin/src';
import { WheeEditorPlugin } from '@/editor/wukong-whee-editor-plugin/src';
import { WheeLayerEditorPlugin } from '@/editor/wukong-whee-layer-editor-plugin/src';
import { v4 as uuidv4 } from 'uuid';
import {
  canvasToBlob,
  getPreMaskWithCanvas,
  paintMaskFromGray
} from '../../utils/maskImageTools';
import { loadingToDrawing } from '@/editor/effect/src';
import message from '../../components/Toast';
import { uploaderFunc } from '../../utils/upload';
import { fetchSmartSelection } from '@/api/imageEditor';
import { createImage } from '@/utils/cropImage';
import { getClock } from '../../utils/clock';
import { handleInvalidTokenError } from '../../utils/handleRequestError';
import { wrapTimeout } from '../../utils/wrapTimeout';
import { filterMaskWithImageAlpha } from '../../utils/filterImage';
import { MtccFuncCode } from '@/api/types';

type SmartSelectionToolPluginDeps = {
  app: App;
  historyPlugin: HistoryPlugin;
  wheeEditorPlugin: WheeEditorPlugin | WheeLayerEditorPlugin;
  getOriginImage(): Promise<string | undefined>;
  getProjectId(): number;
  getHasPainted(): Promise<boolean>;
  transformCustom?: (node: Custom, image: HTMLImageElement) => any;
  hooks?: {
    addMaskNodeAfter: () => void;
  };
};

type SmartSelectionToolConfig = BaseLineConfig & {
  // 涂抹模式的颜色
  paintModeColor: string;
  // 擦除模式的颜色
  eraserModeColor: string;
};

// 智能选择超时时间
const SMART_SELECTION_TIMEOUT = 10 * 1000;

// click_list相邻两点之间的距离不能超过40
// const CLICK_LIST_MIN_DISTANCE = 40;

// 用来绘制mask的canvas
const paintMaskCanvas = document.createElement('canvas');

export class SmartSelectionToolPlugin extends LineTool {
  config: SmartSelectionToolConfig = {
    ...CONFIG_DEFAULT,
    paintModeColor: '#0000ff7f',
    eraserModeColor: '#ff00007f'
  };

  private _isEraserMode = false;

  constructor(private deps: SmartSelectionToolPluginDeps) {
    super(() => {});
  }

  public registerConfig(config: SmartSelectionToolConfig): void {
    super.registerConfig(config);

    if (this._isEraserMode) {
      this.selection.stroke(config.eraserModeColor);
    } else {
      this.selection.stroke(config.paintModeColor);
    }
  }

  public set isEraserMode(isEraser: boolean) {
    this._isEraserMode = isEraser;
    this.registerConfig(this.config);
  }

  public get isEraserMode() {
    return this._isEraserMode;
  }

  public injectHasPaintedChecker(checker: () => Promise<boolean>) {
    this.deps.getHasPainted = checker;
  }

  protected createEraser(newLine: Line) {
    const points = newLine.points();
    const justPoint = this.checkIsJustPoint(points);

    const eraser = justPoint
      ? new Eraser(EraserShapeType.Eliipse, {
          type: ShapeType.Eraser,
          shapeType: EraserShapeType.Eliipse,
          id: uuidv4() as string,
          x: points[0] as number,
          y: points[1] as number,
          radiusX: (newLine.strokeWidth() / 2) as number,
          radiusY: (newLine.strokeWidth() / 2) as number,
          backgroundColor: 'black' as string,
          opacity: 1 as number,
          globalCompositeOperation: 'destination-out'
        })
      : new Eraser(EraserShapeType.Line, {
          type: ShapeType.Eraser,
          shapeType: EraserShapeType.Line,
          globalCompositeOperation: 'destination-out',
          points,
          x: newLine.x(),
          y: newLine.y(),
          lineCap: newLine.lineCap() as any,
          lineJoin: newLine.lineJoin() as any,
          backgroundColor: 'black',
          stroke: 'black',
          strokeWidth: newLine.strokeWidth(),
          id: uuidv4() as string,
          opacity: 1
        });

    return eraser;
  }

  /**
   * 上一次识别失败Toast的关闭函数
   * 多次识别失败时，应该先关闭上一次识别失败的Toast
   */
  private closeFailedToast: null | (() => void) = null;
  public onMouseUp = async (konvaEvent?: { event: KonvaMouseEvent }) => {
    if (
      (konvaEvent &&
        konvaEvent.event.evt.button !== 0 &&
        !konvaEvent.event.isMobile) ||
      !this.listening
    ) {
      return;
    }

    this.closeFailedToast?.();
    this.closeFailedToast = null;

    this.listening = false;
    const newLine = this.selection.clone();

    const { app, historyPlugin, wheeEditorPlugin } = this.deps;

    const points = newLine.points();
    const brushWidth = newLine.strokeWidth();

    const line = this._isEraserMode
      ? this.createEraser(newLine)
      : this.createShape(newLine);

    const preMaskCanvas =
      await wheeEditorPlugin.plugin?.getOptionsLayerWithCanvas();

    if (!preMaskCanvas) {
      this.selection.visible(false);
      this.selection.points([0, 0, 0, 0]);
      return;
    }

    const visibleRect = wheeEditorPlugin.plugin?.getVisibleAreaRect();
    if (!visibleRect) {
      return;
    }

    historyPlugin.disable();
    wheeEditorPlugin.plugin?.addOptions(line);
    historyPlugin.enable();

    this.selection.visible(false);
    this.selection.points([0, 0, 0, 0]);

    const closeMask = openMask();
    const drawAnimation = loadingToDrawing(app);
    drawAnimation?.start();
    const { destroy: closeToast } = message({
      content: '识别中，请稍后...',
      type: 'loading',
      isShowMask: false
    });

    let node = null as null | Custom | undefined;
    try {
      node = await this.handleSmartPaint({
        points,
        preMaskCanvas,
        brushWidth
      });
    } catch (e: any) {
      if (process.env.NODE_ENV === 'development') {
        console.error(e);
      }

      if (handleInvalidTokenError(e)) {
        return;
      }

      const handler = message({
        content: '识别失败，请重试',
        type: 'error',
        isShowMask: false,
        duration: 3000,
        onClose: () => {
          this.closeFailedToast = null;
        }
      });

      this.closeFailedToast = handler.destroy.bind(handler);
    } finally {
      closeMask();
      closeToast();
      drawAnimation?.stop();
      drawAnimation?.destroy();

      historyPlugin.disable();
      app.remove(line);
      historyPlugin.enable();

      if (node) {
        historyPlugin.enableCache();
        wheeEditorPlugin.plugin?.clearMask({ autoCommitHistory: false });
        wheeEditorPlugin.plugin?.addOptions(node);
        await this.deps?.hooks?.addMaskNodeAfter();
        historyPlugin.commitCache();
      }
    }
  };

  handleSmartPaint = async ({
    points,
    preMaskCanvas,
    brushWidth
  }: {
    points: number[];
    preMaskCanvas: HTMLCanvasElement;
    brushWidth: number;
  }) => {
    let remainingTime = SMART_SELECTION_TIMEOUT;
    const clock = getClock();

    const { wheeEditorPlugin, getOriginImage } = this.deps;

    const visibleRect = wheeEditorPlugin.plugin?.getVisibleAreaRect();
    if (!visibleRect) {
      return;
    }

    let clickList: [number, number, number][] = [];
    for (let i = 0; i < points.length; i += 2) {
      clickList.push([
        this._isEraserMode ? 0 : 1,
        (points[i + 1] - visibleRect.y) / visibleRect.height,
        (points[i] - visibleRect.x) / visibleRect.width
      ]);
    }

    clickList = clickList.filter(
      (p) => p && p[1] >= 0 && p[1] <= 1 && p[2] >= 0 && p[2] <= 1
    );

    // 没有可见区域内的交互点
    if (!clickList.length) {
      return;
    }

    // 可见区域内没有被涂抹的地方
    const hasPainted = await this.deps.getHasPainted();
    if (!hasPainted) {
      return;
    }

    const preGrayMaskCanvas = getPreMaskWithCanvas(preMaskCanvas);
    if (!preGrayMaskCanvas) {
      return;
    }
    // preMaskCanvas.style.position = 'static';
    // document.body.appendChild(preMaskCanvas);
    // document.body.appendChild(cloneCanvas(preGrayMaskCanvas));

    const preGrayMaskBlob = await canvasToBlob(preGrayMaskCanvas, 'image/jpeg');

    remainingTime -= clock.getDelta();
    const preMaskURL = await uploaderFunc(
      preGrayMaskBlob,
      'jpg',
      remainingTime
    );

    remainingTime -= clock.getDelta();
    const initImage = await wrapTimeout(getOriginImage, remainingTime)();
    if (!initImage) {
      return;
    }

    remainingTime -= clock.getDelta();
    const originImage = await wrapTimeout(
      createImage,
      remainingTime
    )(initImage);

    remainingTime -= clock.getDelta();
    const response = await fetchSmartSelection(
      {
        projectId: this.deps.getProjectId(),
        initImage,
        maskImage: preMaskURL && preMaskURL.url,
        clickList,
        // 将笔刷宽度除以短边
        brushWidth: (
          brushWidth / (Math.min(originImage.width, originImage.height) || 1)
        ).toString(),
        functionName: MtccFuncCode.FuncCodeSelect
      },
      remainingTime
    );
    const newMask = response.resultImage;
    remainingTime -= clock.getDelta();
    const maskImage = await wrapTimeout(createImage, remainingTime)(newMask);

    const clippedMaskCanvas = filterMaskWithImageAlpha(originImage, maskImage);
    const clippedMaskDataURL = clippedMaskCanvas?.toDataURL('image/png');
    if (!clippedMaskDataURL) {
      return;
    }
    const clippedMaskImage = await createImage(clippedMaskDataURL);
    paintMaskFromGray(clippedMaskImage, paintMaskCanvas);

    // paintMaskFromGray(maskImage, paintMaskCanvas);
    const imageDataURL = paintMaskCanvas.toDataURL('image/png');
    const transformedMaskImage = await createImage(imageDataURL);
    const { x, y } = visibleRect;
    let node = new Custom({
      type: ShapeType.Custom,
      id: uuidv4(),
      x,
      y,
      sceneFunc: (context, shape) => {
        context.drawImage(transformedMaskImage, 0, 0);
      },
      getSelfRect: () => {
        return {
          x: 0,
          y: 0,
          width: transformedMaskImage.width,
          height: transformedMaskImage.height
        };
      }
    });

    if (this.deps.transformCustom) {
      node = this.deps.transformCustom(node, transformedMaskImage);
    }

    return node;
  };
}

// function filterPointsInVisibleArea(
//   points: number[],
//   visibleRect: { x: number; y: number; width: number; height: number }
// ) {
//   const result = [];
//   for (let i = 0; i < points.length; i += 2) {
//     const x = (points[i] - visibleRect.x) / visibleRect.width;
//     const y = (points[i + 1] - visibleRect.y) / visibleRect.height;

//     if (x >= 0 && x <= 1 && y >= 0 && y <= 1) {
//       result.push(points[i], points[i + 1]);
//     }
//   }

//   return result;
// }

/**
 * 将涂抹的点稀疏化
 * @param points 存储了所有点的flat数组
 * @param maxLimit
 */
// function sampleClickList(
//   flatPoints: number[],
//   visibleRect: { x: number; y: number; width: number; height: number },
//   minDIstance: number = CLICK_LIST_MIN_DISTANCE
// ) {
//   if (minDIstance <= 0) {
//     return flatPoints;
//   }

//   const points = filterPointsInVisibleArea(flatPoints, visibleRect).reduce(
//     (ps, p, i) => {
//       if (i % 2 === 0) {
//         ps.push([p, 0]);
//       } else {
//         ps[ps.length - 1][1] = p;
//       }

//       return ps;
//     },
//     [] as [number, number][]
//   );

//   let prePoint = points[0];
//   const result = [...prePoint];

//   for (let i = 1; i < points.length; ++i) {
//     const p = points[i];
//     const deltaX = prePoint[0] - p[0];
//     const deltaY = prePoint[1] - p[1];

//     if (
//       deltaX * deltaX + deltaY * deltaY >
//       CLICK_LIST_MIN_DISTANCE * CLICK_LIST_MIN_DISTANCE
//     ) {
//       result.push(...p);
//       prePoint = p;
//     }
//   }

//   return result;
// }

function openMask() {
  const mask = document.createElement('div');
  mask.style.width = '100vw';
  mask.style.height = '100vh';
  mask.style.position = 'fixed';
  mask.style.left = '0';
  mask.style.top = '0';
  mask.style.zIndex = '1000';
  document.body.append(mask);
  const stopWheel = (event: MouseEvent) => {
    event.preventDefault(); // 阻止默认行为
  };
  // 禁用该元素上的 wheel 事件
  mask.addEventListener('wheel', stopWheel, { passive: false });

  return () => {
    mask.removeEventListener('wheel', stopWheel);
    document.body.removeChild(mask);
  };
}
