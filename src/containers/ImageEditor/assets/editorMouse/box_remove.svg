<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_12954_3356)">
<mask id="path-1-outside-1_12954_3356" maskUnits="userSpaceOnUse" x="5" y="5" width="17" height="17" fill="black">
<rect fill="white" x="5" y="5" width="17" height="17"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14 6L14 13H13L13 6H14ZM13 14V13H6V14H13ZM14 14V13H21V14H14ZM14 14H13V21H14V14Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14 6L14 13H13L13 6H14ZM13 14V13H6V14H13ZM14 14V13H21V14H14ZM14 14H13V21H14V14Z" fill="#1A1A1A"/>
<path d="M14 6L15 6C15 5.73478 14.8946 5.48043 14.7071 5.29289C14.5196 5.10536 14.2652 5 14 5V6ZM13 6V5C12.4477 5 12 5.44772 12 6L13 6ZM6 13V12C5.44772 12 5 12.4477 5 13H6ZM6 14H5C5 14.5523 5.44772 15 6 15V14ZM21 13H22C22 12.4477 21.5523 12 21 12V13ZM21 14V15C21.5523 15 22 14.5523 22 14H21ZM13 21H12C12 21.5523 12.4477 22 13 22V21ZM14 21V22C14.5523 22 15 21.5523 15 21H14ZM15 13L15 6L13 6L13 13H15ZM13 14H14V12H13V14ZM12 6L12 13H14L14 6L12 6ZM14 5H13V7H14V5ZM14 14V13H12V14H14ZM13 12H6V14H13V12ZM5 13V14H7V13H5ZM6 15H13V13H6V15ZM13 13V14H15V13H13ZM21 12H14V14H21V12ZM22 14V13H20V14H22ZM14 15H21V13H14V15ZM14 13H13V15H14V13ZM12 14V21H14V14H12ZM13 22H14V20H13V22ZM15 21V14H13V21H15Z" fill="white" mask="url(#path-1-outside-1_12954_3356)"/>
</g>
<g filter="url(#filter1_d_12954_3356)">
<mask id="path-3-outside-2_12954_3356" maskUnits="userSpaceOnUse" x="20" y="21" width="7" height="3" fill="black">
<rect fill="white" x="20" y="21" width="7" height="3"/>
<path d="M21 22H26V23H21V22Z"/>
</mask>
<path d="M21 22H26V23H21V22Z" fill="#1A1A1A"/>
<path d="M21 22V21C20.4477 21 20 21.4477 20 22H21ZM26 22H27C27 21.4477 26.5523 21 26 21V22ZM26 23V24C26.5523 24 27 23.5523 27 23H26ZM21 23H20C20 23.5523 20.4477 24 21 24V23ZM21 23H26V21H21V23ZM25 22V23H27V22H25ZM26 22H21V24H26V22ZM22 23V22H20V23H22Z" fill="white" mask="url(#path-3-outside-2_12954_3356)"/>
</g>
<defs>
<filter id="filter0_d_12954_3356" x="3" y="4" width="21" height="21" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12954_3356"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12954_3356" result="shape"/>
</filter>
<filter id="filter1_d_12954_3356" x="18" y="20" width="11" height="7" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12954_3356"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12954_3356" result="shape"/>
</filter>
</defs>
</svg>
