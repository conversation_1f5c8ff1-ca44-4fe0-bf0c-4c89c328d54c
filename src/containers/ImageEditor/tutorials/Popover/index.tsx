import classNames from 'classnames';
import 'driver.js/dist/driver.css';
import styles from './index.module.less';
import { toAtlasImageView2URL } from '@meitu/util';

function create(tagName: keyof HTMLElementTagNameMap, className?: string) {
  const dom = document.createElement(tagName);
  if (className) {
    dom.className = className;
  }

  return dom;
}

// function createImage(src: string, className?: string) {
//   const dom = create('div', className);
//   const root = createRoot(dom);
//   root.render(<Image src={src}/>)
//   return dom;
// }

type PopoverProps = {
  title: string;
  subtitle?: string;
  progress?: [number, number];
  renderBody?(): HTMLElement;
  renderFooter?(): HTMLElement;
};

export function Popover({
  title,
  subtitle,
  progress,
  renderBody,
  renderFooter
}: PopoverProps) {
  // <div className={styles.popover}>
  //   <div className="popover-content">
  //     <header className="popover-content-top">
  //       <span className="popover-content-top-title">{title}</span>
  //       {
  //         progress && (
  //           <span className="popover-content-top-progress">{progress[0]}/{progress[1]}</span>
  //         )
  //       }
  //     </header>

  //     <div className="popover-content-subtitle">
  //       {subtitle}
  //     </div>

  //     <main className="popover-content-main">
  //       {renderBody?.()}
  //     </main>

  //     <footer className="popover-content-footer">
  //       {renderFooter?.()}
  //     </footer>
  //   </div>
  // </div>
  const container = create('div', styles.popover);
  const content = create('div', 'popover-content');
  container.appendChild(content);

  const header = create('header', 'popover-content-top');
  content.appendChild(header);

  // 创建标题元素
  const titleEl = create('span', 'popover-content-top-title');
  titleEl.textContent = title;
  header.appendChild(titleEl);

  // 如果有进度信息，创建进度元素
  if (progress) {
    const progressEl = create('span', 'popover-content-top-progress');
    progressEl.textContent = `${progress[0]}/${progress[1]}`;
    header.appendChild(progressEl);
  }

  // 创建副标题元素
  const subtitleEl = create('div', 'popover-content-subtitle');
  subtitleEl.textContent = subtitle || '';
  content.appendChild(subtitleEl);

  // 创建主要内容区域，并渲染renderBody函数
  const mainEl = create('main', 'popover-content-main');
  if (renderBody) {
    const bodyContent = renderBody();
    mainEl.appendChild(bodyContent);
  }
  content.appendChild(mainEl);

  // 创建页脚区域，并渲染renderFooter函数
  const footerEl = create('footer', 'popover-content-footer');
  if (renderFooter) {
    const footerContent = renderFooter();
    footerEl.appendChild(footerContent);
  }
  content.appendChild(footerEl);

  // 将最终的容器元素返回
  return container;
}

type PopoverCover = {
  src?: string;
};

Popover.CoverImage = function ({ src }: PopoverCover) {
  // <div className="popover-content-main-cover">
  //   <Image src={src}/>
  // </div>
  const container = create('div', 'popover-content-main-cover');
  const image = create('img') as HTMLImageElement;

  if (src) {
    image.src = src;
  }
  container.appendChild(image);
  return container;
};

Popover.CoverVideo = function ({ src }: PopoverCover) {
  // <div className="popover-content-main-cover">
  //   <Image src={src}/>
  // </div>
  const container = create('div', 'popover-content-main-cover');
  const video = create('video') as HTMLVideoElement;
  video.autoplay = true;
  video.volume = 0;
  video.loop = true;
  if (src) {
    video.src = src;
    video.play();
  }
  container.appendChild(video);
  return container;
};

type ButtonConfigType = {
  label?: string;
  onClick?: () => void;
  type?: 'primary' | 'secondary';
  tipsClassName?: string;
};
type PopoverFooterBtnBox = {
  buttons: [ButtonConfigType] | [ButtonConfigType, ButtonConfigType];
};

Popover.FooterBtnBox = function ({ buttons }: PopoverFooterBtnBox) {
  const btnCounts = buttons.length;

  // 创建包含按钮的footer-box元素
  const footerBox = create('div', 'popover-content-footer-box');
  footerBox.setAttribute('data-btn-counts', btnCounts + '');

  // 遍历buttons数组，为每个按钮创建元素
  buttons.forEach((btn, i) => {
    const buttonEl = create(
      'div',
      classNames('popover-content-footer-box-btn', btn.type)
    ) as HTMLDivElement;
    if (btn.label) {
      buttonEl.textContent = btn.label;
    }

    // 如果提供了onClick函数，添加点击事件监听器
    if (btn.onClick) {
      // buttonEl.onclick = btn.onClick;
      buttonEl.addEventListener('click', btn.onClick);
    }

    // 如果提供了tipsClassName，添加到类名中
    if (btn.tipsClassName) {
      buttonEl.className += ` ${btn.tipsClassName}`;
    }

    // 将按钮元素添加到footer-box中
    footerBox.appendChild(buttonEl);
  });

  // 返回footerBox以便于可能的进一步操作（例如添加到document中）
  return footerBox;
};

type InitPopoverBodyProps = {
  text?: string;
  images?: [string, string, string];
  onClick?: (url: string) => void;
};

Popover.InitUploadBody = function ({
  text,
  images,
  onClick
}: InitPopoverBodyProps) {
  const container = create('div', 'popover-content-main-init-container');

  const textBox = create('div', 'popover-content-main-init-container-text-box');
  container.appendChild(textBox);

  if (text) {
    textBox.innerText = text;
  }

  const imageBox = create(
    'div',
    'popover-content-main-init-container-image-box'
  );
  container.appendChild(imageBox);

  const imagesDom = document.createDocumentFragment();
  images?.forEach((src, i) => {
    const image = create(
      'img',
      'popover-content-main-init-container-image-box-image'
    ) as HTMLImageElement;
    image.src = toAtlasImageView2URL(src, {
      mode: 2,
      width: 84
    });
    image.setAttribute('data-image-index', i + '');
    imagesDom.appendChild(image);
  });
  imageBox.appendChild(imagesDom);

  if (images && onClick) {
    imageBox.addEventListener('click', (e: Event) => {
      const target = (e as MouseEvent).target as HTMLElement;
      const indexString = target.dataset.imageIndex;

      if (!indexString) {
        return;
      }

      onClick(images[Number(indexString)]);
    });
  }

  return container;
};
