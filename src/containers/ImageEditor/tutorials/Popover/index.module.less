@title-color: #17171a;
@progress-color: #17171a;
@popover-bg-color: #f1f2f6;

@primary-btn-text-color: #fff;
@primary-btn-bg-color: #3549ff;

@secondary-btn-text-color: #17171a;

.popover {
  // overflow: hidden;

  :global {
    .popover-content {
      width: 286px;
      margin: 16px;

      &-top {
        width: 100%;
        height: 22px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        &-title {
          font-size: 16px;
          color: @title-color;
          font-weight: 600;
        }

        &-progress {
          font-size: 12px;
          color: @progress-color;
        }
      }

      &-subtitle {
        margin-bottom: 16px;
      }

      &-main {
        width: 100%;
        margin-bottom: 16px;

        &-cover {
          width: 100%;
          height: 178px;
          border-radius: 4px;
          overflow: hidden;

          video {
            width: 100%;
          }
        }

        &-init-container {
          &-text-box {
            color: @title-color;
            font-size: 14px;
          }

          &-image-box {
            width: 100%;
            height: 112px;
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            box-sizing: content-box;

            img {
              width: 84px;
              height: 112px;
              object-fit: contain;
              border-radius: 4px;
              overflow: hidden;
              cursor: pointer;

              &:hover {
                box-shadow: 0 0 0 2px @primary-btn-bg-color;
              }
            }
          }
        }
      }

      &-footer {
        width: 100%;

        &-box {
          width: 100%;
          display: flex;

          &[data-btn-counts='1'] {
            justify-content: center;
          }

          &[data-btn-counts='2'] {
            justify-content: space-between;
          }

          &-btn {
            width: 130px;
            height: 36px;
            flex: 0 0 auto;
            font-size: 14px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;

            &.primary {
              font-weight: 600;
              color: @primary-btn-text-color;
              background-color: @primary-btn-bg-color;
              border-radius: 4px;

              position: relative;

              &.emphasize {
                @keyframes emphasize {
                  0% {
                    transform: scale(1);
                  }

                  50% {
                    transform: scale(1.08);
                  }

                  100% {
                    transform: scale(1);
                  }
                }

                &::before :local {
                  border-radius: 8px;
                  position: absolute;
                  content: '';
                  display: block;
                  width: 138px;
                  height: 44px;
                  border: 2px solid @primary-btn-bg-color;
                  animation-name: emphasize;
                  animation-duration: 0.8s;
                  animation-timing-function: ease-in-out;
                  animation-iteration-count: 3;
                }
              }
            }

            &.secondary {
              color: @secondary-btn-text-color;
            }
          }
        }
      }
    }
  }

  :global(.popover-content) {
    :global(.popover-content-footer) {
      :global(.popover-content-footer-box) {
        :global(.popover-content-footer-btn.primary) {
          &.emphasize {
            &::before {
              border-radius: 8px;
              position: absolute;
              content: '';
              display: block;
              width: 138px;
              height: 44px;
              border: 2px solid @primary-btn-bg-color;
              animation-name: emphasize;
              animation-duration: 0.8s;
              animation-timing-function: ease-in-out;
              animation-iteration-count: infinite;
            }
          }
        }
      }
    }
  }
}

:global(.driver-popover) {
  min-width: auto;
  max-width: none;
  padding: 0;
  border-radius: 8px;
  background-color: @popover-bg-color;

  :global {
    .driver-popover-arrow {
      &.driver-popover-arrow-side-left {
        border-left-color: @popover-bg-color;
        left: calc(100% - 1px);
      }

      &.driver-popover-arrow-side-right {
        border-right-color: @popover-bg-color;
        right: calc(100% - 1px);
      }

      &.driver-popover-arrow-side-top {
        border-top-color: @popover-bg-color;
        top: calc(100% - 1px);
      }

      &.driver-popover-arrow-side-bottom {
        border-bottom-color: @popover-bg-color;
        bottom: calc(100% - 1px);
      }
    }
  }

  &:global(.only-description) {
    border-radius: 4px;

    :global {
      .driver-popover-description {
        font-size: 12px;
        color: @progress-color;
        padding: 4px 8px;
      }
    }
  }
}
