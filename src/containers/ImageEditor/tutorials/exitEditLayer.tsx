import { driver, DriveStep } from 'driver.js';
import { MutableRefObject, useEffect } from 'react';
import { Popover } from './Popover';
import { Tutorial } from './constant';
import { FeatureTabId } from '../components/FeatureArea';
import { attachStrokeToHighlight } from './utils';
import { TutorialsConfig as config } from './config';
import { afterRefresh, beforeRefresh, setCurDriver } from './refresh';

const UploadButtonSelector = `#${FeatureTabId} .active[data-key="back"]`;

const strokeWidth = Tutorial.StrokeConfig.width;
const strokeColor = Tutorial.StrokeConfig.color;

type UseExitEditLayerTutorialsOptions = {
  layerEditActive: boolean;
  inStyle: boolean;
  hasPaintedPromiseRef: MutableRefObject<Promise<boolean>>;
  delay: number;
};

export function useExitEditLayerTutorials({
  layerEditActive,
  inStyle,
  hasPaintedPromiseRef,
  delay
}: UseExitEditLayerTutorialsOptions) {
  useEffect(() => {
    if (!layerEditActive || !inStyle) {
      return;
    }

    if (localStorage.getItem(Tutorial.OnceKey.ExitEditLayerSubFeature)) {
      return;
    }
    localStorage.setItem(Tutorial.OnceKey.ExitEditLayerSubFeature, 'true');

    const steps: Array<DriveStep> = [
      {
        element: UploadButtonSelector,
        popover: {
          showButtons: [],
          side: 'right',
          align: 'start',
          onPopoverRender(popover, opts) {
            popover.description.style.display = 'block';
            popover.description.innerHTML = '';
            const pop = Popover({
              title: config.exitLayerEdit.title,
              subtitle: config.exitLayerEdit.subtitle,
              renderFooter() {
                return Popover.FooterBtnBox({
                  buttons: [
                    {
                      label: '好的',
                      type: 'primary',
                      onClick() {
                        driverObj.moveNext();
                      }
                    }
                  ]
                });
              }
            });
            popover.description.appendChild(pop);
          }
        },

        onHighlighted(el) {
          attachStrokeToHighlight({
            strokeWidth,
            strokeColor
          });

          afterRefresh(() => {
            attachStrokeToHighlight({
              strokeWidth,
              strokeColor
            });
          });
        }
      }
    ];

    const driverObj = driver({
      disableActiveInteraction: true,
      // 点击蒙层不关闭
      allowClose: false,

      stagePadding: -strokeWidth,
      stageRadius: 0,

      animate: false,

      steps,

      onDestroyed() {
        setCurDriver(null);
        beforeRefresh(null);
        afterRefresh(null);
      }
    });

    let ignore = false;
    // 1. 等待hasPaintedPromiseRef有值
    const timeout = setTimeout(() => {
      // 2. 等待涂抹区计算完成
      //    涂抹区计算会阻塞主线程 会阻止页面渲染
      //    如果在涂抹去计算之前就使用timeout计时 涂抹区计算的时间也会被算到延迟时间里
      hasPaintedPromiseRef.current.then(() => {
        // 3. 等待打开动画完成
        setTimeout(() => {
          if (ignore) {
            return;
          }
          driverObj.drive();
          setCurDriver(driverObj);
        }, delay);
      });
    }, 100);

    return () => {
      ignore = true;
      driverObj.destroy();
      clearTimeout(timeout);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [layerEditActive, inStyle]);
}
