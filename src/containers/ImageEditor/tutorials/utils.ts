const DRIVER_OVERLAY_CLASS_SELECTOR = '.driver-overlay';

export function getTutorialsId(subId: string) {
  return `tutorials--${subId}`;
}

export function getDriverSvg() {
  const svg = document.body.querySelector(
    DRIVER_OVERLAY_CLASS_SELECTOR
  ) as SVGAElement;
  const path = svg?.querySelector('path');

  return { svg, path };
}

type HighlightStrokeOptions = {
  strokeWidth: number;
  strokeColor: string;
};

export function attachStrokeToHighlight({
  strokeWidth,
  strokeColor
}: HighlightStrokeOptions) {
  const strokePathId = '__highlight-stroke';
  const { svg, path } = getDriverSvg();

  if (!svg || !path) {
    return;
  }

  const pathDesc = path.getAttribute('d');
  if (!pathDesc) {
    return;
  }

  const index = pathDesc?.indexOf('0Z');
  const strokePathDesc = pathDesc.slice(index + 2);

  const strokePath = (svg.querySelector(`#${strokePathId}`) ??
    path.cloneNode()) as SVGPathElement;
  strokePath.id = strokePathId;

  strokePath.setAttribute('d', strokePathDesc);
  strokePath.style.fill = 'rgba(0, 0, 0, 0)';
  strokePath.style.opacity = '1';
  strokePath.style.strokeWidth = strokeWidth + '';
  strokePath.style.stroke = strokeColor;
  strokePath.style.pointerEvents = 'none';

  svg.appendChild(strokePath);
}

export function containsElement(parent: Element | null, child: Element | null) {
  return parent && child && (parent === child || parent.contains(child));
}
