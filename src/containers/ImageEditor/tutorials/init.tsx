import { driver, DriveStep } from 'driver.js';
import { useEffect } from 'react';
import EditorStore from '@/store/editorStore';
import { InitUploadButtonId } from '../components/InitState';
import { Tutorial } from './constant';
import { Popover } from './Popover';

import { TutorialsConfig as config } from './config';
import { trackEvent } from '@/services';
import { containsElement, getDriverSvg } from './utils';
import { afterRefresh, beforeRefresh, setCurDriver } from './refresh';

const UploadButtonSelector = `#${InitUploadButtonId}`;

type useInitUploadTutorialsOptions = {
  editorStore: EditorStore;
  initFinish: boolean;
};

export function useInitUploadTutorials({
  editorStore,
  initFinish
}: useInitUploadTutorialsOptions) {
  useEffect(() => {
    if (initFinish || editorStore.globalLoading) {
      return;
    }

    if (localStorage.getItem(Tutorial.OnceKey.InitTutorialsKey)) {
      return;
    }
    localStorage.setItem(Tutorial.OnceKey.InitTutorialsKey, 'true');

    const steps: Array<DriveStep> = [
      {
        element: UploadButtonSelector,
        popover: {
          showButtons: [],
          side: 'right',
          align: 'start',
          onPopoverRender(popover, opts) {
            popover.description.style.display = 'block';
            popover.description.innerHTML = '';
            const pop = Popover({
              title: config.init.title,
              renderBody() {
                return Popover.InitUploadBody({
                  text: config.init.subTitle,
                  images: config.init.images as [string, string, string],
                  onClick: (url: string) => {
                    editorStore.initProjectImageLayer(url);
                    editorStore.setIsNewProject(true);
                    driverObj.moveNext();

                    trackEvent('ai_modification_recommend_pic_click');
                  }
                });
              },
              renderFooter() {
                return Popover.FooterBtnBox({
                  buttons: [
                    {
                      label: '知道了',
                      type: 'primary',
                      onClick() {
                        driverObj.moveNext();
                      }
                    }
                  ]
                });
              }
            });
            popover.description.appendChild(pop);
          }
        },

        onHighlightStarted() {
          document.addEventListener('click', handleClickUpload);
        },
        onDeselected() {
          document.removeEventListener('click', handleClickUpload);
        },
        onHighlighted() {
          const { svg, path } = getDriverSvg();
          document.body.classList.remove('driver-active');
          if (path) {
            path.style.pointerEvents = 'none';
          }

          if (svg) {
            svg.style.pointerEvents = 'none';
          }
        }
      }
    ];

    const driverObj = driver({
      // 点击蒙层不关闭
      allowClose: false,

      stagePadding: 0,
      stageRadius: 0,

      animate: false,
      steps,
      overlayOpacity: 0,
      onDestroyed() {
        setCurDriver(null);
        beforeRefresh(null);
        afterRefresh(null);

        const { svg, path } = getDriverSvg();
        if (path) {
          path.style.pointerEvents = 'auto';
        }

        if (svg) {
          svg.style.removeProperty('pointer-events');
        }
      }
    });

    function handleClickUpload(e: MouseEvent) {
      const target = e.target as HTMLElement;
      const button = document.querySelector(UploadButtonSelector);

      if (!containsElement(button, target)) {
        return;
      }

      driverObj.destroy();
    }

    driverObj.drive();

    editorStore.setFinishInitUploadTutorial(() => {
      driverObj.destroy();
    });

    setCurDriver(driverObj);
    return () => {
      editorStore.setFinishInitUploadTutorial(() => {});
      driverObj.destroy();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initFinish, editorStore.globalLoading]);
}
