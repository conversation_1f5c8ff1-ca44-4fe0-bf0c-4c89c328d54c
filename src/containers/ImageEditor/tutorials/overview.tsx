import { driver, DriveStep } from 'driver.js';
import {
  attachStrokeToHighlight,
  containsElement,
  getDriverSvg
} from './utils';
import { TaskCategory } from '@/api/types/imageEditor';
import { MutableRefObject, useEffect } from 'react';
import { FeatureAreaId, FeatureTabId } from '../components/FeatureArea';
import { HEADER_SELECT_ID } from '../components/Header';
import EditorStore from '@/store/editorStore';
import { CanvasNodeEventName } from '@/editor/core/src';
import { LayerEditorBtnBoxId } from '../components/LayerEditLayout';
import { Popover } from './Popover';
import { Tutorial } from './constant';
import { TutorialsConfig as config } from './config';
import { trackEvent } from '@/services';
import { afterRefresh, beforeRefresh, setCurDriver } from './refresh';

const strokeWidth = Tutorial.StrokeConfig.width;
const strokeColor = Tutorial.StrokeConfig.color;

function getFeatureTabSelector(featureKey: string) {
  return `#${FeatureTabId} .feature-tab-btn[data-key="${featureKey}"]`;
}

function getFeaturePanelSelector() {
  return `#${FeatureAreaId} .feature-panel`;
}

function getHeaderSelector() {
  return '.editor-header-center';
}

function getHeaderSelectionSelector() {
  return `#${HEADER_SELECT_ID}`;
}

function getLayerEditorBtnBoxSelector() {
  return `#${LayerEditorBtnBoxId}`;
}

/**
 * 给高亮部分附加tab所在的区域
 */
function attachTabItemHighlight(
  el: Element | undefined,
  tabItemSelector: string,
  stagePadding: number
) {
  if (!el) {
    return;
  }

  const { svg, path } = getDriverSvg();

  if (!svg || !path) {
    return;
  }
  const tabItem = document.querySelector(tabItemSelector);
  const rect = tabItem?.getBoundingClientRect();
  if (!rect || !tabItem) {
    return;
  }

  const pathDesc = path.getAttribute('d');
  if (!pathDesc) {
    return;
  }

  const index = pathDesc?.indexOf('0Z');
  const overlayPath = pathDesc.slice(0, index + 2);

  const panelRect = el.getBoundingClientRect();
  const tabRect = tabItem.getBoundingClientRect();

  const newDesc = `${overlayPath} M ${tabRect.x - stagePadding} ${
    rect.y - stagePadding
  } h ${panelRect.x - tabRect.x} v ${panelRect.y - tabRect.y} H ${
    panelRect.x + panelRect.width
  } v ${panelRect.height + 2 * stagePadding} H ${panelRect.x} v ${-(
    panelRect.y +
    panelRect.height -
    tabRect.y -
    tabRect.height
  )} H ${tabRect.x - stagePadding} z`;
  path.setAttribute('d', newDesc);
}

const imageLayerIndicator = document.createElement('div');
imageLayerIndicator.style.pointerEvents = 'none';
imageLayerIndicator.style.position = 'fixed';

export let currentIsLayerEditBtnTutorials = false;

type UseOverviewTutorialsOptions = {
  editorStore: EditorStore;
  initFinish: boolean;
  onChangeFeature: (key: string) => void;
  hasPaintedPromiseRef: MutableRefObject<Promise<boolean>>;
};

export function useOverviewTutorials({
  onChangeFeature,
  editorStore,
  initFinish,
  hasPaintedPromiseRef
}: UseOverviewTutorialsOptions) {
  useEffect(() => {
    if (
      !initFinish ||
      editorStore.globalLoading ||
      !editorStore.isNewProject ||
      editorStore.projectIsFromBlankCanvas
    ) {
      return;
    }

    if (localStorage.getItem(Tutorial.OnceKey.OverviewTutorialsKey)) {
      return;
    }

    localStorage.setItem(Tutorial.OnceKey.OverviewTutorialsKey, 'true');

    const tConfig = config.overview;

    /**
     * 在点击画布的图层时 不希望触发强调“下一步”
     */
    let ignoreHandleEmphasize = false;

    const steps: Array<DriveStep> = [
      {
        element: getFeaturePanelSelector(),
        popover: {
          showButtons: [],
          onPopoverRender(popover, opts) {
            popover.description.style.display = 'block';
            popover.description.innerHTML = '';
            const pop = Popover({
              title: tConfig.changePicture.title,
              subtitle: tConfig.changePicture.subTItle,
              // progress:
              //   typeof opts.state.activeIndex === 'number'
              //     ? [opts.state.activeIndex + 1, steps.length]
              //     : undefined,
              renderBody() {
                const src = tConfig.changePicture.video;
                return Popover.CoverVideo({ src });
              },
              renderFooter() {
                return Popover.FooterBtnBox({
                  buttons: [
                    {
                      label: '跳过',
                      type: 'secondary',
                      onClick() {
                        driverObj.destroy();

                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'skip',
                          step: 1
                        });
                      }
                    },
                    {
                      label: '下一步',
                      type: 'primary',
                      onClick() {
                        driverObj.moveNext();
                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'next',
                          step: 1
                        });
                      }
                    }
                  ]
                });
              }
            });
            popover.description.appendChild(pop);
          }
        },
        onHighlightStarted() {
          document.addEventListener('click', handleEmphasizeNextStep, true);
        },
        onHighlighted(el, _, opts) {
          attachTabItemHighlight(
            el,
            getFeatureTabSelector(TaskCategory.inpaint),
            -strokeWidth
          );
          attachStrokeToHighlight({
            strokeWidth,
            strokeColor
          });

          afterRefresh(() => {
            attachTabItemHighlight(
              el,
              getFeatureTabSelector(TaskCategory.inpaint),
              -strokeWidth
            );
            attachStrokeToHighlight({
              strokeWidth,
              strokeColor
            });
          });
        }
      },

      {
        element: getFeaturePanelSelector(),
        popover: {
          showButtons: [],
          onPopoverRender(popover, opts) {
            popover.description.style.display = 'block';
            popover.description.innerHTML = '';
            const pop = Popover({
              title: tConfig.extend.title,
              subtitle: tConfig.extend.subTItle,
              // progress:
              //   typeof opts.state.activeIndex === 'number'
              //     ? [opts.state.activeIndex + 1, steps.length]
              //     : undefined,
              renderBody() {
                const src = tConfig.extend.video;
                return Popover.CoverVideo({ src });
              },
              renderFooter() {
                return Popover.FooterBtnBox({
                  buttons: [
                    {
                      label: '跳过',
                      type: 'secondary',
                      onClick() {
                        driverObj.destroy();
                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'skip',
                          step: 2
                        });
                      }
                    },
                    {
                      label: '下一步',
                      type: 'primary',
                      onClick() {
                        driverObj.moveNext();
                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'next',
                          step: 2
                        });
                      }
                    }
                  ]
                });
              }
            });
            popover.description.appendChild(pop);
          }
        },
        onHighlighted(el, _, opts) {
          attachTabItemHighlight(
            el,
            getFeatureTabSelector(TaskCategory.extend),
            -strokeWidth
          );
          attachStrokeToHighlight({
            strokeWidth,
            strokeColor
          });
          onChangeFeature(TaskCategory.extend);

          afterRefresh(() => {
            attachTabItemHighlight(
              el,
              getFeatureTabSelector(TaskCategory.extend),
              -strokeWidth
            );
            attachStrokeToHighlight({
              strokeWidth,
              strokeColor
            });
          });
        }
      },

      {
        element: getFeaturePanelSelector(),
        popover: {
          showButtons: [],
          onPopoverRender(popover, opts) {
            popover.description.style.display = 'block';
            popover.description.innerHTML = '';
            const pop = Popover({
              title: tConfig.compound.title,
              subtitle: tConfig.compound.subTitle,
              // progress:
              //   typeof opts.state.activeIndex === 'number'
              //     ? [opts.state.activeIndex + 1, steps.length]
              //     : undefined,
              renderBody() {
                const src = tConfig.compound.video;

                return Popover.CoverVideo({ src });
              },
              renderFooter() {
                return Popover.FooterBtnBox({
                  buttons: [
                    {
                      label: '跳过',
                      type: 'secondary',
                      onClick() {
                        driverObj.destroy();
                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'skip',
                          step: 3
                        });
                      }
                    },
                    {
                      label: '下一步',
                      type: 'primary',
                      onClick() {
                        driverObj.moveNext();
                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'next',
                          step: 3
                        });
                      }
                    }
                  ]
                });
              }
            });
            popover.description.appendChild(pop);
          }
        },
        onHighlighted(el, _, opts) {
          attachTabItemHighlight(
            el,
            getFeatureTabSelector(TaskCategory.compound),
            -strokeWidth
          );
          attachStrokeToHighlight({
            strokeWidth,
            strokeColor
          });
          onChangeFeature(TaskCategory.compound);

          afterRefresh(() => {
            attachTabItemHighlight(
              el,
              getFeatureTabSelector(TaskCategory.compound),
              -strokeWidth
            );
            attachStrokeToHighlight({
              strokeWidth,
              strokeColor
            });
          });
        }
      },

      {
        element: getFeaturePanelSelector(),
        popover: {
          showButtons: [],
          onPopoverRender(popover, opts) {
            popover.description.style.display = 'block';
            popover.description.innerHTML = '';
            const pop = Popover({
              title: tConfig.graffiti.title,
              subtitle: tConfig.graffiti.subTitle,
              // progress:
              //   typeof opts.state.activeIndex === 'number'
              //     ? [opts.state.activeIndex + 1, steps.length]
              //     : undefined,
              renderBody() {
                const src = tConfig.graffiti.video;

                return Popover.CoverVideo({ src });
              },
              renderFooter() {
                return Popover.FooterBtnBox({
                  buttons: [
                    {
                      label: '跳过',
                      type: 'secondary',
                      onClick() {
                        driverObj.destroy();
                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'skip',
                          step: 4
                        });
                      }
                    },
                    {
                      label: '下一步',
                      type: 'primary',
                      onClick() {
                        driverObj.moveNext();
                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'next',
                          step: 4
                        });
                      }
                    }
                  ]
                });
              }
            });
            popover.description.appendChild(pop);
          }
        },
        onHighlighted(el, _, opts) {
          attachTabItemHighlight(
            el,
            getFeatureTabSelector(TaskCategory.graffiti),
            -strokeWidth
          );
          attachStrokeToHighlight({
            strokeWidth,
            strokeColor
          });
          onChangeFeature(TaskCategory.graffiti);

          afterRefresh(() => {
            attachTabItemHighlight(
              el,
              getFeatureTabSelector(TaskCategory.graffiti),
              -strokeWidth
            );
            attachStrokeToHighlight({
              strokeWidth,
              strokeColor
            });
          });
        }
      },

      {
        element: getHeaderSelector(),
        popover: {
          side: 'bottom',
          align: 'center',
          showButtons: [],
          onPopoverRender(popover, opts) {
            popover.description.style.display = 'block';
            popover.description.innerHTML = '';
            const pop = Popover({
              title: tConfig.topHeader.title,
              subtitle: tConfig.topHeader.subTItle,
              // progress:
              //   typeof opts.state.activeIndex === 'number'
              //     ? [opts.state.activeIndex + 1, steps.length]
              //     : undefined,
              // renderBody() {
              //   return Popover.CoverImage({ src: tConfig.topHeader.image });
              // },
              renderFooter() {
                return Popover.FooterBtnBox({
                  buttons: [
                    {
                      label: '跳过',
                      type: 'secondary',
                      onClick() {
                        driverObj.destroy();
                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'skip',
                          step: 5
                        });
                      }
                    },
                    {
                      label: '下一步',
                      type: 'primary',
                      onClick() {
                        driverObj.moveNext();
                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'next',
                          step: 5
                        });
                      }
                    }
                  ]
                });
              }
            });
            popover.description.appendChild(pop);
          }
        },

        onHighlightStarted(el, _, opts) {
          driverObj.setConfig({
            ...opts.config,
            stagePadding: 5
          });
        },

        onHighlighted(el, _, opts) {
          attachStrokeToHighlight({
            strokeWidth,
            strokeColor
          });

          afterRefresh(() => {
            attachStrokeToHighlight({
              strokeWidth,
              strokeColor
            });
          });
        }
      },

      {
        element: getHeaderSelectionSelector(),
        popover: {
          description: tConfig.headerSelect.description,
          side: 'bottom',
          align: 'center',
          showButtons: [],

          onPopoverRender(popover) {
            popover.wrapper.classList.add('only-description');
          }
        },

        onHighlightStarted(el, _, opts) {
          driverObj.setConfig({
            ...opts.config,
            stagePadding: 5,
            disableActiveInteraction: false
          });

          document.addEventListener('click', handleClickSelection);
        },

        onDeselected(_, __, opts) {
          document.removeEventListener('click', handleClickSelection);
        },

        onHighlighted(el, _, opts) {
          attachStrokeToHighlight({
            strokeWidth,
            strokeColor
          });

          afterRefresh(() => {
            attachStrokeToHighlight({
              strokeWidth,
              strokeColor
            });
          });
        }
      },

      {
        element: imageLayerIndicator,
        popover: {
          description: tConfig.layerSelect.description,
          side: 'top',
          align: 'center',
          showButtons: [],
          onPopoverRender(popover) {
            popover.wrapper.classList.add('only-description');
          }
        },

        onHighlightStarted(el, _, opts) {
          driverObj.setConfig({
            ...opts.config,
            stagePadding: -strokeWidth,
            disableActiveInteraction: false
          });

          editorStore.editor.app.on(
            CanvasNodeEventName.select,
            handleSelectLayer
          );
          editorStore.editor.app.stage.content.classList.add(
            'driver-active-element'
          );

          ignoreHandleEmphasize = true;
        },

        onDeselected() {
          document.body.removeChild(imageLayerIndicator);
          editorStore.editor.app.off(
            CanvasNodeEventName.select,
            handleSelectLayer
          );
          editorStore.editor.app.stage.content.classList.remove(
            'driver-active-element'
          );
        },

        onHighlighted(el, _, opts) {
          attachStrokeToHighlight({
            strokeWidth,
            strokeColor
          });

          beforeRefresh(setupImageLayerIndicator);

          afterRefresh(() => {
            attachStrokeToHighlight({
              strokeWidth,
              strokeColor
            });
          });
        }
      },

      {
        element: getLayerEditorBtnBoxSelector(),
        popover: {
          side: 'right',
          align: 'start',
          showButtons: [],
          onPopoverRender(popover, opts) {
            popover.description.style.display = 'block';
            popover.description.innerHTML = '';
            const pop = Popover({
              title: tConfig.layerEdit.title,
              subtitle: tConfig.layerEdit.subtitle,
              // progress:
              //   typeof opts.state.activeIndex === 'number'
              //     ? [opts.state.activeIndex + 1, steps.length]
              //     : undefined,
              renderFooter() {
                return Popover.FooterBtnBox({
                  buttons: [
                    {
                      label: '跳过',
                      type: 'secondary',
                      onClick() {
                        driverObj.destroy();
                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'skip',
                          step: 8
                        });
                      }
                    },
                    {
                      label: '下一步',
                      type: 'primary',
                      onClick() {
                        driverObj.moveNext();
                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'next',
                          step: 8
                        });
                      }
                    }
                  ]
                });
              }
            });
            popover.description.appendChild(pop);
          }
        },

        onHighlightStarted(el, _, opts) {
          driverObj.setConfig({
            ...opts.config,
            stagePadding: strokeWidth / 2,
            stageRadius: 6,
            disableActiveInteraction: true
          });

          afterRefresh(null);
        },

        onHighlighted(el, _, opts) {
          attachStrokeToHighlight({
            strokeWidth,
            strokeColor
          });

          requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              ignoreHandleEmphasize = false;
            });
          });
          currentIsLayerEditBtnTutorials = true;
          beforeRefresh(null);
          afterRefresh(() => {
            attachStrokeToHighlight({
              strokeWidth,
              strokeColor
            });
          });
        }
      },

      {
        element: '#scrollableBox',
        popover: {
          side: 'left',
          align: 'start',
          showButtons: [],
          onPopoverRender(popover, opts) {
            popover.description.style.display = 'block';
            popover.description.innerHTML = '';
            const pop = Popover({
              title: tConfig.layers.title,
              subtitle: tConfig.layers.subtitle,
              // progress:
              //   typeof opts.state.activeIndex === 'number'
              //     ? [opts.state.activeIndex + 1, steps.length]
              //     : undefined,
              // renderBody() {
              //   return Popover.CoverImage({ src: tConfig.layers.image });
              // },
              renderFooter() {
                return Popover.FooterBtnBox({
                  buttons: [
                    {
                      label: '开始创作',
                      type: 'primary',
                      onClick() {
                        driverObj.moveNext();
                        trackEvent('ai_modification_guide_flow_click', {
                          click_type: 'start',
                          step: 9
                        });
                      }
                    }
                  ]
                });
              }
            });
            popover.description.appendChild(pop);
          }
        },

        onHighlightStarted(el, _, opts) {
          driverObj.setConfig({
            ...opts.config,
            stagePadding: -strokeWidth,
            stageRadius: 0,
            disableActiveInteraction: true
          });

          currentIsLayerEditBtnTutorials = false;
        },

        onHighlighted(el, _, opts) {
          attachStrokeToHighlight({
            strokeWidth,
            strokeColor
          });

          afterRefresh(() => {
            attachStrokeToHighlight({
              strokeWidth,
              strokeColor
            });
          });
        }
      }
    ];

    const driverObj = driver({
      // 不能与高亮区域交互
      disableActiveInteraction: true,

      // 点击蒙层不关闭
      allowClose: false,

      stagePadding: -strokeWidth,
      stageRadius: 0,

      animate: false,

      steps,
      onDestroyed() {
        document.removeEventListener('click', handleClickSelection);
        document.removeEventListener('click', handleEmphasizeNextStep, true);
        editorStore.editor.app.off(
          CanvasNodeEventName.select,
          handleSelectLayer
        );
        setCurDriver(null);
        beforeRefresh(null);
        afterRefresh(null);
      }
    });

    function setupImageLayerIndicator() {
      const rect =
        editorStore.editor.wheeEditorPlugin.plugin?.editorGroup.getClientRect();
      const container =
        editorStore.editor.app.stage.content.getBoundingClientRect();
      if (rect) {
        imageLayerIndicator.style.left = rect.x + container.x + 'px';
        imageLayerIndicator.style.top = rect.y + container.y + 'px';
        imageLayerIndicator.style.width = rect.width + 'px';
        imageLayerIndicator.style.height = rect.height + 'px';
        document.body.appendChild(imageLayerIndicator);
      }
    }

    function handleClickSelection(e: MouseEvent) {
      const target = e.target as HTMLDivElement | null;
      const headerSelect = document.querySelector(getHeaderSelectionSelector());
      if (!containsElement(headerSelect, target)) {
        return;
      }

      setupImageLayerIndicator();

      driverObj.moveNext();
      document.removeEventListener('click', handleClickSelection);
      trackEvent('ai_modification_guide_flow_click', {
        click_type: 'guide_click',
        step: 6
      });
    }

    function handleSelectLayer() {
      // 需要等待图层编辑的容器渲染完成
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          editorStore.setLayersIsOpen(true);
          driverObj.moveNext();
          trackEvent('ai_modification_guide_flow_click', {
            click_type: 'guide_click',
            step: 7
          });
        });
      });
    }

    /**
     * 突出“下一步按钮”
     */
    const emphasizeClassName = 'emphasize';
    function handleEmphasizeNextStep(e: MouseEvent) {
      if (ignoreHandleEmphasize) {
        return;
      }

      const cancelButton = document.querySelector(
        '.popover-content-footer-box-btn.secondary'
      );
      const nextButton = document.querySelector(
        '.popover-content-footer-box-btn.primary'
      ) as HTMLElement;

      if (
        containsElement(cancelButton, e.target as HTMLElement) ||
        containsElement(nextButton, e.target as HTMLElement)
      ) {
        return;
      }

      nextButton?.classList.add(emphasizeClassName);
      nextButton?.addEventListener(
        'animationend',
        () => nextButton.classList.remove(emphasizeClassName),
        { once: true }
      );
    }

    let ignore = false;
    onChangeFeature(TaskCategory.inpaint);
    // 1. 需要等待状态切换完成 使hasPaintedPromiseRef有值
    const timeout = setTimeout(() => {
      // 2. 需要等待涂抹区计算完成
      console.log('use promise', hasPaintedPromiseRef.current);
      hasPaintedPromiseRef.current.then(() => {
        // 3. 等待侧边栏打开动画完成
        setTimeout(() => {
          if (ignore) {
            return;
          }

          driverObj.drive();
          setCurDriver(driverObj);
        }, 600);
      });
    }, 100);

    return () => {
      ignore = true;
      document.removeEventListener('click', handleClickSelection);
      document.removeEventListener('click', handleEmphasizeNextStep, true);
      editorStore.editor.app.off(CanvasNodeEventName.select, handleSelectLayer);
      clearTimeout(timeout);
      driverObj.destroy();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initFinish, editorStore.globalLoading, editorStore.isNewProject]);
}
