import { driver } from 'driver.js';
import ResizeObserver from 'resize-observer-polyfill';

type Driver = ReturnType<typeof driver>;
let curDriver: null | Driver = null;

export function setCurDriver(d: null | Driver) {
  curDriver = d;
}

let beforeRefreshFn = null as null | (() => void);
export function beforeRefresh(fn: null | (() => void)) {
  beforeRefreshFn = fn;
}

let afterRefreshFn = null as null | (() => void);
export function afterRefresh(fn: null | (() => void)) {
  afterRefreshFn = fn;
}

export function refreshActiveHighlight() {
  beforeRefreshFn?.();
  curDriver?.refresh();
  requestAnimationFrame(() => {
    afterRefreshFn?.();
  });
}

const resizeObserver = new ResizeObserver(refreshActiveHighlight);

export function enableAutoResizeTutorials() {
  resizeObserver.observe(document.body);
}

export function disableAutoResizeTutorials() {
  setCurDriver(null);
  beforeRefresh(null);
  afterRefresh(null);
  resizeObserver.disconnect();
}
