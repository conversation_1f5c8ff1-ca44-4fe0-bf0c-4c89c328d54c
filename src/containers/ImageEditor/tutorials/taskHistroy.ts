import { driver, DriveStep } from 'driver.js';
import { Popover } from './Popover';

import { Tutorial } from './constant';
import { attachStrokeToHighlight } from './utils';
import { TutorialsConfig as config } from './config';
import { afterRefresh, beforeRefresh, setCurDriver } from './refresh';

const strokeWidth = Tutorial.StrokeConfig.width;
const strokeColor = Tutorial.StrokeConfig.color;

export function showTaskHistoryTutorials(layerHistoryBoxId: string) {
  if (localStorage.getItem(Tutorial.OnceKey.TaskHistoryKey)) {
    return;
  }
  localStorage.setItem(Tutorial.OnceKey.TaskHistoryKey, 'true');

  const steps: Array<DriveStep> = [
    {
      element: `#${layerHistoryBoxId}`,
      popover: {
        showButtons: [],
        side: 'left',
        align: 'start',
        onPopoverRender(popover, opts) {
          popover.description.style.display = 'block';
          popover.description.innerHTML = '';
          const pop = Popover({
            title: config.taskHistory.title,
            subtitle: config.taskHistory.subtitle,
            renderFooter() {
              return Popover.FooterBtnBox({
                buttons: [
                  {
                    label: '我知道了',
                    type: 'primary',
                    onClick() {
                      driverObj.moveNext();
                    }
                  }
                ]
              });
            }
          });
          popover.description.appendChild(pop);
        }
      },
      onHighlighted() {
        attachStrokeToHighlight({
          strokeWidth,
          strokeColor
        });

        afterRefresh(() => {
          attachStrokeToHighlight({
            strokeWidth,
            strokeColor
          });
        });
      }
    }
  ];

  const driverObj = driver({
    disableActiveInteraction: true,
    // 点击蒙层不关闭
    allowClose: false,

    stagePadding: -strokeWidth * 2,
    stageRadius: 0,

    animate: false,

    steps,

    onDestroyed() {
      setCurDriver(null);
      beforeRefresh(null);
      afterRefresh(null);
    }
  });

  driverObj.drive();

  setCurDriver(driverObj);

  return () => {
    driverObj.destroy();
  };
}
