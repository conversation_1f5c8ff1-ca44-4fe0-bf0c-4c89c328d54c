import { SelectionWay } from '../components/AreaSelection';
import { AreaSelectionMode } from '../components/AreaSelection/ModeSelection';

export enum Subfunction {
  Modification = 'modification',
  Extension = 'extension',
  Compound = 'compound',
  Cutout = 'cutout',
  Clear = 'clear',
  Matting = 'matting'
}

export enum subFunctionClick {
  SideBar = 'side_bar',
  Floating = 'floating'
}

export namespace Modification {
  export const SelectionToolsTrace = {
    [SelectionWay.Box]: 'box_select',
    [SelectionWay.Smart]: 'ai_select',
    [SelectionWay.Lasso]: 'lasso_select',
    [SelectionWay.Paint]: 'smear',
    [SelectionWay.Ellipse]: 'ellipse',
    [SelectionWay.Eraser]: 'eraser',
    [SelectionWay.Repair]: 'repair'
  };

  export const IsDecreaseTrace = {
    [AreaSelectionMode.Add]: 'increase',
    [AreaSelectionMode.Remove]: 'decrease',
    [AreaSelectionMode.Auto]: 'auto_identify'
  };
}

export namespace Graffiti {
  export const SelectionToolsTrace = {
    [SelectionWay.Box]: 'rectangle',
    [SelectionWay.Paint]: 'draw_line',
    [SelectionWay.Ellipse]: 'circle',
    [SelectionWay.Eraser]: 'repair'
  };
}
