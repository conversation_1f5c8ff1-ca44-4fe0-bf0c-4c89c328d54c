/**
 * 选区颜色 使用16进制数表示
 */
export enum SelectionColor {
  /**
   * 正向选区 及要选中的部分
   */
  Positive = '#4053FF7F',

  /**
   * 负向选区 要擦除的部分
   */
  Negative = '#FA395A7F'
}

export function getSelectionColorRGBATuple(colorString: SelectionColor) {
  return [
    parseInt(colorString.slice(1, 3), 16),
    parseInt(colorString.slice(3, 5), 16),
    parseInt(colorString.slice(5, 7), 16),
    parseInt(colorString.slice(7, 9), 16)
  ] as const;
}

/**
 * 套索工具描边宽度
 */
export const LASSO_TOOL_STROKE_WIDTH = 2;
