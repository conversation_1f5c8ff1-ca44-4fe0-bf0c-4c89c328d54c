import { type TabsProps } from 'antd';
import type {
  EditorConfigControlNetModel,
  EditorConfigResponse
} from '@/api/types';
import type { ReactElement } from 'react';
import { EnableLabel } from './ControlNet/EnableLabel';
import { forwardRef, useImperativeHandle, useMemo, useState } from 'react';
import { Collapse, Tabs } from '@/components';

export const tabs = [
  { label: '参考 1', value: '0' },
  { label: '参考 2', value: '1' },
  { label: '参考 3', value: '2' }
] as const;

export interface ControlNetSectionProps {
  moduleList: EditorConfigResponse['moduleList'];
  renderTabPane: (
    props: ControlNetSectionTabPaneProps
  ) => ReactElement<ControlNetSectionTabPaneProps>;

  /**
   * tab对应的controlnet是否开启
   */
  tabIsEnable?: (index: number) => boolean;
}

export interface ControlNetSectionTabPaneProps
  extends Omit<ControlNetSectionProps, 'renderTabPane'> {
  tab: {
    label: string;
    value: string;
  };
  index: number;

  hasTitle?: boolean;

  onChangeModel?: (model?: EditorConfigControlNetModel) => void;
}

export type ControlNetSectionHandle = {
  activateTab(index: number): void;
};
export const ControlNetSection = forwardRef<
  ControlNetSectionHandle,
  ControlNetSectionProps
>((props, ref) => {
  const { renderTabPane, tabIsEnable, ...tabPaneProps } = props;
  const items: TabsProps['items'] = useMemo(
    () =>
      tabs.map((tab, index) => {
        const enable = !!tabIsEnable?.(index);
        return {
          key: tab.value,
          label: <EnableLabel text={tab.label} enable={enable} />,
          children: renderTabPane({
            ...tabPaneProps,
            tab,
            index
          }),
          forceRender: true
        };
      }),
    [tabPaneProps, renderTabPane, tabIsEnable]
  );

  const [activeKey, setActiveKey] = useState(items[0]?.key);

  useImperativeHandle(ref, () => {
    return {
      /**
       * 根据索引（下标）激活tab
       * @param index
       * @returns
       */
      activateTab(index: number) {
        if (!items || !items[index]) {
          return;
        }

        const key = items[index].key;
        setActiveKey(key);
      }
    };
  });

  return (
    <Collapse.Panel.Section>
      <Tabs
        type="segmented"
        items={items}
        activeKey={activeKey}
        onChange={setActiveKey}
      />
    </Collapse.Panel.Section>
  );
});
