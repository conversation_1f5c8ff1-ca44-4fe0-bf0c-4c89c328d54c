import { Modal } from 'antd';
import { CrossBold } from '@meitu/candy-icons';
import { VipButton } from '@/components/VipButton';
import { useState, forwardRef, useImperativeHandle, useRef } from 'react';

import meidouAward from '@/assets/images/meidou-award.png';
import styles from './index.module.less';
import { useMeiDouBalance } from '@/hooks/useMeiDou';

export interface UserAwardModalRef {
  open: (info?: UserAwardModalProps) => void;
}

export interface UserAwardModalProps {
  title?: string;
  description?: string;
  callback?: () => void;
}

export const UserAwardModal = forwardRef<
  UserAwardModalRef,
  UserAwardModalProps
>(({ title, description }, ref) => {
  const [open, setOpen] = useState(false);
  const closeCallbackRef = useRef<() => void>();
  const [internalTitle, setInternalTitle] = useState<string>();
  const [internalDesc, setInternalDesc] = useState<string>();
  const { updateMeiDouBalance } = useMeiDouBalance();

  useImperativeHandle(ref, () => ({
    open: (info?: UserAwardModalProps) => {
      setOpen(true);
      setInternalTitle(info?.title);
      setInternalDesc(info?.description);
      if (info?.callback) {
        closeCallbackRef.current = info.callback;
      }
    }
  }));

  const close = () => {
    updateMeiDouBalance();
    setOpen(false);
    closeCallbackRef.current?.();
  };

  return (
    <Modal
      open={open}
      centered
      destroyOnClose
      maskClosable
      closeIcon={<CrossBold />}
      width={300}
      footer={
        <VipButton block className={styles.userAwardTipConfirm} onClick={close}>
          好的
        </VipButton>
      }
      className={styles.userAwardTip}
      onCancel={close}
    >
      <img src={meidouAward} alt="meidou-award" />
      <h3>{title ?? internalTitle ?? ''}</h3>
      <p>{description ?? internalDesc ?? ''}</p>
    </Modal>
  );
});
