@import '~@/styles/variables.less';

.user-award-tip:global(.@{ant-prefix}-modal) {
  :global {
    .@{ant-prefix}-modal-content {
      padding: @size-ms;

      .@{ant-prefix}-modal-body {
        display: flex;
        align-items: center;
        flex-direction: column;

        img {
          width: 110px;
          margin-bottom: @size-sm;
          margin-top: @size-xs;
        }

        h3 {
          font-size: @font-size-lg;
        }

        p {
          color: @content-system-tertiary;
          line-height: 20px;
          text-align: center;
        }
      }
    }
  }
}

.user-award-tip-confirm:global(.@{ant-prefix}-btn) {
  background: @background-btn-meidou !important;

  span {
    color: @content-btn-meidou !important;
  }

  &:hover {
    background: @background-btn-meidou !important;
  }
}
