import { Spin } from 'antd';
import { Loading } from '@/components';

export const FullPageLoading = () => {
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100vw',
        height: '100vh'
      }}
    >
      <Spin indicator={<Loading />} size="large" />
    </div>
  );
};

export const FullContainerLoading = () => {
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: '100%'
      }}
    >
      <Spin indicator={<Loading />} size="large" />
    </div>
  );
};
