import { Card, Image, type CardProps, type ImageProps } from 'antd';
import { type ReactNode } from 'react';
import styles from './index.module.less';
import classNames from 'classnames';

export interface ImageCardProps
  extends Pick<
      CardProps,
      'onClick' | 'hoverable' | 'style' | 'bodyStyle' | 'className'
    >,
    Pick<ImageProps, 'preview' | 'src' | 'placeholder'> {
  children?: ReactNode;
  extra?: ReactNode;
  imgHeight?: ImageProps['height'];
  onImageShow?(): void;
}
export const ImageCard = ({
  preview = false,
  src,
  placeholder = true,
  children,
  extra,
  imgHeight,
  className,
  hoverable,
  ...restProps
}: ImageCardProps) => {
  return (
    <Card
      className={classNames(
        styles.imageCard,
        {
          [styles.hoverable]: hoverable
        },
        className
      )}
      hoverable={false}
      bodyStyle={{ padding: '12px' }}
      cover={
        <Image
          height={imgHeight}
          placeholder={placeholder}
          preview={preview}
          src={src}
        />
      }
      {...restProps}
    >
      {children}
    </Card>
  );
};
