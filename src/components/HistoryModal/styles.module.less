@import '~@/styles/variables.less';

.history-wrap:global(.@{ant-prefix}-modal-wrap) {
  position: absolute !important;
  top: 0px !important;
  right: 44px !important;
  left: auto !important;
  width: 160px;
  max-height: calc(100vh - 74px);
  overflow: visible !important;
}

.modal:global(.@{ant-prefix}-modal) {
  top: 0;
  right: 0;
  padding: 0;
  overflow: hidden;
  background: #fff;
  box-shadow: 0px 6px 24px 0px rgba(0, 0, 0, 0.06),
    0px 0px 2px 0px rgba(0, 0, 0, 0.08);
  z-index: 999;
  border-radius: 8px;

  :global {
    .@{ant-prefix}-modal-content {
      width: 100%;
      padding: 16px;
      border-radius: 8px 8px 0 0;

      .@{ant-prefix}-modal-body {
        height: 100%;
      }

      .@{ant-prefix}-modal-title {
        font-weight: 500;
        font-size: 14px;
        font-style: normal;
        line-height: 20px;
      }

      .@{ant-prefix}-modal-close {
        width: 14px;
        height: 14px;
        margin-top: 2px;

        .@{ant-prefix}-modal-close-x {
          width: 14px;
          height: 14px;
          font-size: 14px;
          line-height: 0px;
        }
      }
    }
  }

  .list-box {
    position: relative;
    min-height: 90%;
    margin-top: 8px;
    overflow-x: hidden;
    height: 100%;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .list-item {
      width: 128px;
      height: 128px;
      position: relative;
      margin-bottom: 16px;
      cursor: pointer;

      &.active {
        padding: 4px;
        border: 2.5px solid @stroke-input-selected;
        border-radius: 8px;
      }

      .error {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        background-image: url('~@/assets/images/invisible-graph.jpg');
        background-size: cover;
        background-position: center center;
        color: @color-white;
        line-height: 128px;
        text-align: center;
      }

      :global .@{ant-prefix}-image {
        width: 100%;
        height: 100%;
      }

      .item-img {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        object-fit: cover;
      }

      .item-corner {
        position: absolute;
        top: 4px;
        left: 4px;
        width: 32px;
        height: 18px;
        background: @background-tag-amount;
        border-radius: 4px;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 18px;
        color: @base-white-opacity-100;
        text-align: center;
      }
    }

    .empty {
      position: absolute;
      top: 50%;
      left: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transform: translate(-60%, -60%);

      :global .@{ant-prefix}-empty-image {
        width: 84px;
        height: 84px;
      }

      :global .@{ant-prefix}-empty-description {
        color: @content-system-quaternary;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px;
      }
    }
  }
}
