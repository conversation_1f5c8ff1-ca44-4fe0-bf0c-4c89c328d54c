import styles from './styles.module.less';
import { Empty, Image, Modal } from 'antd';
import { Loading } from '../Loading';
import { CrossBold } from '@meitu/candy-icons';
import {
  ForwardedRef,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState
} from 'react';
import empty from '@/assets/images/empty.jpg';
import classNames from 'classnames';
import InfiniteScroll from 'react-infinite-scroll-component';
import { TaskStatus } from '@/api/types';
import { useInnerHeight } from '@/hooks';

type ListBaseType = {
  resultImages?: Array<{ url: string }>;
  id: string;
  status: TaskStatus;
};

interface HistoryModalProps<T extends ListBaseType> {
  // 是否打开modal
  openModal: boolean;
  // 列表数据
  historyList: Array<T>;
  // 关闭按钮点击事件
  onCancel: () => void;
  // 获取历史记录列表方法
  getHistoryList: () => void;
  // 点击某条记录
  historyItemClicked: (item: T) => void;
  // 是否有更多数据
  hasMore: boolean;
  // 当前active的id
  taskId: string;
}

export type HistoryModalRef = {
  scrollTo: (num: number) => void;
};

/** 图片盒子高度 */
const BOX_HEIGHT = 144;
/** 额外高度 = 头部高度 + 相对定位Top + 页脚高度 + 评分组件占高度 */
const EXTRA_HEIGHT = 220;
function HistoryModalWithoutRef<T extends ListBaseType>(
  props: HistoryModalProps<T>,
  ref: ForwardedRef<HistoryModalRef>
) {
  const {
    openModal,
    historyList,
    getHistoryList,
    onCancel,
    historyItemClicked,
    hasMore,
    taskId
  } = props;

  const innerHeight = useInnerHeight();
  const [activeItemId, setActiveItemId] = useState('');
  const [responsiveHeight, setResponsiveHeight] = useState(BOX_HEIGHT);

  useEffect(() => {
    setActiveItemId(taskId);
  }, [taskId]);

  useEffect(() => {
    if (!historyList.length || !innerHeight) return;
    let calculateHeight = BOX_HEIGHT;
    // 可用高度
    const availableHeight = innerHeight - EXTRA_HEIGHT;
    // 记录数大于1且剩余可用高度大于盒子高度时计算模态框body高度
    if (historyList.length > 1 && BOX_HEIGHT < availableHeight) {
      const listHeight = historyList.length * BOX_HEIGHT;
      if (listHeight < availableHeight) {
        calculateHeight = listHeight;
      } else {
        calculateHeight = availableHeight;
      }
    }
    setResponsiveHeight(calculateHeight);
  }, [innerHeight, historyList]);

  const itemClicked = (item: T) => {
    setActiveItemId(item.id);
    historyItemClicked(item);
  };

  const scrollTo = (num: number) => {
    const scrollableBox = document.getElementById('scrollableBox')!;
    scrollableBox?.scrollTo?.({ top: num, behavior: 'smooth' });
  };

  useImperativeHandle(ref, () => ({ scrollTo }));

  return (
    <div id="extensionHistory">
      <Modal
        open={openModal}
        footer={null}
        mask={false}
        closeIcon={<CrossBold />}
        className={styles.modal}
        title={'历史记录'}
        width={160}
        onCancel={onCancel}
        wrapClassName={styles.historyWrap}
        maskClosable={false}
        styles={{ body: { height: responsiveHeight } }}
        getContainer={() => {
          return document.getElementById('extensionHistory')!;
        }}
        destroyOnClose={false}
      >
        <div className={styles.listBox} id="scrollableBox">
          {historyList.length > 0 ? (
            <InfiniteScroll
              scrollableTarget="scrollableBox"
              dataLength={historyList.length}
              next={getHistoryList}
              hasMore={hasMore}
              loader={<Loading />}
              endMessage={<p style={{ textAlign: 'center' }}>没有更多了～</p>}
            >
              {historyList.map((item) => (
                <div
                  className={classNames(
                    styles.listItem,
                    activeItemId === item.id && styles.active
                  )}
                  key={item.id}
                  onClick={() => {
                    itemClicked(item);
                  }}
                >
                  {item.status === TaskStatus.GENERATING ? (
                    <div className={styles.error}>生成中</div>
                  ) : item.status === TaskStatus.FAILURE ||
                    !item.resultImages?.length ? (
                    <div className={styles.error}>生成失败</div>
                  ) : (
                    <>
                      <Image
                        src={item.resultImages?.[0].url}
                        placeholder={<Loading />}
                        className={styles.itemImg}
                        preview={false}
                      />
                      {item.resultImages && item.resultImages?.length > 1 && (
                        <div className={styles.itemCorner}>
                          {item.resultImages?.length}张
                        </div>
                      )}
                    </>
                  )}
                </div>
              ))}
            </InfiniteScroll>
          ) : (
            <Empty
              className={styles.empty}
              image={empty}
              description={'暂无数据'}
            />
          )}
        </div>
      </Modal>
    </div>
  );
}

export const HistoryModal = forwardRef(HistoryModalWithoutRef) as <
  T extends ListBaseType
>(
  props: HistoryModalProps<T> & React.RefAttributes<HistoryModalRef>
) => React.ReactElement;
