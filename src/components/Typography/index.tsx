import * as React from 'react';

import classNames from 'classnames';
import {
  Typography as AntdTypography,
  TypographyProps as AntdTypographyProps
} from 'antd';
import { TextProps } from 'antd/es/typography/Text';
import styles from './index.module.less';

/**
 * 其余参数参考 https://ant-design.antgroup.com/components/typography-cn#typographytext
 **/
interface TypographyProps extends AntdTypographyProps {
  TextRainbow: typeof TextRainbow;
}
export const Typography = AntdTypography as TypographyProps;

interface TypographyTextProps extends TextProps {
  backgroundImage: string;
}

const TextRainbow: React.ForwardRefRenderFunction<
  HTMLSpanElement,
  TypographyTextProps
> = ({ backgroundImage, className, ...restProps }, ref) => {
  const gradientStyle = React.useMemo(() => {
    return { backgroundImage: backgroundImage };
  }, [backgroundImage]);

  return (
    <AntdTypography.Text
      {...restProps}
      ref={ref}
      style={gradientStyle}
      className={classNames(className, styles.textRainbow)}
    />
  );
};

Typography.TextRainbow = TextRainbow;
