@import '~@/styles/variables.less';

.user {
  font-size: @size-sm;
}

.label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: @size-md;
  font-weight: 600;
  color: @content-system-primary;
  margin: @size 0 @size-sm;
}

.container {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: @size-sm;
  overflow: hidden;

  &-preview {
    flex: 1 1 0;
    height: 100%;
    overflow: hidden;
    background-color: @base-white-opacity-100;
  }

  &-detail {
    height: 100%;
    flex: 0 0 374px;
  }

  :global(.@{ant-prefix}-image-mask) {
    opacity: 1 !important;

    &:hover {
      :local .comparing-action {
        opacity: 1;
      }
    }
  }
}

.cover-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.single-image-preview {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 10px 0 10px 10px;
  flex: 0 0 auto;
  :global(.@{ant-prefix}-image) {
    border-radius: @border-radius-sm;
    overflow: hidden;
    :global(.@{ant-prefix}-image-mask) {
      background-color: transparent;
    }
  }
}
