import type { ReactNode } from 'react';

import { Avatar, Skeleton, Space } from 'antd';
import styles from './styles.module.less';
import classNames from 'classnames';

export { SingleImagePreview } from './SingleImagePreview';

interface UserProps {
  avatar?: string;
  userName?: string;
}

/** 用户信息 */
export function User({ avatar, userName }: UserProps) {
  return avatar && userName ? (
    <Space size={4} className={styles.user}>
      <Avatar size={20} src={avatar} />
      {userName}
    </Space>
  ) : (
    <UserSkeleton />
  );
}

/** 用户信息骨架 */
function UserSkeleton() {
  return (
    <Space size={4} className={styles.user}>
      <Skeleton.Avatar size={20} active />
      <Skeleton.Button size="small" active />
    </Space>
  );
}

interface VerticalWrapperProps {
  label: ReactNode;
  children: ReactNode;
}

/** 垂直布局 */
export function VerticalWrapper({ label, children }: VerticalWrapperProps) {
  return (
    <>
      <div className={styles.label}>{label}</div>
      {children}
    </>
  );
}

interface DetailContainerProps {
  preview: ReactNode;
  previewClassName?: string;
  children: ReactNode;
  className?: string;
}

/** 详情预览 + 信息布局 */
export function DetailContainer({
  preview,
  children,
  previewClassName,
  className
}: DetailContainerProps) {
  return (
    <div className={styles.container}>
      <div className={classNames(styles.containerPreview, previewClassName)}>
        {preview}
      </div>
      <div className={classNames(styles.containerDetail, className)}>
        {children}
      </div>
    </div>
  );
}
