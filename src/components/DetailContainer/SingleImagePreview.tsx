import React, { useState, type ReactNode, useRef, useEffect } from 'react';
import type { CanvasSize } from '@/types';

import { Image } from 'antd';
import { Loading } from '@/components';

import styles from './styles.module.less';
import classNames from 'classnames';
import ResizeObserver from 'resize-observer-polyfill';

interface SingleImagePreviewProps {
  /** 图片路径 */
  src: string;

  /** 遮罩层 */
  mask: ReactNode;

  /** 画布尺寸 */
  size?: CanvasSize;

  className?: string;
}

export function SingleImagePreview({
  src,
  mask,
  size,
  className
}: SingleImagePreviewProps) {
  return (
    <div className={classNames(styles.singleImagePreview, className)}>
      {!!(size && size[0] && size[1]) ? (
        <CoverContainer
          childrenRender={({ width, height }) => {
            if (!width || !height) {
              return <></>;
            }

            const containerAspect = width / height;
            const imgAspect = size[0] / size[1];

            const sizeProps =
              containerAspect > imgAspect
                ? {
                    height: `${height}px`,
                    width: `${imgAspect * height}px`
                  }
                : {
                    width: `${width}px`,
                    height: `${width / imgAspect}px`
                  };

            return (
              <Image
                src={src}
                {...sizeProps}
                preview={{ visible: false, mask }}
                placeholder={<Loading />}
              />
            );
          }}
        />
      ) : (
        <Image
          src={src}
          preview={{ visible: false, mask }}
          placeholder={<Loading />}
        />
      )}
    </div>
  );
}

type CoverContainerProps = {
  childrenRender: (props: {
    width: number;
    height: number;
  }) => React.ReactElement;
};
function CoverContainer({ childrenRender }: CoverContainerProps) {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      const entry = entries[0];
      if (!entry?.contentRect) {
        return;
      }

      setSize({
        width: entry.contentRect.width,
        height: entry.contentRect.height
      });
    });

    resizeObserver.observe(containerRef.current!);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <div className={styles.coverContainer} ref={containerRef}>
      {childrenRender({ ...size })}
    </div>
  );
}
