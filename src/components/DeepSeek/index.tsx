import { useEffect, useRef, useState } from 'react';
import { Button, message, Modal, Checkbox } from 'antd';
import { Button as SelfButton } from '@/components/Button';
import {
  LoadingCircleBold,
  RefreshBold,
  ChevronDownBold,
  ChevronUpBold,
  DeepThinkingBold,
  StopFill
} from '@meitu/candy-icons';
import {
  fetchDeepSeekOptimize,
  fetchDeepSeekOptimizeByToken
} from '@/api/corpus';
import './index.less';
import { trackEvent } from '@/services';
import { AnimatePresence, motion } from 'framer-motion';
import { getLocalStorageItem, setLocalStorageItem } from '@meitu/util';
import {
  // DEEPSEEK_PROMPT_OPTIMIZATION_KEY,
  DEEPSEEK_PROMPT_OPTIMIZATION_KEY_FIRST_CLICK,
  DEEPSEEK_PROMPT_OPTIMIZATION_KEY_TEXT,
  DEEPSEEK_PROMPT_OPTIMIZATION_KEY_IMAGE
} from '@/constants';
import { useAccount } from '@/hooks';

// const DEEPSEEK_PROMPT_OPTIMIZATION_KEY = 'deepseek_prompt_optimization';

const JSON_DATA = [
  {
    direct: '强化细节质感',
    content:
      '民盟，组成人员主要是从事文化教育以及相关的科学技术领域知识分子。民进，主要由从事教育文化出版传媒等领域的知识分子组成。教育界的委员主要是教育领域的专家学者和相关从业者。"教育"、"知识分子"是这三个界别的共同点。'
  },
  {
    direct: '强化细节质感2',
    content:
      '从任务来看，党的二十大对加快建设教育强国、科技强国、人才强国作出一体化部署。党的二十届三中全会对深化教育综合改革作出全面部署。在去年9月召开的全国教育大会上，总书记清晰擘画教育强国蓝图，发出了加快建设教育强国的动员令。蓝图已绘就，实干正当时。'
  },
  {
    direct: '强化细节质感3',
    content:
      '当前，我国人口发展呈现少子化、老龄化等趋势性特征。蔡光洁委员着眼于未来学龄人口的变化趋势，围绕优化基础教育资源配置积极建言献策。'
  }
];

type DeepSeekComponentProps = {
  usePrompt?: (prompt: string, content?: string, direct?: string) => void;
  prompt?: string;
};

let thinkingInterval: NodeJS.Timeout | null = null;

export function DeepSeekComponent(props: DeepSeekComponentProps) {
  const [state, setState] = useState<
    'init' | 'thinking' | 'success' | 'error' | 'expend'
  >('init');
  const [data, setData] = useState<any[]>([]);
  const [canUsePrompt, setCanUsePrompt] = useState<boolean>(false);
  const [expend, setExpend] = useState<boolean>(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [token, setToken] = useState<string>('');
  const [showMask, setShowMask] = useState<boolean>(false);
  const [showAIClearModal, setShowAIClearModal] = useState<boolean>(false);
  const [isCheckIAClearMessage, setIsCheckIAClearMessage] =
    useState<boolean>(false);
  // 存储服务器返回的完整结果
  const [fullThinkingResult, setFullThinkingResult] = useState<string>('');
  // 存储当前显示给用户的文本
  const [visibleThinkingResult, setVisibleThinkingResult] =
    useState<string>('');
  // 流式效果的定时器
  const streamTimerRef = useRef<NodeJS.Timeout | null>(null);
  // 存储结果数据，但不立即显示
  const pendingResultRef = useRef<any[] | null>(null);
  // 标记文本是否完全展示
  const fullyDisplayedRef = useRef<boolean>(false);
  // 延迟显示结果的计时器
  const resultDisplayTimerRef = useRef<NodeJS.Timeout | null>(null);

  const textRef = useRef<HTMLDivElement>(null);
  const promptRef = useRef<string>('');
  const itemRef = useRef<any>(null);

  const isTextToImage = window.location.pathname.includes('text-to-image');

  const { isLogin, openLoginPopup } = useAccount();

  // 处理文本显示完毕后的逻辑
  const handleTextFullyDisplayed = () => {
    fullyDisplayedRef.current = true;

    // 如果有待处理的结果，1秒后再显示它们
    if (pendingResultRef.current) {
      // 清除之前的延迟计时器（如果有）
      if (resultDisplayTimerRef.current) {
        clearTimeout(resultDisplayTimerRef.current);
      }

      // 设置1秒延迟后显示结果
      resultDisplayTimerRef.current = setTimeout(() => {
        setState('success');
        setData(pendingResultRef.current!);
        setExpend(true);
        pendingResultRef.current = null;

        // 清除polling
        if (thinkingInterval) {
          clearInterval(thinkingInterval);
          thinkingInterval = null;
        }

        resultDisplayTimerRef.current = null;
      }, 1000); // 1秒延迟
    }
  };

  // 每当fullThinkingResult更新时，启动或继续流式显示
  useEffect(() => {
    // 重置完全展示标记
    fullyDisplayedRef.current = false;

    // 如果没有内容，不需要做任何事
    if (!fullThinkingResult) return;

    // 如果已有定时器在运行，先清除
    if (streamTimerRef.current) {
      clearInterval(streamTimerRef.current);
    }

    let currentPosition = visibleThinkingResult.length;

    // 创建新的定时器，流式显示文本
    streamTimerRef.current = setInterval(() => {
      // 如果已显示所有内容，停止定时器
      if (currentPosition >= fullThinkingResult.length) {
        if (streamTimerRef.current) {
          clearInterval(streamTimerRef.current);
          streamTimerRef.current = null;
        }

        // 触发文本完全显示的处理函数
        handleTextFullyDisplayed();
        return;
      }

      // 每次只添加1个字符，让显示更加平滑
      const charsToAdd = 1;
      const nextPosition = Math.min(
        currentPosition + charsToAdd,
        fullThinkingResult.length
      );

      // 更新显示的文本
      setVisibleThinkingResult(fullThinkingResult.substring(0, nextPosition));
      currentPosition = nextPosition;

      // 滚动到底部
      if (textRef.current) {
        textRef.current.scrollTop = textRef.current.scrollHeight;
        if (textRef.current.scrollTop > 0) {
          setShowMask(true);
        }
      }
    }, 20); // 50毫秒间隔，适中的速度

    // 清理函数
    return () => {
      if (streamTimerRef.current) {
        clearInterval(streamTimerRef.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fullThinkingResult]);

  useEffect(() => {
    if (state === 'thinking') {
      // 重置状态
      setFullThinkingResult('');
      setVisibleThinkingResult('');
      pendingResultRef.current = null;
      fullyDisplayedRef.current = false;

      // 清除可能存在的延迟显示计时器
      if (resultDisplayTimerRef.current) {
        clearTimeout(resultDisplayTimerRef.current);
        resultDisplayTimerRef.current = null;
      }

      if (props.prompt) {
        fetchDeepSeekOptimize(props.prompt ?? '')
          .then((res: any) => {
            setToken(res.token);
            if (res.token) {
              thinkingInterval = setInterval(() => {
                fetchDeepSeekOptimizeByToken(res.token)
                  .then((res: any) => {
                    if (Array.isArray(res.reasoning)) {
                      // 更新完整的思考结果
                      setFullThinkingResult(res.reasoning.join(''));
                    }

                    if (res.result) {
                      if (thinkingInterval) {
                        clearInterval(thinkingInterval);
                        thinkingInterval = null;
                      }
                      // 如果文本已完全显示，设置1秒延迟显示结果
                      if (fullyDisplayedRef.current) {
                        // 清除之前的延迟计时器（如果有）
                        if (resultDisplayTimerRef.current) {
                          clearTimeout(resultDisplayTimerRef.current);
                        }

                        // 设置1秒延迟后显示结果
                        resultDisplayTimerRef.current = setTimeout(() => {
                          setState('success');
                          setToken('');
                          setData(res.result);
                          setExpend(true);
                          setCanUsePrompt(false);

                          if (thinkingInterval) {
                            clearInterval(thinkingInterval);
                            thinkingInterval = null;
                          }

                          resultDisplayTimerRef.current = null;
                        }, 2000);
                      } else {
                        // 否则，将结果存储起来，等待文本显示完毕
                        pendingResultRef.current = res.result;
                      }
                    }
                  })
                  .catch((err) => {
                    if (thinkingInterval) {
                      clearInterval(thinkingInterval);
                    }
                    message.error('请求失败，请稍后再试');
                    setState('error');
                  });
              }, 1500);
              return () => {
                if (thinkingInterval) {
                  clearInterval(thinkingInterval);
                }
              };
            } else {
              setState('error');
              setToken('');
            }
          })
          .catch((err) => {
            message.error(err.message);
            setState('error');
          });
      } else {
        setState('error');
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state]);

  useEffect(() => {
    if (props.prompt) {
      promptRef.current = props.prompt;
    }
  }, [props.prompt]);

  // 组件卸载时清理所有定时器
  useEffect(() => {
    return () => {
      if (thinkingInterval) {
        clearInterval(thinkingInterval);
      }
      if (streamTimerRef.current) {
        clearInterval(streamTimerRef.current);
      }
      if (resultDisplayTimerRef.current) {
        clearTimeout(resultDisplayTimerRef.current);
      }
      itemRef.current = null;
      setLocalStorageItem(DEEPSEEK_PROMPT_OPTIMIZATION_KEY_FIRST_CLICK, '');
    };
  }, []);

  // 添加展开状态变化后的副作用
  useEffect(() => {
    if (state === 'expend') {
      const timer = setTimeout(() => {
        // 确保DOM更新后再添加动画类
        const contentElement = document.querySelector(
          '.success-container-content'
        );
        if (contentElement) {
          contentElement.classList.remove(
            'expand-animation-enter',
            'expand-animation-exit'
          );
          contentElement.classList.add(
            expend ? 'expand-animation-enter' : 'expand-animation-exit'
          );
        }
      }, 10);
      return () => clearTimeout(timer);
    }
  }, [expend, state]);

  const handleFirstClick = () => {
    // 存储当前的prompt
    // if(!getLocalStorageItem(DEEPSEEK_PROMPT_OPTIMIZATION_KEY_FIRST_CLICK)) {
    setLocalStorageItem(
      DEEPSEEK_PROMPT_OPTIMIZATION_KEY_FIRST_CLICK,
      promptRef.current
    );
    // }
  };

  const handleApplyPrompt = (item: any) => {
    setCanUsePrompt(true);
    props.usePrompt?.(promptRef.current, item.content, item.direct);
    setExpend(false);
    setShowAIClearModal(false);
    trackEvent('deepseek_prompt_result_apply', {
      function: isTextToImage ? 'text_to_image' : 'image_to_image',
      prompt: promptRef.current,
      content: item.content,
      direct: item.direct
    });
  };
  return (
    <>
      <div className="deep-seek-container">
        <div className="deep-seek-title">
          提示词优化
          <div
            className="deep-seek-title-expand"
            onClick={() => {
              setExpend(!expend);
              if (state !== 'expend') setState('expend');
            }}
            style={{
              display: canUsePrompt ? 'block' : 'none'
            }}
          >
            {expend ? '收起' : '展开'}{' '}
            {expend ? <ChevronUpBold /> : <ChevronDownBold />}
          </div>
        </div>
        <div>
          {state === 'init' && (
            <Button
              className="button-container"
              onClick={() => {
                if (!isLogin) {
                  return openLoginPopup();
                }

                setState('thinking');
                handleFirstClick();
                setCanUsePrompt(false);
                trackEvent('deepseek_prompt_optimization_click', {
                  function: isTextToImage ? 'text_to_image' : 'image_to_image'
                });
                // 向下滑动2px
                const scrollElement = document.querySelector(
                  '.deep-seek-container'
                );
                if (scrollElement) {
                  scrollElement.scrollTo(0, 2);
                }
              }}
              disabled={props.prompt ? false : true}
            >
              <span className="icon-text-flex">
                <DeepThinkingBold /> DeepSeek R1 提示词优化
              </span>
            </Button>
          )}
          {state === 'thinking' && (
            <AnimatePresence>
              <motion.div
                className="thinking-container"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                transition={{ duration: 0.5 }}
              >
                <div className="thinking-container-content">
                  {showMask && (
                    <div className="thinking-container-content-mask"></div>
                  )}
                  <div
                    className="thinking-container-content-text"
                    ref={textRef}
                  >
                    <div
                      style={{
                        display: 'flex'
                      }}
                    >
                      <LoadingCircleBold className="thinking-container-content-loading" />
                      正在深度思考中
                    </div>
                    {visibleThinkingResult}
                  </div>
                </div>
                <Button
                  className="thinking-container-cancel"
                  onClick={() => {
                    setState('init');
                    setExpend(false);
                    setCanUsePrompt(false);
                    setToken('');
                    setFullThinkingResult('');
                    setVisibleThinkingResult('');
                    pendingResultRef.current = null;
                    setData([]);

                    // 清除所有计时器
                    if (thinkingInterval) {
                      clearInterval(thinkingInterval);
                      thinkingInterval = null;
                    }
                    if (streamTimerRef.current) {
                      clearInterval(streamTimerRef.current);
                      streamTimerRef.current = null;
                    }
                    if (resultDisplayTimerRef.current) {
                      clearTimeout(resultDisplayTimerRef.current);
                      resultDisplayTimerRef.current = null;
                    }

                    trackEvent('deepseek_prompt_optimization_cancel', {
                      function: isTextToImage
                        ? 'text_to_image'
                        : 'image_to_image'
                    });
                  }}
                >
                  <span className="icon-text-flex">
                    <StopFill /> 取消思考
                  </span>
                </Button>
              </motion.div>
            </AnimatePresence>
          )}

          {state === 'success' && (
            <div className="success-container">
              <div
                className="success-container-content"
                style={{ display: canUsePrompt ? 'none' : 'block' }}
              >
                {data?.map((item, index) => (
                  <AnimatePresence key={index}>
                    <motion.div
                      className="success-container-content-item"
                      key={index}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      transition={{ duration: 0.5, delay: index * 0.5 }}
                    >
                      <div className="success-container-content-item-title">
                        {`${index + 1}.${item.direct}`}{' '}
                        <SelfButton
                          onClick={() => {
                            const localPrompt = getLocalStorageItem(
                              isTextToImage
                                ? DEEPSEEK_PROMPT_OPTIMIZATION_KEY_TEXT
                                : DEEPSEEK_PROMPT_OPTIMIZATION_KEY_IMAGE
                            );
                            itemRef.current = item;
                            if (localPrompt) {
                              setShowAIClearModal(false);
                              handleApplyPrompt(item);
                            } else {
                              trackEvent(
                                'deepseek_prompt_overwrite_popup_expo',
                                {
                                  function: isTextToImage
                                    ? 'text_to_image'
                                    : 'image_to_image'
                                }
                              );
                              setShowAIClearModal(true);
                            }
                          }}
                        >
                          使用该结果
                        </SelfButton>
                      </div>
                      <div className="success-container-content-item-content">
                        {item.content}
                      </div>
                    </motion.div>
                  </AnimatePresence>
                ))}
              </div>
              <Button
                className="thinking-container-cancel"
                onClick={() => {
                  setState('thinking');
                  setCanUsePrompt(false);
                  setExpend(false);
                  setToken('');
                  setFullThinkingResult('');
                  setVisibleThinkingResult('');
                  setData([]);
                  if (thinkingInterval) {
                    clearInterval(thinkingInterval);
                  }
                  if (streamTimerRef.current) {
                    clearInterval(streamTimerRef.current);
                  }
                  setShowMask(false);
                  setLocalStorageItem(
                    DEEPSEEK_PROMPT_OPTIMIZATION_KEY_FIRST_CLICK,
                    props.prompt
                  );
                }}
                disabled={props.prompt ? false : true}
              >
                <RefreshBold /> 重新思考
              </Button>
            </div>
          )}
          {state === 'expend' && (
            <div className="success-container">
              <div
                className={`success-container-content expand-animation ${
                  expend ? 'expand-animation-enter' : 'expand-animation-exit'
                }`}
                style={{ display: 'block' }}
              >
                {data?.map((item, index) => (
                  <div className="success-container-content-item" key={index}>
                    <div className="success-container-content-item-title">
                      {`${index + 1}.${item.direct}`}{' '}
                      <SelfButton
                        onClick={() => {
                          const localPrompt = getLocalStorageItem(
                            isTextToImage
                              ? DEEPSEEK_PROMPT_OPTIMIZATION_KEY_TEXT
                              : DEEPSEEK_PROMPT_OPTIMIZATION_KEY_IMAGE
                          );
                          itemRef.current = item;
                          if (localPrompt) {
                            setShowAIClearModal(false);
                            handleApplyPrompt(item);
                          } else {
                            trackEvent('deepseek_prompt_overwrite_popup_expo', {
                              function: isTextToImage
                                ? 'text_to_image'
                                : 'image_to_image'
                            });
                            setShowAIClearModal(true);
                          }
                          // handleApplyPrompt(item);
                        }}
                      >
                        使用该结果
                      </SelfButton>
                    </div>
                    <div className="success-container-content-item-content">
                      {item.content}
                    </div>
                  </div>
                ))}
              </div>
              <Button
                className="thinking-container-cancel"
                onClick={() => {
                  setState('thinking');
                  setShowMask(false);
                }}
                disabled={props.prompt ? false : true}
              >
                <RefreshBold /> 重新思考
              </Button>
            </div>
          )}
          {state === 'error' && (
            <Button
              className="button-container"
              onClick={() => setState('thinking')}
              disabled={props.prompt ? false : true}
            >
              <DeepThinkingBold /> 重试
            </Button>
          )}
        </div>
      </div>
      <Modal
        open={showAIClearModal}
        wrapClassName="ultraClearBody1"
        className="ultraClearBody1"
        getContainer={document.getElementById('root') || false}
        centered={true}
        width={380}
        footer={null}
        closable={true}
        mask={true}
        onOk={() => {}}
        onCancel={() => {
          setShowAIClearModal(false);
          trackEvent('deepseek_prompt_overwrite_popup_click', {
            click_type: 'close',
            is_check_box: isCheckIAClearMessage ? 1 : 0,
            function: isTextToImage ? 'text_to_image' : 'image_to_image'
          });
        }}
      >
        <h3>提示</h3>
        <div className="detailBox">
          <div className="detailCenter">
            使用该结果将覆盖原输入的提示词，是否继续?
          </div>
          <p className="detailCheck">
            <Checkbox
              onChange={(data) => {
                const checked = data.target.checked;
                setIsCheckIAClearMessage(checked);
              }}
            ></Checkbox>{' '}
            本次登录不再提示
          </p>
          <div className="detailBtnS">
            <div className="footer-box">
              <SelfButton
                type="default"
                onClick={() => {
                  trackEvent('deepseek_prompt_overwrite_popup_click', {
                    click_type: 'cancel',
                    is_check_box: isCheckIAClearMessage ? 1 : 0,
                    function: isTextToImage ? 'text_to_image' : 'image_to_image'
                  });

                  setShowAIClearModal(false);
                }}
                // disabled={upscaleLoading}
              >
                取消
              </SelfButton>
              <SelfButton
                type="primary"
                className="ok-btn"
                onClick={async () => {
                  handleApplyPrompt(itemRef.current);
                  trackEvent('deepseek_prompt_overwrite_popup_click', {
                    click_type: 'confirm',
                    is_check_box: isCheckIAClearMessage ? 1 : 0,
                    function: isTextToImage ? 'text_to_image' : 'image_to_image'
                  });
                  // clearHandle();
                  if (isCheckIAClearMessage)
                    setLocalStorageItem(
                      isTextToImage
                        ? DEEPSEEK_PROMPT_OPTIMIZATION_KEY_TEXT
                        : DEEPSEEK_PROMPT_OPTIMIZATION_KEY_IMAGE,
                      'true'
                    );
                }}
              >
                确定
              </SelfButton>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
}

export default DeepSeekComponent;
