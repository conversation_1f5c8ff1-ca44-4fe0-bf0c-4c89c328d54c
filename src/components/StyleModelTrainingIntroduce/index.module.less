@import '~@/styles/variables.less';

.style-model-training-introduce {
  box-sizing: border-box;
  width: 100%;
  background: @background-system-frame-floatpanel;
  position: relative;
  padding: 5px 0 0 0;
  // --animation-duration: .5s;
  overflow-x: auto;
  :global {
    .wrapper {
      margin: 0 auto;
      max-width: 1030px;
      padding: 0 20px;
      width: 100%;

      .fold-button {
        user-select: none;
        cursor: pointer;
        font-size: @size-ms;
        overflow: visible;
        &-icon {
          margin-left: 8px;
          font-size: 14px;
        }

        &::before {
          display: block;
          content: '';
          position: absolute;
          width: 100%;
          height: 26px;
          left: 0;
          top: 0;
        }
      }

      .introduce {
        margin-top: 0;
        padding-top: 0px;
        height: 0;
        transition: all var(--animation-duration) ease-in-out;
        overflow: hidden;
        max-height: 524px;
        &.active {
          padding-top: 11px;
          // height: 524px;
          height: calc(100vh - 200px);

          .@{ant-prefix}-tabs.introduce-tabs {
            .@{ant-prefix}-tabs {
              &-content-holder {
                .@{ant-prefix}-tabs-content {
                  overflow-x: auto;
                }
              }
            }
          }
        }
        .@{ant-prefix}-tabs.introduce-tabs {
          .@{ant-prefix}-tabs {
            &-nav {
              margin-bottom: 10px;

              &::before {
                border-color: @stroke-btn-secondary;
              }
            }

            &-tab {
              padding: 9px 3px 8px 3px;
              &-btn {
                font-size: @size-ms;
                color: @content-system-tertiary;
              }

              &-active {
                .@{ant-prefix}-tabs-tab-btn {
                  color: @content-system-primary;
                }
              }
            }

            &-ink-bar {
              background: @content-web-tag-selected;
            }

            &-content-holder {
              width: 100%;
              overflow: hidden;
              height: calc(100vh - 273px);
              .@{ant-prefix}-tabs-content {
                overflow: auto;
                height: 100%;
                width: 100%;

                .@{ant-prefix}-tabs-tabpane {
                  padding: 0;
                  width: 990px;
                }
              }
            }
          }
        }
      }
    }
  }
}
