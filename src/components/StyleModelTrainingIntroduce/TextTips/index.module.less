@import '~@/styles/variables.less';

.text-tips {
  width: 100%;
  height: 124px;
  padding: 20px 0 21px 38px;
  background: @background-web-tag-add;
  border-radius: 10px;

  display: flex;

  :global {
    .params {
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      width: 305px;
      // flex: 1 0 305px;
      &-title {
        color: @content-system-primary;
        font-size: @size-ms;
        line-height: 1.375em;
      }

      &-number {
        font-size: 22px;
        color: @content-system-tertiary;
      }

      &-formula {
        font-size: @size-sm;
        color: @content-system-tertiary;
      }
    }

    .divider {
      width: 0;
      height: 74px;
      border-left: 0.5px solid @stroke-btn-secondary;
      align-self: center;
      margin-top: 10px;
    }

    .suggestion {
      box-sizing: border-box;
      width: 674px;
      // flex: 1 0 674px;
      display: flex;
      flex-direction: column;
      padding-left: 47px;

      justify-content: space-between;

      &-title {
        color: @content-system-primary;
        font-size: @size-ms;
        line-height: 1.375em;
        display: flex;
        align-items: center;

        &-icon {
          font-size: 15px;
          color: @content-system-tertiary;
          margin-right: 7px;
        }
      }

      &-item {
        color: @content-system-tertiary;
        font-size: @size-sm;
        overflow: hidden;
        &::before {
          content: '*';
          margin-right: 4px;
        }
      }
    }
  }
}
