import { InfoCircleBoldFill } from '@meitu/candy-icons';
import styles from './index.module.less';

export type TextTipsProps = {
  trainingPictureTips: [string, string];
  paramsNumberRange: [number, number];
};

export function TextTips({
  trainingPictureTips,
  paramsNumberRange
}: TextTipsProps) {
  return (
    <div className={styles.textTips}>
      <div className="params">
        <span className="params-title">推荐参数</span>
        <span className="params-number">
          {paramsNumberRange[0]} ～ {paramsNumberRange[1]}
        </span>
        <span className="params-formula">
          图片数量 x 训练次数 x 迭代周期 = 训练步数
        </span>
      </div>
      <div className="divider"></div>
      <div className="suggestion">
        <span className="suggestion-title">
          <InfoCircleBoldFill className="suggestion-title-icon" />
          训练图建议
        </span>
        <span className="suggestion-item">{trainingPictureTips[0]}</span>
        <span className="suggestion-item">{trainingPictureTips[1]}</span>
      </div>
    </div>
  );
}
