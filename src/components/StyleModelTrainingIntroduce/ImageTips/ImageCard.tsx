import { CheckCircleBold, CrossCircleBold } from '@meitu/candy-icons';
import styles from './index.module.less';

export type ImageCardItem = {
  message: string;
  images: string[];
};

export type ImageCardProps = {
  title: string;
  ok: ImageCardItem;
  error: ImageCardItem;
};

export function ImageCard({ title, ok, error }: ImageCardProps) {
  return (
    <section className={styles.imageCard}>
      <div className={'title'}>{title}</div>
      <div className="item">
        <div className="item-message ok">
          <CheckCircleBold className="item-message-icon ok" />
          <span className="item-message-content">{ok.message}</span>
        </div>
        <div className="item-images" data-images-num={ok.images.length}>
          {ok.images.map((src) => (
            <img key={src} src={src} alt="" />
          ))}
        </div>
      </div>
      <div className="item">
        <div className="item-message error">
          <CrossCircleBold className="item-message-icon error" />
          <span className="item-message-content">{error.message}</span>
        </div>
        <div className="item-images" data-images-num={error.images.length}>
          {error.images.map((src) => (
            <img key={src} src={src} alt="" />
          ))}
        </div>
      </div>
    </section>
  );
}
