@import '~@/styles/variables.less';

.image-tips {
  display: flex;
  justify-content: space-between;
}

.image-card {
  margin-top: 20px;

  :global {
    .title {
      font-size: 14px;
      color: @content-system-primary;
    }

    .item {
      &-message {
        font-size: @size-sm;
        color: @content-system-secondary;
        display: flex;
        align-items: center;
        line-height: 1.333em;
        margin-bottom: 7px;

        &-icon {
          font-size: 15px;
          margin-right: 4px;
          &.ok {
            color: @background-success;
          }
          &.error {
            color: @content-system-lock;
          }
        }

        &.ok {
          margin-top: 12px;
        }

        &.error {
          margin-top: 17px;
        }
      }

      &-images {
        img {
          border-radius: @border-radius;

          &:not(:first-child) {
            margin-left: 10px;
          }
        }

        &[data-images-num='2'] {
          img {
            width: 148px;
            height: 100px;
          }
        }

        &[data-images-num='3'] {
          img {
            width: 95px;
            height: 100px;
          }
        }
      }
    }
  }
}
