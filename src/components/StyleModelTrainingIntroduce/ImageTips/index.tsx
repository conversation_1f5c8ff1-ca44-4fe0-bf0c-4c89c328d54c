import { ImageCard, ImageCardProps } from './ImageCard';
import styles from './index.module.less';

export type ImageTipsProps = {
  tips: [ImageCardProps, ImageCardProps, ImageCardProps];
};

export function ImageTips({ tips }: ImageTipsProps) {
  return (
    <div className={styles.imageTips}>
      <ImageCard {...tips[0]} />
      <ImageCard {...tips[1]} />
      <ImageCard {...tips[2]} />
    </div>
  );
}
