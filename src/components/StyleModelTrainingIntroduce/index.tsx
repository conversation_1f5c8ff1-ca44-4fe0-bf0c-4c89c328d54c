import { ChevronDownBlack, ChevronUpBlack } from '@meitu/candy-icons';

import styles from './index.module.less';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Tabs } from 'antd';
import { tabsOptions } from './tabsOptions';
import classNames from 'classnames';

type StyleModelTrainingIntroduceProps = {
  className?: string;
  /**
   * 教学模块是否展开
   */
  open?: boolean;
  /**
   * 教学模块展开状态改变触发的事件
   */
  onOpenChange?(open: boolean): void;
  /**
   * 正在浏览的教学tab
   */
  activeKey?: string;
  /**
   * 手动切换tab触发的事件
   */
  onActiveKeyChange?(key: string): void;
  /**
   * 点击tab时触发的事件
   *
   * 与onActiveKeyChange的区别：
   * 如果点击当前停留的tab，onTabClick仍会触发，而onActiveKeyChange不会
   */
  onTabClick?(key: string): void;
};

export function StyleModelTrainingIntroduce({
  className,
  open,
  onOpenChange,
  activeKey,
  onActiveKeyChange,
  onTabClick
}: StyleModelTrainingIntroduceProps) {
  const ButtonIcon = open ? ChevronDownBlack : ChevronUpBlack;

  return (
    <section
      className={classNames(
        className,
        styles.styleModelTrainingIntroduce,
        open && 'active'
      )}
    >
      <div className="wrapper">
        <div
          className="fold-button"
          onClick={() => {
            onOpenChange?.(!open);
          }}
        >
          <strong>风格模型教学</strong>
          <ButtonIcon className="fold-button-icon" />
        </div>
        <main className={classNames('introduce', open && 'active')}>
          <Tabs
            items={tabsOptions}
            className="introduce-tabs"
            activeKey={activeKey}
            onChange={onActiveKeyChange}
            onTabClick={onTabClick}
          />
        </main>
      </div>
    </section>
  );
}

type UseOptions = {
  /**
   * 打开时的钩子
   */
  onOpen?(): void;
  /**
   * 关闭时的钩子
   */
  onClose?(): void;
  /**
   * 轮训间隔
   * 默认值: 3000 (ms)
   */
  loopInterval?: number;
  /**
   * 初始是否为打开状态
   * 默认值: true
   */
  initOpen?: boolean;
};
export function useStyleModelTrainingIntroduce({
  initOpen = true,
  loopInterval = 3000,
  onOpen,
  onClose
}: UseOptions) {
  const [open, setOpen] = useState(initOpen);

  const [activeKey, setActiveKey] = useState(tabsOptions[0].key);
  const autoLoop = useRef<NodeJS.Timer>();

  /**
   * 当用户手动切换tab时，会停止轮训
   * 如果用户没有手动改却换tab 但是关闭了引导 需要暂停轮训
   * 在打开引导之后 如果loopIsStop是false 需要恢复轮训
   */
  const loopIsStop = useRef(false);

  const startAutoLoop = useCallback(() => {
    // 1. 如果已经在轮询了 不用开启新的轮询
    // 2. 如果已经停止了 不会回复新一次轮询
    if (autoLoop.current || loopIsStop.current) {
      return;
    }

    autoLoop.current = setInterval(() => {
      setActiveKey((activeKey) => {
        const currentIndex = tabsOptions.findIndex(
          (tab) => tab.key === activeKey
        );
        const nextIndex = (currentIndex + 1) % tabsOptions.length;

        return tabsOptions[nextIndex].key;
      });
    }, loopInterval);
  }, [loopInterval]);

  useEffect(() => {
    if (initOpen) {
      startAutoLoop();
    }

    return () => {
      clearInterval(autoLoop.current);
      autoLoop.current = undefined;
    };
  }, [startAutoLoop, initOpen]);

  function pauseAutoLoop() {
    if (autoLoop.current) {
      clearInterval(autoLoop.current);
      autoLoop.current = undefined;
    }
  }

  function handleOpenChange(open: boolean) {
    if (open) {
      onOpen?.();
      startAutoLoop();
    } else {
      onClose?.();
      pauseAutoLoop();
    }

    setOpen(open);
  }

  function handleTabChange(activeKey: string) {
    // 用户手动切tab 停止自动轮播
    pauseAutoLoop();
    loopIsStop.current = true;
    setActiveKey(activeKey);
  }

  function close() {
    if (open) {
      pauseAutoLoop();
      setOpen(false);
      onClose?.();
    }
  }

  return {
    open,
    activeKey,
    handleOpenChange,
    handleTabChange,
    close
  };
}
