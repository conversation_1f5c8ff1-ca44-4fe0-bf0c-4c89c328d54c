import { TabsProps } from 'antd';
import { TextTips, TextTipsProps } from './TextTips';
import { ImageTips, ImageTipsProps } from './ImageTips';

// 导入图片需要放到模块顶层
const sceneImages = (require as any).context(
  '@/assets/images/style-model-training-introduce/scene',
  true,
  /\.jpg$/
);
const roleImages = (require as any).context(
  '@/assets/images/style-model-training-introduce/role',
  true,
  /\.jpg$/
);
const styleImages = (require as any).context(
  '@/assets/images/style-model-training-introduce/painting-style',
  true,
  /\.jpg$/
);
const productImages = (require as any).context(
  '@/assets/images/style-model-training-introduce/product',
  true,
  /\.jpg$/
);

/**
 * 将图片按顺序转换成数组
 * @returns
 */
function transformImagesList(images: any) {
  const keys = images.keys() as string[];
  return new Array(keys.length).fill(0).map((_, i) => {
    return images(`./${i + 1}.jpg`) as string;
  });
}

const sceneImagesList = transformImagesList(sceneImages);
const styleImagesList = transformImagesList(styleImages);
const roleImagesList = transformImagesList(roleImages);
const productImagesList = transformImagesList(productImages);

type TabContainerProps = {
  textTips: TextTipsProps;
  imageTips: ImageTipsProps;
};

function TabContainer({ textTips, imageTips }: TabContainerProps) {
  return (
    <>
      <TextTips {...textTips} />
      <ImageTips {...imageTips} />
    </>
  );
}

export const tabsOptions: Exclude<TabsProps['items'], undefined> = [
  {
    key: '场景',
    label: '场景',
    children: (
      <TabContainer
        textTips={{
          trainingPictureTips: [
            '确定场景训练的侧重点，例如：侧重构图、光线或某一类型的建筑等等，在此基础上寻找训练图。',
            '建议训练图40-100张，高清无水印，尽量避免出现与画面不相关的元素。'
          ],
          paramsNumberRange: [3200, 4800]
        }}
        imageTips={{
          tips: [
            {
              title: '整体效果：',
              ok: {
                message: '需构图有美感，色彩搭配协调。',
                images: sceneImagesList.slice(0, 2)
              },
              error: {
                message: '光线过暗、构图差、不清晰的图。',
                images: sceneImagesList.slice(2, 4)
              }
            },
            {
              title: '局部',
              ok: {
                message: '需细节清晰，光影关系较为明确。',
                images: sceneImagesList.slice(4, 6)
              },
              error: {
                message: '崩坏、模糊，抽象复制细节的图。',
                images: sceneImagesList.slice(6, 8)
              }
            },
            {
              title: '主体物',
              ok: {
                message: '需主次关系清晰，色调统一。',
                images: sceneImagesList.slice(8, 10)
              },
              error: {
                message: '拼接式的主体，与场景无关元素。',
                images: sceneImagesList.slice(10, 12)
              }
            }
          ]
        }}
      />
    )
  },
  {
    key: '画风',
    label: '画风',
    children: (
      <TabContainer
        textTips={{
          trainingPictureTips: [
            '尽可能统一画风，在构图、笔触、材质等特点上做到相对一致，避免风格差异过大影响训练效果。',
            '建议训练图40-100张，高清无水印，尽量避免出现与画面不相关的元素。'
          ],
          paramsNumberRange: [3200, 4000]
        }}
        imageTips={{
          tips: [
            {
              title: '整体效果：',
              ok: {
                message: '构图有美感、画质清晰、色彩协调。',
                images: styleImagesList.slice(0, 3)
              },
              error: {
                message: '图片构图差、不清晰、亮度过低。',
                images: styleImagesList.slice(3, 5)
              }
            },
            {
              title: '局部',
              ok: {
                message: '细节清晰，若有特写元素需保证单一明确。',
                images: styleImagesList.slice(5, 7)
              },
              error: {
                message: '局部细节断裂、缺失、特写元素重复。',
                images: styleImagesList.slice(7, 9)
              }
            },
            {
              title: '主体物',
              ok: {
                message: '主体物突出、主次清晰、色调统一。',
                images: styleImagesList.slice(9, 11)
              },
              error: {
                message: '主体以拼接形式出现、肢体结构错误或过于抽象。',
                images: styleImagesList.slice(11, 13)
              }
            }
          ]
        }}
      />
    )
  },
  {
    key: '角色',
    label: '角色',
    children: (
      <TabContainer
        textTips={{
          trainingPictureTips: [
            '选择角色多角度、多造型训练图，建议40～100张，高清无水印。',
            '涵盖场景尽可能广泛，如不同焦段、光线、构图、姿势；避免出现不相关人脸影响训练效果。'
          ],
          paramsNumberRange: [4000, 4800]
        }}
        imageTips={{
          tips: [
            {
              title: '整体效果：',
              ok: {
                message: '角色多角度、多造型展示。',
                images: roleImagesList.slice(0, 3)
              },
              error: {
                message: '构图超出画面或拼接，光线较暗看不清主体。',
                images: roleImagesList.slice(3, 5)
              }
            },
            {
              title: '局部',
              ok: {
                message: '保证肢体结构、面部结构清晰。',
                images: roleImagesList.slice(5, 7)
              },
              error: {
                message: '局肢体、面部或结构扭曲，崩坏模糊或细节过于花哨。',
                images: roleImagesList.slice(7, 9)
              }
            },
            {
              title: '姿势',
              ok: {
                message: '固定角色肢体协调、动作清晰。',
                images: roleImagesList.slice(9, 11)
              },
              error: {
                message: '角色角度和姿势难以识别，或动作被障碍物遮挡。',
                images: roleImagesList.slice(11, 13)
              }
            }
          ]
        }}
      />
    )
  },
  {
    key: '产品',
    label: '产品',
    children: (
      <TabContainer
        textTips={{
          trainingPictureTips: [
            '训练图选择同一产品或系列，画面避免出现不相关元素。',
            '建议训练图40-100张，高清切产品表面无水印，展示方式尽可能广泛、多角度、多光线。'
          ],
          paramsNumberRange: [3200, 4000]
        }}
        imageTips={{
          tips: [
            {
              title: '整体效果：',
              ok: {
                message: '训练图都使用同一产品类别或同一系列。',
                images: productImagesList.slice(0, 3)
              },
              error: {
                message: '避免主体物产品类别不对。',
                images: productImagesList.slice(3, 5)
              }
            },
            {
              title: '角度',
              ok: {
                message: '多角度、光线，背景简洁凸显主体。',
                images: productImagesList.slice(5, 7)
              },
              error: {
                message: '出现多个主体物，背景繁杂或被不相关元素遮挡。',
                images: productImagesList.slice(7, 9)
              }
            },
            {
              title: '细节',
              ok: {
                message: '表面细节清晰，无任何水印。',
                images: productImagesList.slice(9, 11)
              },
              error: {
                message: '细节崩坏、重复、LOGO漏出。',
                images: productImagesList.slice(11, 13)
              }
            }
          ]
        }}
      />
    )
  }
];
