@import '~@/styles/variables.less';

@skeleton-color: #efefef;

.model-image {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;

  .image-container {
    position: relative;
    flex: 1 0;
    border-radius: @border-radius-lg;
    overflow: hidden;

    .auditing {
      position: absolute;
      top: 8px;
      left: 8px;
      padding: 1px 6px;
      border-radius: 4px;
      color: @base-white-opacity-100;
      font-size: @text-12;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      cursor: pointer;
      background-color: @background-audit;
    }

    :global .@{ant-prefix}-image {
      width: 100%;
      height: 100%;

      img {
        background-color: @skeleton-color;
        max-width: 100%;
        max-height: 100%;
        min-width: 100%;
        min-height: 100%;
        object-fit: cover;
      }
    }

    .tags {
      position: absolute;
      left: @size-xs;
      top: @size-xs;
      font-size: @size-sm;
      line-height: @size;

      :global path {
        fill: @color-bg-base;
      }
    }

    .mask {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      padding: @size-xs;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 60%,
        rgba(0, 0, 0, 0.6) 93%
      );
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      opacity: 0;
      transition: opacity 0.5s ease-in-out;
      cursor: pointer;

      &:hover {
        opacity: 1;
      }

      &.show {
        opacity: 1;
      }

      .lora {
        width: 34px;
        height: 20px;
        border-radius: 4px;
        background: @base-black-opacity-30;
        display: flex;
        justify-content: center;
        align-items: center;
        color: @base-white-opacity-100;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
      }

      .model-name {
        color: @base-white-opacity-100;
        line-height: @size;
        font-size: @text-14;
        margin: 4px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .bottom-box {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .model-source {
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 4px;
          background: @base-black-opacity-35;
          padding: 3px 4px;
          height: 22px;

          .model-img {
            width: 14px;
            height: 14px;
            border-radius: 2px;
            margin-right: 4px;
          }

          .base-model-name {
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            color: @base-white-opacity-100;
            max-width: 84px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .create-btn {
        width: 100%;
        height: 32px;
        font-size: @text-14;
        font-weight: 400;
        padding: 0px;
        background: @background-btn-primary-2;
        color: #fff;
        border: none;
        display: flex;
        justify-content: center;
        align-items: center;

        & > span {
          margin-right: -4px;
        }

        &:hover {
          background: @background-btn-primary-2;
        }
      }
    }
  }

  .content {
    display: flex;
    height: 44px;
    align-items: center;
    justify-content: space-between;
    padding: 0 @size-xs;

    .profile {
      display: flex;
      align-items: center;

      .avatar {
        width: 20px;
        height: 20px;
        flex: 0 0 auto;
        background: @skeleton-color;
      }

      .name {
        max-width: 110px;
        height: @size-ms;
        line-height: @size-ms;
        margin-left: 4px;
        font-size: @text-12;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .like {
      padding-right: 0;
      color: @content-system-quaternary;
      background-color: transparent;
    }
  }
}

.model-overlay {
  z-index: calc(@z-index-popup-base - 2) !important;
}
