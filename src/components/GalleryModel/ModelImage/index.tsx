/* eslint-disable react-hooks/rules-of-hooks */
import { CreateLocationType, type GalleryProfile } from '@/types';

import { AppModule } from '@/services';
import { App, Avatar, Image, Button } from 'antd';
import {
  EditorUsableAuthorized,
  CollectionSwitch,
  ApplyModelButton
} from '@/components';

import { switchGalleryFavorites } from '@/api';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { getApplySearchParams } from '@/utils/searchParams';
import { useCallback, useMemo, useState } from 'react';
import { toAtlasImageView2URL } from '@meitu/util';
import styles from './index.module.less';
import classNames from 'classnames';
import { FeedStatus } from '@/api/types';
import { ModelBold } from '@meitu/candy-icons';
import AdaptContainer from '@/components/Waterfall/AdaptContainer';
import { useSetFavoriteState } from '@/components/Gallery/hooks';
import { useDetailModal } from '@/components/DetailModalProvider';
import { getSource } from '@/utils';
interface ModelImageProps extends GalleryProfile {
  selectTypeName?: string;
  mode: 'host' | 'visitor';
  feedType: 'gallery' | 'personal';
  tab?: string;
  onTriggerApply?(): void;
  onApplyToPage?(page: AppModule): void;

  onCollectedChange?: (isCollected: boolean) => void;

  /** 收藏页，取消收藏后，有回调 */
  afterSwitchFavorites?: () => void;
}

export function ModelImage(props: ModelImageProps) {
  const {
    id,
    user,
    title,
    picUrl,
    isFavor,
    favorCount,
    mode,
    feedType,
    onTriggerApply,
    onApplyToPage,
    modelId,
    status,
    taskCategory,
    feedId,
    tab,
    afterSwitchFavorites,
    onCollectedChange: onCollectedChangeFromProps
  } = props;
  const userName = user?.userName;
  const avatar = user?.avatar;

  const setFavoriteState = useSetFavoriteState(feedType === 'personal');

  const { message } = App.useApp();

  const [src, placeholderPicUrl] = useMemo<[string, string]>(
    () => [
      toAtlasImageView2URL(picUrl, { mode: 2, width: 350 }),
      toAtlasImageView2URL(picUrl, { mode: 2, width: 50 })
    ],
    [picUrl]
  );

  const [showMask, setShowMask] = useState(false);

  /** 处理收藏和取消收藏 */
  const handleFavorite = useCallback(async () => {
    try {
      setFavoriteState(id, !isFavor);
      // 收藏页面取消收藏
      feedType === 'personal' && isFavor && afterSwitchFavorites?.();

      await switchGalleryFavorites(feedId || '', !isFavor);
      onCollectedChangeFromProps?.(!isFavor);
      isFavor ? message.info('已取消收藏') : message.success('已收藏此模型');
    } catch (err) {
      defaultErrorHandler(err);
    }
  }, [
    setFavoriteState,
    id,
    isFavor,
    feedType,
    afterSwitchFavorites,
    onCollectedChangeFromProps,
    feedId,
    message
  ]);

  const { addModal } = useDetailModal();
  const onMaskClick = () => {
    addModal({ id, taskCategory });
  };

  return (
    <main className={styles.modelImage}>
      <section className={styles.imageContainer}>
        <AdaptContainer size={[512, 512]} columnWidth={0}>
          <Image
            preview={false}
            src={src}
            placeholder={<Image preview={false} src={placeholderPicUrl} />}
          />
        </AdaptContainer>
        {status === FeedStatus.AUDITING && mode === 'host' && (
          <div className={styles.auditing}>审核中</div>
        )}
        <div
          className={classNames(styles.mask, { [styles.show]: showMask })}
          onClick={onMaskClick}
        >
          <div className={styles.lora}>Lora</div>
          <p className={styles.modelName}>{title}</p>
          <div className={styles.bottomBox}>
            <EditorUsableAuthorized>
              <ApplyModelButton
                trigger={
                  <Button block icon={<ModelBold />}>
                    应用模型
                  </Button>
                }
                className={styles.createBtn}
                overlayClassName={styles.modelOverlay}
                onOpenChange={setShowMask}
                popupContainer={() => {
                  return document.getElementById('scrollBox') || document.body;
                }}
                routeToNewTab
                getExtraQueryParams={(appModule) => {
                  const tracerParams = {
                    source: getSource(),
                    modelId,
                    feedId,
                    location: CreateLocationType.Feed,
                    tab
                  };

                  return getApplySearchParams(appModule, {
                    styleModelId: String(modelId),
                    ...tracerParams
                  });
                }}
                onClick={(event, appModule) => {
                  onApplyToPage?.(
                    appModule === AppModule.ImageToImage
                      ? AppModule.ImageToImage
                      : AppModule.TextToImage
                  );
                }}
                onTriggerClick={onTriggerApply}
              />
            </EditorUsableAuthorized>
          </div>
        </div>
      </section>
      {mode === 'visitor' && (
        <section className={styles.content}>
          <div className={styles.profile}>
            {avatar && (
              <Avatar
                className={styles.avatar}
                src={toAtlasImageView2URL(avatar, { mode: 2, width: 36 })}
              />
            )}
            <strong className={styles.name}>{userName}</strong>
          </div>
          {/** 点赞和取消收藏 */}
          <CollectionSwitch
            isCollected={isFavor}
            count={favorCount}
            className={styles.like}
            onChange={(isCollected, event) => {
              event.preventDefault();
              event.stopPropagation();

              handleFavorite();
            }}
          />
        </section>
      )}
    </main>
  );
}
