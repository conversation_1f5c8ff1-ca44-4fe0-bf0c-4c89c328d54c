import type { GalleryDetail } from '@/types';

import { Modal } from 'antd';
import { SingleImagePreview, DetailContainer } from '@/components';
import { forwardRef, useEffect, useState } from 'react';
import { ModelDetailContent } from './ModelDetailContent';
import { fetchGalleryDetail } from '@/api';

import styles from './index.module.less';
import { CrossBold, HomeBold } from '@meitu/candy-icons';

import { DraftType, FeedStatus } from '@/api/types';
import { SameModelGallery } from './SameModelGallery';
import { GalleryImageDetailProps } from '@/components/Gallery/GalleryImageDetailModal/GalleryImageDetail';
import { trackEnterEvent } from './fixtureTrack';
import {
  DetailModalContext,
  useDetailModal
} from '@/components/DetailModalProvider';
import { AppModule } from '@/services';

export interface GalleryImageDetailModalRef {
  open: (feedId: string, taskCategory?: DraftType) => void;
  close: () => void;
}

export interface GalleryImageDetailModalProps
  extends Omit<GalleryImageDetailProps, 'value'> {
  mode?: 'host' | 'visitor';
  afterClose?: () => void;

  id?: string;
  taskCategory?: DraftType;

  // 统计上报 区分tab
  tab?: string;

  onApply?: (appModule: AppModule, detail: GalleryDetail) => void;

  onCollectedChange?: (modelDetail: GalleryDetail) => void;
}

export const ModelDetailModal = forwardRef<
  GalleryImageDetailModalRef,
  GalleryImageDetailModalProps
>((props, ref) => {
  const { tab } = props;
  const [modelDetail, setModelDetail] = useState<GalleryDetail>();

  useEffect(() => {
    if (!modelDetail) return;

    trackEnterEvent(modelDetail, tab);
  }, [modelDetail, tab]);

  useEffect(() => {
    fetchGalleryDetail(
      props.id ?? '',
      props.taskCategory ?? DraftType.MODEL
    ).then((res) => {
      setModelDetail(res);
    });
  }, [props.id, props.taskCategory]);

  const { removeModal, closeAllModals } = useDetailModal();

  const onCancel = () => {
    props.afterClose?.();
    removeModal({ id: props.id || '' });
  };
  // 回到广场
  const backList = () => {
    props.afterClose?.();
    closeAllModals();
  };

  return (
    <Modal
      open={true}
      maskClosable={false}
      footer={null}
      className={styles.modal}
      wrapClassName={styles.modalWrap}
      onCancel={onCancel}
      closeIcon={null}
      mask={false}
      width={'100%'}
    >
      <div className={styles.toolBox}>
        <div className={styles.modalCloseBtn} onClick={onCancel}>
          <CrossBold />
        </div>
        <DetailModalContext.Consumer>
          {({ modals }) => {
            return (
              modals.length > 1 && (
                <div className={styles.modalCloseBtn} onClick={backList}>
                  <HomeBold />
                </div>
              )
            );
          }}
        </DetailModalContext.Consumer>
      </div>
      <div className={styles.modelDetail}>
        <div onClick={onCancel} className={styles.modelDetailInfoContainer}>
          <div
            className={styles.modelDetailInfo}
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <DetailContainer
              preview={
                modelDetail?.picUrl && (
                  <SingleImagePreview
                    src={modelDetail.picUrl}
                    mask={null}
                    size={[1, 1]}
                    className={styles.modelDetailInfoPreview}
                  />
                )
              }
              previewClassName={styles.modelDetailInfoPreviewContainer}
            >
              <ModelDetailContent
                {...modelDetail}
                mode={props.mode ?? 'visitor'}
                onCancel={onCancel}
                onApply={(appModule: AppModule) =>
                  props.onApply?.(appModule, modelDetail!)
                }
                onCollectedChange={(isCollected) =>
                  props.onCollectedChange?.({
                    ...modelDetail,
                    isFavor: isCollected
                  } as GalleryDetail)
                }
              />
            </DetailContainer>
          </div>
        </div>

        {modelDetail?.status === FeedStatus.PUBLISHED && (
          <SameModelGallery feedId={modelDetail.feedId || ''} />
        )}
      </div>
    </Modal>
  );
});
