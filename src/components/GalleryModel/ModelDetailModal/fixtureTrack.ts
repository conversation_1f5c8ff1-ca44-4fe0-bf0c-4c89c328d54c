import { FeedStatus } from '@/api/types';
import { trackEvent } from '@/services';
import { GalleryDetail } from '@/types';
import { getSource } from '@/utils';

export const trackEnterEvent = (detail: GalleryDetail, tab?: string) => {
  trackEvent('model_detail_enter', {
    ...fixturesDetail(detail),
    function: getSource(),
    tab
  });
};

type ClickType =
  | 'collect'
  | 'cancel_collect'
  | 'apply'
  | 'text_to_image'
  | 'image_to_image'
  | 'base_model';

export const trackBtnClickEvent = (
  detail: GalleryDetail,
  clickType: ClickType
) => {
  const location = getLocation() ?? '';
  trackEvent('model_detail_btn_click', {
    ...fixturesDetail(detail),
    clickType,
    location
  });
};

const fixturesDetail = (detail: GalleryDetail) => {
  const {
    favorCount: favoriteNum,
    applyCount: applyNum,
    viewCount: viewNum,
    id,
    trainingFrequency,
    iterationPeriod,
    model: { modelId },
    desc,
    title: name,
    status
  } = detail;

  return {
    id,
    feedId: id,
    modelId,
    name,
    desc,
    favoriteNum,
    applyNum,
    viewNum,
    status: status === FeedStatus.PUBLISHED ? 2 : 1,
    trainingFrequency,
    iterationPeriod
  };
};

export const getLocation = () => {
  // 获取url上的searchparams的location
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('location');
};
