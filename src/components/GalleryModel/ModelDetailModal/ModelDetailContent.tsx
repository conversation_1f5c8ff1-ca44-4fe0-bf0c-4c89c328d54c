import { CreateLocationType, type GalleryDetail } from '@/types';
import type { ReactElement } from 'react';

import { User, VerticalWrapper, CollectionSwitch } from '@/components';
import { Skeleton, Divider, Tooltip, App, message } from 'antd';
import {
  Brush,
  ChevronRightBlack,
  ImageToImageBold,
  ShareBold,
  TextToImageBold
} from '@meitu/candy-icons';
import { BaseModelCard } from '@/components/Model/ModelCard/BaseModelCard';
import { getSwitchCollected } from '@/utils/collection';
import { AppModule, generateRouteTo, trackEvent } from '@/services';
import { Link, useNavigate } from 'react-router-dom';
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react';
import { getApplySearchParams } from '@/utils/searchParams';
import _ from 'lodash';

import styles from './index.module.less';
import { FeedStatus } from '@/api/types';
import { trackBtnClickEvent } from './fixtureTrack';
import classNames from 'classnames';
import { useSetFavoriteState } from '@/components/Gallery/hooks';
import { getSource } from '@/utils';
import { ImageShareModal } from '@/components/ImageShareModal';

interface CountWithIconProps {
  icon: ReactElement;
  count?: number;
}

function CountTag({ icon, count }: CountWithIconProps) {
  return (
    <div className={styles.countTag}>
      {icon}
      {count ?? 0}
    </div>
  );
}

type PublishInfoType = Partial<
  Omit<ModelDetailContentProps, 'user' | 'src'>
> & {
  onCollectedChange?(isCollected: boolean): void;
  onBaseModelClick?(): void;
  onCancel: () => void;
};

type PublishInfoRefType = {
  favorCount: number;
};

/** 发布信息 */
const PublishInfo = forwardRef<PublishInfoRefType, PublishInfoType>(
  (props, ref) => {
    const {
      title,
      text,
      publishTime,
      favorCount = 0,
      applyCount,
      isFavor = false,
      model,
      iterationPeriod,
      trainingFrequency,
      status,
      mode,
      onBaseModelClick: onBaseModelClickProp,
      onCollectedChange: onCollectedChangeProp,
      onCancel,
      feedId,
      id
    } = props;

    const navigate = useNavigate();
    const setFavoriteState = useSetFavoriteState();

    const { message } = App.useApp();
    const switchCollected = getSwitchCollected(feedId);
    const [collect, setCollect] = useState<[boolean, number]>([
      isFavor,
      favorCount
    ]);

    useEffect(() => {
      setCollect([isFavor, favorCount]);
    }, [isFavor, favorCount]);

    const onCollectedChange = async (isCollected: boolean) => {
      setCollect(([, collectedCount]) => [
        isCollected,
        isCollected ? collectedCount + 1 : collectedCount - 1
      ]);

      onCollectedChangeProp?.(isCollected);

      const result = await switchCollected(isCollected);

      if (result) {
        isCollected
          ? message.success('已收藏此模型')
          : message.info('已取消收藏');
        id && setFavoriteState(id, isCollected);
      }
    };

    const onBaseModelClick = (id: number) => {
      onBaseModelClickProp?.();

      navigate(
        generateRouteTo(AppModule.StyleModelTraining, undefined, {
          baseModelId: String(id),
          source: getSource()
        })
      );

      onCancel();
    };

    useImperativeHandle(
      ref,
      () => ({
        favorCount: collect[1]
      }),
      [collect]
    );

    return (
      <>
        <div className={styles.publishInfo}>
          <h3>
            {title}
            {mode === 'host' && status === FeedStatus.AUDITING && (
              <span className={styles.warningTag}>审核中</span>
            )}
          </h3>
          <p>{text}</p>
          <div className={styles.publishInfoFooter}>
            <CollectionSwitch
              count={collect[1]}
              isCollected={collect[0]}
              className={styles.countTag}
              onChange={onCollectedChange}
            />
            <Tooltip title="应用模型数">
              <>
                <CountTag icon={<Brush />} count={applyCount} />
              </>
            </Tooltip>
            {/* TODO 暂时隐藏，有数据了再放开
            {mode === 'host' && (
              <Tooltip title="浏览模型数">
                <>
                  <CountTag icon={<Visible />} count={viewCount} />
                </>
              </Tooltip>
            )} */}
          </div>
        </div>
        <Divider />
        {model?.modelId && (
          <VerticalWrapper label="基础模型">
            <BaseModelCard
              {...model}
              extra={
                <div className={styles.anchorRight}>
                  <ChevronRightBlack />
                </div>
              }
              className={styles.baseModel}
              onClick={onBaseModelClick.bind(null, model.modelId)}
              onExtraClick={onBaseModelClick.bind(null, model.modelId)}
            />
          </VerticalWrapper>
        )}
        {mode === 'host' && (
          <VerticalWrapper label="参数训练">
            <div className={styles.publishInfoRow}>
              <span>训练次数</span>
              <span>{iterationPeriod}</span>
            </div>
            <div className={styles.publishInfoRow}>
              <span>迭代周期</span>
              <span>{trainingFrequency}</span>
            </div>
          </VerticalWrapper>
        )}
        <Divider />
        <div className={styles.publishInfoRow}>
          <span>发布时间：{publishTime}</span>
        </div>
      </>
    );
  }
);

interface ModelDetailContentProps extends Partial<GalleryDetail> {
  mode: 'host' | 'visitor';
  onCancel: () => void;

  onApply?: (appModule: AppModule) => void;

  onCollectedChange?(isCollected: boolean): void;
}

export function ModelDetailContent(props: ModelDetailContentProps) {
  const { user, modelId, mode, onCancel, isExcellent, ...publishInfoProps } =
    props;
  const publishInfoRef = useRef<PublishInfoRefType>(null);
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const canShare = isExcellent;

  const fixturesProps = () =>
    ({
      ...props,
      favorCount: publishInfoRef.current?.favorCount ?? props.favorCount ?? 0
    } as GalleryDetail);

  const onApply = (appModule: AppModule) => {
    trackBtnClickEvent(
      fixturesProps(),
      appModule === AppModule.ImageToImage ? 'image_to_image' : 'text_to_image'
    );

    props?.onApply?.(appModule);
  };

  /**
   * 生成ReactRouter的Link组件使用的to属性配置
   */
  function getLinkToOptions(appModule: AppModule) {
    // 用于追踪来源的埋点信息
    const tracerParams = {
      source: getSource(),
      feedId: props?.feedId,
      modelId: props?.modelId,
      location: CreateLocationType.Detail
    };

    return generateRouteTo(
      appModule,
      getApplySearchParams(appModule, {
        styleModelId: modelId,
        ...tracerParams
      })
    );
  }

  const showShareModal = () => {
    if (!canShare) {
      message.warning('为确保内容品质，该模型的分享功能暂已关闭。');
      return;
    }
    trackEvent('work_model_share_click', {
      task_id: props.id,
      feed_id: props.feedId,
      model_id: props.modelId
    });
    setShareModalVisible(true);
  };

  const hideShareModal = () => {
    setShareModalVisible(false);
  };

  const onShareModalButtonClick = (typeCd: 'save' | 'copy') => {
    trackEvent('work_model_shared_popup_click', {
      task_id: props.id,
      feed_id: props.feedId,
      click_type: typeCd,
      modelId: props.modelId
    });
  };

  return (
    <div className={styles.detail}>
      <div className={styles.detailContent}>
        <User {...user} />
        <div className={styles.detailShareButton}>
          <ShareBold
            className={
              canShare ? styles.detailShareIcon : styles.detailInvalidShareIcon
            }
            onClick={showShareModal}
          />
          {canShare ? (
            <ImageShareModal
              user={props.user}
              onClose={hideShareModal}
              visible={shareModalVisible}
              imageSrc={props.picUrl || ''}
              shareUrl={`${window.location.origin}/art?id=${props.id}&taskCategory=${props.taskCategory}`}
              preIconText="Lora"
              onButtonClick={onShareModalButtonClick}
              text={props.title || ''}
            />
          ) : null}
        </div>
        {_.isEmpty(publishInfoProps) ? (
          <Skeleton className={styles.publishInfo} />
        ) : (
          <PublishInfo
            ref={publishInfoRef}
            {...publishInfoProps}
            mode={mode}
            onBaseModelClick={() => {
              trackBtnClickEvent(fixturesProps(), 'base_model');
            }}
            onCancel={onCancel}
            onCollectedChange={(isCollected: boolean) => {
              const params = fixturesProps();

              props.onCollectedChange?.(isCollected);

              trackBtnClickEvent(
                {
                  ...params,
                  favorCount: isCollected
                    ? params.favorCount + 1
                    : params.favorCount - 1
                },
                isCollected ? 'collect' : 'cancel_collect'
              );
            }}
          />
        )}
      </div>

      <div className={styles.footer}>
        <Divider plain>应用模型</Divider>
        <div className={styles.btnBox}>
          <Link
            to={getLinkToOptions(AppModule.ImageToImage)}
            target="_blank"
            className={styles.applyBtn}
            onClick={(event) => {
              event.stopPropagation();
              onApply(AppModule.ImageToImage);
            }}
          >
            <ImageToImageBold />
            图生图
          </Link>
          <Link
            to={getLinkToOptions(AppModule.TextToImage)}
            target="_blank"
            className={classNames(styles.applyBtn, styles.anotherBtn)}
            onClick={(event) => {
              event.stopPropagation();
              onApply(AppModule.TextToImage);
            }}
          >
            <TextToImageBold />
            文生图
          </Link>
        </div>
      </div>
    </div>
  );
}
