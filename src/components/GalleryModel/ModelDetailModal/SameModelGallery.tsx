import { CreateLocationType, type GalleryProfile } from '@/types';
import type { GalleryQuery } from '@/api/types';

import { fetchGalleryByModelId } from '@/api';
import { Gallery, GalleryImage, GalleryProvider } from '@/components/Gallery';
import { useState, useCallback, useRef } from 'react';

import styles from './index.module.less';
import {
  galleryClickTracking,
  galleryCollectTracking,
  galleryCreateSameTracking,
  galleryExposureTracking
} from '@/utils/galleryTracking';
import { EventsContainer } from '@/components/TrackEvent';

export function SameModelGallery(props: { feedId: string }) {
  const { feedId } = props;

  const [showTitle, setShowTitle] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const fetchGallery = useCallback(
    async (params: GalleryQuery) => {
      if (!feedId) {
        return;
      }
      const gallery = await fetchGalleryByModelId(feedId, {
        ...params,
        // 覆盖默认查询条数
        count: 10
      });

      if (!showTitle && gallery?.list?.length > 0) {
        setShowTitle(true);
      }

      return gallery;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [feedId]
  );

  return (
    <div ref={containerRef} className={styles.sameModelGallery}>
      {showTitle && <h2>同款风格模型作品</h2>}
      <GalleryProvider>
        <Gallery
          fetchGallery={fetchGallery}
          gutter={[16, 4]}
          empty={null}
          renderItem={(profileProps: GalleryProfile) => (
            <EventsContainer
              onView={() =>
                /** 作品曝光上报 */
                galleryExposureTracking(profileProps, CreateLocationType.Detail)
              }
              onClick={() => {
                galleryClickTracking(profileProps, CreateLocationType.Detail);
              }}
            >
              <GalleryImage
                {...profileProps}
                location={CreateLocationType.Detail}
                /** 创作同款点击上报 */
                onCreateClick={() => {
                  galleryCreateSameTracking(
                    CreateLocationType.Detail,
                    profileProps
                  );
                }}
                /** 收藏上报 */
                onCollectedChange={(detail) => {
                  galleryCollectTracking(CreateLocationType.Detail, detail);
                }}
              />
            </EventsContainer>
          )}
        />
      </GalleryProvider>
    </div>
  );
}
