@import '~@/styles/variables.less';

.modal-wrap:global(.@{ant-prefix}-modal-wrap) {
  top: 56px !important;
  background: #f6f7fa;
  padding: 16px 0 0;
  z-index: 900 !important;
}

.modal:global(.@{ant-prefix}-modal) {
  top: 0;
  width: 65vw;
  min-width: 1200px;
  height: 100%;
  padding: 0;
  border-radius: @border-radius-lg;

  :global .@{ant-prefix}-modal-content {
    height: 100%;
    padding: 0;
    background-color: @color-bg-base;
    box-shadow: none;

    .@{ant-prefix}-modal-close {
      top: 0;
      right: 84px;
      width: 32px;
      height: 32px;
      background: @base-white-opacity-100;
      border-radius: 50%;
      cursor: pointer;

      svg {
        width: 18px;
        height: 18px;
        color: @content-system-quaternary;
      }
    }

    .@{ant-prefix}-modal-body {
      height: 100%;
    }
  }

  :global(.@{ant-prefix}-image-mask) {
    &:hover {
      :local .comparing-action {
        opacity: 1;
      }
    }
  }
}

.tool-box {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 9;
  display: flex;
  flex-direction: column;
  gap: 8px;

  .modal-close-btn {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: @background-btn-secondary;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.06),
      0px 0px 2px 0px rgba(0, 0, 0, 0.04);

    svg {
      width: 20px;
      height: 20px;
    }
  }
}

.model-detail {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  overflow-y: auto;
  background-color: @background-web-page-secondary;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  &-info-container {
    width: 100%;
    height: 82%;
    min-height: 480px;
    max-height: 850px;
    margin: 0 auto;
    margin-bottom: @size-lg;
  }

  &-info {
    width: 1056px;
    margin: 0 auto;
    height: 100%;
    border-radius: @size-sm;
    position: relative;

    &-preview-container {
      flex: none;
      width: calc(100% - 374px);
      max-width: none;
    }

    &-preview {
      :global {
        .@{ant-prefix}-image {
          width: 100%;
          height: 100%;

          &-img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover;
          }
        }
      }
    }
  }
}

.detail {
  position: relative;
  height: 100%;
  padding: @size-lg;
  background-color: @background-system-frame-floatpanel;

  &-content {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 90px;
    position: relative;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &-share-button {
    position: absolute;
    right: 0px;
    top: 0px;
  }

  &-share-icon svg {
    color: #000;
    cursor: pointer;
    width: 20px;
    height: 20px;
  }

  &-invalid-share-icon svg {
    color: #aaa;
    cursor: default;
    width: 20px;
    height: 20px;
  }

  .anchor-right {
    position: absolute;
    top: @size;
    right: @size-sm;
  }

  .base-model {
    overflow: hidden;
    transition: background-color 0.3s ease;

    :global(.model-card-extra) {
      background-color: transparent;
    }

    &:hover {
      border-color: @stroke-btn-secondary;
      box-shadow: none;

      :global(.@{ant-prefix}-card-body) {
        background-color: @background-card-hover;

        & > span {
          background-color: transparent;
        }
      }
    }
  }

  .publish-info {
    margin-top: @size-sm;

    .warning-tag {
      border-radius: @size-xxs;
      background: @background-audit;
      color: @base-white-opacity-100;
      font-size: @size-sm;
      margin-left: @size-xxs;
      font-weight: 400;
      padding: 1px 6px;
    }

    h3 {
      word-break: break-all;
    }

    p {
      color: @content-system-tertiary;
      word-break: break-all;
    }

    &-footer {
      display: flex;
      align-items: center;
    }

    &-row {
      display: flex;
      justify-content: space-between;
      color: @content-system-tertiary;

      & + .publish-info-row {
        margin-top: @size-xxs;
      }
    }
  }

  .count-tag {
    display: flex;
    align-items: center;
    min-width: 48px;
    height: @size-lg;
    padding: 0 @size-xs;
    background-color: @background-tag;
    color: @content-system-primary;
    border-radius: @size-xxs;
    margin-right: @size-sm;
    font-size: @font-size-sm;

    span {
      margin-right: @size-xxs;
      font-size: @font-size;
      color: @content-system-primary;
    }
  }

  :global(.@{ant-prefix}-divider-horizontal) {
    margin: @size-ms 0;
  }

  .footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 @size-lg @size-lg;
    background: @base-white-opacity-100;

    :global(.@{ant-prefix}-divider-with-text) {
      margin: 8px 0 !important;
      color: @content-system-quaternary;
      font-size: 12px;

      span {
        line-height: 15px;
      }
    }

    .btn-box {
      display: flex;
      justify-content: space-between;
      width: 100%;

      .apply-btn {
        width: 158px;
        height: 44px;
        border-radius: 8px;
        border: 1px solid @stroke-btn-secondary;
        background: @background-btn-secondary;
        gap: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: @content-btn-secondary;
        cursor: pointer;
      }

      .another-btn {
        border: none;
        background: @background-btn-ai;
        color: @content-btn-primary;
      }
    }

    .confirm-btn {
      width: 100%;
      height: @size-xl;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.same-model-gallery {
  max-width: 100%;
  padding: 0 120px;
}

.apply-overlay {
  min-width: 0 !important;
  right: 0;
  left: auto !important;
  width: 164px;
}
