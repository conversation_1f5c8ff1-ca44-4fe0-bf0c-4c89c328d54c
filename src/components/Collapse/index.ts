import { Collapse as InternalCollapse } from './Collapse';
import { Collapse as AntdCollapse } from 'antd';
import { PanelSection } from './PanelSection';

type InternalCollapseType = typeof InternalCollapse;
type InternalCollapsePanelType = typeof AntdCollapse.Panel;

interface CollapsePanelType extends InternalCollapsePanelType {
  Section: typeof PanelSection;
}

interface CollapseType extends InternalCollapseType {
  Panel: CollapsePanelType;
}

const CollapsePanel = AntdCollapse.Panel as unknown as CollapsePanelType;

const Collapse = InternalCollapse as unknown as CollapseType;

Collapse.Panel = CollapsePanel;
Collapse.Panel.Section = PanelSection;

export type { CollapsePanelProps, CollapseProps } from 'antd';
export { Collapse };
