import type { ReactNode } from 'react';

import { useDesignerPrefixCls } from '@/components';
import classNames from 'classnames';

export interface PanelSectionProps {
  /** 标题 */
  title?: ReactNode;

  /** 额外 */
  extra?: ReactNode;

  /** 内容区 */
  children?: ReactNode;

  /** 唯一标识 */
  id?: string;

  className?: string;
}

export function PanelSection(props: PanelSectionProps) {
  const prefixCls = useDesignerPrefixCls('collapse-panel-section');

  return (
    <section className={classNames(prefixCls, props.className)} id={props.id}>
      {!!(props.title || props.extra) && (
        <div className={`${prefixCls}-header`}>
          {!!props.title && (
            <h2 className={`${prefixCls}-title`}>{props.title}</h2>
          )}

          {!!props.extra && (
            <div className={`${prefixCls}-extra`}>{props.extra}</div>
          )}
        </div>
      )}

      {!!props.children && (
        <div className={`${prefixCls}-content`}>{props.children}</div>
      )}
    </section>
  );
}
