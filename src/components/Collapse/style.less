@import '~@/styles/variables.less';

.@{designer-prefix}-collapse {
  &.@{ant-prefix}-collapse {
    border: none;
    background: transparent;

    > .@{ant-prefix}-collapse-item {
      border: none;
      border-radius: @border-radius-lg;
      background: @background-system-frame-floatpanel;
      box-shadow: @level-2;
      transition: all @motion-duration-mid;

      &:last-child {
        border-radius: @border-radius-lg;
      }

      &:not(:last-child) {
        margin-bottom: @size-xs;
      }

      > .@{ant-prefix}-collapse-header {
        padding: @size;

        .@{ant-prefix}-collapse-header-text {
          font-weight: 600;
        }
      }

      &.@{ant-prefix}-collapse-no-arrow {
        > .@{ant-prefix}-collapse-header {
          display: none;
        }

        .@{ant-prefix}-collapse-content > .@{ant-prefix}-collapse-content-box {
          padding: @size;
        }
      }
    }

    .@{ant-prefix}-collapse {
      &-content {
        border: none;
        background: transparent;

        > .@{ant-prefix}-collapse-content-box {
          padding: 0 @size @size;
        }
      }

      &-arrow {
        transition: all @motion-duration-mid;
      }
    }
  }

  &-panel-section {
    h2 > strong,
    h2 {
      transition: color @motion-duration-mid;
    }

    &:not(:last-child) {
      margin-bottom: @size-sm;
    }

    &-header + &-content {
      margin-top: @size-sm;
    }

    &-header {
      display: flex;
    }

    &-title {
      flex: 1;
      margin: 0;
      font-size: @font-size;
      font-weight: 400;
      color: @color-text-secondary;

      .@{iconfont-css-prefix} {
        margin-left: @size-xxs;
        font-size: @font-size-sm;
        color: @content-input-lable;
      }

      strong {
        font-weight: 600;
        color: @color-text;
      }
    }
  }
}
