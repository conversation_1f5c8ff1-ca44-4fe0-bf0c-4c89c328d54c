import type { CollapseProps } from 'antd';
import { Collapse as AntdCollapse } from 'antd';
import { useDesignerPrefixCls } from '../DesignerConfigProvider';
import classNames from 'classnames';
import './style.less';
import { BackArrowBold } from '@meitu/candy-icons';

const expandIcon: CollapseProps['expandIcon'] = ({ isActive }) => (
  <BackArrowBold
    style={isActive ? { transform: 'rotate(-90deg)' } : undefined}
  />
);

export function Collapse(props: CollapseProps) {
  const prefixCls = useDesignerPrefixCls('collapse');

  return (
    <AntdCollapse
      expandIconPosition="end"
      expandIcon={expandIcon}
      {...props}
      className={classNames(prefixCls, props.className)}
    />
  );
}
