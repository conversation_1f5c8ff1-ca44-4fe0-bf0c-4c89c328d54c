import { useCallback, useEffect } from 'react';
import {
  useBeforeUnload,
  unstable_useBlocker as useBlocker
} from 'react-router-dom';
import { App, Modal, ModalFuncProps } from 'antd';
import { atom, useRecoilState, useSetRecoilState } from 'recoil';

type PromptProps = {
  when: boolean;
  beforeUnload?: boolean;
} & ModalFuncProps;

const initPromptConfig: PromptProps = {
  when: false,
  centered: true,
  title: '提示',
  content: '是否离开当前页面？',
  okButtonProps: { danger: true },
  beforeUnload: true
};

export const promptConfig = atom<PromptProps>({
  key: 'prompt',
  default: initPromptConfig
});

export const Prompt = () => {
  const { modal } = App.useApp();

  const [
    { when, onCancel, onOk, beforeUnload, ...restProps },
    setPromptConfig
  ] = useRecoilState(promptConfig);

  let blocker = useBlocker(when);

  useEffect(() => {
    if (blocker.state === 'blocked' && !when) {
      blocker.reset();
    }
  }, [blocker, when]);

  useEffect(() => {
    Modal.destroyAll();

    if (blocker.state !== 'blocked' || !when) return;
    modal.confirm({
      onCancel: async () => {
        try {
          await onCancel?.();
          Modal.destroyAll();
          blocker.reset?.();
        } catch (error) {
          console.log(error);
        }
      },
      onOk: async () => {
        try {
          await onOk?.();
          Modal.destroyAll();
          blocker.proceed?.();
          setPromptConfig(initPromptConfig);
        } catch (error) {
          console.log(error);
          blocker.reset?.();
        }
      },
      ...restProps
    });
  }, [blocker, modal, onCancel, onOk, restProps, setPromptConfig, when]);

  useBeforeUnload(
    useCallback(
      (event) => {
        if (!!beforeUnload && when) {
          event.preventDefault();
          event.returnValue = restProps.title;
        }
      },
      [beforeUnload, restProps.title, when]
    ),
    { capture: true }
  );

  return null;
};

export const useSetPromptConfig = (config: Omit<PromptProps, 'when'>) => {
  const setConfig = useSetRecoilState(promptConfig);

  useEffect(() => {
    setConfig((prev) => ({ ...prev, ...config }));
  }, [config, setConfig]);

  return () => setConfig((prev) => ({ ...prev, when: true }));
};

export const useSetBlocker = () => {
  const setConfig = useSetRecoilState(promptConfig);
  return (when: boolean) => setConfig((prev) => ({ ...prev, when }));
};
