@import '~@/styles/variables.less';

.model-card {
  transition: all @motion-duration-mid;
  position: relative;

  :global(.model-card) {
    &-extra {
      position: absolute;
      top: 4px;
      right: 4px;
      background-color: @background-input-not-editable;
      padding: 4px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }

  :global(.@{ant-prefix}-space-vertical) {
    gap: 4px 0px;
  }

  &:global(.@{ant-prefix}-card) {
    background-color: @background-input;
    border: 1px solid @stroke-btn-secondary;

    &:hover {
      border-color: @background-slide-fill;
      box-shadow: 0 0 0 2px rgba(5, 155, 255, 0.06);
      img {
        transform: scale(1.1);
      }
    }

    :global(.@{ant-prefix}-card) {
      &-body {
        padding: 4px;
      }
    }
  }

  :global(.@{ant-prefix}-tag) {
    height: 18px;
    line-height: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 6px;
    width: fit-content;

    background-color: #ffefdc;
    color: #ff9a27;
    border-radius: 4px;
    border: none;
  }

  :global(.@{ant-prefix}-image) {
    border-radius: 4px;
    overflow: hidden;

    img {
      border-radius: 4px;
      transition: 0.6s;
      object-fit: cover;
    }

    img[src=''],
    img:not([src]) {
      opacity: 0.3;
    }
  }

  .container {
    flex-wrap: nowrap;
    cursor: pointer;

    .image-container {
      height: 58px;
    }

    .pr-29 {
      padding-right: 29px !important;
    }

    .pr-6 {
      padding-right: 6px !important;
    }

    .m-0 {
      margin: 0;
    }

    .m-auto {
      margin: auto;
    }

    .f-12 {
      font-size: 12px;
    }
  }

  .children {
    margin: 12px 0;
    width: 100%;
    padding: 0 8px;
  }

  .responsive {
    display: grid;
    grid-template-columns: minmax(auto, max-content) auto;
    gap: 6px;
    align-items: center;

    .corner-label {
      padding: 1px 6px;
      border-radius: 4px;
      background-size: cover;
      width: 36px;
      height: 18px;
    }
  }
}
