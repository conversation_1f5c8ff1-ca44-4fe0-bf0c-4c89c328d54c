import { Card, Col, Image, Row, Space, Tag, Typography } from 'antd';
import styles from './index.module.less';
import classNames from 'classnames';
import { Loading } from '@/components';

export interface BaseModelCardProps {
  extra?: React.ReactNode;
  onClick?(): void;
  onExtraClick?(): void;
  children?: React.ReactNode;
  src?: string;
  desc?: string;
  tag?: string;
  title?: string;
  className?: string;
  tips?: string;
  // 角标背景图
  cornerLabelUrl?: string;
  showCornerLabelUrl?: boolean;

  /**
   *  自定义预览图
   */
  renderPreview?: (src?: string) => React.ReactElement;

  /**
   * 自定义描述
   */
  renderDesc?: (desc?: string) => React.ReactElement;
}

export const BaseModelCard = ({
  extra,
  children,
  onClick,
  src,
  desc,
  tag,
  title,
  onExtraClick,
  className,
  cornerLabelUrl,
  renderPreview,
  renderDesc,
  showCornerLabelUrl = true
}: BaseModelCardProps) => {
  const hasChild = !!children;

  return (
    <Card className={classNames(styles.modelCard, className)}>
      <Row>
        <Col span={24}>
          <Row
            onClick={() => {
              onClick?.();
            }}
            gutter={[12, 0]}
            className={styles.container}
          >
            {renderPreview ? (
              <Col flex="70px" style={{ height: '58px' }}>
                {renderPreview(src)}
              </Col>
            ) : (
              <Col flex="58px" className={styles.imageContainer}>
                <Image
                  key={src ?? ''}
                  src={src ?? ''}
                  placeholder={<Loading />}
                  className="image"
                  width={58}
                  height={58}
                  preview={false}
                />
              </Col>
            )}

            <Col
              flex={1}
              className={classNames({
                [styles.pr29]: !!extra,
                [styles.pr6]: !extra,
                [styles.m0]: hasChild,
                [styles.mAuto]: !hasChild
              })}
            >
              <Space direction="vertical" wrap={false} size={[0, 4]}>
                <div className={styles.responsive}>
                  <Typography.Text
                    key={title}
                    ellipsis={{ tooltip: true }}
                    strong={hasChild}
                  >
                    {title ?? ''}
                  </Typography.Text>

                  {showCornerLabelUrl && cornerLabelUrl && (
                    <span
                      className={styles.cornerLabel}
                      style={{ backgroundImage: `url(${cornerLabelUrl})` }}
                    />
                  )}
                </div>

                {hasChild && <Tag>{tag ?? ''}</Tag>}
                {!hasChild &&
                  (renderDesc ? (
                    renderDesc(desc)
                  ) : (
                    <Typography.Text type="secondary" className={styles.f12}>
                      {desc ?? ''}
                    </Typography.Text>
                  ))}
              </Space>
            </Col>
          </Row>
        </Col>
        {children && (
          <Col span={24}>
            <div className={styles.children}>{children}</div>
          </Col>
        )}
      </Row>

      {extra && (
        <span className="model-card-extra" onClick={onExtraClick}>
          {extra}
        </span>
      )}
    </Card>
  );
};
