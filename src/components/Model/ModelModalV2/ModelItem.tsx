import { Card, Image, Spin, Typography, Tooltip } from 'antd';
import styles from './modelItem.module.less';
import { EditorConfigModelListResponse } from '@/api/types/editorConfig';
import { useState } from 'react';
import { Authorized, Loading } from '@/components';
import { toAtlasImageMogr2URL } from '@meitu/util';
const { Meta } = Card;

export interface ModelItemProps {
  item: EditorConfigModelListResponse;
  onClick?(): void;
  onIconClick?(): Promise<void>;
  isShowIcon?: boolean;
  cornerLabelUrl?: string;
  disabled?: boolean;
  tooltip?: string;
}

export const ModelItem = ({
  item,
  isShowIcon,
  onClick,
  onIconClick: onIconClickFromProps,
  cornerLabelUrl,
  disabled = false,
  tooltip = ''
}: ModelItemProps) => {
  const [loading, setLoading] = useState(false);

  const onIconClick = async () => {
    if (loading) return;

    setLoading(true);

    try {
      await onIconClickFromProps?.();
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };
  const image = Array.isArray(item.images) ? item.images[0] : item.images;

  return (
    <Tooltip arrow autoAdjustOverflow title={disabled ? tooltip : ''}>
      <Card
        onClick={onClick}
        className={
          disabled
            ? [styles.modelItemDisabled, styles.modelItem].join(' ')
            : styles.modelItem
        }
        cover={
          <Image
            placeholder={<Loading />}
            preview={false}
            alt={item.name}
            src={toAtlasImageMogr2URL(image, {
              thumbnail: { type: 'size', width: 204, height: 288 }
            })}
          />
        }
      >
        <Meta
          title={
            <Typography.Text
              ellipsis={{
                tooltip: {
                  title: item.name,
                  destroyTooltipOnHide: true
                }
              }}
              className={styles.f12}
            >
              {item.name}
            </Typography.Text>
          }
          description={
            <Typography.Text
              type="secondary"
              ellipsis={{
                tooltip: {
                  title: item.desc,
                  destroyTooltipOnHide: true
                }
              }}
              className={styles.f12}
            >
              {item.desc ?? ''}
            </Typography.Text>
          }
        />
        {isShowIcon && (
          <Authorized.EventHandlerProxy
            events={[
              {
                eventKey: 'onClick',
                stopPropagation: true,
                continueAfterLogin: true
              }
            ]}
          >
            <span className={styles.icon} onClick={onIconClick}>
              {loading ? (
                <Spin spinning size="small" />
              ) : (
                <Star active={item.isCollect} />
              )}
            </span>
          </Authorized.EventHandlerProxy>
        )}
        {cornerLabelUrl && (
          <span
            className={styles.cornerLabel}
            style={{ backgroundImage: `url(${cornerLabelUrl})` }}
          />
        )}
      </Card>
    </Tooltip>
  );
};

const Star = ({ active }: { active?: boolean }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="20" height="20" rx="4" fill="#FFF1D6" />
    <path
      d="M9.53505 3.67539C9.70176 3.25395 10.2982 3.25395 10.4649 3.67539L11.8328 7.13329C11.9026 7.30988 12.0665 7.43165 12.2557 7.4476L15.8494 7.75054C16.2857 7.78731 16.4673 8.32729 16.142 8.62029L13.3679 11.119C13.2333 11.2401 13.1748 11.4245 13.2149 11.6011L14.0559 15.3084C14.1549 15.7448 13.6747 16.0815 13.2981 15.8397L10.2702 13.8951C10.1056 13.7894 9.89442 13.7894 9.72981 13.8951L6.7019 15.8397C6.32534 16.0815 5.84509 15.7448 5.9441 15.3084L6.78513 11.6011C6.82519 11.4245 6.76667 11.2401 6.63215 11.119L3.85797 8.62029C3.53266 8.32729 3.71434 7.78731 4.15059 7.75053L7.74428 7.4476C7.93351 7.43165 8.09737 7.30988 8.16723 7.13329L9.53505 3.67539Z"
      fill={active ? '#FFB649' : '#fff'}
    />
  </svg>
);
