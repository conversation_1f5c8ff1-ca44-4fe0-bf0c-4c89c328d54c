@import '~@/styles/variables.less';

.model-item-disabled:global(.@{ant-prefix}-card) {
  opacity: 0.5;
  background-color: black;
}

.model-item:global(.@{ant-prefix}-card) {
  padding: 4px;
  padding-bottom: 0px;
  background-color: @background-input;
  border: 1px solid @stroke-btn-secondary;
  height: 100%;
  user-select: none;
  cursor: pointer;
  border-radius: 6px;
  max-width: 160px;

  img {
    background-color: @background-system-space-holder;
  }

  :global(.@{ant-prefix}-card-cover) {
    overflow: hidden;
    border-radius: 4px;

    img {
      border-radius: 4px;
      height: 144px;
      object-fit: cover;
      transition: 0.6s;

      &[alt] {
        display: block;
      }
    }
  }

  &:global(.@{ant-prefix}-card):hover {
    img {
      transform: scale(1.1);
    }
  }

  :global(.@{ant-prefix}-card-body) {
    & {
      padding: 8px 0 8px 0;
    }
  }

  :global(.@{ant-prefix}-card-meta) {
    & {
      padding: 0;
      padding-right: 32px;
      padding-left: 4px;
      margin: 0;
    }

    &-title {
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      color: @content-system-primary;
      margin-bottom: 0 !important;
    }

    &-description {
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: @content-system-tertiary;
    }
  }

  .corner-label {
    position: absolute;
    top: 3px;
    right: 3px;
    width: 35px;
    height: 18px;
    padding: 1px 6px;
    border-radius: 0px 3px;
    background-size: cover;
    z-index: 2;
  }

  .f-12 {
    font-size: 12px;
  }

  .icon {
    position: absolute;
    right: 6px;
    bottom: 12px;
  }
}
