import { CrossBold, SearchBold } from '@meitu/candy-icons';
import { Col, Empty, Input, Row, Typography, Modal } from 'antd';
import {
  type ReactElement,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
  createContext,
  useContext,
  startTransition
} from 'react';
import { FixedSizeGrid } from '../../Virtual/FixedSizeGrid';
import { ModelItem } from './ModelItem';
import empty from '@/assets/images/empty.jpg';
import {
  EditorConfigModelListResponse,
  EditorConfigModelResponse
} from '@/api/types/editorConfig';
import { cancelMarkModel, markModel } from '@/api';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import styles from './index.module.less';
import {
  COLLECTION_CATEGORY_ID,
  MY_MODEL_CATEGORY_ID,
  SAME_CATEGORY_ID
} from '@/constants/model';
import { Tabs } from '@/components';
import { produce } from 'immer';

const Ctx = createContext<{
  searchText: string;
}>({
  searchText: ''
});
const useCtx = () => useContext(Ctx);

type SearchTrigger = 'enter' | 'blur' | 'input';

export interface ModelModalPropsV2 {
  title: string;
  list: EditorConfigModelResponse[];

  onClose?: () => void;
  onModelClick?: (item: EditorConfigModelListResponse) => void;
  onCollectMutation?: (
    item: EditorConfigModelListResponse,
    categoryId: number
  ) => Promise<any>;

  /** 自定义Tab内容 */
  tabContainerRender?: (
    tabContainer: ReactElement,
    model: EditorConfigModelResponse
  ) => ReactElement;
  /** 自定义搜索事件 */
  onSearch?: (keyword: string, categoryId: number) => void;
  /** 自定义切换类型事件 */
  onCategoryChange?: (categoryId: number) => void;
  /** 搜索触发条件 */
  searchTrigger?: SearchTrigger[];
  /** 是否禁用 */
  checkDisabled?: (model: any) => boolean;
  /** 禁用后提示 */
  tooltip?: string;
}

// TODO 优化成分页加载
export const ModelModalV2 = forwardRef<
  { setVisible(visible: boolean): void },
  ModelModalPropsV2
>(
  (
    {
      title,
      list,
      onClose,
      onSearch: onSearchFromProps,
      searchTrigger,
      onModelClick,
      onCategoryChange,
      onCollectMutation,
      tabContainerRender,
      checkDisabled,
      tooltip
    },
    ref
  ) => {
    const [searchValue, setSearchValue] = useState('');
    // searchTrigger 触发后将 searchValue 设为 searchText
    const [searchText, setSearchText] = useState('');
    const [activeTab, setActiveTab] = useState('');
    const [isOpen, setIsOpen] = useState(false);
    const onSearch = useCallback(
      (value?: string) => {
        setSearchText(value ?? searchValue);
        onSearchFromProps?.(value ?? searchValue, Number(activeTab));
      },
      [activeTab, searchValue, onSearchFromProps]
    );

    const tabs = useMemo(() => {
      if (!list?.length || !isOpen) return [];

      return list.map((model) => {
        const filteredModel = onSearchFromProps
          ? model
          : produce(model, (draft) => {
              draft.list = filter(searchText, draft.list);
            });

        return {
          label: model.categoryName,
          key: String(model.categoryId),
          children: (
            <TabContainer
              model={filteredModel}
              key={model.categoryId}
              gridRender={
                tabContainerRender
                  ? (grid: ReactElement) => tabContainerRender(grid, model)
                  : undefined
              }
              onCollectMutation={onCollectMutation}
              onModelClick={onModelClick}
              checkDisabled={checkDisabled}
              tooltip={tooltip}
            />
          )
        };
      });
    }, [
      list,
      onCollectMutation,
      isOpen,
      searchText,
      onSearchFromProps,
      onModelClick,
      tabContainerRender,
      checkDisabled,
      tooltip
    ]);
    useEffect(() => {
      if (title === '风格模型') {
        setActiveTab('');
      }
    }, [tabs.length]);

    // 初始化选中第一个tab
    useEffect(() => {
      if (activeTab || !list.length) return;
      setActiveTab(String(list[0].categoryId));
    }, [activeTab, list]);

    const onCancel = useCallback(() => {
      onClose?.();
      setIsOpen(false);
      setSearchValue('');
      onSearch('');
    }, [onClose, setIsOpen, onSearch]);

    useImperativeHandle(
      ref,
      () => ({
        setVisible: (visible: boolean) => {
          if (!visible) {
            onCancel();
            return;
          }
          setIsOpen(true);
        }
      }),
      [onCancel]
    );

    const onPressEnter = searchTrigger?.includes('enter')
      ? () => {
          onSearch();
        }
      : undefined;

    const onSearchBlur = searchTrigger?.includes('blur')
      ? () => {
          onSearch();
        }
      : undefined;

    return (
      <Modal
        open={!!isOpen}
        className={styles.modelModal}
        width={896}
        footer={null}
        onCancel={onCancel}
        closeIcon={<CrossBold />}
        centered
        destroyOnClose
      >
        <Row gutter={[0, 8]}>
          <Col span={24} className={styles.pr40}>
            <Row justify="space-between">
              <Typography.Text className={styles.title} strong>
                {title}
              </Typography.Text>

              <Input
                className={styles.search}
                value={searchValue}
                allowClear
                onChange={({ target: { value }, type }) => {
                  setSearchValue(value);
                  if (searchTrigger?.includes('input')) {
                    onSearch(value);
                  } else if (type === 'click') {
                    onSearch(value);
                  }
                }}
                onPressEnter={onPressEnter}
                onBlur={onSearchBlur}
                placeholder="搜索模型"
                prefix={<SearchBold />}
              />
            </Row>
          </Col>

          <Col span={24} className={styles.pr6}>
            <Ctx.Provider value={{ searchText }}>
              <Tabs
                className={styles.tabs}
                onChange={(active) => {
                  startTransition(() => {
                    setActiveTab(active);
                  });
                  onCategoryChange?.(+active);
                }}
                activeKey={activeTab}
                type="no-line"
                size="small"
                items={tabs}
              />
            </Ctx.Provider>
          </Col>
        </Row>
      </Modal>
    );
  }
);

export interface TabContainerProps {
  model: EditorConfigModelResponse;
  onModelClick?: (item: EditorConfigModelListResponse) => void;
  onCollectMutation?: (
    item: EditorConfigModelListResponse,
    categoryId: number
  ) => Promise<undefined>;
  gridRender?: (grid: ReactElement) => ReactElement;
  onSearch?: (keyword: string) => void;
  checkDisabled?: (model: any) => boolean;
  tooltip?: string;
}

const filter = (keyword: string, list?: EditorConfigModelResponse['list']) => {
  if (!keyword) return list ?? [];

  return (
    list?.filter((item) => {
      return item.name.toLowerCase().includes(keyword.toLowerCase());
    }) ?? []
  );
};

export const TabContainer = ({
  model,
  onModelClick,
  onCollectMutation,
  gridRender,
  checkDisabled,
  tooltip
}: TabContainerProps) => {
  const { searchText } = useCtx();
  return (
    <Row
      className={styles.modelContainer}
      justify={!model.list.length ? 'center' : 'start'}
    >
      {!!model.list.length ? (
        <FixedSizeGrid
          rowKey={(item) => String(item.id)}
          dataSource={model.list}
          columnWidth={176}
          rowHeight={220}
          gutter={16}
          gridRender={gridRender}
          renderItem={(item) => (
            <ModelItem
              disabled={checkDisabled?.(item)}
              tooltip={tooltip}
              cornerLabelUrl={item.tag?.url ?? ''}
              isShowIcon={
                model.categoryId !== MY_MODEL_CATEGORY_ID &&
                model.categoryId !== SAME_CATEGORY_ID
              }
              onClick={() => onModelClick?.(item)}
              item={item}
              onIconClick={async () => {
                const action = item.isCollect ? cancelMarkModel : markModel;

                try {
                  await action({ id: item.id });
                  await onCollectMutation?.(item, model.categoryId);
                } catch (error) {
                  defaultErrorHandler(error);
                }
              }}
            />
          )}
        />
      ) : (
        <Empty
          className={styles.empty}
          image={empty}
          description={
            <Typography.Text type="secondary">
              {model.categoryId === COLLECTION_CATEGORY_ID && !searchText
                ? '您还没有收藏过模型'
                : '暂无数据'}
            </Typography.Text>
          }
        />
      )}
    </Row>
  );
};

ModelModalV2.defaultProps = {
  searchTrigger: ['input']
};
