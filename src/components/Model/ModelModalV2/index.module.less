@import '~@/styles/variables.less';

.model-modal:global(.@{ant-prefix}-modal) {
  font-size: 12px;

  :global(.@{ant-prefix}-modal-content) {
    padding: 12px 16px;
    padding-right: 0;
    background-color: @background-system-frame-floatpanel;

    :global(.anticon-search-bold) {
      color: @color-text;
    }

    :global(.ant-modal-close) {
      top: 12px;
      right: 12px;
    }
  }

  .pr-40 {
    padding-right: 40px;
  }

  .pr-6 {
    padding-right: 6px;
  }

  .title {
    color: @content-system-primary;
    font-size: @text-16;
    font-weight: 600;
    line-height: 22px;
  }

  .search:global(.@{ant-prefix}-input-affix-wrapper) {
    border-radius: 8px;
    width: 218px;
    height: 32px;
  }
}

.empty {
  &:global(.@{ant-prefix}-empty) {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding-bottom: 100px;
  }
}

.model-container {
  height: 580px;
  overflow: hidden;
}

.tabs {
  :global(.@{ant-prefix}-tabs-nav) {
    :global(.@{ant-prefix}-tabs-nav-list) {
      // 对于gap生效的浏览器 需要用上这个属性覆盖掉已经有的生效的gap
      gap: 0;

      // 接下来不论gap是否在浏览器中生效 gap都为0
      // 使用margin-left代替gap
      & > :global(.@{ant-prefix}-tabs-tab) {
        &:not(:first-child) {
          margin-left: 40px;
        }
      }
    }
  }
}
