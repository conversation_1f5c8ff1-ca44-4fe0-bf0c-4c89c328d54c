import { AppOrigin, appOrigin } from '@/constants';
import { PropsWithChildren } from 'react';

export interface PermissionEnvWrapperProps {
  // 在哪些源站点下展示
  includesOrigin?: AppOrigin[];
  // 在哪些源站点下不展示
  excludesOrigin?: AppOrigin[];
  // 在哪些环境下展示
  includesEnv?: NodeJS.ProcessEnv['REACT_APP_ENV'][];
  // 在哪些环境下不展示
  excludeEnv?: NodeJS.ProcessEnv['REACT_APP_ENV'][];
  // 是否展示的后置条件
  extraPermission?: boolean;
}

export const PermissionEnvWrapper = ({
  children,
  ...restProps
}: PropsWithChildren<PermissionEnvWrapperProps>) => {
  if (isAuthenticationPassed(restProps)) return <>{children}</>;

  return null;
};

const isAuthenticationPassed = ({
  includesOrigin = [],
  excludesOrigin = [],
  includesEnv = [],
  excludeEnv = [],
  extraPermission = true
}: PermissionEnvWrapperProps) => {
  const originIncluded =
    includesOrigin.length === 0 || includesOrigin.includes(appOrigin);
  const originExcluded = excludesOrigin.includes(appOrigin);
  const envIncluded =
    includesEnv.length === 0 || includesEnv.includes(process.env.REACT_APP_ENV);
  const envExcluded = excludeEnv.includes(process.env.REACT_APP_ENV);

  if (
    originIncluded &&
    !originExcluded &&
    envIncluded &&
    !envExcluded &&
    extraPermission
  ) {
    return true;
  }

  return false;
};
