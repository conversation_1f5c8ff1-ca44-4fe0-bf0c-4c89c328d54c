import { PermissionContext } from './context';

import { ReactNode, useContext, useMemo } from 'react';
import { useEditorMode } from '@/hooks';
import { getPermissionListByEditorMode } from '@/constants';

/**
 * 是否有权限
 */
export function useHasPermission(keys: string[]) {
  const { permissions } = useContext(PermissionContext);

  return permissions.some((permission) =>
    keys.some((key) => key === permission)
  );
}

/**
 * 显示有权限的内容
 * @param {string} key 对应组件/页面/操作的 key
 * @param {string} content 显示的具体内容
 */
export function useAuthPermission(key: string, content: ReactNode) {
  const hasPermission = useHasPermission([key]);

  return hasPermission ? content : null;
}

/**
 * TODO: 这个 hooks 是跟业务强绑定 后续应该单独抽出去
 * 获取对应有权限列表
 */
export function useAuthPermissionList() {
  const [editorMode] = useEditorMode();
  const permissionList = useMemo(() => {
    return getPermissionListByEditorMode(editorMode);
  }, [editorMode]);

  return permissionList;
}
