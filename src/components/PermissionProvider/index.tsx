import type { ReactNode } from 'react';

import { PermissionContext } from './context';
import { useAuthPermissionList, useHasPermission } from './hooks';

export * from './context';
export * from './hooks';

export type PermissionProviderProps = {
  children?: ReactNode;
};

export function PermissionProvider(props: PermissionProviderProps) {
  const permissionList = useAuthPermissionList();
  const permissionValue = { permissions: permissionList };

  return (
    <PermissionContext.Provider value={permissionValue}>
      {props.children}
    </PermissionContext.Provider>
  );
}

export const Permission = ({
  permissions,
  children
}: {
  permissions: string[];
  children: ReactNode;
}) => {
  const hasPermission = useHasPermission(permissions);

  return hasPermission ? <>{children}</> : null;
};
