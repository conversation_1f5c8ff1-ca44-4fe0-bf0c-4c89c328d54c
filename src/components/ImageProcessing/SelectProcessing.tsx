import { TreeSelect } from 'antd';
import { useMemo } from 'react';
import styles from './SelectProcessing.module.less';
import { EditorConfig } from '@/hooks/useGetEditorConfig/useGetStyleModel';

export type SelectProcessingValue = {
  module?: string;
  model?: string;
};

export interface SelectProcessingProps {
  value?: SelectProcessingValue;
  className?: string;
  placeholder?: string;
  onChange?: (value?: SelectProcessingValue) => void;
  list: EditorConfig['moduleList'];
}

/**
 * 选择预处理器
 */
export function SelectProcessing(props: SelectProcessingProps) {
  const {
    value = {},
    placeholder,
    className,
    onChange,
    list: moduleList
  } = props;

  const options = useMemo(() => {
    if (!moduleList) {
      return [];
    }

    return moduleList.map((module) => {
      return {
        value: module.categoryName,
        label: module.categoryName,
        selectable: false,
        children: module.list.map((processing) => {
          return {
            value: `${processing.module},${processing.model}`,
            label: processing.name
          };
        })
      };
    });
  }, [moduleList]);

  const finalValue = useMemo(() => {
    const values = Object.values(value).filter((item) => item);

    if (!values.length) {
      return undefined;
    }

    return `${value.module},${value.model}`;
  }, [value]);

  return (
    <TreeSelect
      value={finalValue}
      treeData={options}
      placeholder={placeholder}
      style={{ width: 256 }}
      showSearch
      treeDefaultExpandAll
      popupClassName={styles.popup}
      className={className}
      onChange={(value) => {
        let newValue = undefined;

        if (value) {
          const [module, model] = value?.split(',');
          newValue = { module, model };
        }

        onChange && onChange(newValue);
      }}
    />
  );
}

SelectProcessing.defaultProps = {
  placeholder: '请选择'
};
