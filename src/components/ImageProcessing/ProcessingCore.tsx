import { useEffect, useState } from 'react';
import { noop } from 'lodash';
import { Image, Button, Spin, Space, message } from 'antd';

import { createImageProcessingTask, fetchTaskByIds } from '@/api';

// import { ArrowCounterclockwiseBlack, PictureBoldFill } from '@/icons';
// import { Upload } from '../Upload';
// import { UploadStatus } from '../Upload/Upload';

import { SelectProcessing } from './SelectProcessing';
import styles from './ProcessingCore.module.less';
import { LoadingStatus } from '@/types/draft';
import { ImageProcessingParams } from './ImageProcessing';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { EditorConfigResponse } from '@/api/types';

export interface ProcessingCoreProps {
  value?: ImageProcessingParams;
  onChange?: (value?: ImageProcessingParams) => void;
  baseModel: EditorConfigResponse['baseModel'];
  moduleList: EditorConfigResponse['moduleList'];
}

type ImageInfo = {
  width: number;
  height: number;
};

const defaultImageInfo: ImageInfo = {
  width: 512,
  height: 512
};

type ProcessParams = Omit<ImageProcessingParams, 'image'>;

/**
 * 图片处理器核心组件
 * TODO：后续可能单独处理
 */
export function ProcessingCore(props: ProcessingCoreProps) {
  const { value, onChange = noop, baseModel, moduleList } = props;
  const [previewUrl, setPreviewUrl] = useState(value?.image); // 提供预览作用，最终确定了才修改 value 的值
  const [imageInfo, setImageInfo] = useState<ImageInfo>(defaultImageInfo);
  const originImage = value?.image;

  const [processParams, setProcessParams] = useState<ProcessParams>({
    model: value?.model,
    module: value?.module
  });

  const [loading, setLoading] = useState(false);
  const [taskIds, setTaskIds] = useState('');

  const onOk = () => {
    onChange({ image: originImage, ...processParams });
  };

  const imageOnload = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const { naturalWidth: width, naturalHeight: height } =
      e.target as HTMLImageElement;

    setImageInfo({ width, height });
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const onPreview = async () => {
    if (!originImage || !baseModel) {
      return;
    }

    setLoading(true);

    try {
      const baseModelId = baseModel?.[0].list?.[0]?.id;

      // 创建图片处理任务
      const response = await createImageProcessingTask({
        ...imageInfo,
        image: originImage,
        baseModelId,
        module: processParams.module ?? '',
        model: processParams.model ?? ''
      });

      const ids = response?.map((item) => item.id).join(',');
      setTaskIds(ids);
    } catch (error) {
      defaultErrorHandler(error, (err) => err?.message ?? '图片处理失败');

      setLoading(false);
    }
  };

  // 轮询图片生成结果
  useEffect(() => {
    const interval = setInterval(async () => {
      if (!taskIds?.length) {
        return;
      }

      const stopInterval = () => {
        setLoading(false);
        clearInterval(interval);
        setTaskIds('');
      };

      try {
        setLoading(true);
        const res = await fetchTaskByIds({ ids: taskIds });
        const { batch = [], loadingStatus } = res?.[0] || {};
        const { src } = batch[0];

        if (loadingStatus === LoadingStatus.SUCCESS) {
          src && setPreviewUrl(src);
        }

        if (loadingStatus !== LoadingStatus.LOADING) {
          stopInterval();
        }

        if (loadingStatus === LoadingStatus.FAILED) {
          message.error('图片处理失败');
        }
      } catch (error) {
        message.error('图片处理失败');
        stopInterval();
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [taskIds]);

  const onProcessParamsChange = (value?: ProcessParams) => {
    value && setProcessParams(value);
    setPreviewUrl(originImage);
  };

  return (
    <Spin spinning={loading}>
      <div className={styles.core}>
        <SelectProcessing
          list={moduleList}
          value={processParams}
          onChange={onProcessParamsChange}
        />

        <Image
          className={styles.preview}
          src={previewUrl}
          placeholder={false}
          width="100%"
          height={400}
          preview={false}
          onLoad={imageOnload}
        />

        <div className={styles.footer}>
          {/* <div className={styles.control}>
            <Button icon={<ArrowCounterclockwiseBlack />} onClick={onReset}>
              恢复
            </Button>
            <Upload onChange={onUpload} showUploadList={false} maxCount={1}>
              <Button icon={<PictureBoldFill />}>替换</Button>
            </Upload>
          </div> */}

          <Space>
            {/* diffusers目前没效果 临时先关闭 */}
            {/* <Button onClick={onPreview} style={{ width: 144 }}>
              预览
            </Button> */}
            <Button type="primary" onClick={onOk} style={{ width: 144 }}>
              确定
            </Button>
          </Space>
        </div>
      </div>
    </Spin>
  );
}
