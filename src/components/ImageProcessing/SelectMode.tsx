import { Checkbox } from 'antd';

export interface SelectModeValue {
  scribbleMode?: number;
  rgb2bgrMode?: number;
}

export interface SelectModeProps {
  value?: SelectModeValue;
  onChange?: (value: SelectModeValue) => void;
}

const options = [
  { value: 'scribbleMode', label: '反色模式' },
  { value: 'rgb2bgrMode', label: 'RGB 转 BPG' }
];

enum ModeEnum {
  /** 禁用 */
  UnChecked,

  /** 启用 */
  Checked
}

/**
 * 选择图片模式、反色、RGB 等
 */
export function SelectMode(props: SelectModeProps) {
  const { value = {}, onChange } = props;

  const finalValue = Object.keys(value);

  return (
    <Checkbox.Group
      value={finalValue}
      options={options}
      onChange={(value) => {
        const newValue = value?.reduce((prev, cur) => {
          return {
            ...prev,
            [String(cur)]: ModeEnum.Checked
          };
        }, {});
        onChange && onChange(newValue);
      }}
    />
  );
}
