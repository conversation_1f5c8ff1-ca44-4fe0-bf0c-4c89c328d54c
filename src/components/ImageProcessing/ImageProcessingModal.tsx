import type {
  ImageProcessingProps,
  ImageProcessingParams
} from './ImageProcessing';

import { Button, Modal, Typography } from 'antd';
import { ProcessingCore } from './ProcessingCore';
import { useState, useMemo } from 'react';
import { CrossBoldOutlined } from '@meitu/candy-icons';

interface ImageProcessingModalProps
  extends Omit<ImageProcessingProps, 'onChange'> {
  onChange: (params?: ImageProcessingParams) => void;
}

type ModeList = {
  name: string;
  module: string;
  model: string;
}[];

export function ImageProcessingModal(props: ImageProcessingModalProps) {
  const { value, baseModel, moduleList } = props;
  const [open, setOpen] = useState(false);

  const openModal = () => {
    setOpen(true);
  };

  const onCancel = () => {
    setOpen(false);
  };

  const onChange = (params?: ImageProcessingParams) => {
    props.onChange(params);
    setOpen(false);
  };

  const modelName = useMemo(() => {
    if (!value?.image || !moduleList) {
      return;
    }

    const modelList = moduleList?.reduce((acc, cur) => {
      return [...acc, ...cur.list];
    }, [] as ModeList);

    const currentModel = modelList?.find(
      (item) => item.model === value.model && item.module === value.module
    );

    return currentModel?.name;
  }, [value, moduleList]);

  return (
    <>
      <Button onClick={openModal}>
        <Typography.Text ellipsis>
          {modelName ? modelName : '选择模型'}
        </Typography.Text>
      </Button>
      <Modal
        title="选择模型"
        width={600}
        open={open}
        onCancel={onCancel}
        destroyOnClose
        footer={null}
        closeIcon={<CrossBoldOutlined />}
      >
        <ProcessingCore
          moduleList={moduleList}
          onChange={onChange}
          value={value}
          baseModel={baseModel}
        />
      </Modal>
    </>
  );
}
