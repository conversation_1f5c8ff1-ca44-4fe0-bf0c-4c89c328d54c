import { noop, omit } from 'lodash';

import { Upload } from '../Upload';
import { ImageProcessingModal } from './ImageProcessingModal';
import { SelectProcessing } from './SelectProcessing';
import styles from './ImageProcessing.module.less';
import { EditorConfigResponse } from '@/api/types';

export interface ImageProcessingParams {
  image?: string;
  module?: string;
  model?: string;
}

export interface ImageProcessingProps {
  value?: ImageProcessingParams;
  onChange?: (value: ImageProcessingParams) => void;
  moduleList: EditorConfigResponse['moduleList'];
  baseModel: EditorConfigResponse['baseModel'];

  /** 预处理编辑方式：选择器 | 弹窗 */
  processingType?: 'select' | 'modal';
}

/**
 * 图片预处理编辑
 */
export function ImageProcessing(props: ImageProcessingProps) {
  const { value, onChange = noop, processingType, ...restProps } = props;

  const onOriginalImageChange = (url: string, previewUrl: string = '') => {
    onChange({ ...value, image: previewUrl });
  };

  // 图片处理完成后的回调
  const onImageProcessingChange = (params?: ImageProcessingParams) => {
    onChange({ ...value, ...params });
  };

  return (
    <>
      <div className={styles.imageProcessing}>
        <Upload.Dragger value={value?.image} onChange={onOriginalImageChange} />
        {value?.image &&
          (processingType === 'modal' ? (
            <ImageProcessingModal
              value={value}
              {...restProps}
              onChange={onImageProcessingChange}
            />
          ) : (
            <SelectProcessing
              list={restProps.moduleList}
              value={omit(value, 'image')}
              placeholder="选择模型"
              className={styles.selectProcessing}
              onChange={onImageProcessingChange}
            />
          ))}
      </div>
    </>
  );
}

ImageProcessing.defaultProps = {
  processingType: 'modal'
};
