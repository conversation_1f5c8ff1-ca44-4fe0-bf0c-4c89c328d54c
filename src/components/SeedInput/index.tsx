import type { CheckboxProps, InputNumberProps } from 'antd';

import { Checkbox, InputNumber, Tooltip } from 'antd';
import { QuestionMarkCircleBold } from '@meitu/candy-icons';
import { Collapse } from '@/components';
import { useState, useRef } from 'react';
import { RANDOM_SEED_VALUE } from '@/constants';

import styles from './styles.module.less';

interface SeedInputProps {
  value?: number;
  onChange?: (value?: number) => void;
}

export function SeedInput(props: SeedInputProps) {
  const { value, onChange } = props;
  const isRandom = value === RANDOM_SEED_VALUE;

  const [inputValue, setInputValue] = useState(() => (isRandom ? null : value));
  const inputRef = useRef<HTMLInputElement>(null);

  const onCheckChange: CheckboxProps['onChange'] = (event) => {
    const isRandom = event.target.checked;

    if (!isRandom) {
      setTimeout(() => {
        inputRef.current?.focus();
      });
    }
    onChange?.(isRandom ? RANDOM_SEED_VALUE : inputValue ?? undefined);
  };

  const onInputChange: InputNumberProps<number>['onChange'] = (value) => {
    setInputValue(value);
    onChange?.(value == null ? undefined : value);
  };

  return (
    <Collapse.Panel.Section
      title={
        <>
          种子
          <Tooltip title="稳定图片风格的种子，当随机出一组觉得不错的风格图片时，可固定seed种子进行描述或其他参数的微调来达到最佳效果。">
            <QuestionMarkCircleBold />
          </Tooltip>
        </>
      }
      extra={
        <Checkbox checked={isRandom} onChange={onCheckChange}>
          随机
        </Checkbox>
      }
    >
      <InputNumber
        ref={inputRef}
        value={isRandom ? null : inputValue}
        disabled={isRandom}
        placeholder={isRandom ? '随机中...' : '请输入'}
        controls={false}
        min={0}
        max={99999999999}
        precision={0}
        className={styles.seedInputNumber}
        onChange={onInputChange}
      />
    </Collapse.Panel.Section>
  );
}
