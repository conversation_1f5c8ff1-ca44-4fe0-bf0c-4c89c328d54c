import classNames from 'classnames';
import { Button } from '..';
import styles from './index.module.less';
import { ButtonProps, ConfigProvider } from 'antd';

export type VipButtonType = ButtonProps & {
  isVip?: boolean;
};

export function VipButton({
  className,
  isVip = true,
  ...restProps
}: VipButtonType) {
  return (
    // 去除两个中文字符中间的空格
    <ConfigProvider autoInsertSpaceInButton={false}>
      <Button
        {...restProps}
        className={classNames(
          {
            [styles.vipButton]: isVip
          },
          className
        )}
      />
    </ConfigProvider>
  );
}
