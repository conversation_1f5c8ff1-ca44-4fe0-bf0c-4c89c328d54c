import type { ReactNode, MouseEvent } from 'react';
import type { ActionsProps } from '@/components';

import { Actions, Button } from '@/components';
import { AiGenerateImageBold, TextToImageBold } from '@meitu/candy-icons';
import { AppModule, generateRouteTo } from '@/services';
import { useState } from 'react';

import styles from './styles.module.less';
import classNames from 'classnames';
import { Link } from 'react-router-dom';

interface ApplyModelButtonProps {
  /** 触发器节点 */
  trigger?: ReactNode;

  /** 弹窗展开事件 */
  onOpenChange?: (isOpen: boolean) => void;

  /** 触发器点击事件 */
  onTriggerClick?: () => void;

  /** 点击应用事件 */
  onClick?: (
    event: MouseEvent,
    module: AppModule.TextToImage | AppModule.ImageToImage
  ) => void;

  /** 样式 */
  className?: string;

  /** 弹窗样式 */
  overlayClassName?: string;

  /** 触发器属性 */
  triggerProps?: ActionsProps['triggerProps'];

  /** 菜单渲染父节点 */
  popupContainer?: (triggerNode: HTMLElement) => HTMLElement;

  /**
   * 打开新的标签页
   */
  routeToNewTab?: boolean;
  /**
   * 仅在routeToNewTab为true时生效
   * 在打开新的标签页时 在url中添加额外的query参数
   */
  getExtraQueryParams?: (appModule: AppModule) => Record<string, string>;
}

export function ApplyModelButton(props: ApplyModelButtonProps) {
  const {
    trigger,
    className,
    overlayClassName,
    triggerProps,
    onOpenChange,
    onTriggerClick,
    onClick,
    popupContainer,
    routeToNewTab,
    getExtraQueryParams
  } = props;
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Actions
      overlayClassName={classNames(styles.overlay, overlayClassName)}
      open={isOpen}
      onOpenChange={(isOpen) => {
        setIsOpen(isOpen);
        onOpenChange?.(isOpen);
      }}
      getPopupContainer={(triggerNode) =>
        popupContainer?.(triggerNode) ?? triggerNode
      }
      triggerProps={{
        size: 'small',
        type: 'primary',
        ...triggerProps,
        onClick: (event) => {
          event.stopPropagation();
          event.preventDefault();
          onTriggerClick?.();
        },
        className: classNames(styles.button, className),
        children: trigger ?? '应用模型'
      }}
    >
      {!!routeToNewTab
        ? [
            <Link
              key="link-text-to-image"
              className={styles.routerLink}
              to={generateRouteTo(
                AppModule.TextToImage,
                getExtraQueryParams?.(AppModule.TextToImage)
              )}
              target="_blank"
              onClick={(event) => {
                event.stopPropagation();
                onClick?.(event, AppModule.TextToImage);
              }}
            >
              <TextToImageBold className="link-icon" />
              文生图
            </Link>,
            <Link
              key="link-image-to-image"
              className={styles.routerLink}
              to={generateRouteTo(
                AppModule.ImageToImage,
                getExtraQueryParams?.(AppModule.ImageToImage)
              )}
              target="_blank"
              onClick={(event) => {
                event.stopPropagation();
                onClick?.(event, AppModule.ImageToImage);
              }}
            >
              <AiGenerateImageBold className="link-icon" />
              图生图
            </Link>
          ]
        : [
            <Button
              key="button-text-to-image"
              size="small"
              type="text"
              onClick={(event) => {
                event.stopPropagation();
                onClick?.(event, AppModule.TextToImage);
              }}
              icon={<TextToImageBold />}
            >
              文生图
            </Button>,
            <Button
              key="button-image-to-image"
              size="small"
              type="text"
              onClick={(event) => {
                event.stopPropagation();
                onClick?.(event, AppModule.ImageToImage);
              }}
              icon={<AiGenerateImageBold />}
            >
              图生图
            </Button>
          ]}
    </Actions>
  );
}
