import type { ThemeConfig } from 'antd';
import { useMemo } from 'react';
import type { ReactNode } from 'react';

import { RouterProvider, createBrowserRouter } from 'react-router-dom';
import { IconProvider } from '@meitu/candy-icons';
import { ConfigProvider, App, theme } from 'antd';

import { AliveScope } from 'react-activation';
import { wrapCreateBrowserRouter } from '@sentry/react';
import { toThemeTokens } from '@/styles/seedToken.mjs';
import { routeBasename } from '@/constants';
import { useDarkModeState } from '@/hooks';
import locale from 'antd/locale/zh_CN';
import { routes } from '@/routes';
import { defaultBackgroundSideFillHover } from '@meitu/candy-theme';
import { defaultStrokeInputSelected } from '@meitu/candy-theme/dist/variables.mjs';

export interface SiteProviderProps {
  children?: ReactNode;
}

export const router = wrapCreateBrowserRouter(createBrowserRouter)(routes, {
  basename: routeBasename
});

export function SiteProvider(props: SiteProviderProps) {
  const [isDarkMode] = useDarkModeState();

  const themeConfig = useMemo<ThemeConfig>(
    () => ({
      algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
      token: toThemeTokens(isDarkMode),
      components: {
        Slider: {
          colorPrimary: defaultBackgroundSideFillHover
        },
        InputNumber: {
          colorPrimary: defaultStrokeInputSelected
        }
      }
    }),
    [isDarkMode]
  );

  return (
    <ConfigProvider locale={locale} theme={themeConfig}>
      <IconProvider>
        <AliveScope>
          <App>
            <RouterProvider router={router} />
          </App>
        </AliveScope>
      </IconProvider>
    </ConfigProvider>
  );
}
