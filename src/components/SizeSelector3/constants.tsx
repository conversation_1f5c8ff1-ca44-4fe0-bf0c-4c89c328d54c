import { PosterRatio } from '@/api/types/poster';
import {
  <PERSON><PERSON>11,
  <PERSON>io12,
  Ratio169,
  Ratio21,
  Rat<PERSON>23,
  Ratio32,
  Ratio34,
  Ratio43,
  Ratio916,
  Ratio<PERSON>ree,
  Picture
} from '@meitu/candy-icons';
import styles from './index.module.less';

// 宽高 limit
export const maxWidth = 4096;
export const maxHeight = 4096;
export const minWidth = 64;
export const minHeight = 64;

/**
 * 尺寸类型
 */
export enum DimensionType {
  /**
   * 自由尺寸
   *
   * 用户可以自定义宽高
   */
  FREE = 'custom',

  /**
   * 固定比例
   *
   * 当用户输入某一个尺寸（宽或高）时，会按照比例同时修改另一个尺寸
   */
  FIXED_ASPECT = 'fixed_aspect'
}
export type Dimension = {
  id: number;
  display?: string;
  label: React.ReactNode;
  value: PosterRatio;
  size?: [number, number] | PosterRatio.ORIGINAL;
} & (
  | {
      type: DimensionType.FIXED_ASPECT;
      size: [number, number] | PosterRatio.ORIGINAL;
    }
  | {
      type: DimensionType.FREE;
    }
);

let dimensionId = 0;
const getId = () => dimensionId++;

const fixedAspectDimensions: Array<Dimension> = [
  // {
  //   id: getId(),
  //   type: DimensionType.FIXED_ASPECT,
  //   size: PosterRatio.ORIGINAL,
  //   display: '原比例',
  //   value: <Picture />
  // },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [1024, 1024],
    label: (
      <div className={styles.selection}>
        <Ratio11 />
        {PosterRatio.RATIO_1_1}
      </div>
    ),
    value: PosterRatio.RATIO_1_1
  },

  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [768, 1152],
    label: (
      <div className={styles.selection}>
        <Ratio23 />
        {PosterRatio.RATIO_2_3}
      </div>
    ),
    value: PosterRatio.RATIO_2_3
  },
  // {
  //   id: getId(),
  //   type: DimensionType.FIXED_ASPECT,
  //   size: [512, 1024],
  //   label: (
  //     <div className={styles.selection}>
  //       <Ratio12 />
  //       {PosterRatio.RATIO_1_2}
  //     </div>
  //   ),
  //   value: PosterRatio.RATIO_1_2
  // },
  // {
  //   id: getId(),
  //   type: DimensionType.FIXED_ASPECT,
  //   size: [1024, 512],
  //   label: (
  //     <div className={styles.selection}>
  //       <Ratio21 />
  //       {PosterRatio.RATIO_2_1}
  //     </div>
  //   ),
  //   value: PosterRatio.RATIO_2_1
  // },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [768, 1024],
    label: (
      <div className={styles.selection}>
        <Ratio34 />
        {PosterRatio.RATIO_3_4}
      </div>
    ),
    value: PosterRatio.RATIO_3_4
  },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [720, 1280],
    label: (
      <div className={styles.selection}>
        <Ratio916 />
        {PosterRatio.RATIO_9_16}
      </div>
    ),
    value: PosterRatio.RATIO_9_16
  },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [1152, 768],
    label: (
      <div className={styles.selection}>
        <Ratio32 />
        {PosterRatio.RATIO_3_2}
      </div>
    ),
    value: PosterRatio.RATIO_3_2
  },
  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [1024, 768],
    label: (
      <div className={styles.selection}>
        <Ratio43 />
        {PosterRatio.RATIO_4_3}
      </div>
    ),
    value: PosterRatio.RATIO_4_3
  },

  {
    id: getId(),
    type: DimensionType.FIXED_ASPECT,
    size: [1280, 720],
    label: (
      <div className={styles.selection}>
        <Ratio169 />
        {PosterRatio.RATIO_16_9}
      </div>
    ),
    value: PosterRatio.RATIO_16_9
  }
];

export const freeDimension: Dimension = {
  id: getId(),
  type: DimensionType.FREE,
  label: (
    <div className={styles.selection}>
      <RatioFree />
      自由比例
    </div>
  ),
  value: PosterRatio.FREE
};
export const SelfAdaptionDimension: Dimension = {
  id: getId(),
  type: DimensionType.FIXED_ASPECT,
  size: [1024, 1024],
  label: (
    <div className={styles.selection1}>
      <Picture />
      自适应
    </div>
  ),
  value: PosterRatio.SELF_ADAPTION
};

export const dimensionList: Array<Dimension> = [
  ...fixedAspectDimensions,
  freeDimension
];

export function dimensionToRequestImageRatio(dimension: Dimension) {
  if (dimension.type === DimensionType.FREE) {
    return DimensionType.FREE;
  }

  if (dimension.type === DimensionType.FIXED_ASPECT) {
    if (dimension.size === PosterRatio.ORIGINAL) {
      return PosterRatio.ORIGINAL;
    }

    return dimension.size.join(':');
  }
}
