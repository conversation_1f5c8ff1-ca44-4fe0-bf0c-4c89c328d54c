import classNames from 'classnames';
import { InputNumberLimitMax } from '@/components';
import type { CanvasSize } from '@/types';
import {
  ChevronDownBlack,
  DelinkBold,
  LinkBold,
  Picture
} from '@meitu/candy-icons';
import { Flex, Form, Select, Image as AntdImage } from 'antd';
import styles from './index.module.less';
import { minHeight, minWidth } from './constants';
import { useGetSize } from '@/components/SizeSelector/AdaptationSelect';
import { Dimension, DimensionType, SelfAdaptionDimension } from './constants';
import { PosterRatio } from '@/api/types/poster';
import { useEffect, useState } from 'react';

import { observer } from 'mobx-react';
import { useImageSize } from '@/hooks';

export interface RatioProps3 {
  type?: string;
  imageUrl?: string;
  sizeRatio?: any[];
}

export const SizeSelector3 = observer((props: RatioProps3) => {
  let size = [1024, 1024];
  const [imageSize, setImageSize] = useState<CanvasSize>([0, 0]);
  const form = Form.useFormInstance();

  // console.log(props.sizeRatio, 'props.sizeRatio');

  // 初始化时处理sizeRatio
  const getInitialDimensionList = () => {
    if (props.sizeRatio && props.sizeRatio.length > 0) {
      const customDimensions = convertSizeRatioToDimension(props.sizeRatio);
      return [...customDimensions];
    }
    return [];
  };

  const [_dimensionList, setDimensionList] = useState<Array<Dimension>>(
    getInitialDimensionList()
  );

  const picRatioWatch = Form.useWatch('picRatio', {
    form,
    preserve: true
  });

  const canvasWatch = Form.useWatch('canvas', {
    form,
    preserve: true
  });

  const widthWatch = Form.useWatch('width', {
    form,
    preserve: true
  });
  const heightWatch = Form.useWatch('height', {
    form,
    preserve: true
  });
  const type = props.type;
  let canvas = form.getFieldValue('canvas');

  // 处理从props传入的sizeRatio
  useEffect(() => {
    if (type === 'self-adaption') {
      if (props.sizeRatio && props.sizeRatio.length > 0) {
        const customDimensions = convertSizeRatioToDimension(props.sizeRatio);
        setDimensionList([SelfAdaptionDimension, ...customDimensions]);
        // 变动sizeRatio时， 取列表中的3:4比例的选项
        const ratio34 = [SelfAdaptionDimension, ...customDimensions].find(
          (item) => item.value === PosterRatio.RATIO_3_4
        );
        if (ratio34) {
          form.setFieldValue('picRatio', PosterRatio.RATIO_3_4);
          form.setFieldValue('width', ratio34.size?.[0] || 768);
          form.setFieldValue('height', ratio34.size?.[1] || 1024);
        }
      } else {
        setDimensionList([SelfAdaptionDimension]);
      }
    } else {
      if (props.sizeRatio && props.sizeRatio.length > 0) {
        const customDimensions = convertSizeRatioToDimension(props.sizeRatio);
        setDimensionList([...customDimensions]);
        // 变动sizeRatio时， 取列表中的3:4比例的选项
        const ratio34 = [...customDimensions].find(
          (item) => item.value === PosterRatio.RATIO_3_4
        );
        if (ratio34) {
          form.setFieldValue('picRatio', PosterRatio.RATIO_3_4);
          form.setFieldValue('width', ratio34.size?.[0] || 768);
          form.setFieldValue('height', ratio34.size?.[1] || 1024);
        }
      } else {
        setDimensionList([]);
      }
    }
  }, [props.sizeRatio, type]);

  useEffect(() => {
    if (!props.imageUrl) {
      if (canvas?.proportion === PosterRatio.SELF_ADAPTION) {
        form.setFieldValue('width', 0);
        form.setFieldValue('height', 0);
      }
      // else if (canvas?.proportion === PosterRatio.FREE) {
      //   form.setFieldValue('width', widthWatch || 768);
      //   form.setFieldValue('height', heightWatch || 1024);
      // }
      return;
    }

    const img = new Image();

    img.onload = () => {
      setImageSize([img.width, img.height]);
      const curWidth = imageSize?.[0] ?? size?.[0];
      const curHeight = imageSize?.[1] ?? size?.[1];
      // 判断逻辑如下
      // 最大边是1024
      // 如果宽大于高， 则设置宽为1024， 高为 1024 / 宽 * 高, 不要使用getClosest
      // 如果小于高， 则设置高为1024， 宽为 1024 / 高 * 宽, 不要使用getClosest
      // console.log(curWidth, curHeight, 'curWidth, curHeight');
      if (curWidth > curHeight) {
        // 宽大于高，设置宽为1024，高按比例计算
        size = [1024, Number(Math.round((1024 / curWidth) * curHeight))];
      } else {
        // 高大于等于宽，设置高为1024，宽按比例计算
        size = [Number(Math.round((1024 / curHeight) * curWidth)), 1024];
      }

      if (props.imageUrl && canvas?.proportion === PosterRatio.SELF_ADAPTION) {
        form.setFieldValue('width', size[0]);
        form.setFieldValue('height', size[1]);
        form.setFieldValue('picRatio', PosterRatio.SELF_ADAPTION);
        form.setFieldValue('canvas', {
          proportion: PosterRatio.SELF_ADAPTION,
          size: size
        });
      } else if (canvas?.proportion === PosterRatio.FREE) {
        form.setFieldValue('canvas', {
          proportion: PosterRatio.FREE,
          size: [widthWatch || 768, heightWatch || 1024]
        });
        form.setFieldValue('picRatio', PosterRatio.FREE);
        form.setFieldValue('width', widthWatch || 768);
        form.setFieldValue('height', heightWatch || 1024);
      }
    };

    img.src = props.imageUrl ?? '';
  }, [props.imageUrl]);

  useEffect(() => {
    // 默认设置尺寸

    // console.log('canvas', canvas);
    if (type === 'self-adaption') {
      // 如果有自定义的sizeRatio，优先使用自定义的，否则使用默认的自适应
      if (props.sizeRatio && props.sizeRatio.length > 0) {
        const customDimensions = convertSizeRatioToDimension(props.sizeRatio);
        setDimensionList([SelfAdaptionDimension, ...customDimensions]);
      } else {
        setDimensionList([SelfAdaptionDimension]);
      }

      const curWidth = imageSize?.[0] ?? size?.[0];
      const curHeight = imageSize?.[1] ?? size?.[1];
      if (!props.imageUrl) {
        form.setFieldValue('width', 0);
        form.setFieldValue('height', 0);
        form.setFieldValue('canvas', {
          proportion: PosterRatio.SELF_ADAPTION,
          size: size
        });
        form.setFieldValue('picRatio', PosterRatio.SELF_ADAPTION);
        return;
      }
      // 判断逻辑如下
      // 最大边是1024
      // 如果宽大于高， 则设置宽为1024， 高为 1024 / 宽 * 高, 不要使用getClosest
      // 如果小于高， 则设置高为1024， 宽为 1024 / 高 * 宽, 不要使用getClosest
      // console.log(curWidth, curHeight, 'curWidth, curHeight');
      if (curWidth > curHeight) {
        // 宽大于高，设置宽为1024，高按比例计算
        size = [1024, Number(Math.round((1024 / curWidth) * curHeight))];
      } else {
        // 高大于等于宽，设置高为1024，宽按比例计算
        size = [Number(Math.round((1024 / curHeight) * curWidth)), 1024];
      }
      // console.log(size, 'size');
      // _dimensionList.unshift(SelfAdaptionDimension);

      form.setFieldValue(
        'picRatio',
        canvas?.proportion || PosterRatio.SELF_ADAPTION
      );
      if (props.imageUrl && canvas?.proportion === PosterRatio.SELF_ADAPTION) {
        form.setFieldValue('width', size[0]);
        form.setFieldValue('height', size[1]);
        form.setFieldValue('canvas', {
          proportion: PosterRatio.SELF_ADAPTION,
          size: size
        });
      } else {
        if (canvas?.proportion === PosterRatio.SELF_ADAPTION) {
          form.setFieldValue('width', 0);
          form.setFieldValue('height', 0);
        } else {
          form.setFieldValue('width', canvas?.size[0] || 768);
          form.setFieldValue('height', canvas?.size[1] || 1024);
        }
      }
    } else {
      form.setFieldValue(
        'picRatio',
        canvas?.proportion || PosterRatio.RATIO_3_4
      );
      form.setFieldValue('width', canvas?.size[0] || 768);
      form.setFieldValue('height', canvas?.size[1] || 1024);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [imageSize]);
  // console.log(form.getFieldsValue(), 'form.getFieldsValue()');
  // 切换比例
  const ratioChange = (
    val: PosterRatio | undefined,
    preVal: PosterRatio | undefined
  ) => {
    // console.log(val, 'val');
    form.setFieldValue('picRatio', val);
    const temp = _dimensionList.find((item) => item.value === val);
    const width =
      temp?.type === DimensionType.FIXED_ASPECT
        ? Number(temp?.size?.[0])
        : widthWatch;
    const height =
      temp?.type === DimensionType.FIXED_ASPECT
        ? Number(temp?.size?.[1])
        : heightWatch;

    if (val === PosterRatio.FREE) {
      // 如果宽高超过1024， 则设置为1024， 其他按照原值设定
      let _width = width > 1024 ? 1024 : width;
      let _height = height > 1024 ? 1024 : height;
      form.setFieldValue('width', _width);
      form.setFieldValue('height', _height);
      form.setFieldValue('canvas', {
        proportion: val,
        size: [_width, _height]
      });
    } else if (val === PosterRatio.SELF_ADAPTION) {
      const curWidth = imageSize?.[0] ?? size?.[0];
      const curHeight = imageSize?.[1] ?? size?.[1];
      // 判断逻辑如下
      // 最大边是1024
      // 如果宽大于高， 则设置宽为1024， 高为 1024 / 宽 * 高, 不要使用getClosest
      // 如果小于高， 则设置高为1024， 宽为 1024 / 高 * 宽, 不要使用getClosest
      // console.log(curWidth, curHeight, 'curWidth, curHeight');
      if (curWidth > curHeight) {
        // 宽大于高，设置宽为1024，高按比例计算
        size = [1024, Math.round((1024 / curWidth) * curHeight)];
      } else {
        // 高大于等于宽，设置高为1024，宽按比例计算
        size = [Math.round((1024 / curHeight) * curWidth), 1024];
      }

      if (props.imageUrl) {
        form.setFieldValue('width', size[0]);
        form.setFieldValue('height', size[1]);

        form.setFieldValue('canvas', {
          proportion: val,
          size: size
        });
      } else {
        form.setFieldValue('width', 0);
        form.setFieldValue('height', 0);
        form.setFieldValue('canvas', {
          proportion: val,
          size: [0, 0]
        });
      }
    } else {
      form.setFieldValue('width', width);
      form.setFieldValue('height', height);
      form.setFieldValue('canvas', {
        proportion: val,
        size: [width, height]
      });
    }

    //   form.setFieldValue('picRatio', ratio);

    // updateCanvasSize(width, height, preWidth, preHeight, val, preVal);
  };

  const handleWidthChange = (val: number, preVal: number) => {
    // console.log(val, preVal, 'val, preVal');
    form.setFieldValue('width', val);
    form.setFieldValue('canvas', {
      proportion: picRatioWatch,
      size: [val, form.getFieldValue('height')]
    });
  };

  // console.log(, 'picRatioWatch');

  const handleHeightChange = (val: number, preVal: number) => {
    // console.log(val, preVal, 'val, preVal');
    form.setFieldValue('height', val);
    form.setFieldValue('canvas', {
      proportion: picRatioWatch,
      size: [form.getFieldValue('width'), val]
    });
  };

  // console.log(form.getFieldsValue(), 'form.getFieldsValue()');
  // console.log(_dimensionList, 'dimensionList');
  return (
    <>
      <Flex justify="space-between">
        <Form.Item name="picRatio" noStyle>
          <RatioRadio
            dimensions={_dimensionList}
            onChange={(val, preVal) => {
              ratioChange(val, preVal);
            }}
          />
        </Form.Item>

        <Flex justify="space-between">
          <Form.Item name="width" noStyle>
            <InputNumberLimitMax
              max={picRatioWatch === DimensionType.FREE ? 1024 : 4096}
              min={picRatioWatch === PosterRatio.SELF_ADAPTION ? 0 : minWidth}
              step={1}
              onChange={(val, preVal) => {
                handleWidthChange(val, Number(preVal));
              }}
              className={classNames(styles.sizeInputNumber, 'width')}
              precision={0}
              changeOnBlur
              disabled={picRatioWatch !== PosterRatio.FREE}
              controls={false}
              formatter={(value) =>
                (value && value > 0 ? Math.round(value) : '').toString()
              }
              onKeyDown={(e) => {
                if (e.code === 'Enter') {
                  e.preventDefault();
                }
              }}
            />
          </Form.Item>
          {picRatioWatch === PosterRatio.FREE ? (
            <DelinkBold className={styles.linkIcon} />
          ) : (
            <LinkBold
              style={{ cursor: 'pointer' }}
              className={styles.linkIcon}
              onClick={() => {
                ratioChange(PosterRatio.FREE, picRatioWatch);
              }}
            />
          )}
          <Form.Item name="height" noStyle>
            <InputNumberLimitMax
              max={picRatioWatch === PosterRatio.FREE ? 1024 : 4096}
              min={picRatioWatch === PosterRatio.SELF_ADAPTION ? 0 : minHeight}
              step={1}
              onChange={(val, preVal) => {
                handleHeightChange(val, Number(preVal));
              }}
              className={classNames(styles.sizeInputNumber, 'height')}
              precision={0}
              changeOnBlur
              disabled={picRatioWatch !== PosterRatio.FREE}
              controls={false}
              formatter={(value) =>
                (value && value > 0 ? Math.round(value) : '').toString()
              }
              onKeyDown={(e) => {
                if (e.code === 'Enter') {
                  e.preventDefault();
                }
              }}
            />
          </Form.Item>
        </Flex>
      </Flex>
    </>
  );
});

type RatioRadioProps = {
  dimensions?: Array<Dimension>;
  value?: PosterRatio;
  onChange: (val: PosterRatio, preVal?: PosterRatio) => void;
};
function RatioRadio({ dimensions, value, onChange }: RatioRadioProps) {
  return (
    <div className={styles.radioGroup} id="posterSelect">
      <Select
        getPopupContainer={() => document.getElementById('posterSelect')!}
        value={value}
        options={dimensions}
        suffixIcon={<ChevronDownBlack className="suffix-icon" />}
        className={styles.selector}
        onChange={(val) => {
          onChange(val, value);
        }}
        dropdownStyle={{ width: '196px', height: '244px' }}
        listHeight={230}
      />

      {/* {dimensions?.map((dimension) => {
        return (
          <div
            className={classNames(
              'aspect-radio-content',
              value && dimension.label === value.label && 'checked'
            )}
            key={dimension.label}
            onClick={() => {
              if (value?.label !== dimension.label) {
                onChange?.(dimension, value);
              }
            }}
          >
            <span className="aspect-radio-content-icon">{dimension.icon}</span>

            <span className="aspect-radio-content-primary">
              {dimension.label}
            </span>
          </div>
        );
      })} */}
    </div>
  );
}

// 你可以根据实际情况扩展 iconMap
const iconMap: Record<string, React.ReactNode> = {
  [PosterRatio.SELF_ADAPTION]: <Picture />
  // 其他 ratio 可以补充
};

let dimensionId = 10000; // 避免和已有 id 冲突
const getId = () => dimensionId++;

function convertSizeRatioToDimension(sizeRatioArr: any[]): Dimension[] {
  return sizeRatioArr.map((item) => {
    const ratio = item.ratio as PosterRatio;
    return {
      id: getId(),
      type: DimensionType.FIXED_ASPECT,
      size: [item.width, item.height],
      label: (
        <div className={styles.selection}>
          {/* {iconMap[ratio] || null} */}
          <div className={styles.selectionIcon}>
            <AntdImage src={item.icon} preview={false} />
          </div>
          {item.sizeName}
        </div>
      ),
      value: ratio
    };
  });
}
