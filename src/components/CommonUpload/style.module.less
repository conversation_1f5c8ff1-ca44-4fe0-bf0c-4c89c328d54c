@import '~@/styles/variables.less';

.common-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;

  .upload-descWrap {
    margin-top: 8px;
    color: @color-text-secondary;
  }

  .upload-desc {
    margin-right: 4px;
  }

  &.adaptive {
    width: auto;
    height: 100%;
    .upload {
      width: 100%;
      height: 100%;

      :global {
        .ant-upload-drag {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .upload {
    display: block;
    height: 100%;
    width: 100%;
  }
}

.upload-progress-modal {
  .upload-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .upload-progress-title {
      font-size: 16px;
      font-weight: 600;
    }

    :global {
      .ant-progress-line {
        margin: 0;
        line-height: 0;
      }
    }
  }
}

.toolTip {
  width: 500px;
}
