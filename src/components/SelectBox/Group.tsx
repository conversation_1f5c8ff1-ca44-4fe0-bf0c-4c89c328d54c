import { Empty, Row } from 'antd';
import { FixedSizeGrid } from '../Virtual/FixedSizeGrid';
import { SelectBoxItem, SelectBoxItemProps } from './Item';
import styles from './index.module.less';
import { useState } from 'react';
import _ from 'lodash';
export type SelectBoxGroupValueType = string | number | boolean;

export interface SelectBoxGroupOptionType<
  P extends SelectBoxGroupValueType = SelectBoxGroupValueType
> {
  label: React.ReactNode;
  value: P;
  style?: Omit<React.CSSProperties, 'height' | 'width'>;
  disabled?: boolean;
  id?: string;
  onChange?: (
    changedOptions: {
      value: SelectBoxGroupValueType;
      checked: SelectBoxGroupValueType;
    }[]
  ) => boolean | void;
  src?: string;
}

export type ValueType<
  T extends boolean,
  P extends SelectBoxGroupValueType = SelectBoxGroupValueType
> = T extends true ? P[] : P;

export interface SelectBoxGroupProps<
  T extends boolean,
  P extends SelectBoxGroupValueType
> extends Pick<SelectBoxItemProps, 'renderIcon' | 'renderItem'> {
  /**
   * 选项列表
   */
  options: SelectBoxGroupOptionType<P>[];
  /**
   * 是否开启虚拟滚动
   */
  virtual?: boolean;
  /**
   * 单个选项的宽度 虚拟滚动时需要设置
   */
  width?: number;
  /**
   * 单个选项的高度 虚拟滚动时需要设置
   */
  height?: number;
  /**
   * 选项之间的间距
   * @default 8
   */
  gap?: number;
  id?: string;
  /**
   * 是否多选
   */
  isMultiSelect: T;
  onChange?: (value: ValueType<T, P>) => void;
  value?: ValueType<T, P>;
}

export const SelectBoxGroup = <
  T extends boolean,
  P extends SelectBoxGroupValueType = SelectBoxGroupValueType
>({
  options,
  virtual = true,
  width = 0,
  height = 0,
  gap = 8,
  renderIcon,
  isMultiSelect,
  onChange,
  value,
  id,
  renderItem
}: SelectBoxGroupProps<T, P>) => {
  const [selectedValue, setSelectedValue] = useState<ValueType<T, P>>(
    () => (isMultiSelect ? [] : '') as ValueType<T, P>
  );

  const isControlled = !_.isUndefined(value); // 是否受控
  const fixturesValue = isControlled ? value : selectedValue;

  const fixturesItemProps = (option: SelectBoxGroupOptionType<P>) => {
    const {
      label,
      value,
      disabled,
      style,
      onChange: onOptionChange,
      src
    } = option;

    const checked = isMultiSelect
      ? (fixturesValue as ValueType<true, P>).includes(value!)
      : value === (fixturesValue as ValueType<false>);

    return {
      value: checked,
      onChange: (checked: boolean) => {
        let finalValue: ValueType<T, P>;

        if (isMultiSelect) {
          finalValue = (
            checked
              ? [...((fixturesValue ?? []) as ValueType<true>), value]
              : ((fixturesValue ?? []) as ValueType<true>).filter(
                  (item) => item !== value
                )
          ) as ValueType<T, P>;
        } else {
          finalValue = (checked ? value : '') as ValueType<T, P>;
        }

        const changedOptions = [{ value, checked }];

        // 单选 并且选中的不是当前选项
        if (!isMultiSelect && fixturesValue !== value && fixturesValue !== '') {
          changedOptions.push({
            value: fixturesValue as ValueType<false, P>,
            checked: false
          });
        }

        const isBlocker = onOptionChange?.(changedOptions); // 选项的onChange

        if (isBlocker) return;

        onChange?.(finalValue); // 整个组件的onChange

        if (!isControlled) {
          setSelectedValue(finalValue);
        }
      },
      src,
      label: label as string,
      disabled,
      style: { width, height, ...(style ?? {}) },
      renderIcon
    };
  };

  if (!options.length) {
    return <Empty />;
  }

  return (
    <Row
      id={id}
      className={styles.container}
      justify={!options.length ? 'center' : 'start'}
    >
      {virtual ? (
        <FixedSizeGrid
          rowKey={(item, index) => (item.value as string) + index.toString()}
          dataSource={options}
          columnWidth={width + gap}
          rowHeight={height + gap}
          gutter={gap}
          renderItem={(option) => {
            const itemProps = fixturesItemProps(option);
            return <SelectBoxItem {...itemProps} renderItem={renderItem} />;
          }}
        />
      ) : (
        <div className={styles.grid} style={{ gap }}>
          {options.map((option, index) => {
            const itemProps = fixturesItemProps(option);
            return (
              <div key={option.value + index.toString()}>
                <SelectBoxItem {...itemProps} renderItem={renderItem} />
              </div>
            );
          })}
        </div>
      )}
    </Row>
  );
};
