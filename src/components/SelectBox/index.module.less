@import '~@/styles/variables.less';

.select-box {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  &.show-checked-icon {
    :global(.ant-checkbox):global(.ant-checkbox-checked) {
      display: block;
      position: absolute;
      right: 2px;
      top: 2px;
      border-radius: 4px;

      span {
        background-color: #3549ff;
        border-color: #3549ff;
      }
    }
  }

  :global(.ant-checkbox) {
    display: none;

    & + span {
      color: #fff;
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding: 0;
      position: absolute;
      bottom: 0px;
      left: 0px;
      right: 0px;
      padding: 2px;
      border-radius: 4px;
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.5) 100%
      );
    }
  }

  :global(.ant-checkbox-wrapper) {
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: block;
    border-radius: 4px;
    transition: all 0.1s ease-in-out;

    &:global(.ant-checkbox-wrapper-checked) {
      &::before {
        border-color: @stroke-system-selected;
      }
    }

    &::before {
      // 模拟border
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border-radius: 6px;
      border: 2px solid @stroke-system-border-overlay;
    }

    &:after {
      display: none;
    }
  }
}

.select-box-item {
  width: 96px;
  height: 96px;
  border-radius: 6px;
  border: 2px solid @stroke-system-border-overlay;
  transition: all 0.1s ease-in-out;
  cursor: pointer;
  position: relative;

  background: @background-system-space-holder;

  &:hover {
    border-color: @color-primary-hover;
  }

  &.selected {
    border-color: @stroke-system-selected;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  :global(.ant-image) {
    height: 100%;
    width: 100%;

    img {
      display: block;
      width: 100%;
      height: 100% !important;
      border-radius: 4px;
      object-fit: cover;
    }
  }

  .label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    color: #fff;
    text-align: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 0.5) 100%
    );
  }

  .check-icon {
    position: absolute;
    top: 2px;
    right: 2px;
    z-index: 99;

    :global(.ant-checkbox-checked .ant-checkbox-inner) {
      background-color: @content-web-primary;
      border-color: @content-web-primary;
    }
  }
}

.container {
  height: 100%;
  overflow: hidden;

  .grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    height: 100%;
  }
}
