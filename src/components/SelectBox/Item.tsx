import { Checkbox, Image, ImageProps } from 'antd';
import styles from './index.module.less';
import classNames from 'classnames';
import { useInView } from 'react-intersection-observer';
import { cloneElement, useState } from 'react';
import _ from 'lodash';

export interface SelectBoxItemProps extends Pick<ImageProps, 'src' | 'alt'> {
  /**
   * 选项的文本
   */
  label?: string;
  /**
   *  选项的图标
   *  @param checked 是否选中
   *  @returns React.ReactNode
   */
  renderIcon?: (checked: boolean) => React.ReactNode;
  style?: React.CSSProperties;
  disabled?: boolean;
  value?: boolean;
  onChange?: (checked: boolean) => void;
  className?: string;

  renderItem?: ({
    checked,
    disabled,
    label
  }: {
    label?: string;
    checked: boolean;
    disabled: boolean;
  }) => React.ReactNode;
}

export const SelectBoxItem = ({
  label,
  value,
  className,
  style,
  renderIcon,
  disabled,
  onChange,
  renderItem,
  ...imageProps
}: SelectBoxItemProps) => {
  const [ref, inView] = useInView({ triggerOnce: true, threshold: 0.1 });

  const [checked, setChecked] = useState(false);
  const isControlled = !_.isUndefined(value);
  const fixturesValue = isControlled ? value : checked;

  return !!renderItem ? (
    cloneElement(
      renderItem({
        checked: fixturesValue,
        disabled: !!disabled,
        label
      }) as any,
      {
        onClick(e) {
          if (disabled) return;
          onChange?.(!fixturesValue);

          if (!isControlled) {
            setChecked(!fixturesValue);
          }
        }
      }
    )
  ) : (
    <div
      ref={ref}
      className={classNames(
        styles.selectBoxItem,
        {
          [styles.selected]: fixturesValue,
          [styles.disabled]: disabled
        },
        className
      )}
      style={style}
      onClick={() => {
        if (disabled) return;
        onChange?.(!fixturesValue);

        if (!isControlled) {
          setChecked(!fixturesValue);
        }
      }}
    >
      <div className={styles.checkIcon}>
        {!!renderIcon ? (
          renderIcon?.(!!fixturesValue)
        ) : (
          <Checkbox checked={!!fixturesValue} />
        )}
      </div>

      {inView && !!imageProps.src && <Image preview={false} {...imageProps} />}

      {!!label && <span className={styles.label}>{label}</span>}
    </div>
  );
};
