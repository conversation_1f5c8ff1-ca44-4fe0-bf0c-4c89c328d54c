import { MeidouBalanceWithMissionCenterEntry } from '@/components/MeiDouPrice';
import { AppHeader } from '@/layouts/AppHeader';
import { AccountAvatar } from '@/layouts/Header/AccountAvatar';
import { PropsWithChildren, useState } from 'react';
import { Actions } from '../Actions';
import { App } from 'antd';
import { DownloadBold } from '@meitu/candy-icons';
import styles from './index.module.less';
import { useToImageExtension, useToImg2Img } from '../ImagesContainer';
import { downloadFile } from '@/utils/blob';
import { Button } from '../Button';
import { Link } from 'react-router-dom';
import { AppModule, generateRouteTo } from '@/services';
import { downloadImage } from '@/api';
import { isWebSDK } from '@/utils';
export interface HeaderProps {
  onBackClick?: () => void;
  step?: any;
}

export const CommonAppHeader = (props: PropsWithChildren<HeaderProps>) => {
  return (
    <AppHeader
      onBackClick={props.onBackClick}
      step={props.step}
      actionsGutter={20}
      actions={
        <>
          {props.children}
          {isWebSDK() ? null : (
            <>
              <MeidouBalanceWithMissionCenterEntry />
              <AccountAvatar />
            </>
          )}
        </>
      }
    />
  );
};

export type CanvasHeaderProps = HeaderProps & {
  // 目标图片
  src: string;
  // 任务id
  taskId: string;
  // 禁止
  disabled?: boolean;
  // 下载的文件名
  downloadFileName?: () => string | string;
  // 更多编辑点击
  onClickMore?: () => void;
  // ai改图点击
  onClickImgPartialRepaint?: () => void;
  // ai扩图点击
  onClickImgExtension?: () => void;
  // ai图生图点击
  onClickImgGenImg?: () => void;
  // 下载点击
  onDownloadClick?: () => void;
};

export const CanvasHeader = ({
  src,
  taskId,
  disabled,
  downloadFileName,
  onClickImgExtension,
  onClickImgGenImg,
  onClickImgPartialRepaint,
  onClickMore,
  onDownloadClick,
  ...props
}: CanvasHeaderProps) => {
  const toImageExtension = useToImageExtension();
  const toImg2Img = useToImg2Img();
  // const toImgEraser = useToImageEraser();
  const [downloadLoading, setDownloadLoading] = useState(false);
  const { message } = App.useApp();

  const onDownload = async () => {
    if (downloadLoading) return;

    setDownloadLoading(true);
    onDownloadClick?.();

    try {
      let fixturesFileName = '';
      if (typeof downloadFileName === 'string') {
        fixturesFileName = downloadFileName;
      } else {
        fixturesFileName = downloadFileName?.() ?? '';
      }
      await downloadFile(src, fixturesFileName);
      message.success('下载成功');
      downloadImage({ msgId: taskId, imageUrl: src });
    } catch (error) {
      message.error('下载失败');
    } finally {
      setDownloadLoading(false);
    }
  };

  return (
    <CommonAppHeader {...props}>
      {!!src && (
        <>
          <Actions
            placement="bottom"
            disabled={disabled}
            triggerProps={{
              className: styles.btn,
              children: '更多编辑',
              size: 'middle',
              icon: null,
              onClick: onClickMore
            }}
          >
            <Link
              to={generateRouteTo(AppModule.ImageEditor, {
                editorImageUrl: src
              })}
              target="_blank"
              className={styles.linkBox}
              onClick={(e) => {
                onClickImgPartialRepaint?.();
              }}
            >
              <div className={styles.repaintBox}>
                <Button className={styles.btn} size="small" type="text">
                  AI改图
                </Button>
                <div className={styles.insideBadge}>
                  <span>内测</span>
                </div>
              </div>
            </Link>

            <Button
              className={styles.btn}
              onClick={(e) => {
                onClickImgExtension?.();
                toImageExtension(src, taskId);
              }}
              size="small"
              type="text"
            >
              AI扩图
            </Button>
            <Button
              className={styles.btn}
              onClick={(e) => {
                onClickImgGenImg?.();
                toImg2Img(src, taskId);
              }}
              size="small"
              type="text"
            >
              图生图
            </Button>
          </Actions>

          <Button
            disabled={disabled}
            className={styles.downloadBtn}
            icon={<DownloadBold />}
            loading={downloadLoading}
            onClick={onDownload}
          >
            下载
          </Button>
        </>
      )}
    </CommonAppHeader>
  );
};
