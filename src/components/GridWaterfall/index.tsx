import type { GridContainerProps } from './GridContainer';
import type { GridPaginationProps } from './GridPagination';
import type { GridItemProps } from './GridItem';

import { GridPagination } from './GridPagination';
import { GridContainer } from './GridContainer';
import { GridProvider } from './GridProvider';

export { GridPagination, GridContainer };

export function GridWaterfall<T extends Pick<GridItemProps, 'id' | 'colSpan'>>(
  props: GridContainerProps<T> & Omit<GridPaginationProps, 'dataLength'>
) {
  const {
    hasMore,
    endMessage,
    scrollableTarget,
    empty,
    getMore,
    list,
    col,
    gutter,
    renderItem
  } = props;

  return (
    <GridProvider>
      <GridPagination
        hasMore={hasMore}
        dataLength={list.length}
        endMessage={endMessage}
        scrollableTarget={scrollableTarget}
        empty={empty}
        getMore={getMore}
      >
        <GridContainer<T>
          list={list}
          col={col}
          gutter={gutter}
          renderItem={renderItem}
        />
      </GridPagination>
    </GridProvider>
  );
}
