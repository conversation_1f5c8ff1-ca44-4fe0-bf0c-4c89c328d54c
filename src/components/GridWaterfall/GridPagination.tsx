import type { PropsWithChildren, ReactNode } from 'react';

import InfiniteScroll from 'react-infinite-scroll-component';
import styles from './index.module.less';
import { Empty } from 'antd';
import { Loading } from '@/components/Loading';
import emptyImage from '@/assets/images/empty.jpg';

export interface GridPaginationProps {
  /** 是否可翻页 */
  hasMore: boolean;
  /** 数据长度 */
  dataLength: number;
  /** 到底渲染节点 */
  endMessage?: ReactNode;
  /** placeholder */
  empty?: ReactNode;
  /** 滚动父节点(父节点id) 默认 window */
  scrollableTarget?: ReactNode;
  /** 滚动触底触发下一页请求 */
  getMore: () => void;
}

export function GridPagination(props: PropsWithChildren<GridPaginationProps>) {
  const {
    hasMore,
    dataLength,
    endMessage,
    scrollableTarget,
    empty,
    getMore,
    children
  } = props;

  if (dataLength === 0 && hasMore) {
    return <Loading />;
  }

  return dataLength > 0 ? (
    <InfiniteScroll
      scrollableTarget={scrollableTarget}
      dataLength={dataLength}
      next={getMore}
      hasMore={hasMore}
      loader={
        <div className={styles.gridLoading}>
          <Loading />
        </div>
      }
      hasChildren={dataLength > 0}
      endMessage={
        endMessage ?? <p className={styles.endMessage}>没有更多了～</p>
      }
      className={styles.gridPagination}
    >
      {children}
    </InfiniteScroll>
  ) : (
    <>
      {empty === undefined ? (
        <Empty
          className={styles.gridEmpty}
          image={emptyImage}
          description={'暂无数据'}
        />
      ) : (
        empty
      )}
    </>
  );
}
