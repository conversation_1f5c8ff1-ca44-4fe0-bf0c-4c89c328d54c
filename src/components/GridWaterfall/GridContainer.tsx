import type { GridItemProps } from './GridItem';
import type { ReactNode } from 'react';

import { GridContext } from './GridProvider';
import { useMemo, useContext, useEffect } from 'react';
import { useResize } from './useResize';
import { GridItem } from './GridItem';
import { getSpan } from './utils';
import { produce } from 'immer';

import styles from './index.module.less';

/** 占位数 */
export type Span = ReactiveSpan | number;

/** 响应式占位数 */
export interface ReactiveSpan {
  /** 屏幕 < 576px */
  xs?: number;
  /** 屏幕 ≥ 576px */
  sm?: number;
  /** 屏幕 ≥ 768px */
  md?: number;
  /** 屏幕 ≥ 992px */
  lg?: number;
  /** 屏幕 ≥ 1200px */
  xl?: number;
  /** 屏幕 ≥ 1600px */
  xxl?: number;
}

export interface GridContainerProps<
  T extends Pick<GridItemProps, 'id' | 'colSpan'>
> {
  list: T[];
  /** 列数 */
  col: Span;
  /** 间隔 */
  gutter?: Span | [Span] | [Span, Span];
  /** 加载节点 */
  renderItem: (props: T, index: number) => ReactNode;
}

export function GridContainer<T extends Pick<GridItemProps, 'id' | 'colSpan'>>(
  props: GridContainerProps<T>
) {
  const { list, col, gutter: gutterFromProps = [12, 8], renderItem } = props;
  const { setSize } = useContext(GridContext);
  const { targetRef, size } = useResize();
  const width = window.innerWidth;

  const columns = useMemo(() => getSpan(width, col), [width, col]);
  const gutter: [number, number] = useMemo(() => {
    if (typeof gutterFromProps === 'number') {
      return [gutterFromProps / 2, gutterFromProps / 2];
    }

    // Span
    if (!Array.isArray(gutterFromProps)) {
      const gutter = getSpan(width, gutterFromProps) / 2;
      return [gutter, gutter];
    }

    // [Span]
    if (gutterFromProps.length < 2) {
      const gutter = getSpan(width, gutterFromProps[0]) / 2;
      return [gutter, gutter];
    }

    // [Span, Span]
    return [
      getSpan(width, gutterFromProps[0]) / 2,
      getSpan(width, gutterFromProps[1] as Span) / 2
    ];
  }, [width, gutterFromProps]);

  const listWithColSpan = useMemo(() => {
    return produce<T[], (T & { leftCol: number; colSpan: number })[]>(
      list,
      (draft) => {
        let rowCol = 0;

        draft.forEach((item) => {
          if (rowCol >= columns) {
            rowCol = 0;
          }

          item.leftCol = columns - rowCol;
          const colSpan = item.colSpan ? getSpan(width, item.colSpan) : 1;
          item.colSpan = colSpan;

          rowCol += colSpan;
        });
      }
    ) as (T & { leftCol: number; colSpan: number })[];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [columns, list]);

  useEffect(() => {
    size && setSize?.(size);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [size?.[0]]);

  return (
    <div
      ref={targetRef}
      className={styles.gridContainer}
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        margin: `-${gutter[1]}px -${gutter[0]}px 0`,
        width: `calc(100% + ${gutter[0] * 2}px)`
      }}
    >
      {listWithColSpan.map((itemProps, index) => (
        <GridItem key={itemProps.id} {...itemProps} gutter={gutter}>
          {renderItem(itemProps, index)}
        </GridItem>
      ))}
    </div>
  );
}
