import type { PropsWithChildren } from 'react';

import { useEffect, useState, useRef, useContext } from 'react';
import { GridContext } from './GridProvider';

import styles from './index.module.less';

export interface GridItemProps {
  id: string;
  /** 占用列数 */
  colSpan?: number;
  /** 间隔 */
  gutter: [number, number];
  /** 剩余可占用列数 */
  leftCol: number;
}

export function GridItem(props: PropsWithChildren<GridItemProps>) {
  const { colSpan, gutter, leftCol, children } = props;
  const [rowEnd, setRowEnd] = useState<number>();
  const targetRef = useRef<HTMLDivElement>(null);
  const { size } = useContext(GridContext);

  useEffect(() => {
    setTimeout(() => {
      if (targetRef.current) {
        // 使用getBoundingClientRect()获取元素的位置和大小信息
        const height = targetRef.current.getBoundingClientRect().height;
        setRowEnd(Math.ceil(height) + gutter[1] * 2);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [size?.[0]]);

  return (
    <div
      className={styles.gridItem}
      style={{
        gridColumnEnd: `span ${colSpan ? Math.min(colSpan, leftCol) : 1}`,
        gridRowEnd: `span ${rowEnd}`,
        margin: `${gutter[1]}px 0`,
        padding: `0 ${gutter[0]}px`
      }}
    >
      <div ref={targetRef}>{children}</div>
    </div>
  );
}

GridItem.defaultProps = {
  colSpan: 1
};
