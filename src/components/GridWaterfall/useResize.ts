import type { CanvasSize } from '@/types';

import { useRef, useState, useLayoutEffect } from 'react';
import ResizeObserver from 'resize-observer-polyfill';

export function useResize() {
  const targetRef = useRef<HTMLDivElement>(null);
  const [size, setSize] = useState<CanvasSize>();

  useLayoutEffect(() => {
    const targetNode = targetRef?.current;

    if (!targetNode) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setSize([width, height]);
      }
    });
    resizeObserver.observe(targetNode);

    return () => {
      resizeObserver.unobserve(targetNode);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    targetRef,
    size
  };
}
