import type { Span } from './GridContainer';

export function getSpan(width: number, span: Span) {
  if (typeof span === 'number') {
    return span;
  }

  const sortedSpan: Readonly<(number | undefined)[]> = [
    span.xxl,
    span.xl,
    span.lg,
    span.md,
    span.sm,
    span.xs
  ];
  const validSpan = (spans: typeof sortedSpan) =>
    spans.find((span) => span !== undefined);

  return validSpan(sortedSpan.slice(sliceSpan(width))) ?? 1;
}

/**
 * @description {@link https://www.figma.com/file/I0zAyxrBcp1VTDEvxqlFam/AI%E5%BC%80%E6%94%BE%E5%B9%B3%E5%8F%B0?type=design&node-id=865-4839&mode=design&t=f5TDEbx9xavJlTp0-0}
 * @returns
 */
function sliceSpan(width?: number) {
  if (width === undefined || width >= 2306) {
    return 0;
  }
  if (width >= 1920) {
    return 1;
  }
  if (width >= 1200) {
    return 2;
  }
  if (width >= 960) {
    return 3;
  }
  if (width >= 360) {
    return 4;
  }
  return 5;
}
