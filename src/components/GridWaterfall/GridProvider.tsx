import type { PropsWithChildren } from 'react';
import type { CanvasSize } from '@/types';

import { createContext, useState } from 'react';

interface GridContextValue {
  /** 设置容器分栏数 */
  setSize?: (size: CanvasSize) => void;
  /** 容器分栏数 */
  size?: CanvasSize;
}

export const GridContext = createContext<GridContextValue>({});

export function GridProvider({ children }: PropsWithChildren) {
  const [size, setSize] = useState<CanvasSize>();

  return (
    <GridContext.Provider
      value={{
        setSize,
        size
      }}
    >
      {children}
    </GridContext.Provider>
  );
}
