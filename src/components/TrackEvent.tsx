import type { ReactElement } from 'react';

import {
  InView,
  type IntersectionObserverProps
} from 'react-intersection-observer';
import { cloneElement } from 'react';
import _ from 'lodash';

interface EventsContainerProps
  extends Omit<IntersectionObserverProps, 'children'> {
  onClick?: (...args: any) => void;
  onView?: (...args: any) => void;
  children: ReactElement;
}

export function EventsContainer(props: EventsContainerProps) {
  const {
    onClick: onClickFromProps,
    onView: onViewFromProps,
    children,
    ...restIntersectionObserverProps
  } = props;

  if (!onClickFromProps && !onViewFromProps) {
    return children;
  }

  const onClick =
    onClickFromProps &&
    function (...args: any[]) {
      onClickFromProps(...args);
      children.props.onClick?.(...args);
    };

  const element = cloneElement(
    children,
    _.omitBy(
      {
        onClick
      },
      _.isUndefined
    )
  );

  if (onViewFromProps) {
    return (
      <InView
        onChange={(inView) => inView && onViewFromProps()}
        style={{ height: '100%' }}
        {...{ triggerOnce: true, threshold: 1 }}
        {...restIntersectionObserverProps}
      >
        {element}
      </InView>
    );
  }

  return element;
}
