import { fetchPublishConfig } from '@/api/training';
import { ModelListItem, PublishConfigResponse } from '@/api/types';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { atom, useRecoilState } from 'recoil';

type PublishModelStateType = {
  /** 打开弹窗的模型的详情 */
  item: ModelListItem | undefined;
  /** 待裁剪的图片地址 */
  src: string;
  /** 返回的时候恢复原图 */
  tempSrc: string;
  /** 发布的配置，如tags */
  config: PublishConfigResponse | undefined;
  isFetchConfigLoading: boolean;
};

const defaultState = {
  item: undefined,
  src: '',
  tempSrc: '',
  config: undefined,
  isFetchConfigLoading: false
};

export const publishModelState = atom<PublishModelStateType>({
  key: 'publishModelState',
  default: defaultState
});

export const usePublishModel = () => {
  const [state, setState] = useRecoilState(publishModelState);

  const isOpen = !!state.item;

  const isShowCropModal = !!state.src;

  const openModal = async (item: ModelListItem) => {
    if (state.isFetchConfigLoading) return;

    setState((prev) => ({ ...prev, item, isFetchConfigLoading: true }));

    try {
      const config = await fetchPublishConfig({ id: item.id });
      setState((prev) => ({ ...prev, config }));
    } catch (error) {
      // 请求失败时关闭弹窗
      reset();
      defaultErrorHandler(error);
    } finally {
      setState((prev) => ({ ...prev, isFetchConfigLoading: false }));
    }
  };

  const closeModal = () => {
    setState((prev) => ({ ...prev, item: undefined }));
  };

  const go2Crop = (src: string, tempSrc: string) => {
    setState((prev) => ({ ...prev, src, tempSrc }));
  };

  const go2Form = () => {
    setState((prev) => ({ ...prev, src: '', tempSrc: '' }));
  };

  const reset = () => {
    setState(defaultState);
  };

  return {
    ...state,
    openModal,
    closeModal,
    go2Crop,
    go2Form,
    reset,
    isOpen,
    isShowCropModal
  };
};
