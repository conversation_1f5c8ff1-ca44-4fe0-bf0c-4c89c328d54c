import { defaultUploadParam } from '@/constants/uploadParams';
import { useUploader } from '@/hooks';
import { fetchFileBlob } from '@/utils/blob';

/**
 *  裁剪完后的图片重新上传（云处理的话后端加签就会返回原图）
 *  因为之前上传的图已经校验过了， 所以裁剪完后可以不检验
 *  如果需要后续再优化
 */
export const useReUpload = () => {
  const uploader = useUploader();

  const fixturesCover = async (src: string) => {
    const blob = await fetchFileBlob(src);

    const { name } = blob;

    const suffix = name?.slice(name?.lastIndexOf('.')) ?? '.png';

    const res = await uploader.upload(blob, {
      ...defaultUploadParam,
      suffix
    });

    // @ts-ignore  sdk类型和服务端返回的类型不一致
    return res?.previewUrl ?? '';
  };

  return { fixturesCover };
};
