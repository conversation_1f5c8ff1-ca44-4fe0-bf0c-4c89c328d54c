@import '~@/styles/variables.less';

.error {
  border-radius: 8px;
  border: 1.2px solid @base-red-light-50;
}
.errorTip {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 52px;
  display: flex;
  height: 40px;
  padding: 18px 22px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: @base-red-light-50;
  /* text_14 */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  border-radius: 40px;
  background: @background-system-frame-floatpanel;
}
.publish-modal:global(.@{ant-prefix}-modal) {
  border-radius: @border-radius-lg;
  overflow: hidden;

  &.hidden {
    display: none !important;
  }

  :global .@{ant-prefix}-modal-content {
    padding: 0;
    background-color: @color-bg-base;
    overflow: hidden;
  }

  .cover-pic-form-item {
    margin-bottom: 0;
  }
}

.crop-modal:global(.@{ant-prefix}-modal) {
  border-radius: 10px;
  background: @background-system-frame-floatpanel;

  :global .@{ant-prefix}-modal-content {
    padding: 16px;

    :global .@{ant-prefix}-modal-header {
      margin-bottom: 16px;
    }

    :global .@{ant-prefix}-modal-title {
      font-size: @text-14;
      font-weight: 600;
      line-height: 20px;
    }
  }

  .crop-area {
    width: 328px;
    height: 328px;
    position: relative;
  }

  .tips {
    color: @content-system-tertiary;
    font-size: @text-12;
    font-weight: 400;
    line-height: 16px;
    margin: 12px 0;
    text-align: center;
  }
}

.publish-container {
  display: flex;
  min-height: 520px;

  &-preview {
    flex: 1.6;
    padding: 0;
    max-width: 674px;
    background-color: @color-bg-layout;
    border-right: 1px solid @stroke-system-separator;
  }

  &-form {
    position: relative;
    flex: 1;
    padding: @size-lg;
    background-color: @color-white;

    label:global(.ant-form-item-required)::before {
      color: @content-system-primary !important;
    }
  }

  &-form-actions {
    display: flex;
    position: absolute;
    bottom: @size-lg;
    width: calc(100% - @size-xxl);

    .confirm-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: @size-xl;
    }

    :global .@{ant-prefix}-btn {
      flex: 1;

      &:not(:first-child) {
        margin-left: @size-sm;
      }
    }
  }
}

.cover-tips {
  margin-top: 12px;
  color: @content-system-primary;
  font-size: 14px;
  display: flex;
  align-items: center;

  svg {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    color: @background-btn-ai;
  }
}

.preview {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  padding: @size-lg @size-lg 12px;

  :global {
    .@{ant-prefix}-image {
      width: 100%;
      height: 472px;
      overflow: hidden;
      border-radius: @size-xs;
      display: flex;

      .@{ant-prefix}-image-img {
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .upload-btn {
    position: absolute;
    bottom: 58px;
    left: 36px;

    .change-img-button {
      border-radius: 8px;
      background: rgba(0, 0, 0, 0.5) !important;
      color: @base-white-opacity-100;
      height: 28px !important;
      line-height: 16px !important;
      border: none;

      &:hover {
        background: rgba(0, 0, 0, 0.5) !important;
        color: @base-white-opacity-100 !important;
      }
    }
  }

  .upload {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    border: 1px dashed @stroke-system-border-overlay;
    background-image: url('~@/assets/images/upload-placeholder.png');
    background-repeat: no-repeat;
    background-position: center;
    height: 472px !important;
    width: 100% !important;

    &.error {
      border-color: @color-error;
    }

    &-tips {
      color: @content-system-tertiary;
      font-size: @text-12;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
    }
  }
}

.checkbox-group:global(.ant-checkbox-group) {
  display: flex !important;
  margin-bottom: 43px !important;
  flex-wrap: wrap;

  label {
    margin-right: 8px !important;
    margin-bottom: 8px !important;
    margin-left: 0px !important;
  }

  :global(.ant-checkbox) {
    display: none;

    &:global(.ant-checkbox-checked) + span {
      border-color: @stroke-web-tag-selected;
      background: @background-btn-secondary;
      color: @content-web-tag-selected;
    }

    & + span {
      display: flex;
      height: 26px;
      padding: 0px 14px;
      justify-content: center;
      align-items: center;
      border-radius: 6px;
      border: 1px solid @stroke-btn-secondary;
      background: @background-btn-secondary;

      color: @content-btn-secondary;
      font-size: @text-12;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      transition: color 0.3s, background 0.3s, border-color 0.3s;
    }
  }
}

.name-text-area {
  height: calc(@size-xl * 2);
  resize: none;
}

.description-text-area {
  height: calc(@size-xxl * 3);
  resize: none;
}
