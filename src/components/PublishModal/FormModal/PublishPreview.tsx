import { Form, Image, Space } from 'antd';
import { Button, Loading } from '@/components';

import styles from './index.module.less';
import { CommonUpload, CommonUploadProps } from '@/components/CommonUpload';
import { PictureBold, InfoCircleBoldFill } from '@meitu/candy-icons';
import classNames from 'classnames';

interface PublishPreviewProps {
  onChange?: (value: string, oldValue: string | undefined) => void;
  value?: string;
}

export function PublishPreview({ onChange, value }: PublishPreviewProps) {
  const { status } = Form.Item.useStatus();

  const customUploadedToast: CommonUploadProps['customUploadedToast'] = ({
    successTotal,
    reviewErrorTotal
  }) => {
    if (successTotal) return '';

    if (reviewErrorTotal) return '请重新上传合规的图片';
  };

  return (
    // TODO:LYZ 增加校验
    <div className={classNames(styles.preview)}>
      {value && (
        <>
          <Image
            src={value}
            preview={{ visible: false, mask: null }}
            placeholder={<Loading />}
          />

          <CommonUpload
            type="select"
            limit={5}
            customUploadedToast={customUploadedToast}
            className={styles.uploadBtn}
            onFinish={(img) => {
              if (!img[0]) return;
              onChange?.(img[0].previewUrl, value);
            }}
          >
            <Button
              ghost
              size="small"
              className={styles.changeImgButton}
              icon={<PictureBold />}
              type="default"
            >
              更换封面
            </Button>
          </CommonUpload>

          <div className={styles.coverTips}>
            <InfoCircleBoldFill />
            <span>请使用此模型生成的效果图作为封面，充分体现模型特色</span>
          </div>
        </>
      )}

      {!value && (
        <CommonUpload
          limit={5}
          adaptive
          customUploadedToast={customUploadedToast}
          className={classNames(styles.upload, {
            [styles.error]: status === 'error'
          })}
          onFinish={(img) => {
            if (!img[0]) return;
            onChange?.(img[0].previewUrl, value);
          }}
        >
          <Space direction="vertical" size={4}>
            <Button type="primary" icon={<PictureBold />}>
              设置封面
            </Button>
            <div className={styles.coverTips}>
              <InfoCircleBoldFill />
              <span>请使用此模型生成的效果图作为封面，充分体现模型特色</span>
            </div>
            <span className={styles.uploadTips}>
              图片最大不超过5mb，jpg/png格式
            </span>
          </Space>
        </CommonUpload>
      )}
    </div>
  );
}
