import { App, Checkbox, Form, Input, Spin } from 'antd';
import { usePublishModel } from '../store';
import styles from './index.module.less';
import { PublishPreview } from './PublishPreview';
import { ModelStatus, TrainingPublishParams } from '@/api/types';
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState
} from 'react';
import classNames from 'classnames';
import { Button, Modal, TextArea } from '@/components';
import { trackPublishedClickEvent } from '@/containers/Personal/PersonalModels/fixturesTrack';
import { publishTrainingModel, updateTrainingModel } from '@/api/training';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useReUpload } from './useReUpload';
import { trackEvent } from '@/services';
import { delay } from 'lodash';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { AppModuleParam } from '@/types';
import { useCustomVerify, VerifyType } from '@/hooks/useCustomVerify';
import { AxiosError } from 'axios';

export interface FormModalProps {
  hidden: boolean;
  refetchList: () => void;
  source: AppModuleParam;
}
export interface FormModalRefProps {
  setCoverField: (cover: string) => void;
}

export const FormModal = forwardRef<FormModalRefProps, FormModalProps>(
  ({ hidden, refetchList, source }, ref) => {
    const { isOpen, go2Crop, reset, item, isFetchConfigLoading, config } =
      usePublishModel();
    const [form] = Form.useForm<TrainingPublishParams>();
    const { message } = App.useApp();
    const { fixturesCover } = useReUpload();
    const [isLoading, setIsLoading] = useState(false);
    const { updateMeiDouBalance } = useMeiDouBalance();

    const {
      setTitleVerify,
      setDescriptionVerify,
      descriptionVerify,
      titleVerify,
      resetVerify
    } = useCustomVerify();

    const { modal } = App.useApp();

    // 是否编辑
    const isUpdate =
      item?.status === ModelStatus.AUDITING ||
      item?.status === ModelStatus.PUBLISHED;

    const options = useMemo(() => {
      if (!config?.publishTags?.length) return [];

      return config?.publishTags.map((item) => ({
        label: item.tagName,
        value: item.tagId
      }));
    }, [config?.publishTags]);

    const confirmUpdate = (formValues: TrainingPublishParams) => {
      modal.confirm({
        title: '确认提交修改的信息吗？',
        icon: <ExclamationCircleOutlined />,
        content: `提交后会重新进入风格模型审核流程，每个模型最多修改3次，确认修改并提交吗？`,
        okText: '确认',
        okButtonProps: {
          loading: false
        },
        cancelText: '取消',
        onOk: async () => {
          onSubmit(formValues);
        }
      });
    };

    const verifyErrorHandler = (code?: string) => {
      switch (code) {
        case VerifyType.TITLE:
          setTitleVerify('请调整风格模型名称');
          setDescriptionVerify('');
          break;
        case VerifyType.DESCRIPTION:
          setDescriptionVerify('请调整模型介绍');
          setTitleVerify('');
          break;
        case VerifyType.DESCRIPTION_AND_TITLE:
          setTitleVerify('请调整风格模型名称');
          setDescriptionVerify('请调整模型介绍');
          break;
      }
    };

    const onSubmit = async (formValues: TrainingPublishParams) => {
      if (!item?.id || isLoading) return;

      trackPublishedClickEvent(item, formValues, source);

      try {
        setIsLoading(true);
        /**
         *  重新上传， 后端说云处理的图片加签时没法用
         */
        formValues.coverPic = await fixturesCover(formValues.coverPic!);

        // 区分发布、编辑
        const { result, showAwardTip, awardTipTitle } = isUpdate
          ? await updateTrainingModel(formValues)
          : await publishTrainingModel(formValues);

        if (result) {
          onClose();
          refetchList();
          message.success(showAwardTip ? awardTipTitle : '已提交发布信息');
          showAwardTip && updateMeiDouBalance();
        } else {
          message.error('发布失败');
        }
      } catch (err) {
        const { code } = err as AxiosError;
        verifyErrorHandler(code);
        defaultErrorHandler(err);
      } finally {
        setIsLoading(false);
      }
    };

    const onClose = () => {
      reset();
      resetVerify();
      // 优化体验，关闭后才清除表单，放置闪烁
      delay(form.resetFields, 450);
    };

    useImperativeHandle(
      ref,
      () => ({
        setCoverField(coverPic: string) {
          form.setFieldsValue({ coverPic });
        }
      }),
      [form]
    );

    useEffect(() => {
      if (!item) return;

      const cover = item.coverPic ?? item.images[0];

      const tags = config?.publishTags
        .filter((tag) => tag.checked)
        .map((tag) => tag.tagId);

      isUpdate
        ? form.setFieldsValue({
            name: item?.name,
            id: item?.id,
            desc: item.desc,
            tags,
            coverPic: cover
          })
        : form.setFieldsValue({ name: item?.name, id: item?.id });
    }, [config?.publishTags, form, isUpdate, item]);

    return (
      <Modal
        centered
        open={isOpen}
        width={896}
        maskClosable={false}
        footer={null}
        className={classNames(styles.publishModal, {
          // TODO 暂时样式处理， 后期增加StepModal组件后移除
          [styles.hidden]: hidden
        })}
        destroyOnClose
        onCancel={onClose}
        getContainer={document.getElementById('root')!}
      >
        <Spin spinning={isFetchConfigLoading}>
          <Form
            layout="vertical"
            form={form}
            onFinish={(value) => {
              isUpdate ? confirmUpdate(value) : onSubmit(value);
            }}
            onFinishFailed={(errorInfo) => {
              if (errorInfo.errorFields[0].name[0] === 'coverPic') {
                message.error('请设置封面图后发布');
              }
            }}
          >
            <div className={styles.publishContainer}>
              <div className={styles.publishContainerPreview}>
                <Form.Item
                  name="coverPic"
                  className={styles.coverPicFormItem}
                  rules={[{ required: true, message: '' }]}
                >
                  <PublishPreview
                    onChange={(cover, prevCover) => {
                      if (!cover) return;
                      go2Crop(cover, prevCover ?? '');
                    }}
                  />
                </Form.Item>
              </div>

              <div className={styles.publishContainerForm}>
                <Form.Item name="id" hidden noStyle>
                  <Input hidden />
                </Form.Item>

                <Form.Item
                  label="风格模型名称"
                  name="name"
                  validateStatus={titleVerify ? 'error' : ''}
                  help={titleVerify}
                  rules={[{ required: true, message: '请填写模型名称' }]}
                >
                  <TextArea
                    placeholder="请填写模型名称"
                    allowClear
                    maxLength={20}
                    showCount
                    className={styles.nameTextArea}
                  />
                </Form.Item>
                <Form.Item
                  label="模型介绍"
                  name="desc"
                  validateStatus={descriptionVerify ? 'error' : ''}
                  help={descriptionVerify}
                >
                  <TextArea
                    placeholder="请填写模型介绍"
                    allowClear
                    maxLength={100}
                    showCount
                    className={styles.descriptionTextArea}
                  />
                </Form.Item>

                {!!options.length && (
                  <Form.Item label="模型类型" name="tags">
                    <Checkbox.Group
                      rootClassName={styles.checkboxGroup}
                      options={options}
                      onChange={(checkedList) => {
                        const names = checkedList.map((id) => {
                          const item = config?.publishTags.find(
                            (item) => item.tagId === id
                          );

                          return item?.tagName;
                        });

                        trackEvent('my_model_tag_btn_click', {
                          tagsInfo: names.join(',')
                        });
                      }}
                    />
                  </Form.Item>
                )}

                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) =>
                    prevValues.name !== currentValues.name
                  }
                >
                  {({ getFieldValue }) => (
                    <div className={styles.publishContainerFormActions}>
                      <Button
                        htmlType="submit"
                        loading={isLoading}
                        disabled={!getFieldValue('name')}
                        className={styles.confirmBtn}
                      >
                        确认发布
                      </Button>
                    </div>
                  )}
                </Form.Item>
              </div>
            </div>
          </Form>
        </Spin>
      </Modal>
    );
  }
);
