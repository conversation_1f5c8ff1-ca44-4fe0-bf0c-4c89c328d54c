import { BBoxType } from '@/components';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { usePublishModel } from '../store';
import { Spin } from 'antd';
import { Loading } from '@/components';
import Cropper from 'react-easy-crop';
import { delay } from 'lodash';

export type CropType = {
  back: (src: string) => void;
};

export type CropRef = {
  cropResult: BBoxType | null;
};

export const Crop = forwardRef<CropRef, CropType>((props, ref) => {
  const { src } = usePublishModel();

  const [zoom, setZoom] = useState(1);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [cropResult, setCropResult] = useState<BBoxType | null>(null);
  const [loading, setLoading] = useState(true);

  // TODO Cropper有bug, 裁剪区域大小会有问题
  // 延迟渲染后似乎正常， 待观察
  useEffect(() => {
    delay(() => {
      setLoading(false);
    }, 1000);
  }, []);

  useImperativeHandle(ref, () => ({
    cropResult
  }));

  if (loading) return <Spin spinning indicator={<Loading />} />;

  return (
    <Cropper
      key={src}
      image={src}
      crop={crop}
      zoom={zoom}
      aspect={1 / 1}
      onCropChange={setCrop}
      onCropComplete={(_, croppedAreaPixels) => {
        setCropResult(croppedAreaPixels);
      }}
      onZoomChange={setZoom}
    />
  );
});
