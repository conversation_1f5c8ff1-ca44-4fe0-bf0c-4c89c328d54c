import { Col, Row } from 'antd';
import styles from './index.module.less';
import { usePublishModel } from '../store';
import { useCallback, useRef, useState } from 'react';
import { Button, Modal } from '@/components';
import { Crop, CropRef } from './Crop';
import getCroppedImg from '@/utils/cropImage';

export interface CropModalProps {
  onSave: (cover: string) => void;
}

export const CropModal = ({ onSave }: CropModalProps) => {
  const { isShowCropModal, go2Form, tempSrc, src } = usePublishModel();
  const ref = useRef<CropRef>(null);
  const [loading, setLoading] = useState(false);

  const back = useCallback(
    (src: string) => {
      onSave(src);
      go2Form();
    },
    [go2Form, onSave]
  );

  return (
    <Modal
      centered
      open={isShowCropModal}
      width={360}
      mask={false}
      maskClosable={false}
      footer={null}
      closable={false}
      getContainer={document.getElementById('root')!}
      title="上传图片"
      className={styles.cropModal}
      destroyOnClose
    >
      <div className={styles.cropArea}>
        <Crop ref={ref} back={back} />
      </div>

      <div className={styles.tips}>按住鼠标左键拖动调整图片</div>
      <Row gutter={8}>
        <Col span={12}>
          <Button
            type="default"
            block
            onClick={() => {
              back(tempSrc);
            }}
          >
            返回
          </Button>
        </Col>

        <Col span={12}>
          <Button
            block
            type="primary"
            loading={loading}
            onClick={async () => {
              setLoading(true);
              try {
                if (!ref.current?.cropResult) return;
                const img = await getCroppedImg(
                  src,
                  ref.current?.cropResult,
                  0
                );

                back(img ?? '');
              } catch (error) {
                console.log(error);
              } finally {
                setLoading(false);
              }
            }}
          >
            保存为封面
          </Button>
        </Col>
      </Row>
    </Modal>
  );
};
