import { useRef } from 'react';
import { CropModal } from './CropModal';
import { FormModal, FormModalRefProps } from './FormModal';
import { usePublishModel } from './store';
import { AppModuleParam } from '@/types';

export type PublishModalType = {
  refetchList: () => void;
  // 页面来源
  source: AppModuleParam;
};

export const PublishModal = ({ refetchList, source }: PublishModalType) => {
  const { isShowCropModal } = usePublishModel();

  const ref = useRef<FormModalRefProps>(null);

  return (
    <>
      {/* TODO: 暂时样式处理多个modal的场景， 后期增加StepModal组件后移除 */}
      <FormModal
        hidden={isShowCropModal}
        ref={ref}
        refetchList={refetchList}
        source={source}
      />

      <CropModal
        onSave={(cover) => {
          ref.current?.setCoverField(cover);
        }}
      />
    </>
  );
};
