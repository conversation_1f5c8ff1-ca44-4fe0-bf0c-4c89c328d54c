import type { SizeSelectorValue } from '.';

import { SliderInput } from '@/components';

import styles from './styles.module.less';
import { STEP, MAX, MIN } from './constants';

interface SlidersProps {
  value: SizeSelectorValue['size'];
  onChange: (value: SizeSelectorValue['size']) => void;
}

function getClosestMultiple(value: number, multiplier: number = 8) {
  let closestMultiple = Math.round(value / multiplier) * multiplier;
  closestMultiple = Math.max(closestMultiple, 64);
  closestMultiple = Math.min(closestMultiple, 2048);
  return closestMultiple;
}

export default function Sliders(props: SlidersProps) {
  const [width, height] = props.value;

  return (
    <section className={styles.slider}>
      <SliderInput
        title="宽度"
        className={styles.sliderWidth}
        step={STEP}
        min={MIN}
        max={MAX}
        value={width}
        onChange={(value) =>
          props.onChange([getClosestMultiple(value), height])
        }
      />
      <SliderInput
        title="高度"
        step={STEP}
        min={MIN}
        max={MAX}
        value={height}
        onChange={(value) => props.onChange([width, getClosestMultiple(value)])}
      />
    </section>
  );
}
