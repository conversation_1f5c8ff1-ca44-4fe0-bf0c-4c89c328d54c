import type { CanvasSize } from '@/types';
import type { RefSelectProps } from 'antd';

import { Select } from 'antd';
import {
  useRef,
  useImperativeHandle,
  useEffect,
  useCallback,
  forwardRef
} from 'react';
import { useImageSize } from '@/hooks';

import styles from './styles.module.less';
import { STEP, adaptationOptions } from './constants';

interface AdaptSelectRef {
  focus: () => void;
}

interface AdaptSelectProps {
  /** 图片尺寸 - 回填时必传 */
  value?: CanvasSize;

  /** 图片路径 */
  imageUrl?: string;

  onChange: (value: CanvasSize) => void;
}

/** 读取最长边 */
const getLongestEdge = (size: CanvasSize) => Math.max(...size);

const getClosest = (edge: number) => Math.round(edge / STEP) * STEP;

export const useGetSize = (url: string, size?: CanvasSize) => {
  const [width, height] = useImageSize(url);

  return useCallback(
    (longestEdge: number) => {
      const curWidth = width ?? size?.[0];
      const curHeight = height ?? size?.[1];

      if (!curWidth || !curHeight) {
        return [longestEdge, longestEdge] as [number, number];
      }

      return curWidth > curHeight
        ? ([longestEdge, getClosest((longestEdge / curWidth) * curHeight)] as [
            number,
            number
          ])
        : ([getClosest((longestEdge / curHeight) * curWidth), longestEdge] as [
            number,
            number
          ]);
    },
    [width, height, size]
  );
};

const AdaptationSelect = forwardRef<AdaptSelectRef, AdaptSelectProps>(function (
  props,
  ref
) {
  const { value: valueFromProps, imageUrl } = props;
  const selectRef = useRef<RefSelectProps>(null);
  const getSize = useGetSize(imageUrl ?? '');
  const value = valueFromProps ? getLongestEdge(valueFromProps) : 768;

  useImperativeHandle(ref, () => ({
    focus() {
      selectRef.current?.focus();
    }
  }));

  useEffect(
    () => {
      if (imageUrl) {
        props.onChange(getSize(value) as CanvasSize);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [getSize]
  );

  const onChange = (value: number) => {
    props.onChange(getSize(value) as CanvasSize);
  };

  return (
    <Select
      ref={selectRef}
      value={value}
      options={adaptationOptions}
      className={styles.selector}
      onChange={onChange}
    />
  );
});

export { AdaptationSelect };
