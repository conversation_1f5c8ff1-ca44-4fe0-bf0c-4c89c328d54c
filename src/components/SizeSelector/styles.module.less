@import '~@/styles/variables.less';

.space {
  width: 100%;

  :global .@{ant-prefix}-space-item {
    flex: 1;
  }

  .selector {
    width: 100%;
  }

  &:not(:last-child) {
    margin-bottom: @size-sm;
  }
}

.slider {
  color: @color-text-secondary;

  .slider-width {
    margin-bottom: @size-sm;
  }

  &-content {
    display: flex;
    align-items: center;
    width: 100%;

    :global .@{ant-prefix}-slider {
      flex: 1;
    }

    &-input {
      display: block;
      width: calc(2 * @size-xl);
      height: @size-lg;
      margin-left: @size-sm;
    }
  }
}

.selection {
  display: flex;
  gap: 6px;
}
