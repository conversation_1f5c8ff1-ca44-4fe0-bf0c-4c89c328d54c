import {
  Edit,
  Picture,
  Ratio11,
  <PERSON>io169,
  <PERSON><PERSON>23,
  <PERSON><PERSON>32,
  <PERSON><PERSON>34,
  <PERSON><PERSON>43,
  <PERSON>io916
} from '@meitu/candy-icons';
import { getSizeOptions, getAdaptationOptions } from './utils';
import {
  CandyIconProps,
  IconBaseComponent
} from '@meitu/candy-icons/lib/components/CandyIcon';

/** 比例枚举 */
export enum Proportion {
  ONE_TO_ONE = '1:1',
  TWO_TO_THREE = '2:3',
  THREE_TO_FOUR = '3:4',
  NINE_TO_SIXTEEN = '9:16',
  THREE_TO_TWO = '3:2',
  FOUR_TO_THREE = '4:3',
  SIXTEEN_TO_NINE = '16:9',
  CUSTOM = 'custom',
  ADAPTION = 'self-adaption'
}

/** 比例集合 */
export const proportions = [
  Proportion.ONE_TO_ONE,
  Proportion.TWO_TO_THREE,
  Proportion.THREE_TO_FOUR,
  Proportion.NINE_TO_SIXTEEN,
  Proportion.THREE_TO_TWO,
  Proportion.FOUR_TO_THREE,
  Proportion.SIXTEEN_TO_NINE
];

const proportionIconsMap = new Map<
  Proportion,
  IconBaseComponent<CandyIconProps>
>([
  [Proportion.ONE_TO_ONE, Ratio11],
  [Proportion.TWO_TO_THREE, Ratio23],
  [Proportion.THREE_TO_FOUR, Ratio34],
  [Proportion.NINE_TO_SIXTEEN, Ratio916],
  [Proportion.THREE_TO_TWO, Ratio32],
  [Proportion.FOUR_TO_THREE, Ratio43],
  [Proportion.SIXTEEN_TO_NINE, Ratio169]
]);

/** 固定比例选项 */
export const proportionOptions = proportions.map((proportion) => ({
  value: proportion,
  label: proportion,
  Icon: proportionIconsMap.get(proportion)!
}));

/** 自定义选项 */
export const customOption = {
  value: Proportion.CUSTOM,
  label: '自定义',
  Icon: Edit
};

/** 自适应选项 */
export const adaptationOption = {
  value: Proportion.ADAPTION,
  label: '自适应',
  Icon: Picture
};

/** 默认比例 */
export const DEFAULT_RATIO = Proportion.ONE_TO_ONE;

/** 尺寸 - 间隔 */
export const STEP = 8;

/** 尺寸 - 最大值 */
export const MAX = 1024;

/** 尺寸 - 最小值 */
export const MIN = 64;

/** 尺寸选项 映射集合 */
export const sizeOptionsMap = new Map([
  [Proportion.ONE_TO_ONE, getSizeOptions([512, 1024], Proportion.ONE_TO_ONE)],
  [
    Proportion.TWO_TO_THREE,
    getSizeOptions([512, 768], Proportion.TWO_TO_THREE)
  ],
  [
    Proportion.THREE_TO_FOUR,
    getSizeOptions([576, 768], Proportion.THREE_TO_FOUR)
  ],
  [
    Proportion.NINE_TO_SIXTEEN,
    getSizeOptions([504, 720], Proportion.NINE_TO_SIXTEEN)
  ],
  [
    Proportion.THREE_TO_TWO,
    getSizeOptions([768, 1152], Proportion.THREE_TO_TWO)
  ],
  [
    Proportion.FOUR_TO_THREE,
    getSizeOptions([768, 1024], Proportion.FOUR_TO_THREE)
  ],
  [
    Proportion.SIXTEEN_TO_NINE,
    getSizeOptions([896, 1280], Proportion.SIXTEEN_TO_NINE)
  ]
]);

/** 自适应尺寸没有原图时的默认值 */
export const defaultAdaptationSize: [number, number] = [768, 768];

/** 自适应尺寸选项 */
export const adaptationOptions = getAdaptationOptions([768, 1024]);
