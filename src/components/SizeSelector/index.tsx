import type { RefSelectProps } from 'antd';

import { Space, Select, Tooltip } from 'antd';
import Sliders from './Sliders';
import { AdaptationSelect, useGetSize } from './AdaptationSelect';
import { QuestionMarkCircleBold } from '@meitu/candy-icons';
import { useState, useRef, useEffect } from 'react';
import { getSizeWithRatio, generateSize, getMaxSize } from './utils';
import { Collapse } from '@/components';

import styles from './styles.module.less';
import {
  defaultAdaptationSize,
  proportionOptions,
  sizeOptionsMap,
  adaptationOption,
  customOption,
  DEFAULT_RATIO,
  Proportion,
  adaptationOptions
} from './constants';
import { isEqual } from 'lodash';

export interface SizeOption {
  /** 宽度 */
  value: number;
  label: string;
}

export interface SizeSelectorValue {
  /** 画面尺寸 */
  size: [number, number];
  /** 画布比例 */
  proportion: Proportion;
}

type SizeSelectorType = 'custom' | 'adaption';

/**
 * 选择好预设的比例后，需要指定预设的比例
 * 该枚举用来表示怎么选择预设的比例
 */
export enum PresetSizeMode {
  /** 通过下拉菜店选择预设尺寸 （默认值）*/
  SELECT_PRESET_SIZE = 'select',

  /** 自动选择预设比例对应的最大预设尺寸 */
  AUTO_MAX_PRESET_SIZE = 'auto-max'
}

export interface SizeSelectorProps {
  /** props */
  value?: SizeSelectorValue;
  imageUrl?: string;
  type: SizeSelectorType;
  presetSizeMode?: PresetSizeMode;

  /** events */
  onChange?: (value: SizeSelectorValue) => void;
}

// HACK 默认512x512，未刷新页面从别的比例切回自定义按照之前的设置参数，刷新后按照默认512 @lzy5
let customSize: SizeSelectorValue['size'] = [512, 512];

function getProportionOptions(type: SizeSelectorType) {
  const options =
    type === 'adaption'
      ? // 自适应时将自定义排序至后面
        [adaptationOption].concat(proportionOptions).concat(customOption)
      : [customOption].concat(proportionOptions);

  return options.map(({ label, value, Icon }) => {
    return {
      value,
      label: (
        <div className={styles.selection}>
          <Icon />
          {label}
        </div>
      )
    };
  });
}

/**
 * 获取proportion对应的尺寸的最大值
 * @param proportion
 * @returns
 */
function getMaxSizeByProportion(proportion: Proportion) {
  const options =
    proportion === Proportion.ADAPTION
      ? adaptationOptions
      : proportion === Proportion.CUSTOM
      ? undefined
      : sizeOptionsMap.get(proportion);

  return options && getMaxSize(options);
}

export function SizeSelector(props: SizeSelectorProps) {
  const { value, imageUrl, type, onChange } = props;
  const { presetSizeMode = PresetSizeMode.SELECT_PRESET_SIZE } = props;
  const proportion = value?.proportion ?? DEFAULT_RATIO;

  /** 画面尺寸选择框 */
  const sizeSelectorRef = useRef<RefSelectProps>(null);

  const [size, setSize] = useState<[number, number]>(() => generateSize(value));

  useEffect(() => {
    if (value?.proportion === Proportion.CUSTOM) {
      customSize = value.size;
    }

    // 通过form的静态方法设置value时， 手动触发size的更新
    setSize((prev) => {
      const newValue = generateSize(value);
      if (isEqual(newValue, prev)) return prev;
      return newValue;
    });
  }, [value]);

  const getAdaptionSize = useGetSize(imageUrl ?? '', value?.size);

  useEffect(() => {
    // 当设置为自动关联到最大预设尺寸时
    // 需要将value中的size与value中的proportion进行同步
    // 当数据回填时，由于size可能不是proportion对应的最大尺寸，进行同步
    if (!value || presetSizeMode !== PresetSizeMode.AUTO_MAX_PRESET_SIZE) {
      return;
    }

    // 获取proportion对应的最大尺寸
    const maxSize = getMaxSizeByProportion(value.proportion);

    if (!maxSize) {
      return;
    }

    // 计算尺寸
    let size;
    switch (value.proportion) {
      case Proportion.CUSTOM:
        return;

      case Proportion.ADAPTION: {
        size = getAdaptionSize(maxSize.value);
        break;
      }
      default: {
        size = getSizeWithRatio(value.proportion.split(':'))(maxSize.value);
        break;
      }
    }

    // 如果当前尺寸为最大尺寸 则不需要再次同步
    if (isEqual(size, value.size)) {
      return;
    }

    setSize(size);
    onChange?.({
      size,
      proportion: value.proportion
    });
  }, [value, presetSizeMode, getAdaptionSize, onChange]);

  function onRatioChange(proportion: Proportion) {
    switch (proportion) {
      case Proportion.CUSTOM:
        onSlidersChange(proportion, customSize);
        break;
      case Proportion.ADAPTION:
        onSizeChange(proportion, defaultAdaptationSize);
        break;
      default:
        const sizeOptions = sizeOptionsMap.get(proportion);
        if (Array.isArray(sizeOptions)) {
          // 默认选中第一项
          let selectSize = sizeOptions[0] as SizeOption | undefined;

          // 虽然可以依赖前面的Effect进行同步 但这样会导致每次切换比例都会渲染两次
          if (presetSizeMode === PresetSizeMode.AUTO_MAX_PRESET_SIZE) {
            selectSize = getMaxSizeByProportion(proportion);
          }
          onPresetSizeChange(proportion, selectSize?.value);
        }
        break;
    }

    setTimeout(() => {
      sizeSelectorRef.current?.focus();
    });
  }

  function onSizeChange(
    proportion: Proportion,
    size: SizeSelectorValue['size']
  ) {
    setSize(size);
    onChange?.({
      size,
      proportion
    });
  }

  function onPresetSizeChange(
    proportion: Exclude<Proportion, Proportion.CUSTOM>,
    width?: number
  ) {
    if (!width) {
      return;
    }
    const size = getSizeWithRatio(proportion.split(':'))(width);

    onSizeChange(proportion, size);
  }

  function onSlidersChange(
    proportion: Proportion,
    size: SizeSelectorValue['size']
  ) {
    customSize = size;
    onSizeChange(proportion, size);
  }

  // 自定义可见
  const slidersVisible = Proportion.CUSTOM === proportion;
  // 自适应可见
  const adaptationVisible =
    Proportion.ADAPTION === proportion &&
    presetSizeMode === PresetSizeMode.SELECT_PRESET_SIZE;
  // 预设尺寸可见
  const presetSizeVisible =
    !slidersVisible &&
    !adaptationVisible &&
    presetSizeMode === PresetSizeMode.SELECT_PRESET_SIZE;

  return (
    <Collapse.Panel.Section
      title={
        <>
          画面比例
          <Tooltip title="选择需要的比例与尺寸，尺寸越大耗时越久。">
            <QuestionMarkCircleBold />
          </Tooltip>
        </>
      }
    >
      <Space className={styles.space}>
        <Select
          value={proportion}
          options={getProportionOptions(type)}
          className={styles.selector}
          onChange={onRatioChange}
        />
        {presetSizeVisible && (
          <Select
            ref={sizeSelectorRef}
            value={size[0]}
            options={sizeOptionsMap.get(proportion)}
            className={styles.selector}
            onChange={onPresetSizeChange.bind(null, proportion)}
          />
        )}

        {adaptationVisible && (
          <AdaptationSelect
            ref={sizeSelectorRef}
            value={size}
            imageUrl={imageUrl}
            onChange={onSizeChange.bind(null, proportion)}
          />
        )}
      </Space>
      {slidersVisible && (
        <Sliders
          value={size}
          onChange={onSlidersChange.bind(null, proportion)}
        />
      )}
    </Collapse.Panel.Section>
  );
}

SizeSelector.defaultProps = {
  type: 'custom'
};
