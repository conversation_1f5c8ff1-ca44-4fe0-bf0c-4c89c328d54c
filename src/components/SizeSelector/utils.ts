import type { SizeSelectorValue, SizeOption } from '.';

import { Proportion } from './constants';
import _ from 'lodash';

import { sizeOptionsMap, DEFAULT_RATIO } from './constants';

/**
 * 根据比例生成尺寸 [width, height]
 * @param {Proportion} ratio
 * @returns {(number) => [number, number]}
 */
export function getSizeWithRatio([widthRatio, heightRatio]: (
  | number
  | string
)[]): (width: number) => [number, number] {
  return (width) => [width, (width / +widthRatio) * +heightRatio];
}

/**
 * 生成尺寸(自定义/比例值)
 * @param value
 * @returns
 */
export function generateSize(value?: SizeSelectorValue): [number, number] {
  const { size = [], proportion } = value ?? {};
  const [width, height] = size;
  // 可变比例
  const isVariableRatio =
    proportion === Proportion.CUSTOM || proportion === Proportion.ADAPTION;

  if (isVariableRatio && width && height) {
    return [width, height];
  }

  return generateSizeByRatio(
    !proportion || isVariableRatio ? DEFAULT_RATIO : proportion,
    width,
    height
  ) as [number, number];
}

/**
 * 生成尺寸(宽/高未知)
 * @param ratio
 * @param width
 * @param height
 * @returns
 */
function generateSizeByRatio(
  ratio: Exclude<Proportion, Proportion.CUSTOM | Proportion.ADAPTION>,
  width?: number,
  height?: number
) {
  const defaultWidth = (sizeOptionsMap.get(ratio) as SizeOption[])[0].value;
  const getDefaultSize = getSizeWithRatio(ratio.split(':'));

  const [widthRatio, heightRatio] = ratio.split(':');
  // 宽度已知
  if (width !== undefined) {
    return getSizeWithRatio([widthRatio, heightRatio])(width);
  }

  // 宽度未知，高度已知
  if (height !== undefined) {
    return getSizeWithRatio([heightRatio, widthRatio])(height).reverse();
  }

  // 高度宽度未知
  return getDefaultSize(defaultWidth);
}

/**
 * 生成尺寸选项
 * @param widths
 * @param ratio
 * @returns {SelectProps['options']}
 */
export function getSizeOptions(widths: number[], ratio: Proportion) {
  const [widthRatio, heightRatio] = ratio.split(':');
  const getSize = getSizeWithRatio([widthRatio, heightRatio]);

  return widths.reduce<SizeOption[]>((options, width) => {
    if (width % +widthRatio !== 0) {
      return options;
    }

    const size = getSize(width);

    // if (size[1] > MAX) {
    //   return options;
    // }

    options.push({
      value: width,
      label: size.join('x')
    });

    return options;
  }, []);
}

/**
 * 生成自适应尺寸选项
 * @param edges
 * @returns {SelectProps['options']}
 */
export function getAdaptationOptions(edges: number[]) {
  return edges.map((edge: number) => ({ label: `长边${edge}`, value: edge }));
}

/**
 * 根据起止间隔生成 宽度集合
 * @param {number} start
 * @param {number} end
 * @param {number}step
 * @returns {number[]}
 */
export function getWidths(start: number, end: number, step: number) {
  const times = (end - start) / step;

  return _.times(times, (time) => start + time * step);
}

/**
 * 获取尺寸sizeOptions中最大的尺寸
 * @param sizeOptions
 */
export function getMaxSize(sizeOptions: SizeOption[]) {
  function max(s1: SizeOption, s2: SizeOption) {
    return s1.value > s2.value ? s1 : s2;
  }

  return sizeOptions.reduce(max);
}
