import {
  FixedSizeGrid as Grid,
  GridChildComponentProps,
  FixedSizeGridProps as GridProps,
  areEqual
} from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import {
  type ReactElement,
  LegacyRef,
  ReactNode,
  forwardRef,
  memo
} from 'react';

const GUTTER_SIZE = 5;

export interface FixedSizeGridProps<T>
  extends Pick<GridProps, 'columnWidth' | 'rowHeight'> {
  dataSource: T[];
  renderItem(item: T, index: number): ReactNode;
  gutter?: number;
  rowKey?: (item: T, index: number) => string;
  gridRender?: (grid: ReactElement) => ReactElement;
}

const Cell = memo((props: GridChildComponentProps) => {
  const {
    rowIndex,
    columnIndex,
    style,
    data: { rowKey, dataSource, columnCount, gutter, renderItem }
  } = props;
  const index = rowIndex * columnCount + columnIndex;
  const dataItem = dataSource[index];
  if (!dataItem) return null;
  return (
    <div
      key={rowKey?.(dataItem, index) ?? index}
      style={{
        ...style,
        left: (Number(style?.left) ?? 0) + GUTTER_SIZE - 5,
        width: (Number(style?.width) ?? 0) - gutter,
        height: (Number(style?.height) ?? 0) - gutter
      }}
    >
      {renderItem(dataItem, index)}
    </div>
  );
}, areEqual);

const FixedSizeGridWithoutRef = <T extends unknown>(
  {
    columnWidth,
    dataSource,
    renderItem,
    rowHeight,
    gutter = 5,
    rowKey,
    gridRender
  }: FixedSizeGridProps<T>,
  ref: LegacyRef<Grid<any>> | undefined
) => {
  return (
    <AutoSizer>
      {({ height, width }) => {
        const columnCount = Math.min(
          Math.floor((width ?? 0) / (columnWidth - gutter)),
          dataSource.length
        );
        const rowCount = Math.ceil(dataSource.length / columnCount);
        const grid = (
          <Grid
            ref={ref}
            style={{ overflowX: 'hidden' }}
            columnCount={columnCount}
            columnWidth={columnWidth}
            rowHeight={rowHeight}
            height={height ?? 0}
            rowCount={rowCount}
            width={width ?? 0}
            overscanRowCount={4}
            itemData={{ columnCount, dataSource, gutter, renderItem, rowKey }}
          >
            {Cell}
          </Grid>
        );
        return gridRender?.(grid) ?? grid;
      }}
    </AutoSizer>
  );
};

export const FixedSizeGrid = forwardRef(FixedSizeGridWithoutRef) as <T>(
  props: FixedSizeGridProps<T> & { ref?: LegacyRef<Grid<any>> }
) => ReturnType<typeof FixedSizeGridWithoutRef>;
