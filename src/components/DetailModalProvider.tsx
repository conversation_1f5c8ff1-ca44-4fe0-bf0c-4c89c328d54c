import React, {
  createContext,
  useContext,
  useState,
  PropsWithChildren
} from 'react';
import { DraftType } from '@/api/types';

interface ModalType {
  id: string;
  taskCategory?: DraftType;
}

// 创建 Modal 上下文
export const DetailModalContext = createContext({
  addModal: (modal: ModalType) => {},
  removeModal: (modal: ModalType) => {},
  closeAllModals: () => {},
  modals: [] as ModalType[],
  sameModelId: ''
});

export const DetailModalProvider = ({ children }: PropsWithChildren) => {
  const [modals, setModals] = useState<Array<ModalType>>([]);
  const [sameModelId, setSameModelId] = useState('');

  // 关闭全部 Modal 的方法
  const closeAllModals = () => {
    setTimeout(() => {
      setModals([]);
      setSameModelId('');
    }, 300);
  };

  // 添加 Modal 到上下文中的方法
  const addModal = (modal: ModalType) => {
    setModals((prevModals) => [...prevModals, modal]);

    // 记录最新打开的模型id，防止相同的套娃
    if (modal.taskCategory === DraftType.MODEL) {
      setSameModelId(modal.id);
    }
  };

  // 移除 Modal 从上下文中的方法
  const removeModal = (modal: ModalType) => {
    setTimeout(() => {
      setModals((prevModals) =>
        prevModals.filter((item) => item.id !== modal.id)
      );
    }, 300);

    if (modal.id === sameModelId) {
      setSameModelId('');
    }
  };

  return (
    <DetailModalContext.Provider
      value={{ addModal, removeModal, closeAllModals, modals, sameModelId }}
    >
      {children}
    </DetailModalContext.Provider>
  );
};

export const useDetailModal = () => {
  return useContext(DetailModalContext);
};
