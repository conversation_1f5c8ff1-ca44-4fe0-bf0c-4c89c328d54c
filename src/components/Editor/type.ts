import type Konva from 'konva';
import {
  Shape,
  Image,
  Rect,
  Group,
  Transformer,
  Line,
  Text,
  Circle
} from 'react-konva';
import type { ComponentProps, CSSProperties } from 'react';
import { Html } from 'react-konva-utils';

export type KVShapeConfig = ComponentProps<typeof Shape> & {
  readonly type: 'shape';
  transformerProps?: KVTransformerConfig;
};

export type CursorEvent = 'mouseEnter' | 'mouseDown';

export type Cursor =
  | CSSProperties['cursor']
  | Partial<{ [Event in CursorEvent]: CSSProperties['cursor'] }>;

// Because it contains an index signature([x: string]), other properties will be lost when using Omit.
// So we use a hack method to first clear the index signature, then omit it, and finally add the index type.
export type KVImageConfig = Omit<
  RemoveIndexSignature<ComponentProps<typeof Image>>,
  'image'
> & {
  readonly type: 'image';
  src?: string;
  image?: Konva.ImageConfig['image'];
  transformerProps?: KVTransformerConfig;
  cursor?: Cursor;
  canCache?: boolean;

  [x: string]: any;
};
export type KVRectConfig = ComponentProps<typeof Rect> & {
  readonly type: 'rect';
  transformerProps?: KVTransformerConfig;
};

export type KVTextConfig = ComponentProps<typeof Text> & {
  readonly type: 'text';
  transformerProps?: KVTransformerConfig;
};
export type KVCircleConfig = ComponentProps<typeof Circle> & {
  readonly type: 'circle';
  transformerProps?: KVTransformerConfig;
};

export type KVGroupConfig = ComponentProps<typeof Group> & {
  readonly type: 'group';
  elements?: ShapeConfig[];
};

export type KVLineConfig = ComponentProps<typeof Line> & {
  readonly type: 'line';
  mode?: 'pen' | 'eraser';
};

export type KVHTmlConfig = ComponentProps<typeof Html> & {
  readonly type: 'html';
  id?: string;
};
export type KVTransformerConfig = ComponentProps<typeof Transformer>;

type GeometricShapes =
  // | KVShapeConfig
  | KVImageConfig
  | KVRectConfig
  | KVGroupConfig
  | KVHTmlConfig
  | KVLineConfig
  | KVCircleConfig
  | KVTextConfig;
// | KVTransformerConfig;

export type GeometricShapesTypes = GeometricShapes['type'];

export type ShapeConfig<T extends GeometricShapesTypes = GeometricShapesTypes> =
  T extends 'html'
    ? Extract<GeometricShapes, { type: T }>
    : Extract<GeometricShapes, { type: T }> & UpdateShapeType<T>;

export type UpdateShapeType<T extends GeometricShapesTypes> = {
  onUpdateShape?(conf: ShapeConfig<T>): void;

  isSelected?: boolean;
  onSelect?(id: string): void;
};

export type RemoveIndexSignature<T> = {
  [K in keyof T as string extends K
    ? never
    : number extends K
    ? never
    : K]: T[K];
};

export type Vector2d = {
  x: number;
  y: number;
};
