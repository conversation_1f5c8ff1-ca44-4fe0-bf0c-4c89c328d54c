import { RefObject, useEffect, useLayoutEffect, useRef } from 'react';
import { Subscriber } from './usePublishSubscribe';
import { GlobalCtxType } from './components/GlobalProvider';
import { ShapesCtxType } from './components/ShapesProvider';
import { EditorRefType } from './Editor';

export const useRefSubscription = <
  T extends {
    subscribe: (subscriber: Subscriber<P>) => Function;
  },
  P extends unknown
>(
  ref?: RefObject<T | null>,
  cb?: (store: P) => void
) => {
  const cbRef = useRef(cb);

  useEffect(() => {
    cbRef.current = cb;
  }, [cb]);

  useEffect(() => {
    if (!ref?.current) return;

    const unSubscribe = ref.current?.subscribe?.(cbRef.current ?? (() => {}));

    return () => {
      unSubscribe?.();
    };
  }, [ref]);
};

export default useRefSubscription;

export const useGlobalStoreRefSubscription = (
  ref: RefObject<EditorRefType | null>,
  cb?: (store: GlobalCtxType) => void
) => {
  const cbRef = useRef(cb);

  useEffect(() => {
    cbRef.current = cb;
  }, [cb]);

  useLayoutEffect(() => {
    if (!ref?.current || !ref.current?.globalRef?.current) return;

    const unSubscribe = ref.current?.globalRef?.current?.subscribe?.(
      cbRef.current ?? (() => {})
    );

    return () => {
      unSubscribe?.();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ref, ref.current]);
};

export const useShapesRefSubscription = (
  ref: RefObject<EditorRefType | null>,
  cb?: (store: ShapesCtxType) => void
) => {
  const cbRef = useRef(cb);

  useEffect(() => {
    cbRef.current = cb;
  }, [cb]);

  useLayoutEffect(() => {
    if (!ref?.current || !ref.current?.shapesRef?.current) return;

    const unSubscribe = ref.current?.shapesRef?.current?.subscribe?.(
      cbRef.current ?? (() => {})
    );

    return () => {
      unSubscribe?.();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ref, ref.current]);
};
