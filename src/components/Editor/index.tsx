/* eslint-disable react-hooks/rules-of-hooks */
import { useStrictMode } from 'react-konva';

useStrictMode(true);

export const DRAW_SCOPE_ID = 'draw-scope';

export { Editor, type EditorRefType } from './Editor';
export { type ShapesCtxType } from './components/ShapesProvider';
export {
  type GlobalCtxType,
  useEditorGlobalCtx
} from './components/GlobalProvider';
export type { EditorProps } from './Editor';

export * from './useRefSubscription';
export * from './useHtmlCover';

export * from './utils';
