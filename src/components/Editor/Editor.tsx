import {
  PropsWithChildren,
  RefObject,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef
} from 'react';
import { ShapesProvider, ShapesProviderRef } from './components/ShapesProvider';
import { Layer, Stage } from 'react-konva';
import {
  GlobalContext,
  GlobalProvider,
  GlobalProviderProps,
  GlobalProviderRef
} from './components/GlobalProvider';
import { LineShapesFactor, ShapesFactory } from './components/ShapesFactory';

export interface EditorProps extends Partial<GlobalProviderProps> {}

export type EditorRefType = {
  shapesRef: RefObject<ShapesProviderRef>;
  globalRef: RefObject<GlobalProviderRef>;
};

export const Editor = forwardRef<EditorRefType, PropsWithChildren<EditorProps>>(
  (
    {
      width = window.innerWidth,
      height = window.innerHeight,
      responsive = true,
      containerRef,

      zoomAlgorithm = 'mouse',
      maxZoom = 2.0,
      minZoom = 0.2,
      children
    },
    ref
  ) => {
    const shapesRef = useRef<ShapesProviderRef>(null);
    const globalRef = useRef<GlobalProviderRef>(null);
    const outStageRef = useRef<any>(null);

    // 切换浏览器tab，重新绘制
    const handlerChangeTabVisible = useCallback(() => {
      outStageRef.current?.draw();
    }, [outStageRef]);

    useEffect(() => {
      window.addEventListener('visibilitychange', handlerChangeTabVisible);

      return () => {
        window.removeEventListener('visibilitychange', handlerChangeTabVisible);
      };
    }, [handlerChangeTabVisible]);

    useImperativeHandle(
      ref,
      () => ({
        shapesRef,
        globalRef
      }),
      []
    );

    return (
      <div
        style={{
          touchAction: 'none'
        }}
      >
        <GlobalProvider
          width={width}
          height={height}
          responsive={responsive}
          containerRef={containerRef}
          zoomAlgorithm={zoomAlgorithm}
          maxZoom={maxZoom}
          minZoom={minZoom}
          ref={globalRef}
          shapesRef={shapesRef}
        >
          <GlobalContext.Consumer>
            {({
              onWheel,
              stageRef,
              unSelectShapes,
              canvasSize,
              layerRef,
              gesture
            }) => {
              outStageRef.current = stageRef.current;

              return (
                <Stage
                  onWheel={onWheel}
                  ref={stageRef}
                  onClick={unSelectShapes}
                  width={canvasSize?.width}
                  height={canvasSize?.height}
                  {...(gesture as any)}
                >
                  <ShapesProvider ref={shapesRef}>
                    <>
                      <Layer
                        name="common-layer"
                        ref={layerRef}
                        onContextMenu={(e) => {
                          // TODO: 设计右键菜单
                          e.evt.preventDefault();
                        }}
                      >
                        <ShapesFactory />
                      </Layer>

                      <Layer listening={false} name="draw-layer">
                        <LineShapesFactor />
                      </Layer>
                    </>
                  </ShapesProvider>
                  <Layer name="performance-layer"></Layer>

                  {children}
                </Stage>
              );
            }}
          </GlobalContext.Consumer>
        </GlobalProvider>
      </div>
    );
  }
);
