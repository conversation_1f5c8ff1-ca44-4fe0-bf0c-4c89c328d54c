import { Vector2d } from './../type';
import { BBoxType } from '.';

type DimensionsType = { width: number; height: number };

/**
 * 计算目标box在容器box中心的位置
 * @param containerBBox  容器box
 * @param objDimensions  目标box
 * @param rotation  旋转角度
 * @returns Vector2d
 */
export const centerObjectPosition = (
  containerBBox: BBoxType,
  objDimensions: DimensionsType,
  rotation: number = 0
): Vector2d => {
  const centerPosX =
    containerBBox.x + (containerBBox.width - objDimensions.width) / 2;
  const centerPosY =
    containerBBox.y + (containerBBox.height - objDimensions.height) / 2;
  if (rotation && rotation % 360 !== 0) {
    const radAngle = (rotation * Math.PI) / 180;
    const sinAngle = Math.sin(radAngle);
    const cosAngle = Math.cos(radAngle);
    const relX = centerPosX - containerBBox.x;
    const relY = centerPosY - containerBBox.y;
    // 应用旋转变换
    const newRelX = relX * cosAngle - relY * sinAngle;
    const newRelY = relX * sinAngle + relY * cosAngle;
    return { x: containerBBox.x + newRelX, y: containerBBox.y + newRelY };
  }
  return { x: centerPosX, y: centerPosY };
};
