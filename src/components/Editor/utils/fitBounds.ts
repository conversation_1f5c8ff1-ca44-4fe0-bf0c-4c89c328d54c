import { RefObject } from 'react';
import { EditorRefType } from '../Editor';
import { getStore } from './getStore';
import { getBBox, getTransformBBox } from './getBBox';

export type BBoxType = {
  x: number;
  y: number;
  width: number;
  height: number;
};

export type BoxType = { rotation: number } & BBoxType;

export type PaddingType = {
  left?: number;
  right?: number;
  top?: number;
  bottom?: number;
};

export type OptionsType = {
  padding?: PaddingType;
  zoomPreset?: number | 'auto' | 'actual';
  minZoom?: number;
  maxZoom?: number;
};

const PaddingDefault = { left: 25, right: 25, top: 25, bottom: 25 };

// fit the bbox into the container center
export const fitBounds = (
  /**  容器box */
  containerBBox: BBoxType,
  /** 目标box */
  bbox: BBoxType,

  { padding, zoomPreset = 'auto', minZoom = 0.1, maxZoom = 4 }: OptionsType
) => {
  const { width, height } = containerBBox;
  const fixturesPadding = { ...PaddingDefault, ...(padding ?? {}) };

  const { width: cropAreaWidth, height: cropAreaHeight, x, y } = bbox;

  let vw = width - fixturesPadding.left - fixturesPadding.right;
  let vh = height - fixturesPadding.top - fixturesPadding.bottom;

  if (vh <= 0) vh = height;
  if (vw <= 0) vw = width;

  let newZoom: number;
  const viewportRatio = vw / vh;

  const bboxRatio = cropAreaWidth / cropAreaHeight;

  if (viewportRatio > bboxRatio) {
    // basic height
    newZoom = vh / cropAreaHeight;
  } else {
    newZoom = vw / cropAreaWidth;
  }
  if (zoomPreset === 'auto') {
    // 根据 zoomPreset 设置 minZoom 和 maxZoom
    newZoom = Math.max(minZoom, Math.min(maxZoom, newZoom));
  } else if (zoomPreset === 'actual') {
    // 使用实际大小，不应用自动缩放
    newZoom = 1.0;
  } else {
    // 如果 zoomPreset 是数字，使用它作为缩放值
    const presetZoom = typeof zoomPreset === 'number' ? zoomPreset : 1.0;
    newZoom = presetZoom;
  }

  const newViewportX =
    (width - fixturesPadding.left - fixturesPadding.right) / 2 -
    (x + cropAreaWidth / 2) * newZoom +
    fixturesPadding.left;
  const newViewportY =
    (height - fixturesPadding.top - fixturesPadding.bottom) / 2 -
    (y + cropAreaHeight / 2) * newZoom +
    fixturesPadding.top;

  return {
    scale: { x: newZoom, y: newZoom },
    x: newViewportX,
    y: newViewportY
  };
};

export const fitBoundsByStage = ({
  targetBBoxOrName, // 目标box 或者 目标box的name
  editorRef,
  ...restProps
}: OptionsType & {
  targetBBoxOrName: BBoxType | string;
  editorRef: RefObject<EditorRefType | null>;
}) => {
  const store = getStore(editorRef);

  const { canvasSize, stageRef } = store ?? {};

  if (!canvasSize || !stageRef?.current) return;

  const isTargetBoxNameMode = typeof targetBBoxOrName === 'string';

  let fixtureBBox: BBoxType | undefined;

  if (isTargetBoxNameMode) {
    const targetBox = stageRef.current?.findOne(`.${targetBBoxOrName}`);
    if (!targetBox) return;

    fixtureBBox = getTransformBBox(targetBox);
  } else {
    fixtureBBox = targetBBoxOrName;
  }

  if (!fixtureBBox) return;

  const res = fitBounds(getBBox(stageRef.current), fixtureBBox, restProps);

  stageRef.current?.setAttrs(res);

  return res;
};
