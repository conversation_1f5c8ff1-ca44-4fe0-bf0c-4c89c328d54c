import Konva from 'konva';
import { type BBoxType } from '.';

export const getBBox = (node: Konva.Node) => {
  return {
    x: node.x(),
    y: node.y(),
    width: node.width(),
    height: node.height()
  };
};

export const getTransformBBox = (node: Konva.Node) => {
  return {
    x: node.x(),
    y: node.y(),
    width: node.width() * node.scaleX(),
    height: node.height() * node.scaleY()
  };
};

export const getViewportBox = (node: Konva.Node) => {
  return {
    ...node.getClientRect(),
    rotation: node.rotation()
  };
};

export const viewportToCanvasBox = (
  box: Partial<BBoxType>,
  stage: Konva.Stage
) => {
  const { x: canvasX = 0, y: canvasY = 0, width = 0, height = 0 } = box;
  const pos = stage.position();
  const scale = stage.scale();
  const scaleX = scale?.x ?? 1;
  const scaleY = scale?.y ?? 1;
  return {
    x: (canvasX - pos.x) / scaleX,
    y: (canvasY - pos.y) / scaleY,
    width: width / scaleX,
    height: height / scaleY
  };
};

export const viewportToCanvasBoxV2 = (
  box: BBoxType,
  stage: Konva.Stage,
  padding: number
) => {
  const { x: canvasX, y: canvasY, width, height } = box;
  const pos = stage.position();
  const scale = stage.scale();
  const scaleX = scale?.x ?? 1;
  const scaleY = scale?.y ?? 1;

  return {
    x: (canvasX - pos.x) / scaleX + padding,
    y: (canvasY - pos.y) / scaleY + padding,
    width: width / scaleX - padding * 2,
    height: height / scaleY - padding * 2
  };
};

export const canvasToViewportBox = (box: BBoxType, stage: Konva.Stage) => {
  const { x: viewportX, y: viewportY, width, height } = box;
  const pos = stage.position();
  const scale = stage.scale();
  const scaleX = scale?.x ?? 1;
  const scaleY = scale?.y ?? 1;
  return {
    x: (viewportX - pos.x) / scaleX,
    y: (viewportY - pos.y) / scaleY,
    width: width / scaleX,
    height: height / scaleY
  };
};
