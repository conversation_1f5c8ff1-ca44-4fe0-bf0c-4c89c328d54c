import Konva from 'konva';

export function getCenterPosition(shape: Konva.Node) {
  const rotation = shape.rotation();
  const x = shape.x() + shape.width() / 2;
  const y = shape.y() + shape.height() / 2;

  // 计算旋转后的中心点坐标
  const angle = (rotation * Math.PI) / 180;
  const cosAngle = Math.cos(angle);
  const sinAngle = Math.sin(angle);

  const rotatedX = x * cosAngle - y * sinAngle;
  const rotatedY = x * sinAngle + y * cosAngle;

  return { x: rotatedX, y: rotatedY };
}
