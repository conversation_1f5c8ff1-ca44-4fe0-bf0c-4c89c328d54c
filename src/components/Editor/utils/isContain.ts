/**
 * 后面采用矩阵计算， 可以考虑地图的一些工具库
 */
import Konva from 'konva';
import { BoxType } from '.';
import { ceil, map } from 'lodash';
import { Vector2d } from '../type';

export const isBBoxContains = (
  outerNode: Konva.Node,
  innerNode: Konva.Node
) => {
  const outerBBox = getRotatedBoundingBox(outerNode);
  const innerBBox = getRotatedBoundingBox(innerNode);

  return (
    outerBBox.topLeft.x <= innerBBox.topLeft.x &&
    outerBBox.topRight.x >= innerBBox.topRight.x &&
    outerBBox.bottomRight.x >= innerBBox.bottomRight.x &&
    outerBBox.bottomLeft.x <= innerBBox.bottomLeft.x &&
    outerBBox.topLeft.y <= innerBBox.topLeft.y &&
    outerBBox.topRight.y <= innerBBox.topRight.y &&
    outerBBox.bottomRight.y >= innerBBox.bottomRight.y &&
    outerBBox.bottomLeft.y >= innerBBox.bottomLeft.y
  );
};

export const getRotatedBoundingBox = (node: Konva.Node) => {
  const transformMatrix = node.getAbsoluteTransform().getMatrix();

  const topLeft = {
    x: transformMatrix[4],
    y: transformMatrix[5]
  };

  const topRight = {
    x: transformMatrix[0] * node.width() + transformMatrix[4],
    y: transformMatrix[1] * node.width() + transformMatrix[5]
  };

  const bottomRight = {
    x:
      transformMatrix[0] * node.width() +
      transformMatrix[3] * node.height() +
      transformMatrix[4],
    y:
      transformMatrix[1] * node.width() +
      transformMatrix[3] * node.height() +
      transformMatrix[5]
  };

  const bottomLeft = {
    x: transformMatrix[3] * node.height() + transformMatrix[4],
    y: transformMatrix[4] * node.height() + transformMatrix[5]
  };

  return { topLeft, topRight, bottomRight, bottomLeft };
};

export const isBBoxContainsByBox = (outerBox: BoxType, innerBox: BoxType) => {
  // Get the corners of the rotated innerBox
  const innerCorners = getRotatedBoxCorners(innerBox);

  // Get the corners of the rotated outerBox
  const outerCorners = getRotatedBoxCorners(outerBox);

  // Check if all four corners of the innerBox are inside the outerBox
  return innerCorners.every((corner) => isPointInsideBox(corner, outerCorners));
};

const getRotatedBoxCorners = (box: BoxType) => {
  const { x, y, width, height, rotation } = box;
  const angle = (rotation * Math.PI) / 180;
  const centerX = x + width / 2;
  const centerY = y + height / 2;

  const corners = [
    { x, y },
    { x: x + width, y },
    { x: x + width, y: y + height },
    { x, y: y + height }
  ];

  return corners.map((corner) => {
    const dx = corner.x - centerX;
    const dy = corner.y - centerY;
    const rotatedX = centerX + dx * Math.cos(angle) - dy * Math.sin(angle);
    const rotatedY = centerY + dx * Math.sin(angle) + dy * Math.cos(angle);
    return { x: ceil(rotatedX, 2), y: ceil(rotatedY, 2) };
  });
};

function isPointInsideBox(point: Vector2d, box: Vector2d[]) {
  const [topLeft, topRight, , bottomLeft] = box;
  return (
    point.x >= topLeft.x &&
    point.x <= topRight.x &&
    point.y >= topLeft.y &&
    point.y <= bottomLeft.y
  );
}

type EvolveBox = {
  x: (innerTopLeft: number, outerTopLeft: number) => number;
  y: (innerTopLeft: number, outerTopLeft: number) => number;
  width: (innerX: number[], outerX: number[]) => number;
  height: (innerY: number[], outerY: number[]) => number;
};

/**
 * Box限制变化函数
 */
function fixturesBox(evolveBox: EvolveBox) {
  return (outerBox: BoxType, innerBox: BoxType) => {
    const innerCorners = getRotatedBoxCorners(innerBox);
    const outerCorners = getRotatedBoxCorners(outerBox);

    return {
      x: evolveBox.x(innerCorners[0].x, outerCorners[0].x),
      y: evolveBox.y(innerCorners[0].y, outerCorners[0].y),
      width: evolveBox.width(map(innerCorners, 'x'), map(outerCorners, 'x')),
      height: evolveBox.height(map(innerCorners, 'y'), map(outerCorners, 'y')),
      rotation: 0
    };
  };
}

/**
 * 外Box限制
 */
export const fixturesOuterBox = fixturesBox({
  x: Math.min,
  y: Math.min,
  width: (innerCorners, outerCorners) =>
    Math.max(innerCorners[2], outerCorners[2]) -
    Math.min(innerCorners[0], outerCorners[0]),
  height: (innerCorners, outerCorners) =>
    Math.max(innerCorners[2], outerCorners[2]) -
    Math.min(innerCorners[0], outerCorners[0])
});

/**
 * 内Box限制
 */
export const fixturesInnerBox = fixturesBox({
  x: Math.max,
  y: Math.max,
  width: (innerCorners, outerCorners) =>
    Math.min(innerCorners[2], outerCorners[2]) -
    Math.max(innerCorners[0], outerCorners[0]),
  height: (innerCorners, outerCorners) =>
    Math.min(innerCorners[2], outerCorners[2]) -
    Math.max(innerCorners[0], outerCorners[0])
});
