// 适配图片
export const adaptiveImageByContainer = (
  dimensions: {
    width: number;
    height: number;
  },
  originRatio: number,
  targetRatio: number
) => {
  const { width, height } = dimensions;

  if (originRatio > targetRatio) {
    return {
      width: width,
      height: width / originRatio
    };
  } else {
    return {
      width: height * originRatio,
      height: height
    };
  }
};
