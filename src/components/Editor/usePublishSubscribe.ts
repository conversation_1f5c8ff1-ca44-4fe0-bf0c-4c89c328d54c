/**
 * 简易发布订阅模式
 */

import { useRef } from 'react';

export type Subscriber<T> = (data: T) => void;

export interface PublishSubscribe<T> {
  subscribe: (subscriber: Subscriber<T>) => () => void;
  publish: (data: T) => void;
}

function usePublishSubscribe<T>(): PublishSubscribe<T> {
  const subscribers = useRef<Subscriber<T>[]>([]);

  const subscribe = (subscriber: Subscriber<T>): (() => void) => {
    subscribers.current.push(subscriber);

    return () => {
      subscribers.current = subscribers.current.filter((s) => s !== subscriber);
    };
  };

  const publish = (data: T): void => {
    subscribers.current.forEach((subscriber) => {
      subscriber(data);
    });
  };

  return { subscribe, publish };
}

export default usePublishSubscribe;
