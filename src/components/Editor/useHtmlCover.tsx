import type {
  ReactNode,
  HTMLAttributes,
  RefObject,
  PropsWithChildren
} from 'react';

import { useEffect, useState } from 'react';
import { BBoxType, EditorRefType, getStore } from '@/components/Editor';
import Konva from 'konva';
import { useWindowSize } from 'react-use';
import styles from './index.module.less';

interface HtmlCoverProps {
  draggable?: boolean;
  renderCover: ReactNode;
  divProps?: HTMLAttributes<HTMLDivElement>;
}

export function useHtmlCover(
  editorRef: RefObject<EditorRefType | null>,
  props: HtmlCoverProps
) {
  const [coverId, setCoverId] = useState('');

  // console.log('coverId', coverId);

  const addCover = (targetContainer: Konva.Node) => {
    if (coverId) removeCover();

    const { draggable = false, renderCover, divProps } = props;

    const store = getStore(editorRef);

    if (!store) return;

    const { addShapes, setIsOpenMouseZoom, stageRef, shapes } = store;

    // 禁止缩放
    setIsOpenMouseZoom(false);
    !draggable && stageRef.current?.draggable(false);

    const added = addShapes(
      {
        type: 'html',
        transformFunc: (props) => {
          props.scaleX = 1;
          props.scaleY = 1;

          const offsetX = stageRef?.current?.offsetX?.() ?? 0;
          const offsetY = stageRef?.current?.offsetY?.() ?? 0;
          const absPs = targetContainer.absolutePosition();

          props.x = offsetX + absPs.x;
          props.y = offsetY + absPs.y;
          return props;
        },
        divProps,
        children: (
          <RenderChildren target={targetContainer}>
            {renderCover}
          </RenderChildren>
        )
      },
      {
        saveHistory: false
      }
    );

    setCoverId(added[0].id ?? '');
    // console.log('addCover - added shape:', added[0], 'all shapes:', shapes);
    return added[0].id;
  };

  const removeCover = () => {
    if (!coverId) return;

    const store = getStore(editorRef);
    // console.log('removeCover', coverId, store  );

    if (!store) return;

    const { removeShape, setIsOpenMouseZoom, stageRef } = store;
    setIsOpenMouseZoom(true);
    stageRef.current?.draggable(true);
    removeShape(coverId, { saveHistory: false });
    setCoverId('');
  };

  return [addCover, removeCover, coverId] as const;
}

// 宽高响应式
const RenderChildren = ({
  children,
  target
}: PropsWithChildren<{ target: Konva.Node }>) => {
  const size = useWindowSize();

  const [clientRect, setClientRect] = useState<BBoxType>();

  useEffect(() => {
    const time = setTimeout(() => {
      const clientRect = target.getClientRect();
      setClientRect(clientRect);
    }, 400);

    return () => clearTimeout(time);
  }, [size, target]);

  if (!clientRect) return null;

  return (
    <div
      className={styles.cover}
      style={{ width: clientRect.width, height: clientRect.height }}
    >
      {children}
    </div>
  );
};
