import { ShapeConfig } from '../../type';
import { v4 as uuid } from 'uuid';

export const genShape = (config: ShapeConfig): ShapeConfig => {
  const defaultColor = '#637EF7';

  let commonConfig: typeof config = { ...config, id: config.id ?? uuid() };

  if (commonConfig.type === 'group') {
    return {
      ...commonConfig,
      elements: (commonConfig.elements ?? []).map(genShape)
    };
  }

  if (commonConfig.type === 'image') {
    return {
      ...commonConfig,
      draggable: commonConfig.draggable ?? true,
      width: commonConfig.width ?? 50,
      height: commonConfig.height ?? 50,
      y: commonConfig.y ?? Math.random() * 100,
      x: commonConfig.x ?? Math.random() * 100
    };
  }

  if (commonConfig.type === 'rect') {
    return {
      ...commonConfig,
      draggable: commonConfig.draggable ?? true,
      width: commonConfig.width ?? 50,
      height: commonConfig.height ?? 50,
      fill: commonConfig.fill ?? defaultColor,
      y: commonConfig.y ?? Math.random() * 100,
      x: commonConfig.x ?? Math.random() * 100
    };
  }

  if (commonConfig.type === 'circle') {
    return {
      ...commonConfig,
      draggable: commonConfig.draggable ?? true,
      radius: commonConfig.radius ?? 50,
      fill: commonConfig.fill ?? defaultColor,
      y: commonConfig.y ?? Math.random() * 100,
      x: commonConfig.x ?? Math.random() * 100
    };
  }

  if (commonConfig.type === 'line') {
    return {
      ...commonConfig,
      draggable: commonConfig.draggable ?? true,
      points: commonConfig.points ?? [],
      stroke: commonConfig.stroke ?? defaultColor,
      strokeWidth: commonConfig.strokeWidth ?? 10
    };
  }

  return commonConfig;
};
