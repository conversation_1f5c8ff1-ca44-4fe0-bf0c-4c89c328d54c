import produce, { type Draft } from 'immer';
import { useState } from 'react';
import { GeometricShapesTypes, ShapeConfig } from '../../type';
import { genShape } from './genShape';
import useHistoryTravel from '@/hooks/useHistoryTravel';
import { useKeyboard } from '@/hooks/useKeyboard';
import { isMacOS } from '@meitu/account';
import _ from 'lodash';

export const useShapes = () => {
  const [shapes, setShapes] = useState<ShapeConfig[]>([]);
  const [initShapes, setInitShapes] = useState<ShapeConfig[]>([]);
  const [canUseUndoRedoKeyboards, setCanUseUndoRedoKeyboards] = useState(true);

  const {
    setValue: setHistoryShapes,
    back,
    forward,
    reset,
    backLength,
    forwardLength
  } = useHistoryTravel<ShapeConfig[]>([], 30);

  const addShapes = (
    config: ShapeConfig | ShapeConfig[],
    options?: {
      groupId?: string;
      replace?: boolean; // 本地测试防止热更新导致的重复添加
      saveHistory?: boolean;
      isInitial?: boolean;
    }
  ) => {
    const {
      groupId,
      replace,
      saveHistory = true,
      isInitial = false
    } = options ?? {};
    const fixturesConfig = Array.isArray(config) ? config : [config];

    // TODO fix type
    const newShapes: any = fixturesConfig.map(genShape);

    setShapes((shapes) => {
      const fixturesShapes = produce(shapes, (draft: Draft<ShapeConfig[]>) => {
        const addToGroup = (elements: typeof draft, groupId?: string) => {
          for (let i = 0; i < elements.length; i++) {
            const shape = elements[i];

            if (shape.id === groupId && shape.type === 'group') {
              shape.elements = shape.elements || [];
              shape.elements.push(...newShapes);
              return true; // Added shapes to the group's elements
            }

            if (shape.type === 'group' && shape.elements) {
              if (addToGroup(shape.elements, groupId)) {
                return true; // Added shapes to a nested group's elements
              }
            }
          }

          return false; // Group not found in the current level
        };

        if (groupId) {
          addToGroup(draft, groupId);
        } else {
          // Add shapes to the top-level
          if (replace) {
            draft.splice(0, draft.length, ...newShapes);
            return;
          } else {
            draft.push(...newShapes);
          }
        }
      });
      if (isInitial) {
        setInitShapes(fixturesShapes);
        reset(fixturesShapes);
      }
      if (saveHistory) setHistoryShapes(fixturesShapes);

      return fixturesShapes;
    });

    return newShapes;
  };

  const updateShape = <T extends GeometricShapesTypes>(
    config: Partial<ShapeConfig<T>> | Partial<ShapeConfig<T>>[],
    options?: {
      saveHistory?: boolean;
    }
  ) => {
    const { saveHistory = true } = options ?? {};
    const fixturesConfig = Array.isArray(config) ? config : [config];

    setShapes((prev) => {
      const fixtures = produce(prev, (draft) => {
        const findAndReplace = (
          elements: typeof draft,
          config: (typeof fixturesConfig)[0]
        ) => {
          let found = false;

          for (let i = 0; i < elements.length; i++) {
            const shape = elements[i];

            // 如果 id 匹配，则更新当前形状
            if (shape.id === config.id) {
              elements[i] = _.mergeWith(
                {},
                shape,
                config,
                // 防止undefined被忽略
                (objValue, srcValue, key, obj) => {
                  if (objValue !== srcValue && srcValue === undefined) {
                    obj[key] = srcValue;
                  }
                }
              );

              found = true;
            }

            // 如果当前形状是组合类型，则递归查找并替换
            if (shape.type === 'group' && shape.elements && !found) {
              if (findAndReplace(shape.elements, config)) {
                found = true;
              }
            }
          }

          return found;
        };

        // 遍历 fixturesConfig 数组并调用 findAndReplace
        for (const config of fixturesConfig) {
          findAndReplace(draft, config);
        }
      });

      if (saveHistory) setHistoryShapes(fixtures);

      return fixtures;
    });
  };

  const removeShape = (
    id: string,
    options?: {
      saveHistory?: boolean;
    }
  ) => {
    const { saveHistory = true } = options ?? {};
    setShapes((prev) => {
      const fixtures = produce(prev, (draft) => {
        const findAndRemove = (elements: typeof draft) => {
          for (let i = 0; i < elements.length; i++) {
            const shape = elements[i];

            if (shape.id === id) {
              elements.splice(i, 1); // Remove the shape from the current level
              return true;
            }

            if (shape.type === 'group' && shape.elements) {
              if (findAndRemove(shape.elements)) {
                return true; // Found and removed the shape in the nested elements
              }
            }
          }

          return false; // Shape not found in the current level
        };

        findAndRemove(draft);
      });
      if (saveHistory) setHistoryShapes(fixtures);
      return fixtures;
    });
  };
  const removeShapes = (
    ids: string[],
    options?: {
      saveHistory?: boolean;
    }
  ) => {
    const { saveHistory = true } = options ?? {};
    setShapes((prev) => {
      const fixtures = produce(prev, (draft) => {
        const findAndRemove = (elements: typeof draft) => {
          let removed = false;
          for (let i = elements.length - 1; i >= 0; i--) {
            const shape = elements[i];

            if (ids.includes(shape.id!)) {
              elements.splice(i, 1); // Remove the shape from the current level
              removed = true;
            }

            if (shape.type === 'group' && shape.elements) {
              if (findAndRemove(shape.elements)) {
                // Found and removed the shape in the nested elements
                removed = true;
              }
            }
          }
          return removed;
        };

        findAndRemove(draft);
      });
      if (saveHistory) setHistoryShapes(fixtures);
      return fixtures;
    });
  };

  const removeAllShapes = (options?: { saveHistory?: boolean }) => {
    const { saveHistory = true } = options ?? {};

    if (saveHistory) setHistoryShapes([]);
    setShapes([]);
  };

  const canUndo = backLength > 0;
  const canRedo = forwardLength > 0;

  const undo = () => {
    const cur = back();

    if (!cur) return;
    setShapes(cur);

    return cur;
  };
  const redo = () => {
    const cur = forward();
    if (!cur) return;
    setShapes(cur);
    return cur;
  };

  useKeyboard('ctrl + z', () => {
    if (isMacOS || !canUseUndoRedoKeyboards) return;
    undo();
  });
  useKeyboard('command + z', () => {
    if (!canUseUndoRedoKeyboards) return;
    undo();
  });

  useKeyboard('shift + command + z', () => {
    if (!canUseUndoRedoKeyboards) return;
    redo();
  });
  useKeyboard('shift + ctrl + z', () => {
    if (isMacOS || !canUseUndoRedoKeyboards) return;
    redo();
  });

  const resetHistory = (shapes?: ShapeConfig[]) => {
    removeAllShapes({ saveHistory: false });

    addShapes(shapes ?? initShapes, {
      saveHistory: false,
      isInitial: true
    });
  };

  return {
    shapes,
    addShapes,
    updateShape,
    removeShape,
    removeAllShapes,
    undo,
    redo,
    canUndo,
    canRedo,
    resetHistory,
    setCanUseUndoRedoKeyboards,
    removeShapes
  };
};
