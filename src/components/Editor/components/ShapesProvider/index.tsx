import {
  PropsWithChildren,
  createContext,
  forwardRef,
  useContext,
  useEffect,
  useImperativeHandle
} from 'react';
import { useShapes } from './useShapes';
import usePublishSubscribe, { Subscriber } from '../../usePublishSubscribe';

export type ShapesCtxType = ReturnType<typeof useShapes> & {};

export const ShapesContext = createContext<ShapesCtxType>({
  /** useShapes */
  shapes: [],
  addShapes: () => [],
  updateShape: () => [],
  removeShape: () => {},
  removeShapes: () => {},
  removeAllShapes: () => {},
  undo: () => undefined,
  redo: () => undefined,
  canUndo: false,
  canRedo: false,
  resetHistory: () => {},
  setCanUseUndoRedoKeyboards: () => {}
});

export type ShapesProviderProps = {};

export type ShapesProviderRef = ShapesCtxType & {
  subscribe: (subscriber: Subscriber<ShapesCtxType>) => Function;
};

export const ShapesProvider = forwardRef<
  ShapesProviderRef,
  PropsWithChildren<ShapesProviderProps>
>(({ children }, ref) => {
  const shapesStore = useShapes();

  const { subscribe, publish } = usePublishSubscribe<ShapesCtxType>();

  useImperativeHandle(
    ref,
    () => ({
      ...shapesStore,
      subscribe
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [shapesStore, subscribe]
  );

  useEffect(() => {
    publish(shapesStore);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shapesStore]);

  return (
    <ShapesContext.Provider value={shapesStore}>
      {children}
    </ShapesContext.Provider>
  );
});

export const useShapesCtx = () => useContext(ShapesContext);
