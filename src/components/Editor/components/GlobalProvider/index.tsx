import Konva from 'konva';
import {
  PropsWithChildren,
  RefObject,
  createContext,
  forwardRef,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef
} from 'react';
import { useZoom } from './useZoom';
import { useResizer } from './useResizer';
import { useChangeable } from './useChangeable';
import { useMode } from './useMode';
import { useDrawing } from './useDrawing';
import usePublishSubscribe, { Subscriber } from '../../usePublishSubscribe';
import { ShapesProviderRef } from '../ShapesProvider';

export type GlobalCtxType = {
  stageRef: RefObject<Konva.Stage>;
  layerRef: RefObject<Konva.Layer>;
  changeCursor: (cursor: string) => void;
} & ReturnType<typeof useZoom> &
  ReturnType<typeof useResizer> &
  ReturnType<typeof useChangeable> &
  ReturnType<typeof useMode> &
  ReturnType<typeof useDrawing>;

const tipsFunc = (...params: any) => {
  throw new Error('Function not implemented.');
};

export const GlobalContext = createContext<GlobalCtxType>({
  stageRef: { current: null },
  layerRef: { current: null },
  onWheel: tipsFunc,
  setIsOpenMouseZoom: tipsFunc,
  selected: '',
  selectShape: tipsFunc,
  unSelectShapes: tipsFunc,
  setIsAllShapesSelectable: tipsFunc,
  isAllShapesDraggable: true,
  setIsAllShapesDraggable: tipsFunc,
  canvasMode: 'origin',
  changeCanvasMode: tipsFunc,
  canvasSize: null,
  onDrawStart: tipsFunc,
  onDrawing: tipsFunc,
  onDrawEnd: tipsFunc,
  gesture: {},
  lineProperty: { type: 'line' },
  setLineProperty: tipsFunc,
  changeCursor: tipsFunc,
  setCanDynamicChangeCanvasModeByDrawScope: tipsFunc,
  canDynamicChangeCanvasModeByDrawScope: { current: false },
  isDrawingRef: { current: false }
});

export type GlobalProviderProps = {
  /**
   * 缩放算法 mouse: 鼠标位置不变，center: 画布中心不变
   */
  zoomAlgorithm: 'mouse' | 'center';
  /**
   * 最大缩放系数
   */
  maxZoom: number;
  /**
   *  最小缩放系数
   */
  minZoom: number;

  /**
   * 是否是响应式
   */
  responsive: boolean;
  /**
   * 画布宽
   */
  width: number;
  /**
   * 画布高
   */
  height: number;
  /**
   * 容器ref
   */
  containerRef?: RefObject<HTMLElement>;
};

export type GlobalProviderRef = GlobalCtxType & {
  subscribe: (subscriber: Subscriber<GlobalCtxType>) => Function;
};

export const GlobalProvider = forwardRef<
  GlobalCtxType,
  PropsWithChildren<
    GlobalProviderProps & {
      shapesRef?: React.RefObject<ShapesProviderRef>;
    }
  >
>(
  (
    {
      zoomAlgorithm,
      maxZoom,
      minZoom,
      children,

      responsive,
      width,
      height,
      containerRef,
      shapesRef
    },
    ref
  ) => {
    const stageRef = useRef<Konva.Stage>(null);
    const layerRef = useRef<Konva.Layer>(null);

    const changeCursor = (cursor: string) => {
      const container = stageRef.current?.container();
      if (!container) return;
      container.style.cursor = cursor;
    };

    const zoomStore = useZoom({
      zoomAlgorithm,
      maximum: maxZoom,
      minimum: minZoom,
      ref: stageRef
    });

    const resizerStore = useResizer({
      ref: containerRef,
      canvasWidth: width,
      canvasHeight: height,
      responsive
    });

    const changeableStore = useChangeable();

    const modeStore = useMode({
      onChange: (mode) => {
        if (mode === 'origin') {
          changeableStore.setIsAllShapesSelectable(true);
          changeableStore.setIsAllShapesDraggable(true);
          stageRef.current?.draggable(true);
        } else {
          changeableStore.setIsAllShapesSelectable(false);
          changeableStore.setIsAllShapesDraggable(false);
          stageRef.current?.draggable(false);
        }
      }
    });

    const drawStore = useDrawing({
      shapesRef,
      canvasMode: modeStore.canvasMode,
      stageRef,
      changeCanvasMode: modeStore.changeCanvasMode
    });

    const store = useMemo(
      () => ({
        stageRef,
        layerRef,
        changeCursor,
        ...resizerStore,
        ...zoomStore,
        ...changeableStore,
        ...modeStore,
        ...drawStore
      }),
      [resizerStore, changeableStore, modeStore, zoomStore, drawStore]
    );

    const { subscribe, publish } = usePublishSubscribe<GlobalCtxType>();

    useEffect(() => {
      publish(store);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [store]);

    useImperativeHandle(
      ref,
      () => ({
        ...store,
        subscribe
      }),

      // eslint-disable-next-line react-hooks/exhaustive-deps
      [store, subscribe]
    );

    return (
      <GlobalContext.Provider value={store}>{children}</GlobalContext.Provider>
    );
  }
);

export const useEditorGlobalCtx = () => useContext(GlobalContext);
