import Konva from 'konva';
import { RefObject, useState } from 'react';
import { type KonvaNodeEvents } from 'react-konva';
import { getZoomedPosByPointer } from '../../utils/getZoomedPosByPointer';

export type UseZoomType = {
  maximum: number;
  minimum: number;
  zoomAlgorithm?: 'mouse' | 'center';
  ref: RefObject<Konva.Stage>;
};

export const useZoom = ({
  zoomAlgorithm = 'center',
  maximum = 2.0,
  minimum = 0.2,
  ref
}: UseZoomType) => {
  const [isOpenMouseZoom, setIsOpenMouseZoom] = useState(true);

  const onWheel: KonvaNodeEvents['onWheel'] = (e) => {
    e.evt.preventDefault();

    if (!ref.current || !isOpenMouseZoom) return;

    const scaleBy = 1.06;
    const oldScale = ref.current.scaleX();
    const fixtureScale =
      e.evt.deltaY < 0 ? oldScale * scaleBy : oldScale / scaleBy;
    const newScale = Math.max(minimum, Math.min(maximum, fixtureScale));
    // 缩放系数
    const zoomFactor = newScale / oldScale;

    const stagePos = ref.current.position();

    const pointerPos =
      zoomAlgorithm === 'mouse'
        ? ref.current.getPointerPosition()!
        : { x: ref.current.width() / 2, y: ref.current.height() / 2 };

    const newPos = getZoomedPosByPointer(pointerPos, stagePos, zoomFactor);

    ref.current.scale({ x: newScale, y: newScale });
    ref.current.position(newPos);
  };

  return {
    onWheel,
    setIsOpenMouseZoom
  };
};
