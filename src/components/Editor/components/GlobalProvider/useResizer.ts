import { RefObject, useEffect } from 'react';
import { useRafState } from 'react-use';
import ResizeObserver from 'resize-observer-polyfill';

export type UseResizer = {
  ref?: RefObject<HTMLElement>;
  canvasWidth: number;
  canvasHeight: number;
  responsive: boolean;
};

type CanvasSizeType = {
  width: number;
  height: number;
};

export const useResizer = ({
  ref,
  responsive,
  canvasWidth,
  canvasHeight
}: UseResizer) => {
  const [canvasSize, setCanvasSize] = useRafState<CanvasSizeType | null>(null);

  useEffect(() => {
    if (!responsive) {
      setCanvasSize({ width: canvasWidth, height: canvasHeight });
      return;
    }

    const targetNode = ref?.current;

    if (!targetNode) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setCanvasSize({
          width: Math.max(width, 200),
          height: Math.max(height, 200)
        });
      }
    });
    resizeObserver.observe(targetNode);

    return () => {
      resizeObserver.unobserve(targetNode);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return { canvasSize };
};
