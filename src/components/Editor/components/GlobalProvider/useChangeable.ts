/**
 * 允许拖拽或者选中变换
 */

import { useState } from 'react';
import type Konva from 'konva';

export const useChangeable = () => {
  // TODO 支持多选?
  const [selected, setSelected] = useState('');
  const [isSelectable, _setIsSelectable] = useState(true);
  const [isAllShapesDraggable, setIsAllShapesDraggable] = useState(true);

  const unSelectShapes = (e?: Konva.KonvaEventObject<MouseEvent>) => {
    if (!e || !isSelectable) return setSelected('');

    const stage = e.target.getStage();

    const clickedOnEmpty = e.target === stage;

    if (clickedOnEmpty) setSelected('');
  };

  const selectShape: typeof setSelected = (action) => {
    if (!isSelectable) return setSelected('');

    setSelected(action);
  };

  const setIsAllShapesSelectable: typeof _setIsSelectable = (action) => {
    setSelected('');
    _setIsSelectable(action);
  };

  return {
    selected,
    selectShape,
    unSelectShapes,
    setIsAllShapesSelectable,
    isAllShapesDraggable,
    setIsAllShapesDraggable
  };
};
