import { useState } from 'react';

export type CanvasModeType = 'pen' | 'eraser' | 'origin';

export type UseModeType = {
  onChange?: (mode: CanvasModeType) => void;
};

export const useMode = ({ onChange }: UseModeType) => {
  const [canvasMode, setCanvasMode] = useState<CanvasModeType>('origin');

  const changeCanvasMode = (mode: CanvasModeType) => {
    onChange?.(mode);

    setCanvasMode(mode);
  };

  return {
    canvasMode,
    changeCanvasMode
  };
};
