// https://konvajs.org/docs/sandbox/Relative_Pointer_Position.html
import Konva from 'konva';
import { useRef, useState } from 'react';
import { CanvasModeType, useMode } from './useMode';
import { KVLineConfig } from '../../type';
import { useGesture } from '@use-gesture/react';
import { ShapesProviderRef } from '../ShapesProvider';

export type ModeType = 'pen' | 'eraser' | 'inherit';

export type UseDrawingType = {
  shapesRef?: React.RefObject<ShapesProviderRef>;
  canvasMode: CanvasModeType;
  stageRef: React.RefObject<Konva.Stage | null>;
  changeCanvasMode: ReturnType<typeof useMode>['changeCanvasMode'];
};

export const useDrawing = ({
  shapesRef,
  canvasMode,
  stageRef,
  changeCanvasMode
}: UseDrawingType) => {
  // 正在绘制
  const isDrawingRef = useRef(false);
  // 暂存绘制点
  const linePointsRef = useRef<number[]>([]);

  // 是否可以动态变动画布模式
  const canDynamicChangeCanvasModeByDrawScope = useRef(false);
  // 绘制属性
  const [lineProperty, setLineProperty] = useState<KVLineConfig>({
    type: 'line',
    stroke: '#637EF7',
    strokeWidth: 10,
    tension: 0.5,
    lineCap: 'round',
    lineJoin: 'round'
  });

  // 临时绘制的线（绘制中的线）
  const tempLineRef = useRef<Konva.Line>();

  const setCanDynamicChangeCanvasModeByDrawScope = (can: boolean) => {
    canDynamicChangeCanvasModeByDrawScope.current = can;
  };

  const onDrawStart = (e: Konva.KonvaEventObject<DragEvent>) => {
    if (canvasMode === 'origin') {
      onDrawEnd(e);
      return;
    }

    const stage = e.target.getStage();

    if (!stage) throw new Error('Stage is not defined');

    const point = stage.getRelativePointerPosition();

    if (!point) {
      onDrawEnd(e);
      return;
    }

    isDrawingRef.current = true;

    linePointsRef.current = [...linePointsRef.current, point.x, point.y];
  };

  const onDrawing = (e: Konva.KonvaEventObject<DragEvent>) => {
    if (canvasMode === 'origin' || !isDrawingRef.current) {
      onDrawEnd(e);
      return;
    }

    const stage = e.target.getStage();

    if (!stage) throw new Error('Stage is not defined');

    const point = stage.getRelativePointerPosition();

    if (!point) {
      onDrawEnd(e);
      return;
    }

    linePointsRef.current = [...linePointsRef.current, point.x, point.y];
    renderTempLine();
  };

  const onDrawEnd = (e: Konva.KonvaEventObject<DragEvent>) => {
    isDrawingRef.current = false;

    if (!shapesRef?.current) {
      renderTempLine();
      return;
    }

    if (linePointsRef.current.length < 10) {
      const uniquePoints = Array.from(new Set(linePointsRef.current));
      if (uniquePoints.length === 2) {
        linePointsRef.current = [...uniquePoints, ...uniquePoints];
      }
    }

    const scaledPoints = linePointsRef.current;

    // const fixturesPoints = getStroke(originPointRef.current, {
    //   thinning: 0,
    //   smoothing: 0.8,
    //   size: 1
    // });

    shapesRef.current.addShapes?.({
      points: scaledPoints,
      mode: canvasMode === 'eraser' ? 'eraser' : 'pen',
      ...lineProperty,
      globalCompositeOperation:
        canvasMode === 'eraser' ? 'destination-out' : 'source-over',
      scaleX: 1,
      scaleY: 1,
      strokeWidth:
        (lineProperty?.strokeWidth ?? 10) / (stageRef.current?.scaleX() ?? 1)
    });
    renderTempLine();
  };

  const renderTempLine = () => {
    requestAnimationFrame(() => {
      const group = stageRef.current?.findOne('.draw-group') as Konva.Layer;

      if (!isDrawingRef.current) {
        linePointsRef.current = [];
        tempLineRef.current?.visible(false);
        return;
      }

      const scaledPoints = linePointsRef.current;
      // const fixturesPoints = getStroke(originPointRef.current, {
      //   thinning: 0,
      //   smoothing: 0.8,
      //   size: 1
      // });

      if (!tempLineRef.current) {
        tempLineRef.current = new Konva.Line({
          points: scaledPoints,
          visible: true,
          globalCompositeOperation:
            canvasMode === 'eraser' ? 'destination-out' : 'source-over',
          opacity: canvasMode === 'eraser' ? 1 : lineProperty?.opacity ?? 1,
          ...lineProperty,
          scaleX: 1,
          scaleY: 1,
          strokeWidth:
            (lineProperty?.strokeWidth ?? 10) /
            (stageRef.current?.scaleX() ?? 1)
        });
        group?.add?.(tempLineRef.current);
        return;
      }

      tempLineRef.current.setAttrs({
        visible: true,
        points: scaledPoints,
        ...lineProperty,
        scaleX: 1,
        scaleY: 1,
        strokeWidth:
          (lineProperty?.strokeWidth ?? 10) / (stageRef.current?.scaleX() ?? 1)
      });
    });
  };

  // https://github.com/konvajs/konva/issues/1556
  const layGesture = useGesture(
    {
      onDragStart: onDrawStart as any,
      onDrag: onDrawing as any,
      onDragEnd: onDrawEnd as any
    },
    {
      drag: {
        enabled: canvasMode === 'pen',
        pointer: { capture: true },
        transform: () => [1, 1]
      }
    }
  );

  const gesture = layGesture();

  return {
    onDrawStart,
    onDrawing,
    onDrawEnd,
    gesture,
    lineProperty,
    setLineProperty,
    setCanDynamicChangeCanvasModeByDrawScope,
    canDynamicChangeCanvasModeByDrawScope,
    isDrawingRef
  };
};
