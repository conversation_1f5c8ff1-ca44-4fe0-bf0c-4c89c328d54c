import {
  ComponentPropsWithoutRef,
  ComponentRef,
  ReactElement,
  Ref,
  forwardRef
} from 'react';
import { Transformer } from 'react-konva';
import { Portal } from 'react-konva-utils';

export interface PortalTransformProps
  extends ComponentPropsWithoutRef<typeof Transformer> {
  isSelected: boolean;
}

export const PortalTransform = forwardRef<
  ComponentRef<typeof Transformer>,
  PortalTransformProps
>(({ isSelected, ...rest }, ref) => {
  return (
    <Portal selector=".performance-layer" enabled={isSelected}>
      <Transformer
        ref={ref}
        boundBoxFunc={(oldBox, newBox) => {
          // limit resize
          if (newBox.width < 5 || newBox.height < 5) {
            return oldBox;
          }
          return newBox;
        }}
        {...rest}
      />
    </Portal>
  );
}) as (props: PortalTransformProps, ref: Ref<Transformer>) => ReactElement;
