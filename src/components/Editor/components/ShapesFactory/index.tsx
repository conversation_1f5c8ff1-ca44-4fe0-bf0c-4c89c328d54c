import { ShapeConfig } from '../../type';
import { useShapesCtx } from '../ShapesProvider';
import { ImageTransform } from './ImageTransform';
import { RectTransform } from './RectangleTransform';
import { useCallback, useEffect, useMemo } from 'react';
import { GroupTransform } from './GroupTransform';
import { Html } from 'react-konva-utils';
import { LineTransform } from './LineTransform';
import { CircleTransform } from './CircleTransform';
import { useEditorGlobalCtx } from '../GlobalProvider';
import { Group, Line } from 'react-konva';
import { BBoxType, viewportToCanvasBox } from '../../utils';
import { useRafState } from 'react-use';
import { DRAW_SCOPE_ID } from '../..';
import { TextTransform } from './TextTransform';

export interface ShapesFactoryProps {}

export const ShapesFactory = (props: ShapesFactoryProps) => {
  const { shapes, updateShape } = useShapesCtx();

  const { selectShape, selected, isAllShapesDraggable } = useEditorGlobalCtx();

  const shapesWithoutLines = useMemo(
    () => shapes.filter((shape) => shape.type !== 'line'),
    [shapes]
  );

  const renderShapes = useCallback(
    (shape: ShapeConfig, index: number) => {
      if (shape.type === 'group') {
        return (
          <GroupTransform
            key={shape.id}
            {...shape}
            draggable={isAllShapesDraggable && shape.draggable}
          >
            {shape?.elements?.map((shapeInGroup, idx) =>
              renderShapes(shapeInGroup, idx)
            )}
          </GroupTransform>
        );
      }
      if (shape.type === 'image') {
        return (
          <ImageTransform
            key={shape.id}
            isSelected={selected === shape.id}
            onSelect={selectShape}
            onUpdateShape={(conf) => {
              updateShape(conf);
            }}
            {...shape}
            draggable={isAllShapesDraggable && shape.draggable}
          />
        );
      }

      if (shape.type === 'rect') {
        return (
          <RectTransform
            isSelected={selected === shape.id}
            onSelect={selectShape}
            onUpdateShape={(conf) => {
              updateShape(conf);
            }}
            {...shape}
            draggable={isAllShapesDraggable && shape.draggable}
            key={shape.id}
          />
        );
      }

      if (shape.type === 'circle') {
        return (
          <CircleTransform
            isSelected={selected === shape.id}
            onSelect={selectShape}
            onUpdateShape={(conf) => {
              updateShape(conf);
            }}
            {...shape}
            draggable={isAllShapesDraggable && shape.draggable}
            key={shape.id}
          />
        );
      }

      if (shape.type === 'html') {
        return <Html key={shape.id} {...shape} />;
      }

      if (shape.type === 'line') {
        return (
          <LineTransform
            isSelected={selected === shape.id}
            onSelect={selectShape}
            onUpdateShape={(conf) => {
              updateShape(conf);
            }}
            {...shape}
            draggable={isAllShapesDraggable && shape.draggable}
            key={shape.id}
          />
        );
      }

      if (shape.type === 'text') {
        return (
          <TextTransform
            isSelected={selected === shape.id}
            onSelect={selectShape}
            onUpdateShape={(conf) => {
              updateShape(conf);
            }}
            {...shape}
            draggable={isAllShapesDraggable && shape.draggable}
            key={shape.id}
          />
        );
      }

      // TODO add more shapes

      return null;
    },
    [isAllShapesDraggable, selectShape, selected, updateShape]
  );

  return <>{shapesWithoutLines.map(renderShapes)}</>;
};

export const LineShapesFactor = () => {
  const { shapes } = useShapesCtx();

  const {
    selectShape,
    selected,
    canvasMode,
    stageRef,
    changeCanvasMode,
    isDrawingRef,
    canDynamicChangeCanvasModeByDrawScope
  } = useEditorGlobalCtx();

  const lines = shapes.filter((shape) => shape.type === 'line');

  const [drawScopeBBox, setDrawScopeBBox] = useRafState<BBoxType | null>(null);

  useEffect(() => {
    const currentStageRef = stageRef.current;
    currentStageRef?.off('mousemove.drawScope_internal');
    currentStageRef?.on('mousemove.drawScope_internal', (e) => {
      if (!canDynamicChangeCanvasModeByDrawScope.current) return;

      if (e.target.attrs.id === DRAW_SCOPE_ID) {
        canvasMode === 'origin' && changeCanvasMode('pen');
        setDrawScopeBBox(e.target.getClientRect());
      } else if (!isDrawingRef.current) {
        changeCanvasMode('origin');
      }
    });

    return () => {
      currentStageRef?.off('mousemove.drawScope_internal');
    };
  }, [
    canDynamicChangeCanvasModeByDrawScope,
    canvasMode,
    changeCanvasMode,
    isDrawingRef,
    setDrawScopeBBox,
    stageRef
  ]);

  const fixtureDrawScopeBBox = useMemo(() => {
    if (!drawScopeBBox) return {} as any;
    return viewportToCanvasBox(drawScopeBBox, stageRef.current!);
  }, [drawScopeBBox, stageRef]);

  return (
    <Group
      name="draw-group"
      clipX={fixtureDrawScopeBBox?.x}
      clipY={fixtureDrawScopeBBox?.y}
      clipWidth={fixtureDrawScopeBBox?.width}
      clipHeight={fixtureDrawScopeBBox?.height}
    >
      {lines.map((shape: any, index) => {
        return (
          <Line
            key={shape.id + index}
            isSelected={selected === shape.id}
            onSelect={selectShape}
            draggable={false}
            {...shape}
          />
        );
      })}
    </Group>
  );
};
