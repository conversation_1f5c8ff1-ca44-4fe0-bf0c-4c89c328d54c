import Konva from 'konva';
import React, { useLayoutEffect } from 'react';

export const useTransformer = <T extends Konva.Shape>({
  isSelected,
  ref,
  transformer
}: {
  isSelected: boolean;
  ref: React.RefObject<T>;
  transformer: React.RefObject<Konva.Transformer>;
}) => {
  useLayoutEffect(() => {
    if (!ref.current || !transformer.current || !isSelected) return;
    transformer.current.nodes([ref.current]);
    transformer.current?.getLayer()?.batchDraw();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSelected]);
};
