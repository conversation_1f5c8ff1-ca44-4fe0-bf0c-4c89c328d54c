import { useRef } from 'react';
import { Line } from 'react-konva';
import type { KVLineConfig, UpdateShapeType } from '../../type';
import type Konva from 'konva';
import { useTransformer } from './useTransformer';
import { useShapeCache, fixturesDeps } from './useShapeCache';
import { PortalTransform } from './PortalTransform';

export interface LineTransformProps
  extends KVLineConfig,
    UpdateShapeType<'line'> {}

export const LineTransform = ({
  isSelected,
  transformerProps = {},

  onTransformEnd,
  onDragEnd,
  onUpdateShape,
  onSelect,
  onClick,
  onDragStart,
  onMouseDown,
  mode,
  opacity,
  points,

  ...restProps
}: LineTransformProps) => {
  const shapeRef = useRef<Konva.Line>(null);
  const trRef = useRef<Konva.Transformer>(null);

  useTransformer({
    isSelected: !!isSelected,
    ref: shapeRef,
    transformer: trRef
  });

  useShapeCache({
    ref: shapeRef,
    deps: [isSelected, fixturesDeps(restProps)]
  });

  return (
    <>
      <Line
        ref={shapeRef}
        draggable
        opacity={mode === 'eraser' ? 1 : opacity ?? 1}
        strokeWidth={10}
        tension={0.5}
        lineCap="round"
        globalCompositeOperation={
          mode === 'eraser' ? 'destination-out' : 'source-over'
        }
        onDragEnd={(e) => {
          onDragEnd?.(e);

          const updatedConfig = {
            ...restProps,
            x: e.target.x(),
            y: e.target.y()
          };

          onUpdateShape?.(updatedConfig);
        }}
        onTransformEnd={(e) => {
          onTransformEnd?.(e);

          if (!shapeRef.current) return;

          const node = e.target as Konva.Line;
          const scaleX = node.scaleX();
          const scaleY = node.scaleY();

          node.scaleX(1);
          node.scaleY(1);

          const scaledPoints = shapeRef.current.points().map((point, index) => {
            if (index % 2 === 0) {
              return point * scaleX;
            }
            return point * scaleY;
          });

          const updatedConfig = {
            ...restProps,
            rotation: node.rotation(),
            x: node.x(),
            y: node.y(),
            // set minimal value
            width: Math.max(5, node.width() * scaleX),
            height: Math.max(5, node.height() * scaleY),
            points: scaledPoints,
            strokeWidth: node.strokeWidth() * Math.max(scaleX, scaleY)
          };

          onUpdateShape?.(updatedConfig);
        }}
        onClick={(e) => {
          onClick?.(e);
          onSelect?.(restProps.id ?? '');
        }}
        onDragStart={(e) => {
          onDragStart?.(e);
          onSelect?.(restProps.id ?? '');
        }}
        onMouseDown={(e) => {
          onMouseDown?.(e);
          onSelect?.(restProps.id ?? '');
        }}
        {...restProps}
        points={(points ?? []).map(
          (point) => Number(point) / (restProps?.scaleX ?? 1)
        )}
      />
      {isSelected && (
        <PortalTransform
          ref={trRef}
          isSelected={isSelected}
          {...transformerProps}
        />
      )}
    </>
  );
};
