import { useRef } from 'react';
import { Image } from 'react-konva';
import { useTransformer } from './useTransformer';
import type Konva from 'konva';
import useImage from 'use-image';
import type { KVImageConfig, UpdateShapeType } from '../../type';
import { fixturesDeps, useShapeCache } from './useShapeCache';
import { PortalTransform } from './PortalTransform';

export interface ImageTransformProps
  extends KVImageConfig,
    UpdateShapeType<'image'> {}

export const ImageTransform = ({
  isSelected,
  src,
  image,
  transformerProps = {},

  onTransformEnd,
  onDragEnd,
  onUpdateShape,
  onSelect,
  onClick,
  onDragStart,
  onMouseDown,
  canCache,
  ...restProps
}: ImageTransformProps) => {
  if (!src && !image) {
    throw new Error('src or image is required');
  }

  const shapeRef = useRef<Konva.Image>(null);
  const trRef = useRef<Konva.Transformer>(null);
  const [imageBySrc] = useImage(src ?? '', 'anonymous');

  useTransformer({
    isSelected: !!isSelected,
    ref: shapeRef,
    transformer: trRef
  });

  useShapeCache({
    canCache,
    ref: shapeRef,
    deps: [isSelected, fixturesDeps(restProps)]
  });

  return (
    <>
      <Image
        ref={shapeRef}
        src={src}
        image={image ?? imageBySrc}
        draggable
        onTransformEnd={(e) => {
          onTransformEnd?.(e);

          if (!shapeRef.current) return;

          const node = shapeRef.current;
          const scaleX = node.scaleX();
          const scaleY = node.scaleY();

          node.scaleX(1);
          node.scaleY(1);

          const updatedConfig: KVImageConfig = {
            ...restProps,
            rotation: node.rotation(),
            x: node.x(),
            y: node.y(),

            width: Math.max(5, node.width() * scaleX),
            height: Math.max(5, node.height() * scaleY)
          };

          onUpdateShape?.(updatedConfig);
        }}
        onDragEnd={(e) => {
          onDragEnd?.(e);

          const updatedConfig: KVImageConfig = {
            ...restProps,
            x: e.target.x(),
            y: e.target.y()
          };

          onUpdateShape?.(updatedConfig);
        }}
        onClick={(e) => {
          onClick?.(e);
          onSelect?.(restProps.id ?? '');
        }}
        onDragStart={(e) => {
          onDragStart?.(e);
          onSelect?.(restProps.id ?? '');
        }}
        onMouseDown={(e) => {
          onMouseDown?.(e);
          onSelect?.(restProps.id ?? '');
        }}
        {...restProps}
      />
      {isSelected && (
        <PortalTransform
          ref={trRef}
          isSelected={isSelected}
          {...transformerProps}
        />
      )}
    </>
  );
};
