import { useRef } from 'react';
import { Group } from 'react-konva';
import type { ShapeConfig } from '../../type';
import type Konva from 'konva';
import { useTransformer } from './useTransformer';
import { PortalTransform } from './PortalTransform';

export interface GroupTransformProps extends ShapeConfig<'group'> {}

export const GroupTransform = ({
  isSelected,
  transformerProps = {},

  onTransformEnd,
  onDragEnd,
  onUpdateShape,
  onSelect,
  onClick,
  onDragStart,
  onMouseDown,

  ...restProps
}: GroupTransformProps) => {
  const shapeRef = useRef<Konva.Group>(null);
  const trRef = useRef<Konva.Transformer>(null);

  useTransformer({
    isSelected: !!isSelected,
    ref: shapeRef as any,
    transformer: trRef
  });

  return (
    <>
      <Group
        ref={shapeRef}
        onTransformEnd={(e) => {
          onTransformEnd?.(e);

          if (!shapeRef.current) return;

          const node = shapeRef.current;
          const scaleX = node.scaleX();
          const scaleY = node.scaleY();

          node.scaleX(1);
          node.scaleY(1);

          const updatedConfig = {
            ...restProps,
            rotation: node.rotation(),
            x: node.x(),
            y: node.y(),
            // set minimal value
            width: Math.max(5, node.width() * scaleX),
            height: Math.max(5, node.height() * scaleY)
          };

          onUpdateShape?.(updatedConfig);
        }}
        onDragEnd={(e) => {
          onDragEnd?.(e);

          const updatedConfig = {
            ...restProps,
            x: e.target.x(),
            y: e.target.y()
          };

          onUpdateShape?.(updatedConfig);
        }}
        onClick={(e) => {
          onClick?.(e);
          onSelect?.(restProps.id ?? '');
        }}
        onDragStart={(e) => {
          onDragStart?.(e);
          onSelect?.(restProps.id ?? '');
        }}
        onMouseDown={(e) => {
          onMouseDown?.(e);
          onSelect?.(restProps.id ?? '');
        }}
        {...restProps}
      />
      {isSelected && (
        <PortalTransform
          ref={trRef}
          isSelected={isSelected}
          {...transformerProps}
        />
      )}
    </>
  );
};
