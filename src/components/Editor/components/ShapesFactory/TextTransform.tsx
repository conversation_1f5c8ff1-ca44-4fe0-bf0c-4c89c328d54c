import { useRef } from 'react';
import { Text } from 'react-konva';
import type { KVTextConfig, UpdateShapeType } from '../../type';
import type Konva from 'konva';
import { useTransformer } from './useTransformer';
import { useShapeCache, fixturesDeps } from './useShapeCache';
import { PortalTransform } from './PortalTransform';

export interface TextTransformProps
  extends KVTextConfig,
    UpdateShapeType<'text'> {}

export const TextTransform = ({
  isSelected,
  transformerProps = {},

  onTransformEnd,
  onDragEnd,
  onUpdateShape,
  onSelect,
  onClick,
  onDragStart,
  onMouseDown,

  ...restProps
}: TextTransformProps) => {
  const shapeRef = useRef<Konva.Text>(null);
  const trRef = useRef<Konva.Transformer>(null);

  useTransformer({
    isSelected: !!isSelected,
    ref: shapeRef,
    transformer: trRef
  });

  useShapeCache({
    ref: shapeRef,
    deps: [isSelected, fixturesDeps(restProps)]
  });

  return (
    <>
      <Text
        ref={shapeRef}
        draggable
        onDragEnd={(e) => {
          onDragEnd?.(e);

          const updatedConfig = {
            ...restProps,
            x: e.target.x(),
            y: e.target.y()
          };

          onUpdateShape?.(updatedConfig);
        }}
        onTransformEnd={(e) => {
          onTransformEnd?.(e);

          if (!shapeRef.current) return;

          const node = e.target;
          const scaleX = node.scaleX();
          const scaleY = node.scaleY();

          node.scaleX(1);
          node.scaleY(1);

          const updatedConfig = {
            ...restProps,
            rotation: node.rotation(),
            x: node.x(),
            y: node.y(),
            // set minimal value
            width: Math.max(5, node.width() * scaleX),
            height: Math.max(node.height() * scaleY)
          };

          onUpdateShape?.(updatedConfig);
        }}
        onClick={(e) => {
          onClick?.(e);
          onSelect?.(restProps.id ?? '');
        }}
        onDragStart={(e) => {
          onDragStart?.(e);
          onSelect?.(restProps.id ?? '');
        }}
        onMouseDown={(e) => {
          onMouseDown?.(e);
          onSelect?.(restProps.id ?? '');
        }}
        {...restProps}
      />
      {isSelected && (
        <PortalTransform
          ref={trRef}
          isSelected={isSelected}
          {...transformerProps}
        />
      )}
    </>
  );
};
