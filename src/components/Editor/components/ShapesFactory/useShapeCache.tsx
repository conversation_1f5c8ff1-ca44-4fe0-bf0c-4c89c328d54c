import Konva from 'konva';
import { omit } from 'lodash';
import { RefObject, useLayoutEffect, useRef } from 'react';

export const useShapeCache = <T extends Konva.Shape>({
  ref,
  deps,
  canCache = true
}: {
  canCache?: boolean;
  ref: RefObject<T>;
  deps: any[];
}) => {
  const canCacheRef = useRef(canCache);

  useLayoutEffect(() => {
    if (!ref.current || !canCacheRef.current) return;

    const clearCache = ref.current.clearCache.bind(ref.current);
    ref.current.cache();

    return () => {
      clearCache();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [...deps]);
};

export const fixturesDeps = (props: Object) => {
  return omit(props, [
    'x',
    'y',
    'scaleX',
    'scaleY',
    'skewX',
    'skewY',
    'rotation'
  ]);
};
