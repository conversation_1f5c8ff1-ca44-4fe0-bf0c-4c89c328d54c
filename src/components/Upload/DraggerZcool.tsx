import { ReactNode, useEffect, useRef, useState } from 'react';
import classNames from 'classnames';

import { Space, Spin, Typography, Button, UploadProps } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { TrashCan } from '@meitu/candy-icons';

import { IMAGE_MAX_SIZE, Upload, UploadStatus } from './Upload';
import styles from './Dragger.module.less';
import { beforeUploadHelperZcool } from './utils';

export type DraggerProps = Omit<UploadProps, 'onChange' | 'value'> & {
  value?: string;
  /**
   * 上传回调
   * @param url 请求需要用到的数据
   * @param previewUrl 前端需要用的预览地址
   */
  onChange?: (url: string, previewUrl?: string, type?: string) => void;
  renderChildren?(loading: boolean): ReactNode;
  taskCategory?: string;
  limitMinEdge?: number;
  // 上传前是否需要转为png
  needToPng?: boolean;
};

/**
 * 图片上传组件，拖拽上传，默认单图。
 * 仅支持 JPG/PNG 格式，大小限制为 10MB 以内的图片。
 */
export function DraggerZcool(props: DraggerProps) {
  const {
    value,
    className,
    maxCount = 1,
    onChange,
    renderChildren: renderChildrenFromProp,
    disabled,
    children,
    limitMinEdge,
    needToPng,
    ...restProps
  } = props;
  const [fileList, setFileList] = useState<UploadProps['fileList']>();
  const isAntUpload = useRef(false); // 是否通过组件上传
  const isSingle = maxCount === 1; // 是否单图上传

  const [uploadStatus, setUploadStatus] = useState<UploadStatus>();
  const uploading = uploadStatus === UploadStatus.Uploading;
  const uploaded = uploadStatus === UploadStatus.Done;
  const hasError = uploadStatus === UploadStatus.Error; // 文件上传错误
  const couldUpload = [undefined, UploadStatus.Removed].includes(uploadStatus);

  const renderChildren = () => {
    if (children) {
      return children;
    }
    if (uploading) {
      return <Spin spinning />;
    }

    // 单图上传且未上传时显示上传按钮
    if (isSingle && couldUpload && !Boolean(value)) {
      return (
        <Space direction="vertical">
          <Button icon={<UploadOutlined />}>拖拽或点击</Button>
          <Typography.Text type="secondary">
            {`支持 JPG/PNG${
              restProps.accept && restProps.accept?.indexOf('svg') !== -1
                ? '/SVG'
                : ''
            } ${IMAGE_MAX_SIZE}MB 以内`}
          </Typography.Text>
        </Space>
      );
    }

    return <span />;
  };

  const itemRender: UploadProps['itemRender'] = (
    node,
    file,
    fileList,
    { remove }
  ) => {
    return (
      <div className={styles.result}>
        {file.thumbUrl && (
          <img
            src={file.thumbUrl}
            alt={file.fileName}
            className={styles.thumbnail}
          />
        )}
        <div
          onClick={(e) => {
            // 埋点patch 删除按钮时候不触发
            e.stopPropagation();
            remove();
          }}
          className={styles.removeBtn}
        >
          <TrashCan />
        </div>
      </div>
    );
  };

  const onFileChange: UploadProps['onChange'] = (info) => {
    // 不满足上传条件时，不触发 onChange。即 info.file.status 不存在
    if (!onChange || !info.file.status) {
      return;
    }

    const { file, fileList } = info;
    const { status, response = {}, type } = file;
    let url = response.url ?? '';
    let previewUrl = response.previewUrl ?? '';

    // 移除或上传错误应该返回空字符串
    const shouldReset = [UploadStatus.Error, UploadStatus.Removed].includes(
      status as UploadStatus
    );

    if (shouldReset) {
      url = '';
      previewUrl = '';
    }

    const finalFileList = fileList.map((item) => {
      return {
        ...item,
        thumbUrl: previewUrl || item.thumbUrl
      };
    });

    // 注意：应产品要求，错误要重置上传内容
    if (UploadStatus.Error === status) {
      setFileList(undefined);
      setUploadStatus(undefined);
      url = undefined;
      previewUrl = undefined;
    } else {
      setUploadStatus(status as UploadStatus);
      setFileList(finalFileList);
    }

    onChange(url, previewUrl, type);
    isAntUpload.current = true;
  };

  useEffect(() => {
    // 只有手动设置时，才自定义 fileList
    if (value && !isAntUpload.current) {
      const customFile = {
        url: value,
        thumbUrl: value,
        uid: `uid-${+new Date()}`,
        name: '图片名称'
      };

      setFileList([customFile]);
    }

    isAntUpload.current = false;
  }, [value]);

  const showUploadList = isSingle && (uploaded || hasError); // 单图上传中，不显示上传列表

  return (
    <Upload
      fileList={fileList}
      type="drag"
      disabled={disabled || uploading}
      maxCount={maxCount}
      beforeUpload={beforeUploadHelperZcool({
        supports:
          restProps.accept && restProps.accept?.indexOf('svg') !== -1
            ? ['image/jpg', 'image/jpeg', 'image/png', 'image/svg+xml']
            : ['image/jpg', 'image/jpeg', 'image/png'],
        limitAspectRatio: 3,
        typeErrorMsg:
          restProps.accept && restProps.accept?.indexOf('svg') !== -1
            ? '只能上传 JPG/PNG/SVG 格式的文件。'
            : '只能上传 JPG/PNG 格式的文件。',
        limitMinEdge
      })}
      needToPng={needToPng}
      showUploadList={showUploadList || Boolean(value)}
      onChange={onFileChange}
      itemRender={itemRender}
      className={classNames(
        styles.dragger,
        { [styles.single]: isSingle },
        { [styles.error]: hasError },
        className
      )}
      taskCategory={props.taskCategory}
      {...restProps}
    >
      {renderChildrenFromProp?.(uploading) ?? renderChildren()}
    </Upload>
  );
}
