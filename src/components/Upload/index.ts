import { <PERSON>agger } from './<PERSON>agger';
import { DraggerZcool } from './DraggerZcool';

import { Upload as InternalUpload } from './Upload';
import { Upload as InternalUploadZcool } from './UploadZcool';
import { Upload as InternalUploadSDK } from './UploadSDK';

type InternalCollapseType = typeof InternalUpload;

interface UploadType extends InternalCollapseType {
  Dragger: typeof Dragger;
}

interface UploadType extends InternalCollapseType {
  DraggerZcool: typeof DraggerZcool;
}

const Upload = InternalUpload as unknown as UploadType;
const UploadZcool = InternalUploadZcool as unknown as UploadType;
const UploadSDK = InternalUploadSDK as unknown as UploadType;

Upload.Dragger = Dragger;
Upload.DraggerZcool = DraggerZcool;

export type { DraggerProps } from './Dragger';
export type { UploadProps } from 'antd';
export type { UploadStatus } from './Upload';
export { Upload };
export { UploadZcool };
export { UploadSDK };

export * from './utils';
