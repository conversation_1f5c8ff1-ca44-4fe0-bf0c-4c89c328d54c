import { useRef } from 'react';
import classNames from 'classnames';
import { noop } from 'lodash';
import type { UploadProps } from 'antd';
import { Upload as AntdUpload, Button, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';

import type { UploadTokenRequestParams, UploaderError } from '@meitu/upload';
import { useAccount, useUploader } from '@/hooks';
import { imageMonitor } from '@/api';
import styles from './Upload.module.less';
import { UploadError } from './utils';
import { UPLOAD_ERR_STATUS } from './constants';
import { defaultUploadParam } from '@/constants/uploadParams';
import { createImage } from '@/utils/cropImage';
import { RcFile } from 'antd/es/upload';

export const IMAGE_MAX_SIZE = 10; // 上传图片最大限制

/**
 * 上传状态，与 antd 中 Upload 组件 UploadFileStatus 保持一致
 *
 * 'error' | 'success' | 'done' | 'uploading' | 'removed';
 */
export enum UploadStatus {
  Error = 'error',
  Success = 'success',
  Done = 'done',
  Uploading = 'uploading',
  Removed = 'removed'
}

export type UploadOption = {
  uploadParam?: Partial<UploadTokenRequestParams>;
  isShowError?: boolean;
  // 上传任务类型
  taskCategory?: string;
  onMessageError?: (msg: string) => void;
  // 上传前是否需要转为png
  needToPng?: boolean;
  // 隐藏上传 button
  noUploadBut?: boolean;
};

/**
 * 图片上传组件，默认单图上传。
 * 仅支持 JPG/PNG 格式，大小限制为 10MB 以内的图片。
 */
export function Upload(props: UploadProps & UploadOption) {
  const {
    className,
    children,
    uploadParam,
    beforeUpload: beforeUploadFromProps,
    isShowError = true,
    taskCategory,
    onMessageError,
    needToPng,
    noUploadBut,
    ...restProps
  } = props;
  const isSingleRef = useRef(false);
  const uploader = useUploader();

  const { isLogin, openLoginPopup } = useAccount();

  const renderChildren = () => {
    if (children) {
      return children;
    }
    if (noUploadBut) {
      return null;
    }

    return <Button icon={<UploadOutlined />}>点击上传</Button>;
  };

  const messageError = (msg: string) => {
    // 注意：多图上传时不提示
    if (!isSingleRef.current || !isShowError) {
      return;
    }

    message.error(msg);
  };

  // 把svg转为png
  const toPng = async (file: string | Blob | RcFile) => {
    let pngBlob = null;

    const url = URL.createObjectURL(file as Blob);
    try {
      const img = await createImage(url);

      // 创建 Canvas
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        console.error('Failed to get canvas context');
        return;
      }

      // 将 SVG 绘制到 Canvas
      ctx.drawImage(img, 0, 0, img.width, img.height);

      // 导出 PNG
      pngBlob = await new Promise<Blob>((res, rej) => {
        canvas.toBlob((blob) => {
          if (blob) {
            res(blob);
          } else {
            rej('svg转换png失败');
          }
          URL.revokeObjectURL(url);
        }, 'image/png');
      });

      return pngBlob;
    } catch (e) {
      console.error('Error loading SVG');
      URL.revokeObjectURL(url);
    }
  };

  // 自定义上传方法，使用 @meitu/upload 上传文件
  const customRequest: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess = noop, onError = noop } = options;
    const { name } = file as File;
    let suffix = name?.slice(name?.lastIndexOf('.')) ?? '.png';
    let pngBlob = null;

    // 上传前是否需要转为png
    if (needToPng) {
      pngBlob = await toPng(file);
      suffix = '.png';
    }

    try {
      const res = await uploader.upload(pngBlob || file, {
        ...defaultUploadParam,
        suffix,
        ...uploadParam
      });
      // v198 上传图片后合规性接口新增 taskCategory 参数
      let uploadQuery: {
        data: string;
        task_category?: string;
      } = {
        data: res.url
      };
      // 如果存在taskCategory参数，则传入
      if (props?.taskCategory) {
        uploadQuery = {
          ...uploadQuery,
          task_category: props.taskCategory
        };
      }
      const { result, message: msg } = await imageMonitor({ ...uploadQuery });

      if (result) {
        onSuccess(res);
      } else {
        messageError(msg ?? '此图片不合规，请重新选择图片上传');
        onError(new UploadError('文件不合规', UPLOAD_ERR_STATUS.REVIEW_FAILED));
      }
    } catch (error: any) {
      messageError('文件上传失败');
      onError(error as UploaderError);
      onMessageError?.(error.message ?? '文件上传失败');
    }
  };

  const beforeUpload: UploadProps['beforeUpload'] = async (file, fileList) => {
    if (!isLogin) {
      await openLoginPopup();
    }

    isSingleRef.current = fileList?.length === 1;

    if (beforeUploadFromProps) {
      return beforeUploadFromProps(file, fileList);
    }

    const isPNG = file.type === 'image/png';
    const isJPG = ['image/jpg', 'image/jpeg'].includes(file.type);
    const isLtMax = file.size / 1024 / 1024 < IMAGE_MAX_SIZE;

    if (!isPNG && !isJPG) {
      messageError('只能上传 JPG/PNG 格式的文件');
    }

    if (!isLtMax) {
      messageError(`文件大小不能超过 ${IMAGE_MAX_SIZE}MB!`);
    }

    return (isPNG || isJPG) && isLtMax;
  };

  return (
    <div
      onClickCapture={(e) => {
        if (!isLogin) {
          e.stopPropagation();
          openLoginPopup();
        }
      }}
      className={classNames(styles.clickCapture, 'upload-click-capture')}
    >
      <AntdUpload
        action=""
        // listType="picture"
        accept=".png,.jpg,.jpeg"
        customRequest={customRequest}
        beforeUpload={beforeUpload}
        className={classNames(styles.upload, className)}
        {...restProps}
      >
        {renderChildren()}
      </AntdUpload>
    </div>
  );
}
