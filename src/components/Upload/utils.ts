import { UploadProps, message } from 'antd';
import { UPLOAD_ERR_STATUS } from './constants';
import { isUndefined } from 'lodash';
import { checkAspectRatio, checkLimitMinEdge } from '@/utils/blob';
import { mimeMap } from '@/constants/mimeMap';

export type supportType = keyof typeof mimeMap;

export type BeforeUploadTypes = {
  /**
   * tips: 不懂就问gpt， "xxx mime 格式"
   * MIME Types
   * https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types
   */
  supports?: supportType[];
  /**
   * 单个文件大小限制 MB
   */
  limit?: number;
  /**
   * 文件个数
   */
  num?: number;

  /**
   * 是否展示错误信息
   */
  showErrorMessage?: boolean;

  /**
   * 文件类型限制提示
   */
  typeErrorMsg?: string;
  /**
   *  文件大小限制提示
   */
  limitErrorMsg?: string;
  /**
   *  文件个数限制提示
   */
  numberErrorMsg?: string;

  /** 图片比例 */
  limitAspectRatio?: number;

  onLimitError?: (file: File) => void;

  onTypeError?: (file: File) => void;

  onNumberError?: (file: File, overFileLength: number) => void;

  onLimitAspectRatio?: (file: File) => void;

  // 最小边
  limitMinEdge?: number;
};

export const beforeUploadHelper = ({
  supports = ['image/jpg', 'image/jpeg', 'image/png'],
  limit = 10,
  num = 3,
  showErrorMessage = true,
  typeErrorMsg,
  limitErrorMsg,
  numberErrorMsg,
  onLimitError,
  onTypeError,
  onNumberError,
  limitAspectRatio,
  onLimitAspectRatio,
  limitMinEdge
}: BeforeUploadTypes): UploadProps['beforeUpload'] => {
  return async (file, fileList) => {
    const isAllowedType = fileList.every((file) =>
      supports.includes(file.type as supportType)
    );
    const isGtMax = file.size / 1024 / 1024 > limit;

    const isLimitAspectRatio = isUndefined(limitAspectRatio)
      ? false
      : !(await checkAspectRatio(file, limitAspectRatio));

    const isLimitMinEdge = isUndefined(limitMinEdge)
      ? false
      : !(await checkLimitMinEdge(file, limitMinEdge));

    let errMessage = '';

    if (!isAllowedType) {
      onTypeError?.(file);

      if (typeErrorMsg) {
        errMessage = typeErrorMsg;
      } else {
        const supportsMsg = supports
          .map((mimeType) => mimeMap[mimeType])
          .join('、');

        errMessage = `文件类型不支持，仅支持${supportsMsg}格式。`;
      }
    }

    if (isGtMax) {
      onLimitError?.(file);

      errMessage += limitErrorMsg || `文件大小不能超过 ${limit}MB！`;
    }

    if (fileList.length > num) {
      onNumberError?.(file, fileList.length - num);
    }

    if (isLimitAspectRatio) {
      onLimitAspectRatio?.(file);
      errMessage += `图片比例过长，请将图片比例裁剪为 ${limitAspectRatio} 后上传。`;
    }

    if (isLimitMinEdge) {
      errMessage += `图片分辨率过低，请上传 ${limitMinEdge}p（${limitMinEdge}×${limitMinEdge} 像素）以上的图片`;
    }

    // 多图上传，同步改造  超出最大数量限制，可上传
    if (!isAllowedType || isGtMax || isLimitAspectRatio || isLimitMinEdge) {
      if (showErrorMessage) message.error(errMessage);

      return false;
    }

    return true;
  };
};

export const beforeUploadHelperZcool = ({
  supports = ['image/jpg', 'image/jpeg', 'image/png'],
  limit = 10,
  num = 3,
  showErrorMessage = true,
  typeErrorMsg,
  limitErrorMsg,
  numberErrorMsg,
  onLimitError,
  onTypeError,
  onNumberError,
  limitAspectRatio,
  onLimitAspectRatio,
  limitMinEdge
}: BeforeUploadTypes): UploadProps['beforeUpload'] => {
  return async (file, fileList) => {
    const isAllowedType = fileList.every((file) =>
      supports.includes(file.type as supportType)
    );
    const isGtMax = file.size / 1024 / 1024 > limit;
    if (isGtMax) {
      onLimitError?.(file);
      message.error(limitErrorMsg || `文件大小不能超过 ${limit}MB！`);
      return false;
    }

    const isLimitAspectRatio = isUndefined(limitAspectRatio)
      ? false
      : !(await checkAspectRatio(file, limitAspectRatio ?? 3));

    const isLimitMinEdge = isUndefined(limitMinEdge)
      ? false
      : !(await checkLimitMinEdge(file, limitMinEdge));

    let errMessage = '';

    if (!isAllowedType) {
      onTypeError?.(file);

      if (typeErrorMsg) {
        errMessage = typeErrorMsg;
      } else {
        const supportsMsg = supports
          .map((mimeType) => mimeMap[mimeType])
          .join('、');

        errMessage = `文件类型不支持，仅支持${supportsMsg}格式。`;
      }
    }

    if (isGtMax) {
      onLimitError?.(file);

      errMessage += limitErrorMsg || `文件大小不能超过 ${limit}MB！`;
    }

    if (fileList.length > num) {
      onNumberError?.(file, fileList.length - num);
    }

    if (isLimitAspectRatio) {
      onLimitAspectRatio?.(file);
      errMessage += `图片比例过长，请将图片比例裁剪为 ${limitAspectRatio} 后上传。`;
    }

    if (isLimitMinEdge) {
      errMessage += `图片分辨率过低，请上传 ${limitMinEdge}p（${limitMinEdge}×${limitMinEdge} 像素）以上的图片`;
    }

    // 多图上传，同步改造  超出最大数量限制，可上传
    if (!isAllowedType || isGtMax || isLimitAspectRatio || isLimitMinEdge) {
      if (showErrorMessage) message.error(errMessage);

      return false;
    }

    return true;
  };
};

// 扩展UploadError
export class UploadError extends Error {
  status?: UPLOAD_ERR_STATUS;

  constructor(message: string, status?: UPLOAD_ERR_STATUS) {
    super(message);
    this.status = status;
  }
}
