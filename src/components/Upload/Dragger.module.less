@import '~@/styles/variables.less';

.dragger {
  width: auto;
  min-width: 150px;
  height: 144px;
  display: block;

  :global {
    .@{ant-prefix}-upload-drag {
      position: absolute;
      z-index: 1;
    }
  }
}

@result-padding: 8px;

.single {
  position: relative;

  :global {
    .@{ant-prefix}-upload-list {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      overflow: hidden;
    }

    .@{ant-prefix}-upload-list-item {
      border: none;
    }

    .ant-upload-list-item-container {
      width: 100%;
      height: 100%;
    }
  }

  .result {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .thumbnail {
      width: 100%;
      height: 100%;
      object-fit: contain;
      object-position: center center;
    }

    .removeBtn {
      position: absolute;
      z-index: 2;
      top: @result-padding;
      right: @result-padding;
      cursor: pointer;
      padding: 8px;
      border-radius: @border-radius-sm;
      border: 1px solid @color-border;
      line-height: 0;
      background-color: @base-white-opacity-50;

      &:hover {
        opacity: 0.7;
      }
    }
    .removeBtnInpaint {
      position: absolute;
      z-index: 2;
      top: @result-padding;
      right: @result-padding;
      cursor: pointer;
      padding: 8px;
      border-radius: @border-radius-sm;
      border: 1px solid @color-border;
      line-height: 0;
      border: 1px solid #464653;
      color: #81858f;
      background: linear-gradient(
          0deg,
          var(--background-editorSpaceHolder, rgba(235, 238, 255, 0.08)) 0%,
          var(--background-editorSpaceHolder, rgba(235, 238, 255, 0.08)) 100%
        ),
        #1f1f23;
      &:hover {
        // opacity: 0.7;
        color: #fff;
      }
    }
  }

  &.error {
    :global {
      .@{ant-prefix}-upload-drag {
        border-color: red;

        &:hover {
          border-color: red;
        }
      }
    }

    .result {
      .removeBtn {
        color: red;
        border-color: red;
      }
    }
  }
}
.loading {
}

.loading {
  transform: rotate(45deg);
  animation-name: antRotate;
  animation-duration: 1.2s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

@keyframes antRotate {
  100% {
    transform: rotate(405deg);
  }
}
