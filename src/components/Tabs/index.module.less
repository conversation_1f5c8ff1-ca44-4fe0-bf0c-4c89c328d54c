@import '~@/styles/variables.less';

@tabs-nav-border-radius: 8px;
@tabs-nav-height: 30px;
@minor-tabs-height: 60px;

.button-tabs.weight {
  :global {
    .@{ant-prefix}-tabs-nav {
      &::before {
        display: none;
      }

      .@{ant-prefix}-tabs-nav-list {
        flex-wrap: wrap;
        gap: calc(@size-md / 2);
      }

      .@{ant-prefix}-tabs-ink-bar {
        display: none;
      }

      .@{ant-prefix}-tabs-tab {
        padding: calc(@size-sm / 2) @size-sm;
        margin-right: 0;
        margin-bottom: 0;
        font-size: @font-size;
        background-color: @background-tab;
        border-radius: @border-radius;
        color: @content-tag;
        line-height: 20px;

        + .@{ant-prefix}-tabs-tab {
          margin-left: 0;
        }

        &.@{ant-prefix}-tabs-tab-active {
          background: @content-web-primary;

          .ant-tabs-tab-btn {
            font-weight: normal;
            color: @color-bg-base;
          }
        }
      }
    }
  }
}

.tabs {
  :global {
    .@{ant-prefix}-tabs-nav {
      &::before {
        display: none;
      }

      .@{ant-prefix}-tabs-nav-list {
        gap: 36px;
      }

      .@{ant-prefix}-tabs-ink-bar {
        height: @size-xxs !important;
        border-radius: @border-radius-xs;
        background: @content-web-primary;
        // scale: 0.5 1;
        transform: scaleX(0.3);
      }

      .@{ant-prefix}-tabs-tab {
        padding-top: 0px;
        padding-bottom: 6px;
        font-size: @text-18-bold;
        font-weight: 600;
        line-height: 26px;
        color: @content-system-quaternary;
        margin-right: 0;
        margin-bottom: 0;

        + .@{ant-prefix}-tabs-tab {
          margin-left: 0;

          &:hover {
            color: @content-system-primary;
          }
        }

        &.@{ant-prefix}-tabs-tab .@{ant-prefix}-tabs-tab-btn {
          color: @content-system-quaternary;
          font-weight: 500;
        }

        &.@{ant-prefix}-tabs-tab-active .@{ant-prefix}-tabs-tab-btn {
          color: @content-system-primary;
        }
      }
    }
  }

  &:global {
    &.ant-tabs-small {
      .ant-tabs-nav {
        margin-bottom: 12px !important;
      }

      .@{ant-prefix}-tabs-nav-list {
        gap: 40px;
      }

      .ant-tabs-nav .ant-tabs-tab {
        line-height: 20px;
      }
    }
  }
}

.segmented-tabs {
  :global {
    .@{ant-prefix}-tabs-nav {
      border-radius: @tabs-nav-border-radius;
      background-color: @background-tab;

      &::before {
        display: none;
      }

      .@{ant-prefix}-tabs-nav-list {
        margin: 0;
        padding: 2px;
        flex: 1;

        .@{ant-prefix}-tabs-tab {
          position: relative;
          z-index: 9;
          flex: 1;
          margin: 0;
          padding: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          height: @tabs-nav-height;
          color: @content-tab;

          &.@{ant-prefix}-tabs-tab-disabled {
            color: rgba(28, 29, 31, 0.25);
          }

          &.@{ant-prefix}-tabs-tab-active {
            .@{ant-prefix}-tabs-tab-btn {
              color: @content-tab-selected;
              font-weight: 500;
            }
          }
        }

        .@{ant-prefix}-tabs-ink-bar {
          height: @tabs-nav-height;
          bottom: auto;
          border-radius: @tabs-nav-border-radius;
          background-color: @background-tab-selected;
          box-shadow: @level-1;
        }
      }
    }
  }
}
