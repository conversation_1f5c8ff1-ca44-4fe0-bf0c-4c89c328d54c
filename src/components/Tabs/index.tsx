import type { TabsProps as AntdTabsProps } from 'antd';
import { Tabs as AntdTabs } from 'antd';
import styles from './index.module.less';
import classNames from 'classnames';

export interface TabsProps extends Omit<AntdTabsProps, 'type'> {
  type?: AntdTabsProps['type'] | 'segmented' | 'no-line' | 'button';
}

export function Tabs({
  type = 'no-line',
  className,
  ...resetProps
}: TabsProps) {
  return (
    <AntdTabs
      className={classNames([
        {
          [styles.segmentedTabs]: type === 'segmented',
          [styles.buttonTabs]: type === 'button',
          [styles.weight]: type === 'button',
          [styles.tabs]: type === 'no-line'
        },
        className
      ])}
      {...resetProps}
    />
  );
}
