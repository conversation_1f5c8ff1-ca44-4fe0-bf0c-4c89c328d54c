import { Button } from '@/components';
import styles from './index.module.less';
import {
  QuestionMarkCircleBoldOutlined,
  UploadBlack
} from '@meitu/candy-icons';
import { Space, Tooltip } from 'antd';
import { CommonUpload } from '@/components/CommonUpload';

export interface UploadStepProps {
  title: string;
  description: string;
  videoSource: string;
  onUploadClick?(): void;
  onUploaded(src?: string): void;
  taskCategory?: string;
}

export const UploadBootSplash = ({
  title,
  description,
  videoSource,
  onUploaded,
  onUploadClick,
  taskCategory
}: UploadStepProps) => {
  return (
    <div className={styles.uploadContainer}>
      <div className={styles.header}>
        <h1>{title}</h1>
        <span>{description}</span>
      </div>

      <div className={styles.content}>
        <video
          className={styles.video}
          loop
          muted
          autoPlay
          playsInline
          preload="auto"
          controls={false}
          disablePictureInPicture
        >
          <source src={videoSource} type="video/mp4" />
          Your browser does not support the video tag
        </video>
        <div className={styles.upload}>
          <CommonUpload
            customUploadedToast={({ successTotal, reviewErrorTotal }) => {
              if (successTotal) return '';

              if (reviewErrorTotal) return '请重新上传合规的图片';
            }}
            adaptive
            onClick={onUploadClick}
            accept=".png,.jpg,.jpeg,.bmp"
            supports={['image/jpeg', 'image/png', 'image/jpg', 'image/bmp']}
            limit={30}
            onFinish={(v) => {
              if (!v[0]?.previewUrl) return;
              onUploaded(v[0]?.previewUrl);
            }}
            taskCategory={taskCategory}
          >
            <Space size={10} direction="vertical">
              <Button className={styles.button} icon={<UploadBlack />}>
                上传图片
              </Button>
              <Space size={3} className={styles.tips}>
                <span>
                  拖拽图片&<span className={styles.active}>点击上传</span>
                </span>
                <Tooltip
                  overlayInnerStyle={{ width: '300px' }}
                  title={
                    <Space direction="vertical">
                      <div>上传图片要求</div>
                      <div>格式：jpg、jpeg、bmp、png</div>
                      <div>大小：30M</div>
                    </Space>
                  }
                >
                  <QuestionMarkCircleBoldOutlined />
                </Tooltip>
              </Space>
            </Space>
          </CommonUpload>
        </div>
      </div>
    </div>
  );
};
