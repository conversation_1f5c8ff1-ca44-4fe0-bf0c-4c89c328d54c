@import '~@/styles/variables.less';

.upload-container {
  height: calc(100vh - 56px);
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .header {
    margin-bottom: 48px;
    h1 {
      color: @content-system-primary;
      font-size: 34px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
    }
    span {
      color: @content-system-quaternary;
      font-family: PingFang SC;
      font-size: 22px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      margin-top: 8px;
    }
  }
}

.content {
  display: flex;
  gap: 32px;
  justify-content: center;

  .video {
    width: 575px;
    height: 400px;
    border-radius: 10px;
  }
}

.upload {
  width: 576px;
  height: 400px;
  padding: 8px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: @level-2;

  //   position: absolute;
  //   top: 50%;
  //   left: 50%;
  //   transform: translate(-50%, -58%);

  .button {
    width: 150px;
    height: 54px;
  }

  .tips {
    color: @content-system-quaternary;
    text-align: center;
    font-size: @text-14;
    line-height: 20px;

    .active {
      color: @content-system-link;
    }
  }

  :global {
    .ant-upload-drag {
      background-color: @background-web-page !important;
    }
  }
}
