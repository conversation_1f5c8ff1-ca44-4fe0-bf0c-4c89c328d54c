import { Col, Popover, Row, Space, Tooltip, Image } from 'antd';
import styles from './meiDouButton.module.less';
import { ChevronDownBold, ExclamationMarkCircleBold } from '@/icons';
import { ComponentPropsWithoutRef, MouseEvent } from 'react';
import { Disclaimer } from '../Disclaimer';
import { MeiDouFetchPriceDescResponse } from '@/api/types/meidou';
import classNames from 'classnames';
import { useMembershipDesc } from '@/hooks/useMember';
import { AppOrigin, appOrigin } from '@/constants';
import { VipButton } from '../VipButton';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useOpenSubscribePopup } from '@/hooks/useSubscribe';
import { MemberGroupCategory } from '@/types';
// import { abTest } from '@/services';
import meidou from '@/assets/images/meidou.png';
import { isWebSDK } from '@/utils';
import { useAccount } from '@/hooks';

export interface MeiDouButtonProps
  extends ComponentPropsWithoutRef<typeof VipButton> {
  price: MeiDouFetchPriceDescResponse | undefined;
  buttonText?: string;
  fetchPriceLoading?: boolean;
  hasBorder?: boolean;
  functionId?: string;
}
// ab测试code
// const abTestCode = process.env.REACT_APP_ABTEST_CODE;
// const abTestMeidouCode = process.env.REACT_APP_ABTEST_CODE_MEIDOU;

export const MeiDouButton = ({
  price,
  buttonText: buttonTextFromProps,
  fetchPriceLoading,
  hasBorder = true,
  onClick: onClickProp,
  className,
  functionId,
  ...restProps
}: MeiDouButtonProps) => {
  const { isVipCurrent } = useMembershipDesc();
  const { availableAmount } = useMeiDouBalance();
  const openSubscribePopup = useOpenSubscribePopup();
  const { isLogin, openLoginPopup } = useAccount();

  let deficit = (availableAmount ?? 0) < (price?.amount ?? 0);
  let buttonText = deficit ? '余额不足' : buttonTextFromProps ?? '立即生成';
  if (isWebSDK()) {
    buttonText = '立即生成';
    deficit = false;
  }

  const handleLogin = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    openLoginPopup();
  };

  const onClick = (e: MouseEvent<HTMLAnchorElement & HTMLButtonElement>) => {
    if (restProps.loading) {
      e.preventDefault();
      return;
    }

    if (deficit) {
      e.preventDefault();
      // 余额不足时，调用美豆充值弹窗
      // 项目接入ABTest， 当abTestCode命中，跳转到会员订阅页面
      if (isVipCurrent) {
        openSubscribePopup(MemberGroupCategory.Meidou, undefined, functionId);
        // if (abTest.isInABTesting(Number(abTestCode))) {
        //   openSubscribePopup(MemberGroupCategory.Member);
        //   // abTest.reportABCodeEnterTest(Number(abTestCode));
        // } else if (abTest.isInABTesting(Number(abTestMeidouCode))) {
        //   openSubscribePopup(MemberGroupCategory.Meidou);
        // } else {
        //   openSubscribePopup(MemberGroupCategory.Meidou);
        // }
      } else {
        openSubscribePopup(MemberGroupCategory.Member, undefined, functionId);
      }

      return;
    }

    onClickProp?.(e);
  };

  return (
    <div
      className={classNames(
        styles.meiDou,
        {
          [styles.hasBorder]: hasBorder
        },
        className
      )}
    >
      {appOrigin === AppOrigin.Whee && !isWebSDK() && (
        <Row justify="space-between" align="middle">
          <>
            <Col>
              <Space size={0}>
                {isLogin && (
                  <Image src={meidou} preview={false} className={styles.icon} />
                )}
                <span className={styles.count}>
                  {price?.costPriceText ?? ''}
                </span>

                {price?.costPriceTextOrigin &&
                  '消耗 ' + price.costPriceTextOrigin !==
                    price.costPriceText && (
                    <span className={styles.beforeDiscount}>
                      {price.costPriceTextOrigin}
                    </span>
                  )}

                {price?.costPriceTips && (
                  <Tooltip
                    overlayClassName={styles.tooltip}
                    title={
                      <span
                        dangerouslySetInnerHTML={{
                          __html: price.costPriceTips
                        }}
                      />
                    }
                  >
                    <ExclamationMarkCircleBold className={styles.icon2} />
                  </Tooltip>
                )}
              </Space>
            </Col>

            {price?.priceDetail && (
              <Col>
                <Popover
                  overlayClassName={styles.detailsPopover}
                  title="明细"
                  arrow={false}
                  placement="topRight"
                  content={
                    <>
                      {price.priceDetail.map((detail) => (
                        <div key={detail.itemName} className={styles.details}>
                          <Space size={4}>
                            <span>{detail.itemName}</span>
                            <span>{detail.itemCount}</span>
                          </Space>
                          <Space>
                            {detail.priceOrigin !== detail.priceNow && (
                              <span className={styles.beforeDiscount}>
                                {detail.priceOrigin}
                              </span>
                            )}

                            <span>{detail.priceNow}</span>
                          </Space>
                        </div>
                      ))}
                    </>
                  }
                >
                  <Space
                    className={styles.detailsTrigger}
                    size={4}
                    align="center"
                  >
                    明细
                    <ChevronDownBold className={styles.icon2} />
                  </Space>
                </Popover>
              </Col>
            )}
          </>
        </Row>
      )}

      <VipButton
        isVip={price?.isVip || isVipCurrent}
        type="primary"
        {...restProps}
        onClick={!isLogin ? handleLogin : onClick}
      >
        {buttonText}
      </VipButton>

      <Disclaimer />
    </div>
  );
};
