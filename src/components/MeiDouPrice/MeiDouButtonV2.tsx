import { Flex } from 'antd';
import styles from './meiDouButtonV2.module.less';
import { Meidou } from '@/icons';
import { ComponentPropsWithoutRef, MouseEvent } from 'react';
import { Button } from '../Button';
import { MeiDouFetchPriceDescResponse } from '@/api/types/meidou';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useOpenSubscribePopup } from '@/hooks/useSubscribe';
import { MemberGroupCategory } from '@/types';
import { useMembershipDesc } from '@/hooks/useMember';
// import { abTest } from '@/services';

export interface MeiDouButtonV2Props
  extends ComponentPropsWithoutRef<typeof Button> {
  price: MeiDouFetchPriceDescResponse | undefined;
  buttonText?: string;
  fetchPriceLoading?: boolean;
  hasBorder?: boolean;
  functionId?: string;
}

export const MeiDouButtonV2 = ({
  price,
  buttonText: buttonTextFromProps,
  fetchPriceLoading,
  onClick: onClickProp,
  className,
  functionId,
  ...restProps
}: MeiDouButtonV2Props) => {
  const { availableAmount } = useMeiDouBalance();
  const { isVipCurrent } = useMembershipDesc();
  const openSubscribePopup = useOpenSubscribePopup();

  const deficit = (availableAmount ?? 0) < (price?.amount ?? 0);

  const buttonText = deficit ? '余额不足' : buttonTextFromProps ?? '立即生成';

  const hasFreeTimes = (price?.totalFreeNum ?? 0) > 0;

  const onClick = async (
    e: MouseEvent<HTMLAnchorElement & HTMLButtonElement>
  ) => {
    if (restProps.loading) {
      e.preventDefault();
      return;
    }
    if (deficit) {
      e.preventDefault();
      // 余额不足时，调用美豆充值弹窗
      // 项目接入ABTest， 当abTestCode命中，跳转到会员订阅页面
      if (isVipCurrent) {
        // if (abTest.isInABTesting(Number(process.env.REACT_APP_ABTEST_CODE))) {
        //   openSubscribePopup(MemberGroupCategory.Member);
        //   // abTest.reportABCodeEnterTest(Number(abTestCode));
        // } else if (
        //   abTest.isInABTesting(Number(process.env.REACT_APP_ABTEST_CODE_MEIDOU))
        // ) {
        //   openSubscribePopup(MemberGroupCategory.Meidou);
        // } else {
        //   openSubscribePopup(MemberGroupCategory.Meidou);
        // }
        openSubscribePopup(MemberGroupCategory.Meidou);
      } else {
        openSubscribePopup(MemberGroupCategory.Member);
      }

      return;
    }
    onClickProp?.(e);
  };

  return (
    <div className={styles.meidouButton}>
      {(hasFreeTimes || !!price?.amount) && (
        <Flex gap={2} align="center" className={styles.meidou}>
          <span>{hasFreeTimes ? '限免' : price?.amount}</span>

          {!hasFreeTimes && <Meidou className={styles.icon} />}
        </Flex>
      )}

      <Button
        {...restProps}
        disabled={fetchPriceLoading || restProps?.disabled}
        onClick={onClick}
      >
        {buttonText}
      </Button>
    </div>
  );
};
