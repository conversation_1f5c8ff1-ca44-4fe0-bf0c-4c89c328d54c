import { Col, Popover, Row, Space, Tooltip, Image } from 'antd';
import styles from './meiDouButton.module.less';
import { ChevronDownBold, ExclamationMarkCircleBold } from '@/icons';
import { Disclaimer } from '../Disclaimer';
import { MeiDouFetchPriceDescResponse } from '@/api/types/meidou';
import classNames from 'classnames';
import { useMembershipDesc } from '@/hooks/useMember';
import { AppOrigin, appOrigin } from '@/constants';
import meidou from '@/assets/images/meidou.png';

export type RenderButtonOptions = {
  isVip: boolean;
};

export interface MeiDouButtonProps {
  price: MeiDouFetchPriceDescResponse | undefined;
  hasBorder?: boolean;
  className?: string;
  renderButton?(options: RenderButtonOptions): React.ReactNode;
}
// ab测试code
// const abTestCode = process.env.REACT_APP_ABTEST_CODE;
// const abTestMeidouCode = process.env.REACT_APP_ABTEST_CODE_MEIDOU;

export const MeiDouCustomButton = ({
  price,
  hasBorder = true,
  className,
  renderButton
}: MeiDouButtonProps) => {
  const { isVipCurrent } = useMembershipDesc();

  return (
    <div
      className={classNames(
        styles.meiDou,
        {
          [styles.hasBorder]: hasBorder
        },
        className
      )}
    >
      {appOrigin === AppOrigin.Whee && (
        <Row justify="space-between" align="middle">
          <>
            <Col>
              <Space size={0}>
                <Image src={meidou} preview={false} className={styles.icon} />
                <span className={styles.count}>
                  {price?.costPriceText ?? ''}
                </span>

                {price?.costPriceTextOrigin &&
                  '消耗 ' + price.costPriceTextOrigin !==
                    price.costPriceText && (
                    <span className={styles.beforeDiscount}>
                      {price.costPriceTextOrigin}
                    </span>
                  )}

                {price?.costPriceTips && (
                  <Tooltip
                    overlayClassName={styles.tooltip}
                    title={
                      <span
                        dangerouslySetInnerHTML={{
                          __html: price.costPriceTips
                        }}
                      />
                    }
                  >
                    <ExclamationMarkCircleBold className={styles.icon2} />
                  </Tooltip>
                )}
              </Space>
            </Col>

            {price?.priceDetail && (
              <Col>
                <Popover
                  overlayClassName={styles.detailsPopover}
                  title="明细"
                  arrow={false}
                  placement="topRight"
                  content={
                    <>
                      {price.priceDetail.map((detail) => (
                        <div key={detail.itemName} className={styles.details}>
                          <Space size={4}>
                            <span>{detail.itemName}</span>
                            <span>{detail.itemCount}</span>
                          </Space>
                          <Space>
                            {detail.priceOrigin !== detail.priceNow && (
                              <span className={styles.beforeDiscount}>
                                {detail.priceOrigin}
                              </span>
                            )}

                            <span>{detail.priceNow}</span>
                          </Space>
                        </div>
                      ))}
                    </>
                  }
                >
                  <Space
                    className={styles.detailsTrigger}
                    size={4}
                    align="center"
                  >
                    明细
                    <ChevronDownBold className={styles.icon2} />
                  </Space>
                </Popover>
              </Col>
            )}
          </>
        </Row>
      )}

      {renderButton?.({ isVip: price?.isVip || isVipCurrent })}
      {/* <VipButton
        isVip={price?.isVip || isVipCurrent}
        type="primary"
        {...restProps}
        onClick={onClick}
      >
        {buttonText}
      </VipButton> */}

      <Disclaimer />
    </div>
  );
};
