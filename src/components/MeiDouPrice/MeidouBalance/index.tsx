import { Col, Popover, Row, Space } from 'antd';
import styles from './index.module.less';
import { Meido<PERSON> } from '@/icons';
import { Button } from '@/components';
import { useAccount } from '@/hooks';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import {
  useOpenSubscribePopup,
  useOpenMeiDouRecordsPopup
} from '@/hooks/useSubscribe';
import { useMembershipDesc } from '@/hooks/useMember';
import { trackMeidouEvent } from './trackMeidouEvent';
import { VipButton } from '@/components/VipButton';
import { AppOrigin, appOrigin } from '@/constants';
import { InfoCircleBold } from '@meitu/candy-icons';
import { useState, useRef } from 'react';
import classNames from 'classnames';
import { BenefitsType, MemberGroupCategory } from '@/types';
import { Link } from 'react-router-dom';
import { AppModule, generateRouteTo, trackEvent } from '@/services';
import { isWebSDK } from '@/utils';

export interface MeidouBalanceProps {}

export const MeidouBalance = (props: MeidouBalanceProps) => {
  const { isLogin } = useAccount();
  const {
    availableAmount,
    tips,
    benefitsDescription,
    detailTitle,
    detailDesc,
    benefitsDetail
  } = useMeiDouBalance();
  const openSubscribePopup = useOpenSubscribePopup();
  const openMeiDouRecordsPopup = useOpenMeiDouRecordsPopup();
  const { isVipCurrent } = useMembershipDesc();
  const [detailModalOpen, setDetailModalOpen] = useState(false);

  const [popverOpen, setPopverOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  if (!isLogin || appOrigin !== AppOrigin.Whee) return null;

  const Detail = () => {
    return (
      <section>
        <h2 className={styles.title}>
          {detailTitle ?? '剩余美豆'}
          <span
            className={styles.detailTrigger}
            onClick={() => {
              setPopverOpen(false);
              setDetailModalOpen(false);
              openMeiDouRecordsPopup();
            }}
          >
            明细
          </span>
        </h2>
        <pre className={styles.description}>
          {detailDesc ?? '优先消耗临期美豆'}
        </pre>
        <div className={styles.container}>
          {benefitsDetail.length > 0 &&
            benefitsDetail.map(({ type, key, value }, index) => {
              return (
                <section className={styles.content} key={index}>
                  <span
                    className={classNames(
                      styles.type,
                      type === BenefitsType.Permanent ? styles.bold : ''
                    )}
                  >
                    {key}
                  </span>
                  <span className={styles.amount}>{value}</span>
                </section>
              );
            })}
        </div>

        <Button type="default" block onClick={() => setDetailModalOpen(false)}>
          好的
        </Button>
      </section>
    );
  };

  return (
    <Popover
      overlayClassName={styles.popoverOverlay}
      placement="bottomLeft"
      arrow={false}
      trigger={'hover'}
      open={popverOpen}
      onOpenChange={(open) => {
        // console.log('open', open);
        setPopverOpen(open);
        if (!open) {
          setDetailModalOpen(false);
          return;
        }

        trackMeidouEvent('beauty_coin_floating_layer_exp');
      }}
      content={
        <>
          {benefitsDescription && (
            <p className={styles.benefitsDesc}>{benefitsDescription}</p>
          )}
          <pre className={styles.contents}>{tips} </pre>
          <Row gutter={12}>
            <Col flex={1}>
              <Button
                type="default"
                onClick={() => {
                  trackMeidouEvent(
                    'beauty_coin_floating_layer_btn_click',
                    'buy'
                  );
                  openSubscribePopup(MemberGroupCategory.Meidou);
                  setPopverOpen(false);
                }}
                block
              >
                购买美豆
              </Button>
            </Col>
            <Col flex={1}>
              <VipButton
                onClick={() => {
                  trackMeidouEvent(
                    'beauty_coin_floating_layer_btn_click',
                    'vip'
                  );

                  openSubscribePopup(MemberGroupCategory.Member);
                  setPopverOpen(false);
                }}
                block
              >
                {isVipCurrent ? '续费会员' : '开通会员'}
              </VipButton>
            </Col>
          </Row>
        </>
      }
      title={
        <Space size={4} className={styles.title}>
          <span ref={containerRef} className={styles.container}>
            <Meidou />
          </span>
          {availableAmount}
          <Popover
            overlayClassName={styles.detail}
            arrow={false}
            open={detailModalOpen}
            content={<Detail />}
            getPopupContainer={(trigger) => containerRef.current ?? trigger}
          >
            <InfoCircleBold
              className={styles.infoCircle}
              onClick={() =>
                setDetailModalOpen((preDetailModalOpen) => !preDetailModalOpen)
              }
            />
          </Popover>
        </Space>
      }
    >
      <Button className={styles.trigger}>
        <Meidou className={styles.icon} />
        {availableAmount}
      </Button>
    </Popover>
  );
};

export type MeidouBalanceWithMissionCenterEntryProps = {
  /**
   * 自定义点击“任务中心”入口的行为
   */
  onClickEntry?: (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => void;
};

export function MeidouBalanceWithMissionCenterEntry({
  onClickEntry
}: MeidouBalanceWithMissionCenterEntryProps) {
  const { isLogin } = useAccount();
  /**
   * 在没有登录或者在大模型官网时 不展示
   */
  if (!isLogin || appOrigin !== AppOrigin.Whee) return null;

  if (isWebSDK()) return null;

  function defaultClickEntryHandler() {
    trackEvent('task_center_click');
  }

  const handleClickEntry = onClickEntry ?? defaultClickEntryHandler;

  return (
    <div className={styles.balance}>
      <Link
        to={generateRouteTo(AppModule.MissionCenter)}
        className="mission-entry"
        onClick={handleClickEntry}
      >
        任务中心
      </Link>
      <MeidouBalance />
    </div>
  );
}
