import type { CanvasSize } from '@/types';
import type { CSSProperties, ReactNode } from 'react';
import type { Gutter } from './WaterfallContainer';

import { getHorizontal, getVertical } from './WaterfallContainer';

import styles from './styles.module.less';

interface AdaptContainerProps {
  size: CanvasSize;
  children: ReactNode;
  style?: CSSProperties;
  padding?: Gutter;
  columnWidth: number;
}

/**
 * 生成草稿上的尺寸
 * @param {CanvasSize} size
 * @return {CSSProperties}
 */
function generateSize(
  [width, height]: CanvasSize,
  columnWidth: number,
  padding?: Gutter
): Pick<CSSProperties, 'paddingBottom'> {
  const proportion = height / width;

  if (!padding) {
    return {
      paddingBottom: `${+proportion.toFixed(4) * 100}%`
    };
  }

  const paddingWidth = getHorizontal(padding);
  const paddingHeight = getVertical(padding);

  const actualWidth = paddingWidth + columnWidth;
  const actualHeight = paddingHeight + columnWidth * proportion;

  return {
    paddingBottom: `${+(actualHeight / actualWidth).toFixed(4) * 100}%`
  };
}

/** 自适应容器 */
export default function AdaptContainer(props: AdaptContainerProps) {
  const { children, size, padding, columnWidth } = props;
  const style = generateSize(size, columnWidth, padding);

  return (
    <div className={styles.adaptContainer} style={props.style}>
      <div className={styles.adaptContainerHydraulic} style={style}></div>
      <div className={styles.adaptContainerContainer}>{children}</div>
    </div>
  );
}
