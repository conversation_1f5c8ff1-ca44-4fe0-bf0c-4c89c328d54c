@import '~@/styles/variables.less';

.waterfall {
  display: flex;

  &-column {
    display: flex;
    flex-direction: column;
    flex: 1;

    &-item {
      :global(.loading) {
        background-color: @color-bg-layout;
      }
    }
  }

  &-placeholder:global(.@{ant-prefix}-empty) {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -54%);

    :global .@{ant-prefix}-empty-image {
      width: 144px;
      height: 144px;
    }

    :global .@{ant-prefix}-empty-description {
      color: @content-system-tertiary;
    }
  }
}

.waterfall-pagination {
  position: relative;
  min-height: 100%;

  .footer {
    height: @size-xxl;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: @color-text-secondary;
  }

  .sentinel {
    position: absolute;
    bottom: 0;
    left: 0;
    height: @size-xl;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    color: @base-white-opacity-100;
    background-color: @base-black-opacity-50;
    transition-property: height;
    transition-duration: 0.5s;
    border-radius: @border-radius-sm @border-radius-sm;
    opacity: 0;

    &:global(.in-child) {
      position: static;
    }
  }
}

.adapt-container {
  position: relative;
  width: 100%;
  overflow: hidden;

  &-hydraulic {
    height: 0;
  }

  &-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
