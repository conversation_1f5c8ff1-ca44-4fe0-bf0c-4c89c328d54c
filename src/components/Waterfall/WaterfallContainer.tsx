import type { CanvasSize } from '@/types';
import type { ReactNode, PropsWithChildren } from 'react';

import { Empty } from 'antd';
import AdaptContainer from './AdaptContainer';
import { useLayoutEffect, useRef, useState } from 'react';
import ResizeObserver from 'resize-observer-polyfill';
import _ from 'lodash';

import styles from './styles.module.less';
import empty from '@/assets/images/empty.jpg';

export type Gutter = number | [number] | [number, number];

/**
 * 水平gutter值
 * @param gutter
 * @returns {number}
 */
export function getHorizontal(gutter: Gutter) {
  return Array.isArray(gutter) ? gutter[0] : gutter;
}
function gutterHorizontal(gutter: Gutter) {
  return getHorizontal(gutter) / 2;
}

/**
 * 垂直gutter值
 * @param gutter
 * @returns {number}
 */
export function getVertical(gutter: Gutter) {
  return Array.isArray(gutter) ? gutter[1] ?? gutter[0] : gutter;
}
function gutterVertical(gutter: Gutter) {
  return getVertical(gutter) / 2;
}

export type WaterfallItemProps = {
  id: string | number;
  size: CanvasSize;
};

function WaterfallItem(
  props: PropsWithChildren<WaterfallItemProps> & {
    gutter: Gutter;
    padding?: Gutter;
    columnWidth: number;
  }
) {
  const { children, size, gutter, padding, columnWidth } = props;

  return (
    <AdaptContainer
      size={size}
      padding={padding}
      columnWidth={columnWidth}
      style={{
        margin: `${gutterVertical(gutter)}px 0`
      }}
    >
      {children}
    </AdaptContainer>
  );
}

interface WaterfallColumnsProps<T extends WaterfallItemProps> {
  columnNumber: number;
  colIndex: number;
  batches: T[];
  gutter: Gutter;
  padding?: Gutter;
  columnWidth: number;
  renderItem: (props: T) => ReactNode;

  paginationSentinel?: ReactNode;
}

function WaterfallColumn<T extends WaterfallItemProps>(
  props: WaterfallColumnsProps<T>
) {
  const {
    columnNumber,
    columnWidth,
    colIndex,
    batches,
    gutter,
    padding,
    renderItem,
    paginationSentinel
  } = props;
  const filteredBatches = batches.filter(
    (batch, index) => index % columnNumber === colIndex
  );

  return (
    <div
      className={styles.waterfallColumn}
      style={{ padding: `0 ${gutterHorizontal(gutter)}px` }}
    >
      {filteredBatches.map((batchProps) => (
        <div
          key={`waterfall-item-${batchProps.id}`}
          className={styles.waterfallColumnItem}
          id={`${batchProps.id}`}
        >
          <WaterfallItem
            {...batchProps}
            gutter={gutter}
            padding={padding}
            columnWidth={columnWidth}
          >
            {renderItem(batchProps)}
          </WaterfallItem>
        </div>
      ))}

      {colIndex === batches.length % columnNumber && paginationSentinel}
    </div>
  );
}

const COLUMN_EXPECT_WIDTH = 256;

function getColumnNumber(containerWidth: number, expectWidth: number) {
  return Math.floor(containerWidth / expectWidth);
}

export interface WaterfallContainerProps<T extends WaterfallItemProps> {
  /** 批次列表 */
  batches?: T[];
  /** 节点组件 */
  renderItem: (props: T) => ReactNode;
  /** 加载完成(控制是否显示 Empty占位) */
  loaded?: boolean;
  /** Empty占位 */
  empty?: ReactNode;
  /** 期望分栏宽度 */
  expectColumnWidth?: number;
  /** 间隔 */
  gutter?: Gutter;
  /** 填充 */
  padding?: Gutter;
  /** 分页哨兵 */
  paginationSentinel?: ReactNode;
}

export default function WaterfallContainer<T extends WaterfallItemProps>(
  props: WaterfallContainerProps<T>
) {
  const {
    batches,
    loaded,
    empty,
    expectColumnWidth,
    gutter = 8,
    padding,
    renderItem,
    paginationSentinel
  } = props;
  const nodeRef = useRef<HTMLDivElement>(null);
  const [columnNumber, setColumnNumber] = useState<number>();
  const [columnWidth, setColumnWidth] = useState<number>(0);

  useLayoutEffect(() => {
    const container = nodeRef.current;

    if (!container) {
      return;
    }

    const observer = new ResizeObserver(
      _.throttle(() => {
        const columnNumber = getColumnNumber(
          container.clientWidth,
          expectColumnWidth ?? COLUMN_EXPECT_WIDTH
        );

        setColumnNumber(columnNumber);
        sessionStorage.setItem('columnNumber', columnNumber.toString());
        setColumnWidth(
          container.clientWidth / columnNumber - getHorizontal(gutter)
        );
      }, 256)
    );

    observer.observe(container);

    return () => {
      // 移除 observer
      observer.disconnect();
    };
  }, [expectColumnWidth, gutter]);

  const renderContainer = () => {
    if (!columnNumber) {
      return null;
    }

    if (!batches?.length) {
      return loaded ? empty : paginationSentinel;
    }

    return _.times(columnNumber).map((colIndex) => (
      <WaterfallColumn
        key={`waterfall-column-${colIndex}`}
        columnNumber={columnNumber}
        colIndex={colIndex}
        batches={batches}
        gutter={gutter}
        padding={padding}
        columnWidth={columnWidth}
        renderItem={renderItem}
        paginationSentinel={paginationSentinel}
      />
    ));
  };

  return (
    <div
      ref={nodeRef}
      className={styles.waterfall}
      style={{
        marginTop: `-${gutterVertical(gutter)}px`,
        marginLeft: `-${gutterHorizontal(gutter)}px`,
        marginRight: `-${gutterHorizontal(gutter)}px`
      }}
    >
      {renderContainer()}
    </div>
  );
}

WaterfallContainer.defaultProps = {
  empty: (
    <Empty
      image={empty}
      description="你还没有生成过创意喔～"
      className={styles.waterfallPlaceholder}
    />
  )
};
