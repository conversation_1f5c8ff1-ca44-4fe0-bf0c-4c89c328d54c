import type {
  WaterfallContainerProps,
  WaterfallItemProps
} from './WaterfallContainer';
import type { WaterfallPaginationProps } from './WaterfallPagination';

import WaterfallPagination from './WaterfallPagination';
import WaterfallContainer from './WaterfallContainer';
import { ReactNode } from 'react';

export { WaterfallPagination, WaterfallContainer };

export function Waterfall<T extends WaterfallItemProps>(
  props: WaterfallContainerProps<T> &
    WaterfallPaginationProps & {
      /**
       * 分页哨兵是否在列里
       *
       * 如果为false，则分页哨兵会在整个流容器的下边，只有翻页到最下方，才会出现开始加载下一页
       *
       * 如果为true，则分页哨兵会在流容器内，当流容器有空位，就会开始加载下一页
       */
      paginationSentinelInColumn?: boolean;
    }
) {
  const {
    hasMore,
    loading,
    isAtBottom,
    getMore,
    loaded = !loading,
    paginationSentinelInColumn,
    ...containerProps
  } = props;

  return (
    <WaterfallPagination
      hasMore={hasMore}
      isAtBottom={isAtBottom}
      getMore={getMore}
      loading={loading}
    >
      {!paginationSentinelInColumn ? (
        <WaterfallContainer<T> loaded={loaded} {...containerProps} />
      ) : (
        (paginationSentinel: ReactNode) => {
          return (
            <WaterfallContainer<T>
              loaded={loaded}
              {...containerProps}
              paginationSentinel={paginationSentinel}
            />
          );
        }
      )}
    </WaterfallPagination>
  );
}
