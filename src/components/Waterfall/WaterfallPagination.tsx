import { ReactNode } from 'react';
import { Loading } from '@/components';
import { useLayoutEffect, useRef } from 'react';

import styles from './styles.module.less';
import classNames from 'classnames';

export interface WaterfallPaginationProps {
  /** 是否可翻页 */
  hasMore: boolean;
  loading: boolean;
  /** 是否滚动到底 */
  isAtBottom?: boolean;
  /** 滚动触底触发下一页请求 */
  getMore?: () => void;

  /**
   * 如果传递的children是一个JSX 则将分页哨兵放在最下面
   *
   * 如果传递的children是一个函数，则分页哨兵的摆放位置交给children决定
   */
  children?: ReactNode | ((sentinel: ReactNode) => ReactNode);
}

export default function WaterfallPagination(props: WaterfallPaginationProps) {
  const { hasMore, loading, isAtBottom, getMore, children } = props;
  const containerRef = useRef<HTMLDivElement>(null);
  const sentinelRef = useRef<HTMLDivElement>(null);
  const throttleTimer = useRef<number>(0);

  useLayoutEffect(() => {
    const container = containerRef.current;
    const sentinel = sentinelRef.current;

    if (!container || !sentinel) {
      return;
    }

    const observer = new IntersectionObserver(
      (entries: IntersectionObserverEntry[]) => {
        const currentTimer = +new Date();
        if (currentTimer - throttleTimer.current <= 1024) {
          return;
        }
        // 加载中 或 没有更多时 返回
        if (loading || !hasMore) {
          return;
        }

        // 哨兵不可见时 返回
        if (entries[0].intersectionRatio <= 0) {
          return;
        }

        getMore?.();
        throttleTimer.current = currentTimer;
      }
    );

    observer.observe(sentinel);

    return () => {
      // 移除 observer
      observer.disconnect();
    };
  });

  const renderFooter: () => ReactNode = () => {
    if (loading) {
      return <Loading />;
    }

    if (hasMore) {
      return null;
    }

    return isAtBottom ? '没有更多了~' : null;
  };

  const sentinelInChild = typeof children === 'function';

  const sentinel = (
    <div
      ref={sentinelRef}
      className={classNames(styles.sentinel, sentinelInChild && 'in-child')}
    >
      往下滑动加载更多
    </div>
  );

  return (
    <div ref={containerRef} className={styles.waterfallPagination}>
      {typeof children === 'function' ? children(sentinel) : children}
      <div className={styles.footer}>{renderFooter()}</div>

      {!sentinelInChild && sentinel}
    </div>
  );
}
