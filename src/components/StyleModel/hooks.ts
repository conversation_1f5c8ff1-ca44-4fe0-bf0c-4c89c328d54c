import type {
  StyleModelListQuery,
  EditorConfigModelListResponse,
  StyleModelResponse
} from '@/api/types';
import type { StyleModelContextValue } from './Provider';

import { fetchStyleModelList } from '@/api/editor';
import { StyleModelContext } from './Provider';
import { useContext } from 'react';
import { getAllModelByConfig } from '@/hooks/useGetEditorConfig/getAllModelByConfig';
import { produce } from 'immer';
import { useSearchParams } from '@/hooks';

import { COLLECTION_CATEGORY_ID } from '@/constants/model';

/** 获取风格模型类型 */
function getModelById(
  key: keyof StyleModelResponse,
  models?: StyleModelContextValue['styleModelList']
): (id?: number) => number;

/** 获取风格模型 */
function getModelById(
  key: keyof EditorConfigModelListResponse,
  models?: EditorConfigModelListResponse[]
): (id?: number) => number;

function getModelById(key: string, models?: Record<string, any>[]) {
  return (id?: number) => models?.findIndex((model) => id === model[key]) ?? -1;
}

export const useGetStyleModelById = () => {
  const { styleModelList } = useContext(StyleModelContext);

  return getModelById('categoryId', styleModelList);
};

/**
 * 获取风格模型列表
 */
export const useStyleModelList = () => {
  const { from, styleModelList, keyword, setStyleModelList } =
    useContext(StyleModelContext);

  const getStyleModelById = useGetStyleModelById();
  const { styleModelId } = useSearchParams();

  const fetchStyleModels = async (
    params?: Omit<StyleModelListQuery, 'from' | 'cursor' | 'styleModelIds'> & {
      reset?: boolean;
    }
  ) => {
    const {
      categoryId,
      reset,
      keyword: keywordFromParams,
      ...leftParams
    } = params ?? {};
    const matchedIndex = getStyleModelById(categoryId);

    // 如果styleModelList为空或匹配的索引不存在，直接获取数据
    if (styleModelList.length === 0 || matchedIndex === -1) {
      const styleModels = await fetchStyleModelList({
        categoryId,
        from,
        cursor: undefined, // 始终从头开始获取
        keyword: keywordFromParams ?? keyword,
        styleModelIds: styleModelId,
        ...leftParams
      });

      setStyleModelList(styleModels);
      return;
    }

    const { cursor, fetchingCursor } = styleModelList[matchedIndex] ?? {};

    if (cursor === fetchingCursor) {
      return;
    }

    setStyleModelList(
      produce(styleModelList, (draft) => {
        draft[matchedIndex].fetchingCursor = cursor;
        draft[matchedIndex].preKeyword = keywordFromParams ?? keyword;
      })
    );

    const styleModels = await fetchStyleModelList({
      categoryId,
      from,
      cursor: reset ? undefined : cursor,
      keyword: keywordFromParams ?? keyword,
      styleModelIds: styleModelId,
      ...leftParams
    });

    setStyleModelList(
      matchedIndex > -1
        ? produce(styleModelList, (draft) => {
            const { cursor, list } =
              styleModels.find(
                (styleModel) => styleModel.categoryId === categoryId
              ) ?? {};
            if (list) {
              draft[matchedIndex].list = reset
                ? list
                : draft[matchedIndex].list.concat(list);
            }
            draft[matchedIndex].cursor = cursor ?? '';
            draft[matchedIndex].fetchingCursor = undefined;
            draft[matchedIndex].preKeyword = keywordFromParams ?? keyword;
          })
        : reset
        ? produce(styleModelList, (draft) => {
            draft = draft.map((originModel) => {
              const matchedModel = styleModels.find(
                (styleModel) => styleModel.categoryId === originModel.categoryId
              ) ?? {
                list: [],
                cursor: ''
              };
              return Object.assign(originModel, matchedModel);
            });
          })
        : styleModels
    );
  };

  const updateModelItem = (
    modelId: number,
    props: Partial<EditorConfigModelListResponse>
  ) => {
    setStyleModelList(
      produce(styleModelList, (draft) => {
        draft.forEach((styleModel, index) => {
          const getModelItemById = getModelById(
            'id',
            styleModelList[index]?.list
          );
          const matchedModelIndex = getModelItemById(modelId);

          if (matchedModelIndex < 0) {
            return;
          }

          // HACK 如果当前为收藏Tab则移除
          if (
            COLLECTION_CATEGORY_ID === styleModel.categoryId &&
            props.isCollect === false
          ) {
            styleModel.list.splice(matchedModelIndex, 1);
            return;
          }

          Object.assign(styleModel.list[matchedModelIndex], props);
        });
      })
    );
  };

  return {
    styleModel: getAllModelByConfig(styleModelList),
    styleModelResponse: styleModelList,
    fetchStyleModels,
    updateModelItem
  };
};
