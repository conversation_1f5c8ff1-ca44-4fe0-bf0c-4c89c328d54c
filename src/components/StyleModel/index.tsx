import type { ReactElement } from 'react';
import type { ModalRef, ModelModalPropsV2 } from '@/components';
import type { EditorConfigModelListResponse } from '@/api/types';

import { ModelModalV2 } from '@/components';
import { StyleModelContext } from './Provider';
import { useStyleModelList, useGetStyleModelById } from './hooks';
import { StyleModelTabContainer } from './StyleModelTabContainer';

import { useMount } from 'react-use';
import { forwardRef, useContext, useState } from 'react';
import _ from 'lodash';

import { COLLECTION_CATEGORY_ID, ModelMvType } from '@/constants/model';
import { trackEvent } from '@/services';
import { getSource } from '@/utils';

export { useStyleModelList } from './hooks';
export { StyleModelProvider } from './Provider';

export const StyleModelModal = forwardRef<
  ModalRef,
  {
    onModelClick: ModelModalPropsV2['onModelClick'];
    checkDisabled?: ModelModalPropsV2['checkDisabled'];
    modelMvType?: ModelMvType;
    tooltip?: string;
  }
  // { checkDisabled: ModelModalPropsV2['checkDisabled'] }
>(({ onModelClick, checkDisabled, modelMvType, tooltip }, ref) => {
  const { styleModelResponse, fetchStyleModels, updateModelItem } =
    useStyleModelList();
  const {
    keyword: keywordFromCtx,
    styleModelList,
    setKeyword
  } = useContext(StyleModelContext);
  const getStyleModelById = useGetStyleModelById();

  const [categoryName, setCategoryName] = useState('');

  // HACK onCollectMutation 类型需要返回 Promise<any>
  const onCollectMutation = async (item: EditorConfigModelListResponse) => {
    const { isCollect, id } = item;

    trackEvent('model_favorite_add', {
      function: getSource(),
      location: 'model_store',
      favor_type: item.isCollect ? 'cancel' : 'favor'
    });

    updateModelItem(id, {
      isCollect: !isCollect
    });
  };

  const onSearch = _.throttle(
    (keyword: string, categoryId: number) => {
      trackEvent('whee_search_click', {
        function: getSource(),
        location: 'edit_model_search'
      });

      if (keywordFromCtx === keyword) {
        return;
      }
      setTimeout(() => {
        setKeyword(() => keyword);
        fetchStyleModels({
          keyword,
          categoryId,
          reset: true,
          baseModelType: modelMvType === ModelMvType.SpecialBeta ? 2 : 1
        });
        trackEvent('whee_search_results_expo', {
          function: getSource(),
          location: 'edit_model_search'
        });
      });
    },
    1024,
    {
      trailing: false
    }
  );

  const onCategoryChange = (categoryId: number) => {
    const categoryName =
      styleModelList[getStyleModelById(categoryId)]?.categoryName;

    setCategoryName(categoryName);

    trackEvent('edit_model_add_popuptab_click', {
      popupTab: categoryName
    });

    const preKeyword =
      styleModelList[getStyleModelById(categoryId)]?.preKeyword;
    // 切换到收藏时刷新
    if (
      COLLECTION_CATEGORY_ID === categoryId ||
      preKeyword !== keywordFromCtx
    ) {
      fetchStyleModels({
        categoryId,
        reset: true,
        baseModelType: modelMvType === ModelMvType.SpecialBeta ? 2 : 1
      });
    }
  };
  return (
    <ModelModalV2
      ref={ref}
      title="风格模型"
      checkDisabled={checkDisabled}
      tooltip={tooltip}
      list={styleModelResponse}
      tabContainerRender={(tabContainer, model) => (
        <StyleModelTabContainer
          tabContainer={tabContainer}
          model={model}
          modelMvType={modelMvType}
        />
      )}
      searchTrigger={['enter']}
      onCollectMutation={onCollectMutation}
      onSearch={onSearch}
      onModelClick={(item) => {
        onModelClick?.(item);

        trackEvent('style_model_use_click', {
          popupTab: categoryName
        });
      }}
      onCategoryChange={onCategoryChange}
    />
  );
});

export interface StyleModelProps {
  children: ReactElement;
  modelMvType?: ModelMvType;
}

export function StyleModel(props: StyleModelProps) {
  const { children } = props;
  const { fetchStyleModels } = useStyleModelList();
  useMount(() => {
    fetchStyleModels({
      baseModelType: props.modelMvType === ModelMvType.SpecialBeta ? 2 : 1
    });
  });

  return children;
}
