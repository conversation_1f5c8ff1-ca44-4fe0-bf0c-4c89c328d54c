import type { ReactElement } from 'react';
import type {
  EditorConfigModelResponse,
  StyleModelResponse
} from '@/api/types';
import type { GridOnItemsRenderedProps } from 'react-window';

import InfiniteLoader from 'react-window-infinite-loader';
import { useStyleModelList } from './hooks';
import { cloneElement } from 'react';
import { ModelMvType } from '@/constants/model';

interface StyleModelTabContainerProps {
  tabContainer: ReactElement;
  model: EditorConfigModelResponse;
  modelMvType?: ModelMvType;
}

function getStyleModelById(id: number, styleModels: StyleModelResponse[]) {
  return styleModels.find(({ categoryId }) => categoryId === id);
}

export function StyleModelTabContainer(props: StyleModelTabContainerProps) {
  const { model, tabContainer, modelMvType } = props;
  const { styleModelResponse, fetchStyleModels } = useStyleModelList();
  const styleModel = getStyleModelById(model.categoryId, styleModelResponse);
  const loaded = styleModel?.cursor === '';
  const styleModelSize = styleModel?.list?.length ?? 0;

  // 如果没有styleModel数据，尝试获取
  if (!styleModel && styleModelResponse.length > 0) {
    fetchStyleModels({
      categoryId: model.categoryId,
      baseModelType: modelMvType === ModelMvType.SpecialBeta ? 2 : 1
    });
  }

  return (
    <InfiniteLoader
      isItemLoaded={() => loaded}
      itemCount={999}
      loadMoreItems={(startIndex, stopIndex) => {
        // stopIndex 存在缓冲值
        if ((stopIndex - 2) * 5 < styleModelSize) {
          return;
        }
        fetchStyleModels({
          categoryId: model.categoryId,
          baseModelType: modelMvType === ModelMvType.SpecialBeta ? 2 : 1
        });
      }}
      threshold={0}
    >
      {({ onItemsRendered }) =>
        cloneElement(tabContainer, {
          onItemsRendered: (props: GridOnItemsRenderedProps) => {
            onItemsRendered({
              overscanStartIndex: props.overscanRowStartIndex,
              overscanStopIndex: props.overscanRowStopIndex,
              visibleStartIndex: props.visibleRowStartIndex,
              visibleStopIndex: props.visibleRowStopIndex
            });
          }
        })
      }
    </InfiniteLoader>
  );
}
