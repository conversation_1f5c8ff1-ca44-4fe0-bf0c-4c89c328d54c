import { type StyleModelResponse, DraftType } from '@/api/types';
import type { PropsWithChildren, Dispatch, SetStateAction } from 'react';

import { createContext, useState, useEffect, useRef } from 'react';

export interface StyleModelResponseType extends StyleModelResponse {
  fetchingCursor?: string;
  preKeyword?: string;
}

export interface StyleModelContextValue {
  styleModelList: Array<StyleModelResponseType>;
  setStyleModelList: (
    styleModelList: StyleModelContextValue['styleModelList']
  ) => void;

  from: DraftType;

  keyword: string;
  setKeyword: Dispatch<SetStateAction<string>>;
  initStyleModelList: (callback?: () => void) => void;
}

function initialContextMethod() {
  console.warn('正在使用 `StyleModelContext` 的默认值');
}

export const StyleModelContext = createContext<StyleModelContextValue>({
  styleModelList: [],
  setStyleModelList: initialContextMethod,
  from: DraftType.TEXT_TO_IMAGE,
  keyword: '',
  setKeyword: initialContextMethod,
  initStyleModelList: initialContextMethod
});

interface StyleModelProviderProps {
  from: DraftType;
}

export function StyleModelProvider({
  children,
  from
}: PropsWithChildren<StyleModelProviderProps>) {
  const [styleModelList, setStyleModelList] = useState<
    StyleModelContextValue['styleModelList']
  >([]);
  const [keyword, setKeyword] = useState<string>('');

  // 使用 useRef 追踪 initStyleModelList 是否被调用
  const resetRequested = useRef<boolean>(false);
  const callbackRef = useRef<(() => void) | undefined>(undefined);

  const initStyleModelList = (callback?: () => void) => {
    // console.log('Provider.tsx: initStyleModelList 被调用了!', { from, currentListLength: styleModelList.length });

    // 标记需要重置
    resetRequested.current = true;

    // 保存回调函数以在状态更新后执行
    callbackRef.current = callback;

    // 设置空数组
    setStyleModelList([]);

    // console.log('Provider.tsx: styleModelList 状态已设置为空数组');
  };

  // 监听 styleModelList 变化，当它变为空数组且有重置请求时执行回调
  useEffect(() => {
    // console.log('Provider.tsx: styleModelList 已更新:', {
    //   length: styleModelList.length,
    //   from,
    //   resetRequested: resetRequested.current,
    //   hasCallback: !!callbackRef.current
    // });

    // 如果是由 initStyleModelList 引起的重置，且数组现在为空
    if (resetRequested.current && styleModelList.length === 0) {
      // console.log('Provider.tsx: 检测到由 initStyleModelList 触发的状态更新');

      // 如果有回调函数，执行它
      if (callbackRef.current) {
        // console.log('Provider.tsx: 执行回调函数');
        const callback = callbackRef.current;

        // 重置标记和回调引用
        resetRequested.current = false;
        callbackRef.current = undefined;

        // 执行回调
        callback();
      } else {
        // 重置标记
        resetRequested.current = false;
      }
    }
  }, [styleModelList, from]);

  // 在组件加载时记录Provider的唯一标识，帮助调试
  useEffect(() => {
    const providerId = Math.random().toString(36).substring(7);
    // console.log(`Provider.tsx: StyleModelProvider 已加载 [ID: ${providerId}, from: ${from}]`);

    // 返回清理函数
    return () => {
      // console.log(`Provider.tsx: StyleModelProvider 已卸载 [ID: ${providerId}]`);
    };
  }, [from]);

  const contextValue = {
    from,
    styleModelList,
    setStyleModelList,
    keyword,
    setKeyword,
    initStyleModelList
  };

  // console.log('Provider.tsx: 渲染 StyleModelContext.Provider', { from, hasChildren: !!children });

  return (
    <StyleModelContext.Provider value={contextValue}>
      {children}
    </StyleModelContext.Provider>
  );
}
