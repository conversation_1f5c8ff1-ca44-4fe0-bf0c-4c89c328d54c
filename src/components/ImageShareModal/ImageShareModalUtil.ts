import { downloadFile } from '@/utils/blob';
import html2canvas from 'html2canvas';
import Konva from 'konva';

const CARD_WIDTH = 440;
const CARD_HEIGHT = 644;

/**
 * 获得分享card截图,补充模糊背景（html2canvas不支持）。
 *
 * @param cardEl
 * @param bgClassName
 * @param bgSrc
 */
export const downloadCardImageAsync = async (
  cardEl: HTMLDivElement,
  bgClassName: string,
  imgClassName: string,
  bgSrc: string
) => {
  const cloneCard = getCloneCardDiv(cardEl, bgClassName, imgClassName);
  document.body.appendChild(cloneCard);
  const contentCanvas = await getHtmlImageAsync(cloneCard);
  const cardCanvas = await getCardCanvasAsync(
    cloneCard,
    contentCanvas,
    bgSrc,
    CARD_WIDTH,
    CARD_HEIGHT
  );
  document.body.removeChild(cloneCard);
  downloadCanvasImage(cardCanvas);
};

/**
 * 获得成图。
 *
 * @param cardel
 * @param contentCanvas
 * @param bgSrc
 * @param width
 * @param height
 * @returns
 */
const getCardCanvasAsync = (
  cardel: HTMLDivElement,
  contentCanvas: HTMLCanvasElement,
  bgSrc: string,
  width: number,
  height: number
) => {
  return new Promise<HTMLCanvasElement>((res, rej) => {
    // 初始化舞台
    const stage = new Konva.Stage({
      container: cardel,
      width: width,
      height: height
    });

    const layer = new Konva.Layer();
    stage.add(layer);

    // 加载背景
    Konva.Image.fromURL(bgSrc, (bgImg) => {
      bgImg.setAttrs({
        x: 0,
        y: 0,
        width: width,
        height: height,
        blurRadius: 50
      });
      bgImg.cache();
      bgImg.filters([Konva.Filters.Blur]);
      layer.add(bgImg);
      // 渐变遮罩
      const linearGradMask = new Konva.Rect({
        x: 0,
        y: 0,
        width: width,
        height: height,
        fillLinearGradientStartPoint: { x: 0, y: 0 },
        fillLinearGradientEndPoint: { x: 0, y: height },
        fillLinearGradientColorStops: [0, '#0a0a0a', 1, 'rgba(10, 10, 10, 0.5)']
      });
      layer.add(linearGradMask);
      // 添加内容
      const contentImage = new Konva.Image({
        x: 0,
        y: 0,
        width: width,
        height: height,
        image: contentCanvas
      });
      layer.add(contentImage);
      // 绘制
      layer.batchDraw();
      res(stage.toCanvas());
      stage.destroy();
    });
  });
};

/**
 * 下载图片。
 *
 * @param canvas
 */
const downloadCanvasImage = (canvas: HTMLCanvasElement) => {
  canvas.toBlob((data) => {
    if (data) {
      downloadFile(data, 'shareCard.jpg');
    }
  }, 'image/jpeg');
};

/**
 * 克隆一个屏外卡片。
 *
 * @param cardEl
 * @param bgClassName
 * @returns
 */
const getCloneCardDiv = (
  cardEl: HTMLDivElement,
  bgClassName: string,
  imgClassName: string
): HTMLDivElement => {
  //克隆一个不可见元素（canvas内容不被克隆）
  const cloneCardEl = cardEl.cloneNode(true) as HTMLDivElement;
  cloneCardEl.style.position = 'fixed';
  cloneCardEl.style.zIndex = '-999';
  cloneCardEl.style.left = '-100%';
  const bgDiv = cloneCardEl.getElementsByClassName(
    bgClassName
  )[0] as HTMLDivElement;
  bgDiv.style.display = 'none';
  //克隆二维码内容
  const sourceQrCanvas = cardEl.getElementsByTagName('canvas')[0];
  const targetQrCanvas = cloneCardEl.getElementsByTagName('canvas')[0];
  const canvasCtx = targetQrCanvas.getContext('2d');
  canvasCtx?.drawImage(sourceQrCanvas, 0, 0);
  return cloneCardEl;
};

/**
 * html2canvas绘制(不支持模糊、必须document内元素)。
 *
 * @param cardEl
 * @returns
 */
const getHtmlImageAsync = (cardEl: HTMLDivElement) => {
  // 需要应用样式，所以保留head
  const headCompare = getNeedsIgnoreComparor(document.head);
  // 保留要卡片
  const cardCompare = getNeedsIgnoreComparor(cardEl);

  return new Promise<HTMLCanvasElement>((res, rej) => {
    html2canvas(cardEl, {
      allowTaint: true,
      useCORS: true,
      backgroundColor: null,
      ignoreElements: (el) => headCompare(el) && cardCompare(el)
    }).then((canvas) => {
      res(canvas);
    });
  });
};

/**
 * 获取一个比较器 用来比较和节点node的关系
 *
 * 返回比较器 function compare(el: element): boolean;
 * 满足一下条件之一 比较器返回false（即，不忽略，保留）
 * 1. el和node是同一个节点
 * 2. el是node的祖先节点
 * 3. el是node的后代节点
 */
function getNeedsIgnoreComparor(node: Element) {
  const mask =
    document.DOCUMENT_POSITION_CONTAINS |
    document.DOCUMENT_POSITION_CONTAINED_BY;

  return function compare(el: Element) {
    const position = node.compareDocumentPosition(el);
    return position !== 0 && (position & mask) === 0;
  };
}
