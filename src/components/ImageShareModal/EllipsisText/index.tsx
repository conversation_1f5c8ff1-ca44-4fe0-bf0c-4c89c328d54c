import styles from './index.module.less';

type EllipsisTextProps = {
  text: string;
  iconText: string;
  style?: React.CSSProperties;
};

/**
 * icon在多行文字前的样式
 *
 * @param param0
 * @returns
 */
export function EllipsisText({ text, iconText, style }: EllipsisTextProps) {
  return (
    <div style={style} className={styles.textContainer}>
      <span className={styles.content}>
        <div className={styles.iconText1}>
          <span>{iconText}</span>
        </div>
        {text}
      </span>
    </div>
  );
}
