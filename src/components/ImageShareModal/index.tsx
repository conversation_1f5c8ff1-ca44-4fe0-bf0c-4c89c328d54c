import styles from './index.module.less';
import { CrossBold, UserBoldOutlined, LinkBold } from '@meitu/candy-icons';
import { ReactComponent as LogoSvg } from './logo.svg';
import { Avatar, Button, Image, Modal, QRCode, message } from 'antd';
import { EntryTypeEnum } from '@/types';
import { EllipsisText } from './EllipsisText';
import { useEffect, useRef, useState } from 'react';
import { useCopyToClipboard } from '@/hooks';
import { downloadCardImageAsync } from './ImageShareModalUtil';
import classNames from 'classnames';

type User = {
  /** 用户昵称 */
  userName: string;
  /** 用户用户头像 */
  avatar: string;
};

const CARD_IMAGE_SIZE = 391;

type ImageShareModalProps = {
  user?: User;
  /** 可见 */
  visible: boolean;
  /** 关闭 */
  onClose?: Function;
  /** 点击回调 */
  onButtonClick?: (type: 'save' | 'copy') => void;
  /** 图片地址 */
  imageSrc: string;
  /** 分享地址 */
  shareUrl: string;
  /** 前缀icon文字 */
  preIconText: string;
  /** 描述文本 */
  text: string;
};

/**
 * 图片分享弹框。
 *
 * @param param0
 * @returns
 */
export function ImageShareModal({
  visible,
  user,
  preIconText,
  imageSrc,
  onClose,
  shareUrl,
  onButtonClick,
  text
}: ImageShareModalProps) {
  const cardRef = useRef<HTMLDivElement>(null);
  const [downloading, setDownloading] = useState(false);
  const isDownloadingRef = useRef(false);
  const copyToClipboard = useCopyToClipboard({
    failText: '复制失败',
    successText: '已复制链接'
  });

  /**
   * 复制链接。
   */
  const copyUrl = () => {
    const text = `灵感探索，激发想象。来WHEE一起体验更多AI功能吧～\n${shareUrl}&entryType=${EntryTypeEnum.CopyUrl}`;

    copyToClipboard(text);
    if (onButtonClick) {
      onButtonClick('copy');
    }
  };

  useEffect(() => {
    if (downloading) {
      setTimeout(() => {
        downloadImgAsync();
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [downloading]);

  useEffect(() => {}, [imageSrc]);

  /**
   * 下载图片。
   *
   * @param canvas
   */
  const downloadImgAsync = async () => {
    if (cardRef.current) {
      await downloadCardImageAsync(
        cardRef.current,
        styles.cardBackground,
        styles.shareImage,
        imageSrc
      );
    }
    setDownloading(false);
    isDownloadingRef.current = false;
    message.success('已保存图片。');
    if (onButtonClick) {
      onButtonClick('save');
    }
  };

  const downloadImg = () => {
    if (!isDownloadingRef.current && cardRef.current && imageSrc) {
      setDownloading(true);
      isDownloadingRef.current = true;
    }
  };

  const onCloseButtonClick = () => {
    if (onClose) {
      onClose();
    }
  };

  const onImageLoad = (event: any) => {
    const img = event.target as HTMLImageElement;
    const imgRate = img.naturalWidth / img.naturalHeight;
    const targetWidth =
      imgRate > 1 ? CARD_IMAGE_SIZE : CARD_IMAGE_SIZE * imgRate;
    const targetHeight =
      imgRate > 1 ? CARD_IMAGE_SIZE / imgRate : CARD_IMAGE_SIZE;
    img.style.width = targetWidth + 'px';
    img.style.height = targetHeight + 'px';
  };

  return (
    <Modal
      open={visible}
      footer={null}
      className={styles.shareModal}
      // maskClosable={true} 失效
      width={'100%'}
      classNames={{
        header: styles.shareModalHeader,
        footer: styles.shareModalFooter,
        body: styles.shareModalBody,
        content: styles.shareModalContent,
        mask: styles.shareModalMask
      }}
      closeIcon={false}
    >
      <div onClick={onCloseButtonClick} className={styles.modalContent}>
        <div
          onClick={(e) => e.stopPropagation()}
          className={styles.shareContainer}
        >
          <div className={styles.shareContent}>
            <div ref={cardRef} className={styles.shareCard}>
              <div
                style={{ backgroundImage: `url(${imageSrc})` }}
                className={styles.cardBackground}
              >
                <div className={styles.bgFilter}></div>
              </div>
              <div className={styles.logoContainer}>
                <LogoSvg width={132} height={43} />
                {/* <Image width={132} height={43} src={logoSrc} preview={false} /> */}
              </div>
              <span className={styles.shareText}>
                来自好友的分享，来 WHEE一起体验更多AI功能吧～
              </span>
              <div className={styles.shareImageContainer}>
                <Image
                  className={styles.shareImage}
                  preview={false}
                  src={imageSrc}
                  onLoad={onImageLoad}
                />
              </div>
              <div className={styles.infoContainer}>
                <div className={styles.leftInfoContainer}>
                  <div className={styles.userInfo}>
                    <Avatar
                      className={styles.avatar}
                      src={user?.avatar}
                      icon={<UserBoldOutlined />}
                    />
                    <span>{user?.userName}</span>
                  </div>
                  <EllipsisText text={text} iconText={preIconText} />
                </div>
                <div className={styles.qrcodeContainer}>
                  <QRCode
                    className={styles.qrcode}
                    size={96}
                    value={`${shareUrl}&entryType=${EntryTypeEnum.QrCode}`}
                  />
                </div>
              </div>
            </div>
            <div className={styles.urlContainer}>
              <span>{shareUrl}</span>
            </div>
            <div className={styles.buttonContainer}>
              <Button
                block
                className={classNames(
                  styles.shareButton,
                  styles.shareButtonDownload
                )}
                type="default"
                onClick={downloadImg}
                loading={downloading}
              >
                保存图片
              </Button>
              <Button
                block
                className={classNames(
                  styles.shareButton,
                  styles.shareButtonLink
                )}
                type="primary"
                onClick={copyUrl}
              >
                <LinkBold />
                复制链接
              </Button>
            </div>
          </div>
          <div
            className={styles.closeButton}
            onClick={() => {
              onCloseButtonClick();
            }}
          >
            <CrossBold />
          </div>
        </div>
      </div>
    </Modal>
  );
}
