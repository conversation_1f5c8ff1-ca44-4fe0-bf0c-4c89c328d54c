@import '~@/styles/variables.less';

.share-modal:global(.@{ant-prefix}-modal) {
  margin: auto;
  position: relative;
  height: 100%;
  top: 0px;
  width: 100%;
  height: 100%;
  padding-bottom: 0px !important;
}

.share-modal-header {
  display: none;
}

.share-modal-footer {
  display: none;
}

.share-modal-content:global(.@{ant-prefix}-modal-content) {
  width: 100%;
  height: 100%;
  background-color: transparent !important;
  box-shadow: none !important;
  padding: 0px !important;
}

.share-modal-body {
  width: 100%;
  height: 100%;
  background-color: transparent;
}

.share-modal-mask {
  background-color: rgba(0, 0, 0, 0.75) !important;
}

.modal-content {
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
}

.share-container {
  position: absolute;
  width: 544px;
  height: 780px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

@media (max-height: 780px) {
  .share-container {
    top: 0;
    transform: translate(-50%, 0);
  }
}

.share-content {
  position: absolute;
  width: 440px;
  left: 50%;
  transform: translateX(-50%);
}

.share-card {
  position: relative;
  width: 440px;
  height: 644px;
  text-align: center;
  padding-top: 24px;
  padding-bottom: 24px;
  overflow: hidden;
}

.card-background {
  z-index: -1;
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.bg-filter {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  background: linear-gradient(180deg, #0a0a0a 0%, rgba(10, 10, 10, 0.5) 100%);
  backdrop-filter: blur(50px);
}

.logo-container {
  display: block;
  position: relative;
  width: 132px;
  height: 43px;
  margin: auto;
}

.share-text {
  color: rgba(255, 255, 255, 0.7);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin-top: 16px;
  display: inline-block;
}

.share-image-container {
  position: relative;
  margin: auto;
  margin-top: 16px;
  width: 391px;
  height: 391px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-image {
  display: block;
  max-width: 391px;
  max-height: 391px;
  object-fit: contain;
  border-radius: 4px;
}

.info-container {
  position: relative;
  width: 391px;
  height: 96px;
  margin: auto;
  margin-top: 16px;
}

.left-info-container {
  width: 272px;
  height: 96px;
  padding-top: 6px;
}

.user-info {
  margin-left: 0px;
  text-align: left;
  height: 32px;
  display: flex;
  align-items: center;

  span {
    color: rgba(255, 255, 255, 0.7);
    margin-left: 12px;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}

.avatar {
  width: 32px;
  height: 32px;
}

.qrcode-container {
  border-radius: 8px;
  background-color: #fff;
  width: 96px;
  height: 96px;
  position: absolute;
  right: 0px;
  top: 0px;
}

.qrcode:global(.@{ant-prefix}-qrcode) {
  padding: 6px;
  width: 96px;
  height: 96px;
}

.url-container {
  width: 100%;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
  margin-top: 16px;

  span {
    flex: 1 0 0;
    color: rgba(255, 255, 255, 0.7);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    white-space: normal;
    word-break: break-all;
  }
}

.button-container {
  margin-top: 16px;
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px;
  gap: 16px;
}

.share-button:global(.@{ant-prefix}-btn) {
  flex: 1 0 0;
  height: 48px;
  border-radius: 8px;
  /* text_16 */
  span {
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
}

.share-button-download:global(.@{ant-prefix}-btn) {
  span {
    color: #1c1d1f;
  }
}

.share-button-link:global(.@{ant-prefix}-btn) {
  background: var(--background-btnAi, #3549ff);
}

.close-button {
  position: absolute;
  right: 0px;
  top: 0px;
  width: 36px;
  height: 36px;
  border-radius: 18px;
  border: solid 2px rgba(255, 255, 255, 0.25);
  cursor: pointer;

  svg {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 18px;
    height: 18px;
    fill: #fff;
  }
}
