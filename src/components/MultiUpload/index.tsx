import classNames from 'classnames';
import { ReactNode, useState } from 'react';
import type { UploadFile } from 'antd';
import { Modal, Progress, message, Space, Tooltip } from 'antd';
import {
  UploadBlack,
  QuestionMarkCircleBoldOutlined
} from '@meitu/candy-icons';

import { type UploadProps, Upload, beforeUploadHelper } from '../Upload';
import styles from './style.module.less';
import { UploadStatus } from '../Upload/Upload';
import { UPLOAD_ERR_STATUS } from '../Upload/constants';
import { Button } from '../Button';

export type UploadResult = {
  url: string;
  previewUrl: string;
};

export interface MultiUploadProps extends Pick<UploadProps, 'disabled'> {
  onFinish?: (value: UploadResult[]) => void;
  /**
   * 是否跟随父级元素自适应大小
   */
  adaptive?:
    | boolean
    | {
        enabled: boolean;

        // enable为true时 是否展示边框
        // adaptive为true时 默认不展示边框
        showBorder?: boolean;
      };

  onButtonClick?(): void;
  // 上传区域
  renderContent?: ReactNode;
  // 覆盖类名
  className?: string;
  // 单次最大上传数量
  maxCount?: number;
  // 上传参数，目前除了风格模型训练，其他不需要
  uploadParam?: {};
  // 上传任务类型
  taskCategory?: string;
}

export function MultiUpload(props: MultiUploadProps) {
  const {
    onFinish: onFinished,
    adaptive = false,
    disabled,
    onButtonClick,
    renderContent,
    className,
    maxCount = 200,
    uploadParam
  } = props;
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [open, setOpen] = useState(false); // 是否显示图片进度
  const [percent, setPercent] = useState(0); // 图片上传进度
  const [finishedTotal, setFinishedTotal] = useState(0); // 图片上传完成总数
  const [hasLimitError, setHasLimitError] = useState(false); // 有不满足尺寸的
  const [hasNumberError, setHasNumberError] = useState(false); // 有不满足数量的
  const [hasTypeError, setHasTypeError] = useState(false); // 有不满足格式的
  const [overFileLength, setOverFileLength] = useState(0); // 超出maxCount的数量

  const onFileChange: UploadProps['onChange'] = (info) => {
    const { fileList } = info;
    setFileList(fileList);
    const filesTotal = fileList.length;
    const filesValid = fileList.filter((item) => item.status); // 需要过滤掉没有通过校验的文件
    const filesStatus = filesValid.map((item) => item.status);
    const filesResponse = filesValid.map((item) => item.response);
    const filesValidResponse = filesResponse.filter(Boolean); // 过滤掉无效响应的文件

    const uploading = filesStatus.some(
      (status) => status === UploadStatus.Uploading
    );

    const errors = filesStatus.filter(
      (status) => status === UploadStatus.Error
    );

    const reviewErrorLength = fileList.filter(
      (file) => file.error?.status === UPLOAD_ERR_STATUS.REVIEW_FAILED
    )?.length;

    const fileFinishedTotal = filesStatus.filter(
      (item) => item === UploadStatus.Done
    );
    const currentFinishedTotal = fileFinishedTotal.length;

    setOpen(true); // 显示图片上传进度
    setFinishedTotal(currentFinishedTotal); // 设置图片上传完成总数
    setPercent(Math.floor((currentFinishedTotal / fileList.length) * 100));

    if (!uploading) {
      setPercent(100);

      setTimeout(() => {
        const limitMsg = hasLimitError ? '单张图片大小不能超过10M！' : '';
        const typeMsg = hasTypeError ? '请上传合适格式（JPG/PNG）的文件！' : '';
        // 如果有触发了最大数量限制，失败的数量要+超出未上传的数量
        const failedLength = hasNumberError
          ? filesTotal - currentFinishedTotal + overFileLength
          : filesTotal - currentFinishedTotal;
        const reviewErrorMsg = reviewErrorLength
          ? `，包含${reviewErrorLength}张不合规。`
          : '。';

        const limitCountMsg = hasNumberError
          ? `最多只允许上传${maxCount}张，`
          : '';

        const reUploadMsg =
          hasLimitError || !!errors.length ? '请重新选择照片上传。' : '';

        const uploadInfo = `${limitCountMsg}已上传 ${currentFinishedTotal} 张，失败 ${failedLength} 张${reviewErrorMsg}${reUploadMsg}`;

        // 如果包含有格式错误的，只提示格式
        if (hasTypeError) {
          message.info(`${typeMsg}`);
        } else if (hasLimitError) {
          message.info(`${limitMsg}`);
        } else {
          message.info(`${uploadInfo}`);
        }

        setPercent(0);
        setOpen(false);
        setHasLimitError(false);
        setHasNumberError(false);
        setHasTypeError(false);
        setOverFileLength(0);
        setFileList([]);
        onFinished?.(filesValidResponse);
      }, 1000);
    }
  };

  const renderUploadBtn = () => {
    return (
      <>
        <Button
          icon={<UploadBlack />}
          className={classNames(styles.uploadBtn, {
            [styles.disabled]: disabled
          })}
          onClick={onButtonClick}
        >
          上传图片
        </Button>
        <div className={styles.uploadDescWrap}>
          <span className={styles.uploadDesc}>拖拽图片&点击上传</span>
          <Tooltip
            overlayInnerStyle={{ width: '300px' }}
            title={
              <Space direction="vertical">
                <div>风格模型训练 Tips</div>
                <div>
                  图片内容建议：统一风格和内容类型，保证主体和主题明确，避免杂乱和元素过小等。
                </div>
                <div>图片上传建议：数量 20~50 张，分辨率 512 x 512 及以上</div>
                <div>
                  高级设置建议：数值越高训练时间越长，建议训练次数不超过5，迭代周期不超过40
                </div>
              </Space>
            }
          >
            <QuestionMarkCircleBoldOutlined />
          </Tooltip>
        </div>
      </>
    );
  };

  const uniformAdaptive =
    typeof adaptive === 'undefined'
      ? {
          enabled: false
        }
      : typeof adaptive === 'object'
      ? adaptive
      : adaptive === true
      ? {
          enabled: true,
          showBorder: false
        }
      : {
          enabled: false
        };

  return (
    <div
      className={classNames(styles.multiUpload, {
        [styles.adaptive]: uniformAdaptive.enabled,
        [styles.hideBorder]:
          uniformAdaptive.enabled && !uniformAdaptive.showBorder
      })}
    >
      <Upload
        type="drag"
        disabled={disabled}
        className={classNames(styles.upload, className)}
        fileList={fileList}
        onChange={onFileChange}
        uploadParam={uploadParam}
        multiple={maxCount > 1}
        taskCategory={props.taskCategory}
        showUploadList={false}
        beforeUpload={beforeUploadHelper({
          onLimitError: () => {
            setHasLimitError(true);
          },
          onTypeError: () => {
            setHasTypeError(true);
          },
          onNumberError: (file, overFileLength) => {
            setOverFileLength(overFileLength);
            setHasNumberError(true);
          },
          num: maxCount,
          showErrorMessage: false
        })}
        maxCount={maxCount}
      >
        {renderContent ?? renderUploadBtn()}
      </Upload>
      <Modal
        open={open}
        closable={false}
        footer={null}
        width={360}
        centered
        className={styles.uploadProgressModal}
      >
        <div className={styles.uploadProgress}>
          <span className={styles.uploadProgressTitle}>
            图片上传中：{`${finishedTotal}/${fileList.length}`}
          </span>
          <Progress
            percent={percent}
            status="active"
            size="small"
            showInfo={false}
            strokeColor={{ from: '#F201F7', to: '#FFBA53' }}
          />
        </div>
      </Modal>
    </div>
  );
}
