@import '~@/styles/variables.less';

.multiUpload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;

  .upload {
    .uploadBtn {
      width: 150px;
      height: 54px;

      &.disabled {
        color: @content-btn-primary;
        opacity: 0.3;
        cursor: not-allowed;
      }
    }

    :global {
      .ant-upload-drag {
        width: 450px;
        height: 300px;
        background: transparent;
      }
    }
  }

  .uploadDescWrap {
    margin-top: 8px;
    color: @color-text-secondary;
  }

  .uploadDesc {
    margin-right: 4px;
  }

  &.adaptive {
    width: auto;
    height: 100%;
    background-color: @background-system-space-holder;
    .upload {
      width: 100%;
      height: 100%;

      :global {
        .ant-upload-drag {
          width: 100%;
          height: 100%;
        }
      }
    }

    &.hide-border {
      .upload {
        :global {
          .ant-upload-drag {
            border: none;
          }
        }
      }
    }
  }
}

.uploadProgressModal {
  .uploadProgress {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .uploadProgressTitle {
      font-size: 16px;
      font-weight: 600;
    }

    :global {
      .ant-progress-line {
        margin: 0;
        line-height: 0;
      }
    }
  }
}

.toolTip {
  width: 500px;
}
