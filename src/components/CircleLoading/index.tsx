import classNames from 'classnames';
import styles from './index.module.less';

type CircleLoadingProps = {
  className?: string;
};

export function CircleLoading({ className }: CircleLoadingProps) {
  return (
    <svg
      className={classNames(styles.loading, className)}
      width="50"
      height="50"
      viewBox="0 0 50 50"
    >
      <circle className={styles.track} cx="25" cy="25" r="20"></circle>
      <circle className={styles.progress} cx="25" cy="25" r="20"></circle>
    </svg>
  );
}
