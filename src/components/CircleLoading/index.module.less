.loading {
  animation: rotate 2s linear infinite;

  .progress,
  .track {
    fill: none;
    stroke-width: 4;
    stroke-linecap: round;
    stroke: var(--stroke-color, white);
  }

  .progress {
    stroke-dasharray: 126;
    stroke-dashoffset: 0;
    animation: dash 1.5s ease-in-out infinite;
  }
  .track {
    stroke-opacity: 0.2;
  }

  @keyframes rotate {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes dash {
    0% {
      stroke-dasharray: 1, 126;
      stroke-dashoffset: 0;
    }
    50% {
      stroke-dasharray: 80, 126;
      stroke-dashoffset: -40;
    }
    100% {
      stroke-dasharray: 1, 126;
      stroke-dashoffset: -126;
    }
  }
}
