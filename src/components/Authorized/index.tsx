import { useAccount } from '@/hooks';
import { cloneElement, PropsWithChildren, useEffect } from 'react';

/**
 * 需要登录认证的组件
 *
 * @param props 组件属性
 */
export function Authorized(props: PropsWithChildren) {
  const { isLogin, login } = useAccount();

  useEffect(() => {
    if (!isLogin) {
      login();
    }
  }, [isLogin, login]);

  return <>{isLogin ? props.children : null}</>;
}

/**
 * 如果未登陆 则不渲染
 *
 * @param props
 * @returns
 */
function NoRender(props: PropsWithChildren) {
  const { isLogin } = useAccount();

  if (!isLogin) {
    return <></>;
  }

  return <>{props.children}</>;
}

Authorized.NoRender = NoRender;

namespace EventHandlerProxyType {
  /**
   * 为true时，表示始终触发
   * 为'un-login'时，表示在未登陆时触发
   * 为'login'时，表示在登陆时触发
   */
  export type TreatEventScope = undefined | boolean | 'un-login' | 'login';

  export type ProxyEventOptions = {
    eventKey: string;
    /**
     * 事件处理执行stopPropagation的时机
     */
    stopPropagation?: TreatEventScope;
    /**
     * 事件处理执行preventDefault的时机
     */
    preventDefault?: TreatEventScope;
    /**
     * 登陆后是否继续执行
     */
    continueAfterLogin?: boolean;
  };
}

type EventHandlerProxyProps = {
  children: React.ReactElement | null;
  events: EventHandlerProxyType.ProxyEventOptions[];
};
/**
 * 如果未登陆 eventKey事件出发时会先调起登陆弹窗
 * @param
 * @returns
 */
function EventHandlerProxy({ children, events }: EventHandlerProxyProps) {
  const { isLogin, openLoginPopup } = useAccount();

  const handlerProxy = events.reduce((acc, options) => {
    const { eventKey, stopPropagation, preventDefault, continueAfterLogin } =
      options;
    const originalHandler = children?.props?.[eventKey];
    const handler = async (
      event?: React.MouseEvent<HTMLElement, MouseEvent>
    ) => {
      const treatEvent = (scope: EventHandlerProxyType.TreatEventScope) => {
        if (stopPropagation === scope) {
          event?.stopPropagation?.();
        }

        if (preventDefault === scope) {
          event?.preventDefault?.();
        }
      };

      treatEvent(true);

      if (!isLogin) {
        treatEvent('un-login');
        await openLoginPopup();
        return continueAfterLogin && originalHandler?.();
      }

      treatEvent('login');
      return originalHandler?.();
    };

    acc[eventKey] = handler;
    return acc;
  }, {} as any);

  return (
    children &&
    cloneElement(children, {
      ...handlerProxy
    })
  );
}

Authorized.EventHandlerProxy = EventHandlerProxy;
