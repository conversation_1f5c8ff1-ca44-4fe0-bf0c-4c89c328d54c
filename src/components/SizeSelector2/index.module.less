@import '~@/styles/variables.less';

.radioGroup {
  width: 108px;
  height: 32px;

  .selection {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;

    svg {
      width: 16px;
      height: 16px;
    }
  }

  :global {
    .ant-select {
      width: 100%;
      height: 100%;
    }

    .rc-virtual-list-scrollbar-thumb {
      width: 50% !important;
      background: #d0d2d6 !important;
      right: -1px !important;
    }

    .ant-select-item {
      height: 34px;

      .ant-select-item-option-content {
        line-height: 34px;
      }
    }
  }

  & > .selector {
    &:global(
        .ant-select-focused.ant-select:not(.ant-select-disabled):not(
            .ant-select-customize-input
          ):not(.ant-pagination-size-changer)
      ) {
      & > :global(.ant-select-selector) {
        border-color: @stroke-input-selected;
      }
    }

    :global {
      .ant-select-arrow {
        .suffix-icon {
          pointer-events: none !important;

          svg {
            width: 10px;
            height: 10px;
          }
        }
      }
    }
  }
}

.size-title {
  color: @color-text-secondary;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 8px;
}

.size-input-number:global(.@{ant-prefix}-input-number) {
  width: 66px;
  height: 32px;
  z-index: 1;

  :global {
    .@{ant-prefix}-input-number-handler-wrap {
      z-index: 2;
    }

    .@{ant-prefix}-input-number-input-wrap {
      position: relative;
      z-index: 1;
      width: 100%;
      height: 100%;

      .ant-input-number-input {
        width: 100%;
        height: 100%;
        padding: 0 8px 1px 24px;
        line-height: 100%;
      }

      &::after {
        display: block;
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--content-inputLable, #abadb2);
        font-size: 12px;
      }
    }
  }

  &:global(.width) {
    :global {
      .@{ant-prefix}-input-number-input-wrap {
        &::after {
          content: '\5bbd';
        }
      }
    }
  }

  &:global(.height) {
    :global {
      .@{ant-prefix}-input-number-input-wrap {
        &::after {
          content: '\9ad8';
        }
      }
    }
  }
}

.linkIcon {
  margin: 0 10px;

  svg {
    width: 12px;
    height: 12px;
    color: #abadb2;
  }
}
