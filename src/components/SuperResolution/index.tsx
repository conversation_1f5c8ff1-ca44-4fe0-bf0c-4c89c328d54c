import { Form, Switch, type FormItemProps } from 'antd';
import { Collapse, Title } from '@/components';

interface SuperResolutionProps {
  formItemName?: FormItemProps['name'];
}

export function SuperResolution(props: SuperResolutionProps) {
  const { formItemName = 'superResolution' } = props;

  return process.env.REACT_APP_ENV !== 'release' ? (
    <Collapse.Panel.Section
      title={<Title tooltip="图像超分" title="超清生成" />}
      extra={
        <Form.Item name={formItemName} valuePropName="checked" noStyle>
          <Switch />
        </Form.Item>
      }
    />
  ) : null;
}
