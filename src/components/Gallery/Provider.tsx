import type { GalleryImageDetailModalRef } from './GalleryImageDetailModal';
import type { GalleryProfile } from '@/types';
import type { PropsWithChildren, RefObject } from 'react';

import { createContext, useState, useRef } from 'react';

interface GalleryContextValue {
  /** 画廊作品列表 */
  gallery: GalleryProfile[];

  modalRef?: RefObject<GalleryImageDetailModalRef>;

  /** 设置画廊作品列表 */
  setGallery: React.Dispatch<React.SetStateAction<GalleryProfile[]>>;
}

function initialContextMethod() {
  console.warn('正在使用 `GalleryContext` 的默认值');
}

/**
 * 画廊上下文
 */
export const GalleryContext = createContext<GalleryContextValue>({
  gallery: [],
  setGallery: initialContextMethod
});

export function GalleryProvider({ children }: PropsWithChildren) {
  const modalRef = useRef<GalleryImageDetailModalRef>(null);
  const [gallery, setGallery] = useState<GalleryProfile[]>([]);

  return (
    <GalleryContext.Provider
      value={{
        gallery,
        modalRef,
        setGallery
      }}
    >
      {children}
    </GalleryContext.Provider>
  );
}
