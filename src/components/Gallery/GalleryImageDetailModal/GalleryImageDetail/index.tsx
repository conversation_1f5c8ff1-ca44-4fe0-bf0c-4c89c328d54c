import { CreateLocationType, type GalleryDetail } from '@/types';

import { Skeleton, message } from 'antd';
import { generateRouteTo, trackEvent } from '@/services';
import { EditorUsableAuthorized, User } from '@/components';

import styles from '../galleryImageDetail.module.less';
import { Link } from 'react-router-dom';
import { getApplySearchParams } from '@/utils/searchParams';
import _ from 'lodash';

import { DraftType } from '@/api/types';
import { PublishInfo, PublishInfoRefType } from './PublishInfo';
import { PromptAndModelInfo } from './PromptAndModelInfo';
import { useRef, useState } from 'react';
import { BrushBold, ShareBold } from '@meitu/candy-icons';
import { getSource } from '@/utils';
import { ImageShareModal } from '@/components/ImageShareModal';
import { getAppModuleByTaskCategory } from '../../GalleryImage';

export interface GalleryImageDetailProps {
  value?: GalleryDetail;
  /** 收藏点击事件 */
  onCollectedChange?: (detail: GalleryDetail) => void;
  /** 复制点击事件 */
  onCopy?: (prompt: string, detail: GalleryDetail) => void;
  /** 创作同款点击事件 */
  onCreateClick?: (detail: GalleryDetail) => void;
  /** 模型点击事件 */
  onModelClick?: (modelId: number, detail: GalleryDetail) => void;
  /** 风格模型点击事件 */
  onStyleModelClick?: (styleModelId: string, detail: GalleryDetail) => void;

  mode?: 'host' | 'visitor';

  // 同款风格模型id
  sameModelId?: string;
  // 图片索引
  graphIndex?: number;
}

export const typeString: { [key: string]: string } = {
  [DraftType.TEXT_TO_IMAGE]: '文生图',
  [DraftType.IMAGE_TO_IMAGE]: '图生图',
  [DraftType.IP_CONCEPT]: '概念图设计'
};

export function GalleryImageDetail({
  value,
  graphIndex,
  ...props
}: GalleryImageDetailProps) {
  const {
    user,
    canUseSame,
    effectId,
    taskCategory,
    styleModelIds,
    id,
    feedId
  } = value ?? {};
  const publishInfoRef = useRef<PublishInfoRefType>(null);
  const fixturesFavorCount = () => ({
    favorCount: publishInfoRef.current?.favorCount ?? value?.favorCount ?? 0
  });
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const canShare = value?.isExcellent;
  const shareUrl = `${window.location.origin}/art?id=${id}&taskCategory=${taskCategory}`;

  const showShareModal = () => {
    if (!canShare) {
      message.warning('为确保内容品质，该作品的分享功能暂已关闭。');
      return;
    }
    trackEvent('work_model_share_click', {
      task_id: value.id,
      feed_id: value.feedId
    });
    setShareModalVisible(true);
  };

  const hideShareModal = () => {
    setShareModalVisible(false);
  };

  const onClick = () => {
    value && props.onCreateClick?.({ ...value, ...fixturesFavorCount() });
  };

  const onShareModalButtonClick = (typeCd: 'save' | 'copy') => {
    trackEvent('work_model_shared_popup_click', {
      task_id: id,
      feed_id: feedId,
      click_type: typeCd
    });
  };

  const appModule = getAppModuleByTaskCategory(taskCategory);

  const tracerParams = {
    source: getSource(),
    feedId: value?.feedId,
    location: CreateLocationType.Detail
  };

  return (
    <div className={styles.detail}>
      <div className={styles.detailContent}>
        <User {...user} />
        <div className={styles.detailShareButton}>
          <ShareBold
            className={
              canShare ? styles.detailShareIcon : styles.detailInvalidShareIcon
            }
            onClick={showShareModal}
          />
          {canShare ? (
            <ImageShareModal
              user={value.user}
              preIconText={typeString[taskCategory as string] || '未知'}
              onClose={hideShareModal}
              visible={shareModalVisible}
              imageSrc={value.images[graphIndex || 0]?.src || ''}
              shareUrl={shareUrl}
              onButtonClick={onShareModalButtonClick}
              text={value.title}
            />
          ) : null}
        </div>
        {_.isEmpty(value) ? (
          <Skeleton className={styles.publishInfo} />
        ) : (
          <PublishInfo
            ref={publishInfoRef}
            {...value}
            onCollectedChange={() => {
              props?.onCollectedChange?.({ ...value, ...fixturesFavorCount() });
            }}
            mode={props.mode}
          />
        )}

        {canUseSame && value && (
          <PromptAndModelInfo
            {...value}
            onCopy={(prompt, detail) => {
              props?.onCopy?.(prompt, { ...detail, ...fixturesFavorCount() });
            }}
            onModelClick={(modelId, detail) => {
              props.onModelClick?.(modelId, {
                ...detail,
                ...fixturesFavorCount()
              });
            }}
            onStyleModelClick={(modelId) => {
              props.onStyleModelClick?.(modelId, value);
            }}
            mode={props.mode || 'visitor'}
            sameModelId={props.sameModelId}
          />
        )}

        <div className={styles.time}>
          <p>发布时间：{value && value.publishTime}</p>
        </div>
      </div>

      {canUseSame && (
        <div className={styles.footer}>
          <EditorUsableAuthorized>
            <Link
              to={generateRouteTo(
                appModule,
                effectId
                  ? getApplySearchParams(appModule, {
                      id: String(effectId),
                      styleModelId: styleModelIds || undefined,
                      ...tracerParams
                    })
                  : undefined
              )}
              target="_blank"
              className={styles.confirmBtn}
              onClick={onClick}
            >
              <BrushBold className="btn-icon" />
              创作同款
            </Link>
          </EditorUsableAuthorized>
        </div>
      )}
    </div>
  );
}
