import type { GalleryDetail } from '@/types';
import { forwardRef, useImperativeHandle, useState } from 'react';
import { App } from 'antd';
import { CollectionSwitch } from '@/components';
import styles from '../galleryImageDetail.module.less';
import { useSetFavoriteState } from '../../hooks';
import { getSwitchCollected } from '@/utils/collection';
import { GalleryImageDetailProps, typeString } from '.';

export type PublishInfoType = GalleryDetail &
  Pick<GalleryImageDetailProps, 'onCollectedChange'> & {
    mode?: 'host' | 'visitor';
  };

export type PublishInfoRefType = {
  favorCount: number;
};

export const PublishInfo = forwardRef<PublishInfoRefType, PublishInfoType>(
  (props, ref) => {
    const {
      id,
      title,
      text,
      isFavor = false,
      favorCount = 0,
      taskCategory,
      feedId
    } = props;
    const { message } = App.useApp();
    const switchCollected = getSwitchCollected(feedId);
    const [collect, setCollect] = useState<[boolean, number]>([
      isFavor,
      favorCount
    ]);

    const setFavoriteState = useSetFavoriteState();

    const onCollectedChange = async (isCollected: boolean) => {
      props.onCollectedChange?.({
        ...props,
        isFavor: collect[0],
        favorCount: collect[1]
      });

      setCollect(([, collectedCount]) => [
        isCollected,
        isCollected ? collectedCount + 1 : collectedCount - 1
      ]);

      const result = await switchCollected(isCollected);

      if (result) {
        isCollected
          ? message.success('已收藏此作品')
          : message.info('已取消收藏');
        id && setFavoriteState(id, isCollected);
      }
    };

    useImperativeHandle(
      ref,
      () => ({
        favorCount: collect[1]
      }),
      [collect]
    );

    return (
      <div className={styles.publishInfo}>
        <h3>
          <span className={styles.title}>{title}</span>
          <span className={styles.taskCategory}>
            {typeString[taskCategory as string] || '未知'}
          </span>
        </h3>
        <p>{text}</p>
        <div className={styles.publishInfoFooter}>
          <CollectionSwitch
            isCollected={collect[0]}
            count={collect[1]}
            onChange={onCollectedChange}
            className={styles.countTag}
          />
          {/* TODO 暂时隐藏，有数据了再放开 */}
          {/* {mode === 'host' && (
              <>
                <Tooltip title="创作同款次数">
                  <>
                    <CountTag icon={<Brush />} count={templateUseCount} />
                  </>
                </Tooltip>
                <Tooltip title="浏览作品次数">
                  <>
                    <CountTag icon={<Visible />} count={viewCount} />
                  </>
                </Tooltip>
              </>
            )} */}
        </div>
      </div>
    );
  }
);
