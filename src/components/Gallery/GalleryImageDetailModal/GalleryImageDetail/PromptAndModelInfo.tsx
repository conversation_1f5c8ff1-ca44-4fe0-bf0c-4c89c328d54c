import type { GalleryDetail } from '@/types';

import { BaseModelCard } from '@/components/Model/ModelCard/BaseModelCard';
import { ChevronRightBlack, DuplicateBold } from '@meitu/candy-icons';
import { Button, Image, message } from 'antd';
import { generateRouteTo } from '@/services';
import { EditorUsableAuthorized, VerticalWrapper } from '@/components';

import styles from '../galleryImageDetail.module.less';
import { useNavigate } from 'react-router-dom';
import { useCopyToClipboard } from '@/hooks';

import { DraftType } from '@/api/types';
import { GalleryImageDetailProps } from '.';

import classNames from 'classnames';
import { useDetailModal } from '@/components/DetailModalProvider';
import { getSource } from '@/utils';
import { getAppModuleByTaskCategory } from '../../GalleryImage';

export type PromptAndModelInfoType = GalleryDetail &
  Pick<GalleryImageDetailProps, 'onCopy' | 'onModelClick'> & {
    mode: 'host' | 'visitor';
    sameModelId?: string;

    /** 风格模型点击事件 */
    onStyleModelClick?: (styleModelId: string) => void;
  };

export const PromptAndModelInfo = ({
  onModelClick,
  onStyleModelClick,
  onCopy,
  ...props
}: PromptAndModelInfoType) => {
  const { prompt, model, taskCategory, styleModelList, sameModelId } = props;

  const { addModal } = useDetailModal();

  const copyToClipboard = useCopyToClipboard({
    failText: '复制失败',
    successText: '已复制',
    onSuccess: () => {
      onCopy?.(prompt, props);
    }
  });

  const navigate = useNavigate();

  const onClick = () => {
    model?.modelId && onModelClick?.(model.modelId, props);

    /** 创作同款区分文生图/图生图 */
    const appModule = getAppModuleByTaskCategory(taskCategory);

    navigate(
      generateRouteTo(
        appModule,
        model?.modelId
          ? {
              baseModelId: String(model.modelId),
              source: getSource()
            }
          : undefined
      )
    );
  };

  return (
    <div className={styles.info}>
      {prompt ? (
        <VerticalWrapper
          label={
            <>
              提示词
              <Button
                type="link"
                icon={<DuplicateBold />}
                className={styles.copyBtn}
                onClick={copyToClipboard.bind(null, prompt ?? '')}
              >
                复制提示词
              </Button>
            </>
          }
        >
          <p>{prompt}</p>
        </VerticalWrapper>
      ) : null}

      <VerticalWrapper label="基础模型">
        <EditorUsableAuthorized>
          <div>
            <BaseModelCard
              {...model}
              extra={
                <div className={styles.anchorRight}>
                  <ChevronRightBlack />
                </div>
              }
              className={styles.baseModel}
              onClick={onClick}
              onExtraClick={onClick}
            />
          </div>
        </EditorUsableAuthorized>
      </VerticalWrapper>

      {styleModelList && styleModelList.length > 0 && (
        <VerticalWrapper label="风格模型">
          <EditorUsableAuthorized>
            <div className={styles.styleModel}>
              {styleModelList.slice(0, 5).map((item) => (
                <div
                  className={classNames(
                    styles.styleModelItem,
                    sameModelId === String(item.id) && styles.disable
                  )}
                  onClick={() => {
                    onStyleModelClick?.(item.id);
                    if (sameModelId === String(item.id)) return;

                    if (item.hasPublished) {
                      addModal({ id: item.id, taskCategory: DraftType.MODEL });
                    } else {
                      message.warning('此模型未发布到模型广场，敬请期待');
                    }
                  }}
                >
                  <Image
                    key={item.id ?? ''}
                    src={item.picUrl ?? ''}
                    className={styles.image}
                    width={20}
                    height={20}
                    preview={false}
                  />
                  <span className={styles.modelName}>{item.name}</span>
                </div>
              ))}
            </div>
          </EditorUsableAuthorized>
        </VerticalWrapper>
      )}
    </div>
  );
};
