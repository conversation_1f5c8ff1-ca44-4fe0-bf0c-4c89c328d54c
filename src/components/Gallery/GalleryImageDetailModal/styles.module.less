@import '~@/styles/variables.less';

.modal-wrap:global(.@{ant-prefix}-modal-wrap) {
  top: 56px !important;
  background: #f6f7fa;
  padding: 16px;
  z-index: 900 !important;
}

.modal:global(.@{ant-prefix}-modal) {
  top: 0;
  width: 65vw;
  min-width: 1200px;
  height: 100%;
  padding: 0;
  border-radius: @border-radius-lg;

  :global .@{ant-prefix}-modal-content {
    height: 100%;
    padding: 0;
    background-color: transparent;
    box-shadow: none;

    .@{ant-prefix}-modal-close {
      top: 0;
      right: -40px;
      width: 32px;
      height: 32px;
      background: @base-white-opacity-100;
      border-radius: 50%;
      cursor: pointer;

      svg {
        width: 18px;
        height: 18px;
        color: @content-system-quaternary;
      }
    }

    .@{ant-prefix}-modal-body {
      height: 100%;
    }
  }

  :global(.@{ant-prefix}-image-mask) {
    &:hover {
      :local .comparing-action {
        opacity: 1;
      }
    }
  }
}

.tool-box {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 9;
  display: flex;
  flex-direction: column;
  gap: 8px;

  .modal-close-btn {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: @background-btn-secondary;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.06),
      0px 0px 2px 0px rgba(0, 0, 0, 0.04);

    svg {
      width: 20px;
      height: 20px;
    }
  }
}

.model-container {
  width: 100%;
  min-height: 100%;
  overflow: hidden;

  .modal-detail {
    width: 1056px;
    height: 77vh;
    min-height: 480px;
    max-height: 850px;
    margin: 0 auto;
    margin-bottom: @size-lg;
    border-radius: @size-sm;
    position: relative;

    .work-detail-preview-container {
      flex: 1 1 0;
    }

    .work-detail-info-container {
      flex: 0 0 374px;
    }
  }
}

.container-preview-content {
  height: calc(100% + @size) !important;

  :global .graph-preview-content {
    padding: @size-sm;
    max-width: none;
  }
}

.container-preview-tabs {
  :global .@{ant-prefix}-tabs {
    height: 100%;

    &-nav {
      margin-top: 0 !important;
      margin-bottom: @size-sm !important;
      max-width: 380px !important;

      &-wrap {
        justify-content: flex-start !important;
      }

      .@{ant-prefix}-tabs-tab {
        width: 69px !important;
        height: 69px !important;
      }
    }

    &-content {
      height: 100%;
      display: flex;
      align-items: center;
    }

    &-tabpane {
      height: 100%;
    }
  }
}

.comparing-action {
  position: absolute !important;
  right: @size-xs;
  bottom: @size-xs;
  opacity: 0;
}

.comparing-mask {
  background-color: @color-bg-layout;
}

.upscaler-corner {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 4px 8px;
  border-radius: 13px;
  background: @background-tag-amount;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  color: @base-white-opacity-100;
  cursor: pointer;
  z-index: 3;
}

.no-transition {
  transition: none;
}
