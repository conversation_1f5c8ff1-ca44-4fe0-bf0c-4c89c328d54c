import type { GalleryDetail, Graph, SwitchType } from '@/types';
import type { GalleryImageDetailProps } from './GalleryImageDetail';

import { Modal, Tooltip } from 'antd';
import { SingleImagePreview, DetailContainer } from '@/components';
import { forwardRef, useEffect, useState } from 'react';
import GraphPreview from '@/components/GraphPreview';
import { CompareGraphAction } from '@/components/GraphPreview/CompareGraphAction';
import { GalleryImageDetail } from './GalleryImageDetail';
import { fetchGalleryDetail } from '@/api';

import styles from './styles.module.less';
import { CrossBold, HomeBold } from '@meitu/candy-icons';
import classNames from 'classnames';
import { DraftType } from '@/api/types';
import {
  DetailModalContext,
  useDetailModal
} from '@/components/DetailModalProvider';

export interface GalleryImageDetailModalRef {
  open: (feedId: string, taskCategory?: DraftType) => void;
  close: () => void;
}

export interface GalleryImageDetailModalProps
  extends Omit<GalleryImageDetailProps, 'value'> {
  onCompare?: (detail: GalleryDetail) => void;
  onGraphChange?: (
    switchType: SwitchType,
    src: string,
    detail: GalleryDetail
  ) => void;
  mode?: 'host' | 'visitor';
  afterClose?: () => void;
  // 同款风格模型id
  sameModelId?: string;

  id?: string;
  taskCategory?: DraftType;
}

export const GalleryImageDetailModal = forwardRef<
  GalleryImageDetailModalRef,
  GalleryImageDetailModalProps
>((props, ref) => {
  const [galleryDetail, setGalleryDetail] = useState<GalleryDetail>();
  // 用于上报判断 next | last
  const [graphIndex, setGraphIndex] = useState(0);
  const [compareStatus, setCompareStatus] = useState(false);

  useEffect(() => {
    fetchGalleryDetail(
      props.id ?? '',
      props.taskCategory ?? DraftType.TEXT_TO_IMAGE
    ).then((res) => {
      setGalleryDetail(res);
    });
  }, [props.id, props.taskCategory]);

  const { removeModal, closeAllModals } = useDetailModal();

  const onCancel = () => {
    props.afterClose?.();

    removeModal({ id: props.id || '' });
  };
  // 回到广场
  const backList = () => {
    props.afterClose?.();

    closeAllModals();
  };

  const onChange = (graph: Graph, index?: number, switchType?: SwitchType) => {
    if (index === undefined || index === graphIndex) {
      return;
    }

    graph.src &&
      galleryDetail &&
      props.onGraphChange?.(switchType ?? 'click', graph.src, galleryDetail);

    setGraphIndex(index);
  };

  const renderMask = (galleryDetail: GalleryDetail) => {
    const activeGraph = galleryDetail?.images[graphIndex];

    const activeIsUpscaler = activeGraph?.isUpscaler;
    const originalPic = activeIsUpscaler
      ? activeGraph.hdOriginUrl
      : galleryDetail?.originalPic;

    return (
      <>
        {activeIsUpscaler && !compareStatus && (
          <Tooltip title={`分辨率提升：${activeGraph.resolution}`}>
            <div
              className={styles.upscalerCorner}
              style={{ borderRadius: '4px', padding: '0 6px' }}
            >
              超分
            </div>
          </Tooltip>
        )}
        {originalPic && (
          <CompareGraphAction
            originGraph={originalPic}
            zoomMode={galleryDetail.zoomMode}
            className={styles.comparingAction}
            maskClassName={classNames(
              styles.comparingMask,
              activeIsUpscaler && styles.noTransition
            )}
            onCompare={() => {
              props.onCompare?.(galleryDetail);
            }}
            getCompareStatus={(status: boolean) => {
              setCompareStatus(status);
            }}
          />
        )}
      </>
    );
  };

  return (
    <Modal
      open={true}
      maskClosable={true}
      footer={null}
      className={styles.modal}
      wrapClassName={styles.modalWrap}
      onCancel={onCancel}
      closeIcon={null}
      mask={false}
      width={'100%'}
    >
      <div className={styles.toolBox}>
        <div className={styles.modalCloseBtn} onClick={onCancel}>
          <CrossBold />
        </div>
        <DetailModalContext.Consumer>
          {({ modals }) => {
            return (
              modals.length > 1 && (
                <div className={styles.modalCloseBtn} onClick={backList}>
                  <HomeBold />
                </div>
              )
            );
          }}
        </DetailModalContext.Consumer>
      </div>
      <div onClick={onCancel} className={styles.modelContainer}>
        <div
          className={styles.modalDetail}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <DetailContainer
            previewClassName={styles.workDetailPreviewContainer}
            className={styles.workDetailInfoContainer}
            preview={
              galleryDetail &&
              (galleryDetail.images.length > 1 ? (
                <GraphPreview
                  size={galleryDetail.size}
                  batch={galleryDetail.images}
                  tabsClassName={styles.containerPreviewTabs}
                  className={styles.containerPreviewContent}
                  mask={renderMask(galleryDetail)}
                  onChange={onChange}
                />
              ) : (
                <SingleImagePreview
                  src={galleryDetail.images[0]?.src ?? ''}
                  mask={renderMask(galleryDetail)}
                  size={galleryDetail.size}
                />
              ))
            }
          >
            <GalleryImageDetail
              value={galleryDetail}
              graphIndex={graphIndex}
              onCollectedChange={props.onCollectedChange}
              onCopy={props.onCopy}
              onModelClick={props.onModelClick}
              onStyleModelClick={props.onStyleModelClick}
              onCreateClick={props.onCreateClick}
              mode={props.mode}
              sameModelId={props.sameModelId}
            />
          </DetailContainer>
        </div>
      </div>
    </Modal>
  );
});
