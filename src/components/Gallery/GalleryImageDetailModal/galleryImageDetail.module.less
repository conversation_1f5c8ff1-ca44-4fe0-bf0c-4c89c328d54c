@import '~@/styles/variables.less';

.detail {
  height: 100%;
  position: relative;
  padding: @size-lg;
  background-color: @background-system-frame-floatpanel;

  &-content {
    position: relative;
    height: 100%;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 60px;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  p {
    margin-bottom: 0;
    color: @content-system-tertiary;
    line-height: @size-md;
    word-break: break-all;
  }

  &-share-button {
    position: absolute;
    right: 0px;
    top: 0px;
  }

  &-share-icon svg {
    color: #000;
    cursor: pointer;
    width: 20px;
    height: 20px;
  }

  &-invalid-share-icon svg {
    color: #aaa;
    cursor: default;
    width: 20px;
    height: 20px;
  }

  .publish-info {
    padding: @size 0;
    border-bottom: 1px solid @stroke-system-separator;

    h3 {
      margin-bottom: @size-sm;
      color: @content-system-primary;
      font-size: @size-ms;
      line-height: 22px;
    }

    .title {
      margin-right: 6px;
    }

    .task-category {
      display: inline-block;
      height: 18px;
      padding: 1px 6px;
      background: @background-tag-work-type;
      border-radius: 4px;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      color: @content-tag-work-type;
      line-height: 16px;
      vertical-align: text-top;
    }

    &-footer {
      display: flex;
      align-items: center;
      // justify-content: space-between;
      margin-top: @size-sm;
      font-size: @size-sm;
    }
  }

  .count-tag {
    display: flex;
    align-items: center;
    min-width: 48px;
    height: @size-lg;
    padding: 0 @size-xs;
    background-color: @background-tag;
    color: @content-system-primary;
    border-radius: @size-xxs;
    margin-right: @size-sm;
    font-size: @font-size-sm;

    span {
      margin-right: @size-xxs;
      font-size: @font-size;
      color: @content-system-primary;
    }
  }

  .info {
    padding-bottom: @size;

    .copy-btn:global(.@{ant-prefix}-btn-link) {
      display: flex;
      align-items: center;
      color: @content-system-link;
      padding: 0;
      font-size: @size-sm;
      transition: color 0.2s ease;

      &:hover {
        color: @content-system-link-hover;
      }

      :global(.@{ant-prefix}icon + span) {
        margin-left: @size-xxs;
      }

      path {
        fill: @content-system-link;
      }
    }
  }

  .time {
    border-top: 1px solid @stroke-system-separator;
    padding-top: @size;
  }

  .anchor-right {
    position: absolute;
    top: @size;
    right: @size-sm;
  }

  .base-model {
    overflow: hidden;
    transition: background-color 0.3s ease;

    :global(.model-card-extra) {
      background-color: transparent;
    }

    &:hover {
      border-color: @stroke-btn-secondary;
      box-shadow: none;

      :global(.@{ant-prefix}-card-body) {
        background-color: @background-card-hover;

        & > span {
          background-color: transparent;
        }
      }
    }
  }

  .style-model {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .style-model-item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 8px;
      border-radius: 4px;
      border: 1px solid @stroke-btn-secondary;
      cursor: pointer;

      &.disable {
        cursor: not-allowed;
        opacity: 0.5;
      }

      &:hover {
        background: @background-btn-hover;
      }

      .image {
        object-fit: cover;
        border-radius: 4px;
        vertical-align: baseline;
      }

      .model-name {
        margin-left: 4px;
        color: @content-btn-secondary;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        max-width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    align-items: center;
    padding: @size-ms @size-lg @size-lg;
    background-color: @color-bg-base;

    .confirm-btn {
      width: 100%;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: @background-btn-ai;
      color: @content-btn-primary;
      font-size: 16px;
      border-radius: 8px;

      :global(.btn-icon) {
        margin-right: 5px;
      }

      &:hover {
        background: @background-btn-ai !important;
        color: @content-btn-primary !important;
      }
    }
  }
}
