import type { Gallery, GalleryProfile } from '@/types';
import type { GalleryQuery } from '@/api/types';

import { useState, useEffect, useContext, useRef } from 'react';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { GalleryContext } from './Provider';
import { produce } from 'immer';

import _ from 'lodash';

/**
 * 获取首页banner和画廊图片列表
 */
export function useGalleryState(
  fetchGalleryApi: (params: GalleryQuery) => Promise<Gallery | undefined>
) {
  const [loading, setLoading] = useState(false);
  const [cursor, setCursor] = useState<Gallery['cursor']>();
  const { gallery, setGallery } = useContext(GalleryContext);
  const loadingRef = useRef(false);

  const setLoadingStatus = (flag: boolean) => {
    setLoading(flag);
    loadingRef.current = flag;
  };

  // 获取画廊数据
  const fetchGallery = async (replace?: boolean) => {
    // 切换type，防止多次请求
    if (loadingRef.current) return;

    setLoadingStatus(true);
    try {
      const galleryResponse = await fetchGalleryApi({
        cursor: replace ? '' : cursor ?? ''
      });

      if (!galleryResponse) {
        setLoadingStatus(false);
        return;
      }

      setCursor(galleryResponse.cursor);
      //  setGallery([]);  gallery值不会立刻变化
      setGallery((pre: GalleryProfile[]) =>
        replace ? galleryResponse.list : _.concat(pre, galleryResponse.list)
      );
    } catch (err) {
      defaultErrorHandler(err);
    } finally {
      setLoadingStatus(false);
    }
  };

  // 刷新画廊数据
  const refetch = () => {
    fetchGallery(true);
  };

  useEffect(
    () => {
      // 切换tab 画廊数据要刷新
      fetchGallery(true);
      return () => {
        setGallery([]);
        setCursor(undefined);
      };
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [fetchGalleryApi]
  );

  return {
    loading,
    gallery,
    hasMore: cursor !== '',
    fetchGallery,
    refetch
  };
}

/**
 * 设置画廊feed收藏状态
 * @returns
 */
export function useSetFavoriteState(deleteItem?: boolean) {
  const { gallery, setGallery } = useContext(GalleryContext);

  return (id: string, value: boolean) => {
    const result = produce(gallery, (draft) => {
      const targetIndex = _.findIndex(draft, (galleryProfile) => {
        return galleryProfile.id === id;
      });

      draft[targetIndex].isFavor = value;
      value ? draft[targetIndex].favorCount++ : draft[targetIndex].favorCount--;
      // 在当前列表移除此item
      deleteItem && draft.splice(targetIndex, 1);
    });

    setGallery(result);
  };
}
