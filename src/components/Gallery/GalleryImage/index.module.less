@import '~@/styles/variables.less';

@skeleton-color: #efefef;

.gallery {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;

  .image-container {
    position: relative;
    flex: 1 0;
    border-radius: @border-radius-lg;
    overflow: hidden;

    :global .@{ant-prefix}-image {
      width: 100%;
      height: 100%;

      img {
        background-color: @skeleton-color;
        max-width: 100%;
        max-height: 100%;
        min-width: 100%;
        min-height: 100%;
        object-fit: cover;
      }
    }

    .tag {
      position: absolute;
      left: @size-xs;
      top: @size-xs;
      font-size: @size-sm;
      line-height: @size;
      display: inline-flex;
      align-items: center;
      height: 18px;
      padding: 0 6px;

      :global path {
        fill: @color-bg-base;
      }
    }

    .mask {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      padding: @size-xs;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 60%,
        rgba(0, 0, 0, 0.6) 93%
      );
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      opacity: 0;
      transition: opacity 0.5s ease-in-out;
      cursor: pointer;

      &:hover {
        opacity: 1;
      }

      &.show {
        opacity: 1;
      }

      .tools {
        position: absolute;
        pointer-events: all;
        top: 8px;
        right: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: opacity 0.3s ease-in-out;

        & > :global(.@{ant-prefix}-btn) {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 28px;
          height: 28px;
          padding: 6px;
          color: @base-white-opacity-100;
          border-radius: 4px;
          border: 1px solid @base-white-opacity-25;
          background: @base-black-opacity-65;

          & + button {
            margin-left: @size-xs;
          }
        }
      }

      .title {
        color: @base-white-opacity-100;
        line-height: @size;
        font-size: @text-14;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .create-btn {
        width: 100%;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: @border-radius;
        font-size: @text-14;
        font-weight: 400;
        padding: 0px;
        background: @background-btn-ai;
        color: @content-btn-primary;
        border: none;
        align-self: flex-end;

        &:hover {
          background: @background-btn-ai;
          color: #fff;
        }

        .btn-icon {
          margin-right: 4px;
        }

        &:hover {
          color: @content-btn-primary;
        }
      }
    }
  }

  .content {
    display: flex;
    height: 44px;
    align-items: center;
    justify-content: space-between;
    padding: 0 @size-xs;

    .profile {
      display: flex;
      align-items: center;

      .avatar {
        width: 20px;
        height: 20px;
        flex: 0 0 auto;
        background: @skeleton-color;
      }

      .name {
        max-width: 130px;
        height: @size-ms;
        line-height: @size-ms;
        margin-left: 4px;
        font-size: @text-12;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .like {
      padding-right: 0;
      color: @content-system-quaternary;
      background-color: transparent;
    }
  }
}
