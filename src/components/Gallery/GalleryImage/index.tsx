import type { CreateLocationType, GalleryProfile } from '@/types';
import { AppModule, generateRouteTo } from '@/services';
import { App, Avatar, Button, Image, Tag } from 'antd';
import {
  EditorUsableAuthorized,
  CollectionSwitch,
  Actions
} from '@/components';

import { switchGalleryFavorites } from '@/api';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState
} from 'react';
import { toAtlasImageView2URL } from '@meitu/util';
import { Link } from 'react-router-dom';
import { useSetFavoriteState } from '../hooks';
import { getApplySearchParams } from '@/utils/searchParams';
import styles from './index.module.less';
import { defaultBackgroundTagAmount } from '@meitu/candy-theme/dist/variables.mjs';
import { DraftType } from '@/api/types';
import { BrushBold, EllipsisBlack, TrashCanBold } from '@meitu/candy-icons';
import classNames from 'classnames';
import AdaptContainer from '@/components/Waterfall/AdaptContainer';
import { useDetailModal } from '@/components/DetailModalProvider';
import { getSource } from '@/utils';

interface GalleryImageProps extends GalleryProfile {
  onCollectedChange?: (galleryItem: GalleryImageProps) => void;
  onCreateClick?: (galleryItem: GalleryImageProps) => void;
  onClick?: (galleryItem: GalleryImageProps) => void;
  // 删除作品
  onDelClick?: (id: string) => void;
  onTriggerClick?: () => void;
  mode?: 'host' | 'visitor';
  feedType?: 'gallery' | 'personal';

  location?: CreateLocationType;

  tab?: string;

  /** 创作同款跳转时添加额外参数 */
  searchParams?: Record<string, string>;

  /** 收藏页，取消收藏后，有回调 */
  afterSwitchFavorites?: () => void;
}

export const getAppModuleByTaskCategory = (taskCategory?: DraftType) => {
  switch (taskCategory) {
    case DraftType.TEXT_TO_IMAGE:
      return AppModule.TextToImage;

    case DraftType.IMAGE_TO_IMAGE:
      return AppModule.ImageToImage;

    case DraftType.IP_CONCEPT:
      return AppModule.IPCharacterConcept;
    default:
      return AppModule.TextToImage;
  }
};

export interface GalleryImageRef {
  setShowStatus: () => void;
}

export const GalleryImage = forwardRef<GalleryImageRef, GalleryImageProps>(
  (props, ref) => {
    const {
      id,
      user,
      prompt,
      picUrl,
      isFavor,
      effectId,
      picCount,
      favorCount,
      canUseSame,
      taskCategory,
      mode,
      feedType,
      styleModelIds,
      size,
      feedId,
      location,
      tab,
      afterSwitchFavorites
    } = props;
    const userName = user?.userName;
    const avatar = user?.avatar;

    const setFavoriteState = useSetFavoriteState(feedType === 'personal');
    const { message } = App.useApp();

    const [src, placeholderPicUrl] = useMemo<[string, string]>(
      () => [
        toAtlasImageView2URL(picUrl, { mode: 2, width: 350 }),
        toAtlasImageView2URL(picUrl, { mode: 2, width: 50 })
      ],
      [picUrl]
    );

    const [showMask, setShowMask] = useState(false);
    const [openTools, setOpenTools] = useState(false);

    useImperativeHandle(ref, () => ({
      setShowStatus() {
        setOpenTools(false);
        setShowMask(false);
      }
    }));

    /** 处理收藏和取消收藏 */
    const handleFavorite = useCallback(async () => {
      try {
        setFavoriteState(id, !isFavor);
        // 收藏页面取消收藏
        feedType === 'personal' && isFavor && afterSwitchFavorites?.();

        await switchGalleryFavorites(feedId || '', !isFavor);

        isFavor ? message.info('已取消收藏') : message.success('已收藏此作品');
      } catch (err) {
        defaultErrorHandler(err);
      }
    }, [
      setFavoriteState,
      id,
      isFavor,
      feedType,
      afterSwitchFavorites,
      feedId,
      message
    ]);

    const renderImage = () => {
      return (
        <AdaptContainer size={size} columnWidth={0}>
          <Image
            preview={false}
            src={src}
            placeholder={<Image preview={false} src={placeholderPicUrl} />}
          />
        </AdaptContainer>
      );
    };

    /** 渲染多张图的标签 */
    const renderTag = () => {
      return picCount > 1 ? (
        <Tag color={defaultBackgroundTagAmount} className={styles.tag}>
          {picCount}张
        </Tag>
      ) : null;
    };

    const { addModal } = useDetailModal();

    const onMaskClick = () => {
      addModal({ id, taskCategory });

      props.onClick?.(props);
    };

    function renderCreateSameButton() {
      const appModule = getAppModuleByTaskCategory(taskCategory);

      const tracerParams = {
        source: getSource(),
        feedId,
        location,
        tab
      };

      return (
        <EditorUsableAuthorized>
          <Link
            to={generateRouteTo(
              appModule,
              getApplySearchParams(appModule, {
                id: String(effectId),
                styleModelId: styleModelIds || undefined,
                // 因为要打开新的标签页
                // 需要通过URL的query参数提供来源埋点信息
                ...tracerParams
              })
            )}
            target="_blank"
            className={styles.createBtn}
            onClick={(event) => {
              event.stopPropagation();
              props.onCreateClick?.(props);
            }}
          >
            <BrushBold className={styles.btnIcon} />
            创作同款
          </Link>
        </EditorUsableAuthorized>
      );
    }

    /** 渲染遮罩层 title 和 创作同款*/
    const renderMask = () => {
      return (
        <div
          className={classNames(styles.mask, { [styles.show]: showMask })}
          onClick={onMaskClick}
        >
          {mode === 'host' && (
            <div className={styles.tools}>
              <Actions
                open={openTools}
                onOpenChange={(val: boolean) => {
                  setShowMask(val);
                  setOpenTools(val);
                }}
                triggerProps={{
                  onClick: (e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    props.onTriggerClick?.();
                  },
                  icon: <EllipsisBlack />
                }}
              >
                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    props.onDelClick?.(feedId || '');
                  }}
                  size="small"
                  type="text"
                  icon={<TrashCanBold />}
                >
                  删除
                </Button>
              </Actions>
            </div>
          )}
          <p className={styles.title}>{prompt}</p>
          {canUseSame ? renderCreateSameButton() : null}
        </div>
      );
    };

    /** 渲染头像 */
    const renderAvatar = () => {
      return (
        <div className={styles.profile}>
          {avatar && (
            <Avatar
              className={styles.avatar}
              src={toAtlasImageView2URL(avatar, { mode: 2, width: 36 })}
            />
          )}
          <strong className={styles.name}>{userName}</strong>
        </div>
      );
    };

    return (
      <main className={styles.gallery}>
        <section className={styles.imageContainer}>
          {renderImage()}
          {renderTag()}
          {renderMask()}
        </section>
        {mode !== 'host' && (
          <section className={styles.content}>
            {renderAvatar()}

            {/** 点赞和取消收藏 */}
            <CollectionSwitch
              isCollected={isFavor}
              count={favorCount}
              className={styles.like}
              onChange={(isCollected, event) => {
                event.preventDefault();
                event.stopPropagation();

                props.onCollectedChange?.({ ...props, isFavor: isCollected });

                handleFavorite();
              }}
            />
          </section>
        )}
      </main>
    );
  }
);
