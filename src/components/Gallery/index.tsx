import type { PropsWithChildren, ReactElement, ReactNode } from 'react';
import type { GalleryQuery } from '@/api/types';
import type { GalleryProfile, Gallery as GalleryValue } from '@/types';
import type { GridContainerProps } from '@/components/GridWaterfall/GridContainer';
import type { GridPaginationProps } from '@/components/GridWaterfall/GridPagination';

import { useInView } from 'react-intersection-observer';
import { GridWaterfall } from '@/components/GridWaterfall';

import { useGalleryState } from './hooks';
import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';

import styles from './index.module.less';

export * from './GalleryImage';
export * from './GalleryImageDetailModal';
export * from './Provider';

interface GalleryItemProps {
  onClick?: () => void;
  onView?: () => void;
}

export function GalleryItem(props: PropsWithChildren<GalleryItemProps>) {
  const { children, onClick, onView } = props;
  const [isViewed, setIsViewed] = useState(false);
  const [ref, inView] = useInView();

  useEffect(() => {
    if (inView && !isViewed) {
      setIsViewed(true);
      onView?.();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [inView]);

  return (
    <div ref={ref} className={styles.galleryItem} onClick={onClick}>
      {children}
    </div>
  );
}

type GalleryWaterfallProps = Pick<
  GridContainerProps<GalleryProfile>,
  'renderItem' | 'gutter'
> &
  Pick<GridPaginationProps, 'endMessage' | 'scrollableTarget' | 'empty'>;

interface GalleryProps extends GalleryWaterfallProps {
  fetchGallery: (params: GalleryQuery) => Promise<GalleryValue | undefined>;
  detailModal?: ReactElement;
  children?: ReactNode;
}

export interface GalleryRef {
  refetch: () => void;
}

const GalleryWaterfall = forwardRef<GalleryRef, GalleryProps>(function (
  {
    fetchGallery: fetchGalleryApi,
    detailModal,
    children,
    ...waterfallProps
  }: PropsWithChildren<GalleryProps>,
  ref
) {
  const { gallery, hasMore, fetchGallery, refetch } =
    useGalleryState(fetchGalleryApi);

  useImperativeHandle(ref, () => ({
    refetch
  }));

  return (
    <>
      <GridWaterfall<GalleryProfile>
        hasMore={hasMore}
        list={gallery}
        getMore={fetchGallery}
        col={{
          xxl: 7,
          xl: 6,
          lg: 5,
          md: 4,
          sm: 3,
          xs: 2
        }}
        {...waterfallProps}
      />
      {children}
    </>
  );
});

export { GalleryWaterfall as Gallery };
