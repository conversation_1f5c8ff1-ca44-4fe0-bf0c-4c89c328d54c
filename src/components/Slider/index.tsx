import { SliderSingleProps, SliderRangeProps } from 'antd/es/slider';
import { Slider as AntdSlider } from 'antd';
import styles from './index.module.less';

import classNames from 'classnames';
import { SliderBaseProps } from 'antd/es/slider';
import { useEffect, useRef } from 'react';

type AntdSliderProps = SliderSingleProps | SliderRangeProps;
export type SliderProps = AntdSliderProps & {
  /** 刻度标记个数 */
  markNum?: number;

  /**
   * 起点刻度
   * 仅在单值模式下生效
   */
  origin?: number;

  onPointerEnterSlider?: () => void;

  onPointerLeaveSlider?: () => void;
};

const markObj = {
  style: {
    display: 'none'
  },
  label: '-'
} as const;
/**
 * 生成 刻度标记
 * @param max 最大值
 * @param min 最小值
 * @param markNum 刻度标记个数
 */
function toMarks(
  max: number,
  min: number,
  markNum: number = 2
): NonNullable<SliderProps['marks']> {
  if (!markNum || markNum <= 1) {
    return {};
  }

  const maskPoints = [min, max];
  const interval = (max - min) / (markNum - 1); // 标尺间隔大小

  for (let start = min + interval; start < max; start += interval) {
    maskPoints.push(start);
  }

  return maskPoints.reduce((acc, current) => {
    acc[current] = markObj;
    return acc;
  }, {} as NonNullable<SliderProps['marks']>);
}

/**
 * 判断x是否介于edge1和edge2之间
 * @param x
 * @param edge1
 * @param edge2
 * @returns
 */
function interval(x: number, edge1: number, edge2: number) {
  const min = Math.min(edge1, edge2);
  const max = Math.max(edge1, edge2);

  return x >= min && x <= max;
}

const MARKS_CONTAINER_SELECTOR = '.ant-slider-step';
const ACTIVE_MARK_CLASS_NAME = 'ant-slider-dot-active';
export function Slider(props: SliderProps) {
  const {
    className,
    min = 0,
    max = 100,
    markNum,
    origin,
    value,
    range,
    onPointerEnterSlider,
    onPointerLeaveSlider
  } = props;
  const sliderClass = classNames(styles.slider, className);
  const marks = toMarks(max, min, markNum);

  const containerRef = useRef<HTMLDivElement | null>(null);
  const { sliderStyles } = useOrigin({
    range,
    min,
    max,
    origin,
    value,
    marks,
    containerRef
  });
  return (
    <div
      ref={containerRef}
      onPointerEnter={onPointerEnterSlider}
      onPointerLeave={onPointerLeaveSlider}
    >
      <AntdSlider
        marks={marks}
        {...props}
        className={sliderClass}
        styles={sliderStyles}
      />
    </div>
  );
}

type OriginOptions = {
  range: SliderProps['range'];
  origin?: number;
  min: number;
  max: number;
  value?: SliderProps['value'];
  marks: ReturnType<typeof toMarks>;
  containerRef: React.MutableRefObject<HTMLDivElement | null>;
};

function useOrigin(options: OriginOptions) {
  const { range, max, min, value, origin, marks, containerRef } = options;

  // 仅在单值的情况下生效
  const enable = !range && !Array.isArray(value);
  const sliderStyles: SliderBaseProps['styles'] = {};

  if (enable && origin !== undefined && value !== undefined) {
    const totalLength = max - min;
    const curLength = value - origin;

    const widthNum = Math.abs(curLength / totalLength);
    let leftNum = (origin - min) / totalLength;
    if (curLength < 0) {
      leftNum -= widthNum;
    }

    const trackStyle = {
      width: `${widthNum * 100}%`,
      left: `${leftNum * 100}%`
    };

    sliderStyles.track = trackStyle;
    marks[origin] = markObj;
  }

  useEffect(() => {
    if (!enable || origin === undefined || value === undefined) {
      return;
    }
    const dom = containerRef.current!;
    const marksContainer = dom.querySelector(
      MARKS_CONTAINER_SELECTOR
    ) as HTMLDivElement;
    for (const dot of marksContainer.children) {
      // 刻度距离最左端的百分比（用浮点数表示）
      const styleLeft = parseFloat((dot as HTMLElement).style.left) / 100;
      // 刻度所代表的值
      const dotNum = (max - min) * styleLeft + min;
      // 如果这个刻度介于orgin和value之间 则是active
      if (interval(dotNum, origin, value)) {
        (dot as HTMLElement).classList.add(ACTIVE_MARK_CLASS_NAME);
      } else {
        (dot as HTMLElement).classList.remove(ACTIVE_MARK_CLASS_NAME);
      }
    }
  }, [value, origin, max, min, containerRef, enable]);

  return { sliderStyles };
}
