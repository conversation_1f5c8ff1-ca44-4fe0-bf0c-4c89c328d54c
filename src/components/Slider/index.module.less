@import '~@/styles/variables';

.slider:global(.@{ant-prefix}-slider) {
  margin: 0 0 0 2px !important;

  :global .@{ant-prefix}-slider-rail {
    background-color: @background-slide;
  }

  :global .@{ant-prefix}-slider-track {
    background-color: @background-btn-ai;
  }

  :global {
    .@{ant-prefix}-slider-dot {
      width: 3px;
      height: 8px;
      border-radius: 1.5px;
      border-color: @background-slide;

      &.@{ant-prefix}-slider-dot-active {
        border-color: @background-btn-ai;
      }
    }
  }

  :global .@{ant-prefix}-slider-handle {
    &::after {
      box-shadow: 0 0 0 2px @background-btn-ai !important;
    }
  }

  &:hover {
    :global .@{ant-prefix}-slider-track {
      background-color: @background-btn-ai-hover;
    }

    :global .@{ant-prefix}-slider-dot-active {
      border-color: @background-btn-ai-hover !important;
    }

    :global .@{ant-prefix}-slider-handle {
      &::after {
        box-shadow: 0 0 0 2px @background-btn-ai-hover !important;
      }
    }
  }
}

.slider:global.zcool-slider {
  :global .@{ant-prefix}-slider-rail {
    background-color: #ebedf2 !important;
  }

  :global .@{ant-prefix}-slider-track {
    background-color: @background-btn-ai;
  }

  :global {
    .@{ant-prefix}-slider-dot {
      width: 3px;
      height: 8px;
      border-radius: 1.5px;
      border-color: #ebedf2 !important;

      &.@{ant-prefix}-slider-dot-active {
        background-color: #222222;
      }
    }
  }

  :global .@{ant-prefix}-slider-handle {
    &::after {
      box-shadow: 0 0 0 2px #222222 !important;
    }
  }

  &:hover {
    :global .@{ant-prefix}-slider-track {
      background-color: #222222;
    }

    :global .@{ant-prefix}-slider-dot-active {
      border-color: #222222 !important;
    }

    :global .@{ant-prefix}-slider-handle {
      &::after {
        box-shadow: 0 0 0 2px #222222 !important;
      }
    }
  }
}
