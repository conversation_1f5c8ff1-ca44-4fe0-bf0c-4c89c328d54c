import classNames from 'classnames';
import styles from './style.module.less';

type SVIPIconProps = {
  className?: string;
};

export function SVIPIcon({ className }: SVIPIconProps) {
  return (
    <span className={classNames(styles.icon, className)}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="9"
        height="8"
        viewBox="0 0 9 8"
        fill="none"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.15786 0.542805L8.79146 2.34983C9.06952 2.66774 9.06952 3.13622 8.79146 3.44576L5.1593 7.46138C4.81173 7.84621 4.19479 7.84621 3.84721 7.46138L0.215062 3.4374C-0.0716872 3.12786 -0.0716872 2.65937 0.215062 2.34147L1.84866 0.53444C2.01376 0.358756 2.25706 0.25 2.50905 0.25H6.49747C6.74946 0.25 6.98407 0.358756 7.15786 0.542805ZM5.6807 3.88979L6.54964 3.33764C6.74949 3.21215 6.80163 2.95281 6.67129 2.7604C6.54095 2.56798 6.27157 2.50942 6.08041 2.64328L5.21147 3.19542C4.75963 3.47986 4.18613 3.47986 3.74297 3.17869L2.95224 2.64328C2.75239 2.50942 2.48302 2.55125 2.34399 2.74367C2.20496 2.93608 2.2484 3.19542 2.44826 3.32928L3.23899 3.86469C3.61263 4.11567 4.0471 4.24116 4.48157 4.24116C4.89866 4.24116 5.31575 4.12403 5.6807 3.88979Z"
          fill="url(#paint0_linear_19643_6352)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_19643_6352"
            x1="0"
            y1="0.25"
            x2="7.49638"
            y2="7.98311"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#402B20" stopOpacity="0.8" />
            <stop offset="1" stopColor="#402B20" />
          </linearGradient>
        </defs>
      </svg>

      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="8"
        viewBox="0 0 20 8"
        fill="none"
      >
        <path
          d="M12.4062 7.38184C12.4223 7.39005 12.4466 7.39825 12.4708 7.39825H13.409C13.4332 7.39825 13.4494 7.39005 13.4737 7.38184C13.4898 7.37364 13.5141 7.35722 13.5303 7.34902C13.5464 7.3326 13.5545 7.31619 13.5626 7.29157C13.5707 7.26695 13.5788 7.25054 13.5788 7.22592V0.742944C13.5788 0.693706 13.5626 0.652674 13.5303 0.619849C13.4979 0.587024 13.4575 0.570611 13.409 0.570611H12.4708C12.4304 0.570611 12.3819 0.587024 12.3495 0.619849C12.3172 0.652674 12.301 0.693706 12.301 0.742944V7.21772C12.301 7.24233 12.3091 7.25875 12.3172 7.28337C12.3253 7.30799 12.3415 7.3244 12.3495 7.34081C12.3657 7.35722 12.3819 7.37364 12.4062 7.38184Z"
          fill="#3C2404"
        />
        <path
          d="M5.76656 0.578774C5.65334 0.578774 5.57247 0.693662 5.61291 0.800344L7.90967 7.24229C7.94202 7.34077 8.03906 7.40642 8.1442 7.40642H8.98527C9.0904 7.40642 9.17936 7.34077 9.2198 7.24229L11.5166 0.800344C11.557 0.693662 11.4761 0.578774 11.3629 0.578774H10.4248C10.3197 0.578774 10.2307 0.644425 10.1903 0.7429L8.56473 5.2974L6.93921 0.7429C6.90686 0.644425 6.80981 0.578774 6.70468 0.578774H5.76656Z"
          fill="#3C2404"
        />
        <path
          d="M14.7191 0.570611C14.6139 0.570611 14.5331 0.652674 14.5331 0.759356V7.2013C14.5331 7.30798 14.6139 7.39005 14.7191 7.39005H15.6167C15.7219 7.39005 15.8027 7.30798 15.8027 7.2013V1.859H17.6466C18.2451 1.859 18.7303 2.35138 18.7303 2.95865C18.7303 3.56591 18.2451 4.05829 17.6466 4.05829H16.4093C16.3042 4.05829 16.2233 4.14035 16.2233 4.24703V5.15793C16.2233 5.26462 16.3042 5.34668 16.4093 5.34668H17.6466C18.9487 5.34668 20 4.27986 20 2.95865C20 1.63743 18.9487 0.570611 17.6466 0.570611H14.7191Z"
          fill="#3C2404"
        />
        <path
          d="M2.65865 7.5C3.21699 7.5 3.69557 7.41404 4.09439 7.24213C4.49627 7.06709 4.80305 6.82172 5.01473 6.50603C5.22948 6.18721 5.33685 5.81056 5.33685 5.37609V5.3714C5.33685 4.84003 5.17426 4.42275 4.84907 4.11956C4.52695 3.81324 4.01769 3.58975 3.3213 3.4491L2.61724 3.30375C2.23683 3.22561 1.95766 3.1209 1.77972 2.98962C1.60179 2.85834 1.51282 2.68643 1.51282 2.47388V2.46919C1.51282 2.30665 1.55731 2.16443 1.64627 2.04253C1.73831 1.92063 1.87022 1.8253 2.04202 1.75653C2.21382 1.68464 2.41783 1.64869 2.65405 1.64869C2.88414 1.64869 3.08661 1.68308 3.26148 1.75184C3.43941 1.82061 3.58206 1.91907 3.68944 2.04722C3.77108 2.14229 3.82667 2.25327 3.85936 2.37195C3.88778 2.4751 3.97312 2.55827 4.07867 2.55827H4.9896C5.10582 2.55827 5.19922 2.4592 5.18469 2.34219C5.14425 2.01664 5.0465 1.70749 4.85367 1.43302C4.64506 1.13608 4.35362 0.906341 3.97935 0.743804C3.60814 0.581268 3.16637 0.5 2.65405 0.5C2.1632 0.5 1.72757 0.585957 1.34716 0.75787C0.966751 0.926658 0.667639 1.16577 0.449824 1.47522C0.232009 1.78154 0.123102 2.14099 0.123102 2.55358V2.55827C0.123102 3.06776 0.288764 3.48192 0.620088 3.80074C0.951412 4.11956 1.4438 4.34617 2.09724 4.48058L2.7967 4.62123C3.21086 4.70875 3.50537 4.81815 3.68023 4.94943C3.85817 5.07758 3.94713 5.25575 3.94713 5.48393V5.48861C3.94713 5.66053 3.89498 5.81212 3.79068 5.9434C3.68944 6.07156 3.54525 6.17158 3.35811 6.24347C3.17404 6.31223 2.95316 6.34662 2.69547 6.34662C2.4439 6.34662 2.21842 6.31223 2.01901 6.24347C1.8196 6.17158 1.65701 6.07312 1.53123 5.94809C1.43583 5.84847 1.37345 5.73404 1.33287 5.60845C1.3023 5.51385 1.2206 5.44173 1.12249 5.44173H0.196364C0.0793509 5.44173 -0.0143193 5.5421 0.00181362 5.65971C0.0476476 5.99382 0.155452 6.31135 0.362391 6.58573C0.586342 6.87955 0.893123 7.10616 1.28274 7.26557C1.67542 7.42186 2.13405 7.5 2.65865 7.5Z"
          fill="#3C2404"
        />
      </svg>
    </span>
  );
}
