import type { InputNumberProps as AntdInputNumberProps } from 'antd';
import { InputNumber as AntdInputNumber } from 'antd';
import { ChevronUpBlack, ChevronDownBlack } from '@meitu/candy-icons';
import styles from './inex.module.less';

import classNames from 'classnames';
import { isNumber } from 'lodash';

export interface InputNumberProps extends AntdInputNumberProps<number> {
  /**
   * 是否 显示操作区
   */
  showHandler?: Boolean;

  /** 后置图标 */
  suffix?: React.ReactNode;

  /** 是否展示控制 */
  showControls?: boolean;
}

export function InputNumber(props: InputNumberProps) {
  const {
    className,
    showHandler = true,
    suffix,
    showControls = true,
    ...restProps
  } = props;
  const controls = showControls
    ? {
        upIcon: <ChevronUpBlack />,
        downIcon: <ChevronDownBlack />
      }
    : false;
  const inputNumberClass = classNames(styles.inputNumber, className, {
    [styles.handler]: showHandler,
    [styles.suffix]: !!suffix
  });

  return (
    <AntdInputNumber
      controls={controls}
      prefix={suffix}
      className={inputNumberClass}
      {...restProps}
    />
  );
}

export function InputNumberLimitMax(
  props: Omit<InputNumberProps, 'onChange'> & {
    onChange?: (value: number, preVal?: number | null) => void;
  }
) {
  const { max, onChange } = props;

  function handleChange(value: number | null) {
    if (!isNumber(value)) {
      return;
    }

    if (typeof max === 'number') {
      onChange?.(Math.min(max, value), props.value);
    } else {
      onChange?.(value, props.value);
    }
  }

  return <InputNumber {...props} max={max} onChange={handleChange} />;
}
