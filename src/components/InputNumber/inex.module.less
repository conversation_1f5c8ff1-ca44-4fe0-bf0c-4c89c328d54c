@import '~@/styles/variables.less';

.input-number:global(.@{ant-prefix}-input-number-affix-wrapper),
.input-number:global(.@{ant-prefix}-input-number) {
  height: 26px;
  width: 80px;
  background: transparent;

  &.suffix {
    display: flex;

    :global .@{ant-prefix}-input-number-prefix {
      margin-left: 4px;
      order: 1;
    }
  }

  :global .@{ant-prefix}-input-number-input {
    height: 24px;
  }

  &.handler :global .@{ant-prefix}-input-number-handler-wrap {
    opacity: 1;
    background-color: transparent;

    :global {
      .@{ant-prefix}-input-number-handler {
        &-up-inner,
        &-down-inner {
          font-size: 8px;
        }
      }
    }
  }

  :global .@{ant-prefix}-input-number-handler-wrap {
    &::before {
      position: absolute;
      width: 1px;
      height: 60%;
      top: 20%;
      background-color: #d9d9d9; // TODO: 边框颜色替换
      content: '';
    }

    :global .@{ant-prefix}-input-number-handler {
      height: 50% !important;
      border-inline-start: none;
    }

    :global .@{ant-prefix}-input-number-handler-up {
      padding-top: 2px;
    }

    :global .@{ant-prefix}-input-number-handler-down {
      padding-bottom: 2px;
      border-block-start: none;
    }
  }
}
