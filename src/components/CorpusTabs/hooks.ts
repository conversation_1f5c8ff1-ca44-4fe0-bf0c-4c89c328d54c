import type { Corpus, CorpusCategory, CorpusWithPriority } from '@/types';

import { fetchCorpusCategory, fetchCorpusList, searchCorpusList } from '@/api';
import { useContext, useState, useEffect, useRef } from 'react';
import { getCorporaIds, getCorporaMap } from '@/hooks/useCorpus';
import { CorpusTabsContext } from './Provider';
import { formatTagByPriority } from '@/utils/corpus';
import { produce } from 'immer';
import _ from 'lodash';

/**
 * 语料库分类钩子
 * @returns
 */
export function useCorpusCategoryState() {
  const [categories, setCategories] = useState<CorpusCategory[]>([]);
  const [activeMajorId, setActiveMajorId] = useState<CorpusCategory['id']>();
  const { corpusFilter, activeMinorId, setActiveMinorId } =
    useContext(CorpusTabsContext);
  const fetchedRef = useRef(false);
  const [loading, setLoading] = useState(false);

  const initActiveCategories = (categories: CorpusCategory[]) => {
    // 默认第一条二级分类 !_.isNil(categories[0]?.list?.[0]?.id)
    if (categories[0]?.list?.[0]?.id != null) {
      setActiveMajorId(categories[0].id);
      setActiveMinorId(categories[0].list[0].id);
    }
  };

  const fetchCategory = async () => {
    setLoading(true);
    const categoryResponse = await fetchCorpusCategory(corpusFilter);
    const categories = categoryResponse.list || [];

    setCategories(categories);

    if (activeMinorId == null) {
      initActiveCategories(categories);
    }
    setLoading(false);
  };

  useEffect(
    () => {
      const resetCategories = () => {
        initActiveCategories(categories);
      };

      if (fetchedRef.current || categories.length) {
        return resetCategories;
      }

      fetchedRef.current = true;
      fetchCategory();

      return resetCategories;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [categories]
  );

  return {
    categories,
    activeMajorId,
    activeMinorId,
    loading,
    setActiveMajorId,
    setActiveMinorId
  } as const;
}

function getCorpusWithPriority(
  corpusLists: Record<number, Corpus[]> | Corpus[],
  corpusPriority: Record<Corpus['id'], CorpusWithPriority>,
  activeMinorId?: CorpusCategory['id']
): CorpusWithPriority[] {
  const mapper = (corpus: Corpus) => ({
    ...corpus,
    priority: corpusPriority[corpus.id]?.priority
  });

  if (Array.isArray(corpusLists)) {
    return corpusLists.map(mapper);
  }

  if (activeMinorId == null) {
    return [];
  }

  return corpusLists[activeMinorId]?.map(mapper) ?? [];
}

/**
 * 语料库列表钩子
 */
export function useCorpusListState() {
  const { activeMinorId, corpusLists, corpusPriority, setCorpusLists } =
    useContext(CorpusTabsContext);
  const [loading, setLoading] = useState(false);

  const fetchCorpora = async () => {
    if (activeMinorId == null) {
      return;
    }

    setLoading(_.isEmpty(corpusLists[activeMinorId]));

    const corpus = await fetchCorpusList(activeMinorId);

    setCorpusLists(corpus, activeMinorId);
    setLoading(false);
  };

  useEffect(
    () => {
      if (activeMinorId != null && !corpusLists[activeMinorId]) {
        fetchCorpora();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [activeMinorId]
  );

  return {
    corpus: getCorpusWithPriority(corpusLists, corpusPriority, activeMinorId),
    loading
  };
}

/**
 * 语料库列表模糊搜索钩子
 */
export function useFilterCorporaState() {
  const [loading, setLoading] = useState(false);
  const {
    keywords,
    corpusFilter,
    corpusPriority,
    filterCorpora,
    setFilterCorpora
  } = useContext(CorpusTabsContext);

  const fetchCorpora = async () => {
    setLoading(true);

    try {
      const corpus = await searchCorpusList(keywords, corpusFilter);

      setFilterCorpora(corpus);
    } catch (err) {
      setFilterCorpora([]);
    }
    setLoading(false);
  };

  useEffect(
    () => {
      if (!!keywords) {
        fetchCorpora();
      } else {
        setFilterCorpora([]);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [keywords]
  );

  return {
    showFilter: !!keywords,
    loading,
    corpora: getCorpusWithPriority(filterCorpora, corpusPriority)
  };
}
export function useSelectedCorpus() {
  const {
    keywords,
    filterCorpora,
    corpusLists: corpora,
    corpusPriority,
    setCorpusPriority
  } = useContext(CorpusTabsContext);

  const setPriority = (
    selectedCorpora: Corpus[],
    minorId: CorpusCategory['id']
  ) => {
    const corporaIds = getCorporaIds(
      keywords ? filterCorpora : corpora[minorId]
    );
    const selectedCorporaMap = getCorporaMap(
      selectedCorpora.map(formatTagByPriority)
    );

    const nextCorpusPriority: Record<string, Corpus> = produce(
      _.omit(corpusPriority, corporaIds),
      (draft) => {
        Object.assign(
          draft,
          _.omitBy(
            selectedCorporaMap,
            ({ id }) => !_.has(selectedCorporaMap, id)
          )
        );
      }
    );

    setCorpusPriority(nextCorpusPriority);

    return nextCorpusPriority;
  };

  return {
    setPriority
  } as const;
}
