@import '~@/styles/variables.less';

.corpus-tabs-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.corpus-major-tabs:global(.@{ant-prefix}-tabs) {
  :global .@{ant-prefix}-tabs-nav {
    &::before {
      border-bottom: none;
    }

    .@{ant-prefix}-tabs-ink-bar {
      height: @size-xxs;
      border-radius: @border-radius-xs;
      background: @content-web-primary;
      // scale: 0.5 1;
      transform: translateX(-50%) scaleX(0.5) !important;
    }

    .@{ant-prefix}-tabs-tab {
      padding: @size-xxs 0;
      font-size: calc(@font-size + @size-xxs);
      color: @content-input-lable;

      + .@{ant-prefix}-tabs-tab {
        margin-left: calc(@size-xl + @size-xxs);
      }

      &.@{ant-prefix}-tabs-tab-active .@{ant-prefix}-tabs-tab-btn {
        color: @content-btn-secondary;
      }
    }
  }
}

@minor-tabs-height: 60px;

.corpus-minor-tabs:global(.@{ant-prefix}-tabs) {
  position: sticky;
  top: 0;
  width: 100%;
  min-height: @minor-tabs-height;
  background-color: @background-system-frame-floatpanel;
  transition: opacity 0.2s ease-in-out;
  z-index: 2;

  &.hidden {
    visibility: hidden;
    opacity: 0;
  }

  :global .@{ant-prefix}-tabs-nav {
    &::before {
      border-bottom: none;
    }

    .@{ant-prefix}-tabs-nav-list {
      flex-wrap: wrap;
    }

    .@{ant-prefix}-tabs-ink-bar {
      display: none;
    }

    .@{ant-prefix}-tabs-tab {
      padding: calc(@size-sm / 2) @size-sm;
      margin-right: calc(@size-md / 2);
      margin-bottom: calc(@size-md / 2);
      font-size: @font-size;
      background-color: @background-tab;
      border-radius: @border-radius;
      color: @content-tag;

      + .@{ant-prefix}-tabs-tab {
        margin-left: 0;
      }

      &.@{ant-prefix}-tabs-tab-active {
        background: @content-web-primary;

        .ant-tabs-tab-btn {
          font-weight: normal;
          color: @color-bg-base;
        }
      }
    }
  }
}

.corpus-content-placeholder {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, 0);

  :global .@{ant-prefix}-empty-description {
    color: @content-system-tertiary;
  }
}

.corpus-search-input:global(.@{ant-prefix}-input-affix-wrapper) {
  padding-right: 6px;

  :global .@{ant-prefix}-input-prefix {
    margin-right: calc(@size-sm / 2);
  }

  :global .@{ant-prefix}-input-clear-icon {
    font-size: @font-size;
  }
}

.corpus-layout {
  position: relative;
  flex: 1;
  overflow-y: auto;
}

.corpus-content {
  display: flex;
  padding-top: 1px;
  padding-left: 1px;
  flex: 1;

  :global .@{ant-prefix}-spin-nested-loading {
    flex: 1;
  }
}
