import type { Corpus, CorpusCategory, CorpusWithPriority } from '@/types';
import type { PropsWithChildren } from 'react';

import { createContext, useState } from 'react';
import { produce } from 'immer';

import { CorpusFilter } from '@/types';

interface CorpusTabsContextValue {
  /** 语料库列表 -> 分类id 映射 */
  corpusLists: Record<CorpusCategory['id'], Corpus[]>;

  /** 语料库设置列表 -> 分类id 映射 */
  setCorpusLists: (corpus: Corpus[], id: CorpusCategory['id']) => void;

  /** 语料库查询关键字 */
  keywords: string;

  /** 语料库设置查询关键字 */
  setKeywords: (keywords: string) => void;

  /** 语料库次级分类id */
  activeMinorId?: CorpusCategory['id'];

  /** 语料库设置次级分类id */
  setActiveMinorId: (id?: CorpusCategory['id']) => void;

  /** 语料库筛选类型 */
  corpusFilter: CorpusFilter;

  /** 语料库查询结果 */
  filterCorpora: Corpus[];

  /** 语料库设置查询结果 */
  setFilterCorpora: (corpus: Corpus[]) => void;

  /** 语料库带优先级（已选中）语料 */
  corpusPriority: Record<Corpus['id'], CorpusWithPriority>;

  /** 语料库设置语料优先级（选中） */
  setCorpusPriority: (corpus: Record<Corpus['id'], CorpusWithPriority>) => void;
}

function initialContextMethod() {
  console.warn('正在使用 `CorpusTabsContext` 的默认值');
}

/**
 * 语料库上下文
 */
export const CorpusTabsContext = createContext<CorpusTabsContextValue>({
  corpusLists: {},
  setCorpusLists: initialContextMethod,
  keywords: '',
  setKeywords: initialContextMethod,
  setActiveMinorId: initialContextMethod,
  corpusFilter: CorpusFilter.ALL,
  filterCorpora: [],
  setFilterCorpora: initialContextMethod,
  corpusPriority: {},
  setCorpusPriority: initialContextMethod
});

interface CorpusTabsProviderProps {
  corpusFilter?: CorpusFilter;
}

export function CorpusTabsProvider({
  corpusFilter = CorpusFilter.ALL,
  children
}: PropsWithChildren<CorpusTabsProviderProps>) {
  const [corpusLists, setCorpusLists] = useState<
    CorpusTabsContextValue['corpusLists']
  >({});
  const [keywords, setKeywords] = useState('');
  const [activeMinorId, setActiveMinorId] = useState<CorpusCategory['id']>();
  const [filterCorpora, setFilterCorpora] = useState<Corpus[]>([]);
  const [corpusPriority, setCorpusPriority] = useState<
    Record<Corpus['id'], CorpusWithPriority>
  >({});

  return (
    <CorpusTabsContext.Provider
      value={{
        corpusLists,
        setCorpusLists: (corpus, id) => {
          setCorpusLists((corpusLists) =>
            produce(corpusLists, (draft) => {
              draft[id] = corpus;
            })
          );
        },
        keywords,
        setKeywords,
        activeMinorId,
        setActiveMinorId,
        corpusFilter,
        filterCorpora,
        setFilterCorpora,
        corpusPriority,
        setCorpusPriority
      }}
    >
      {children}
    </CorpusTabsContext.Provider>
  );
}
