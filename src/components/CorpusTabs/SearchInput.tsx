import { AutoComplete, Input } from 'antd';
import { Search, CrossCircleFillBold } from '@meitu/candy-icons';
import { CorpusTabsContext } from './Provider';
import { useContext, useEffect, useState, useRef } from 'react';
import { fetchSuggestKeywords } from '@/api';
import _ from 'lodash';

import styles from './styles.module.less';

/**
 * 语料关键字处理钩子
 * @returns
 */
function useKeywordsOptions() {
  const { setKeywords } = useContext(CorpusTabsContext);
  const [optionKeywords, setOptionKeywords] = useState('');
  const [options, setOptions] = useState<{ value: string }[]>([]);
  const selected = useRef(false);

  const getOptions = async (keywords: string) => {
    const options = await fetchSuggestKeywords(keywords);

    setOptions(options);
  };

  useEffect(() => {
    if (!optionKeywords) {
      return;
    }
    getOptions(optionKeywords);
  }, [optionKeywords]);

  return {
    options: options ?? [],
    onSearch: _.debounce((keywords: string) => {
      selected.current = false;
      if (!keywords) {
        setKeywords('');
      }
      setOptionKeywords(keywords);
    }, 512),
    onSelect: (keywords: string) => {
      selected.current = true;
      setKeywords(keywords);
    },
    onBlur: () => {
      if (!selected.current) {
        setKeywords(optionKeywords);
      }
    },
    onPressEnter: (keywords: string) => {
      selected.current = true;
      setKeywords(keywords);
    }
  };
}

export function SearchInput() {
  const { options, onSearch, onSelect, onBlur, onPressEnter } =
    useKeywordsOptions();

  return (
    <AutoComplete
      options={options}
      onSearch={onSearch}
      onSelect={onSelect}
      onBlur={onBlur}
    >
      <Input
        placeholder="输入语料关键词"
        prefix={<Search />}
        allowClear={{
          clearIcon: <CrossCircleFillBold />
        }}
        className={styles.corpusSearchInput}
        onChange={(event) => {
          onSearch(event.target.value);
        }}
        onPressEnter={(event) => {
          onPressEnter((event.target as EventTarget & HTMLInputElement).value);
        }}
      />
    </AutoComplete>
  );
}
