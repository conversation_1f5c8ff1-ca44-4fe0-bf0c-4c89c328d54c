import type { CorpusCategory, Corpus } from '@/types';

import { Tabs, Spin, Skeleton, Empty } from 'antd';
import CorpusCards from '@/components/CorpusCards';
import { SearchInput } from './SearchInput';
import {
  useCorpusCategoryState,
  useFilterCorporaState,
  useCorpusListState,
  useSelectedCorpus
} from './hooks';
import { getCorporaIds } from '@/hooks/useCorpus';
import {
  useContext,
  useEffect,
  useState,
  useRef,
  forwardRef,
  useImperativeHandle
} from 'react';
import { CorpusTabsContext } from './Provider';
import _ from 'lodash';

import styles from './styles.module.less';
import empty from '@/assets/images/empty.jpg';
import classNames from 'classnames';

export { CorpusTabsProvider } from './Provider';
export { SearchInput } from './SearchInput';

interface CorpusTabItemProps extends Omit<CorpusTabsProps, 'onChange'> {
  items: Omit<CorpusCategory, 'list'>[];
  onChange: (corpora: Corpus[], activeMinorId: number) => void;
}

function CorpusTabItem(props: CorpusTabItemProps) {
  const { items } = props;
  const { corpus: fetchCorpora, loading: fetchLoading } = useCorpusListState();
  const {
    showFilter,
    loading: filterLoading,
    corpora: filterCorpora
  } = useFilterCorporaState();
  const { activeMinorId, corpusPriority, setActiveMinorId } =
    useContext(CorpusTabsContext);
  const [tabVisible, setTabVisible] = useState(true);
  const scrollRef = useRef<HTMLDivElement>(null);
  const prevScrollRef = useRef(0);

  const corpus = showFilter ? filterCorpora : fetchCorpora;
  const loading = showFilter ? filterLoading : fetchLoading;

  const categoryById = getCategoryById(items);

  useEffect(
    () => {
      const container = scrollRef.current;

      function handleScroll() {
        const scrollTop = container?.scrollTop ?? 0;

        // 兼容Safari触控板
        if (scrollTop < 0) {
          return;
        }

        // 向下滚动的时候需要保证下次向上滚动时能够正常显示
        if (scrollTop > prevScrollRef.current) {
          setTabVisible(false);
        } else if (scrollTop <= prevScrollRef.current) {
          setTabVisible(true);
        }

        prevScrollRef.current = scrollTop;
      }

      // 兼容处理 Safari
      setTimeout(() => {
        if (!container) {
          return;
        }

        container.addEventListener('scroll', handleScroll);
      }, 256);

      return () => {
        container?.removeEventListener('scroll', handleScroll);
      };
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  useEffect(
    () => {
      !_.isEmpty(corpus) && props.onCorpusLoaded?.(corpus);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [corpus]
  );

  const onChange = (corpora: Corpus[]) => {
    activeMinorId != null && props.onChange?.(corpora, activeMinorId);
  };

  const renderContent = () => {
    if (loading) {
      return null;
    }

    return _.isEmpty(corpus) ? (
      <Empty
        image={empty}
        description={showFilter ? '未找到相关语料' : '暂无语料'}
        className={styles.corpusContentPlaceholder}
      />
    ) : (
      <CorpusCards
        list={corpus}
        disabled={
          props.disabled
            ? ({ id }: Corpus) => !_.has(corpusPriority, id)
            : undefined
        }
        onChange={onChange}
      />
    );
  };

  return (
    <div
      ref={scrollRef}
      className={classNames(styles.corpusLayout, props.className)}
    >
      <Tabs
        activeKey={activeMinorId?.toString() ?? ''}
        items={items.map(({ id, name }) => ({
          key: id.toString(),
          label: name
        }))}
        className={classNames(
          styles.corpusMinorTabs,
          !tabVisible && styles.hidden
        )}
        onChange={(activeKey) => {
          setActiveMinorId(+activeKey);
          const minorCategory = categoryById(+activeKey);
          minorCategory && props.onCategoryChange?.(minorCategory);
        }}
      />
      <div className={styles.corpusContent}>
        <Spin spinning={loading} delay={256}>
          {renderContent()}
        </Spin>
      </div>
    </div>
  );
}

export interface CorpusTabsRef {
  setSelectedCorpora: (corpora: Corpus[]) => void;
  selectCategory: (id: number) => void;
}

interface CorpusTabsProps {
  /** 语料搜素框是否可见 */
  searchVisible?: boolean;
  /** 词库分类切换事件 */
  onCategoryChange?: (
    activeMajorCategory: CorpusCategory,
    activeMinorCategory?: CorpusCategory
  ) => void;
  /** 词库变化事件 */
  onChange?: (corpora: Corpus[]) => void;
  /** 词库搜索事件 */
  onSearch?: (keywords: string) => void;
  /** 词库语料加载完成事件 */
  onCorpusLoaded?: (corpora: Corpus[]) => void;
  /** 词库分类样式 */
  majorClassName?: string;
  /** 词库样式 */
  className?: string;
  /** 词条可选中 */
  disabled?: boolean;
}

export const CorpusTabs = forwardRef<CorpusTabsRef, CorpusTabsProps>(
  (props, ref) => {
    const { searchVisible = true } = props;
    const { categories, activeMajorId, loading, setActiveMajorId } =
      useCorpusCategoryState();
    const { keywords, setActiveMinorId, setCorpusPriority } =
      useContext(CorpusTabsContext);
    const { setPriority } = useSelectedCorpus();

    // 已选中词条id（排序用）
    const [corpusIds, setCorpusIds] = useState<string[]>([]);

    const onChange = (corpora: Corpus[], activeMinorId: number) => {
      const corpusPriority = setPriority(corpora, activeMinorId);

      const nextCorpusIds = corpusIds
        .concat(_.difference(getCorporaIds(corpora), corpusIds))
        .filter((id) => !!corpusPriority[id]);
      setCorpusIds(nextCorpusIds);

      props.onChange?.(nextCorpusIds.map((id) => corpusPriority[id]));
    };

    const selectCategory = (majorId: number) => {
      setActiveMajorId(majorId);
      const activeMajor = categoryById(majorId);

      if (!activeMajor) {
        return;
      }
      const minorCategory = activeMajor.list?.[0];
      setActiveMinorId(minorCategory?.id ?? undefined);
      props.onCategoryChange?.(activeMajor, minorCategory);
    };

    useImperativeHandle(ref, () => ({
      setSelectedCorpora(corpora) {
        setCorpusPriority(
          corpora.reduce(
            (corpora, corpus) =>
              Object.assign(corpora, {
                [corpus.id]: corpus
              }),
            {}
          )
        );

        setCorpusIds(corpora.map(({ id }) => id));
      },

      selectCategory
    }));

    useEffect(
      () => {
        keywords && props.onSearch?.(keywords);
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [keywords]
    );

    const categoryById = getCategoryById(categories);
    return (
      <div className={styles.corpusTabsContainer}>
        <Tabs
          items={
            loading
              ? _.times(4, (index) => ({
                  key: index.toString(),
                  label: <Skeleton.Button size="small" active />
                }))
              : categories.map(({ id, name }) => ({
                  key: id.toString(),
                  label: name
                }))
          }
          activeKey={activeMajorId + ''}
          tabBarExtraContent={searchVisible && <SearchInput />}
          className={classNames(styles.corpusMajorTabs, props.majorClassName)}
          onChange={(activeKey) => selectCategory(+activeKey)}
        />
        <CorpusTabItem
          items={categories.find(({ id }) => id === activeMajorId)?.list ?? []}
          disabled={props.disabled}
          onCategoryChange={(minorCategory) => {
            const majorCategory = categoryById(activeMajorId);
            majorCategory &&
              props.onCategoryChange?.(majorCategory, minorCategory);
          }}
          className={props.className}
          onChange={onChange}
          onCorpusLoaded={props.onCorpusLoaded}
        />
      </div>
    );
  }
);

const getCategoryById =
  (categories: CorpusCategory[]) => (id?: CorpusCategory['id']) => {
    return categories.find((category) => id === category.id);
  };
