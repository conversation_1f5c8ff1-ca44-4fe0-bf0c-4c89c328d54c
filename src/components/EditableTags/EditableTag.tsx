import { CorpusPriorityLevel } from '@/types';

import { Tag } from 'antd';
import TagContent from './TagContent';
import { MinusBlack } from '@meitu/candy-icons';
import { useRef, useLayoutEffect, useEffect, useState } from 'react';
import { getPriorityLevel } from '@/utils/corpus';

import classNames from 'classnames';
import styles from './styles.module.less';

/** 权重映射样式 */
const priorityClassNameMap = {
  [CorpusPriorityLevel.High]: styles.tagPriorityMedium,
  [CorpusPriorityLevel.Medium]: styles.tagPriorityMedium,
  [CorpusPriorityLevel.Low]: styles.tagPriorityLow,
  [CorpusPriorityLevel.None]: styles.tagPriorityNone
};

export interface EditableTagProps {
  /** 原文 - 左侧文案 */
  text: string;
  /** 译文 - 右侧文案 */
  translation?: string;
  /** 权重 - 根据权重更新样式 */
  priority?: CorpusPriorityLevel;
  /** 初始聚焦编辑状态 */
  autoFocus?: boolean;
  /** 标签删除事件 */
  onClose?: () => void;
  /** 原文变更 - 触发翻译 */
  onTextChange?: (text: string) => void;
  /** 译文变更 */
  onTranslationChange?: (translation: string, text?: string) => void;
  /** 翻译 api */
  fetchTranslation?: (text: string) => Promise<string>;
}

export default function EditableTag(props: EditableTagProps) {
  const { text, translation, priority, autoFocus = false, onClose } = props;
  const shadowTagRef = useRef<HTMLDivElement>(null);
  const translated = useRef(false);

  const [isEditing, setIsEditing] = useState(autoFocus);
  const [translating, setTranslating] = useState(false);
  // 是否被裁剪以此判断标签是否需要换行展示
  const [isClipped, setIsClipped] = useState(false);

  useLayoutEffect(() => {
    const shadowTag = shadowTagRef.current;
    if (shadowTag) {
      // 通过影子tag判断tag文本是否需要裁剪
      setIsClipped(shadowTag.clientWidth < shadowTag.scrollWidth);
    }
  }, [text, translation]);

  const translate = async (text: string) => {
    if (!props.fetchTranslation) {
      return;
    }
    setTranslating(true);

    const nextTransition = await props.fetchTranslation(text);
    props.onTranslationChange?.(nextTransition, text);

    setTranslating(false);
  };

  const onTextChange = (nextText: string) => {
    props.onTextChange?.(nextText);

    if (nextText === text) {
      return;
    }

    translate(nextText);
  };

  useEffect(() => {
    // _.isNil(originalTranslation)
    if (!translated.current && translation == null) {
      translated.current = true;
      translate(text);
    }
  });

  return (
    <div className={styles.tagWrap}>
      <Tag
        closable={!isEditing}
        closeIcon={<MinusBlack className={styles.closeIcon} />}
        className={classNames(
          styles.tag,
          priorityClassNameMap[priority ?? getPriorityLevel(text)]
        )}
        onClose={onClose}
      >
        <TagContent
          isClipped={isClipped}
          text={text}
          translation={translating ? undefined : translation}
          autoFocus={autoFocus}
          onFocus={setIsEditing.bind(null, true)}
          onBlur={setIsEditing.bind(null, false)}
          onTextChange={onTextChange}
          onTranslationChange={props.onTranslationChange}
        />
      </Tag>
      <Tag
        ref={shadowTagRef}
        className={classNames(styles.tag, styles.shadowTag)}
      >
        {text} {translation}
      </Tag>
    </div>
  );
}

EditableTag.defaultProps = {};
