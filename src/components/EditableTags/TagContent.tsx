import type { TagInputRef } from './TagInput';
import type { EditableTagProps } from './EditableTag';

import TagInput from './TagInput';
import { RefObject, useRef, useState, useEffect } from 'react';

import classNames from 'classnames';
import styles from './styles.module.less';

interface TagContentProps
  extends Pick<
    EditableTagProps,
    | 'text'
    | 'autoFocus'
    | 'translation'
    | 'onTextChange'
    | 'onTranslationChange'
  > {
  isClipped: boolean;
  onFocus: () => void;
  onBlur: () => void;
}

export default function TagContent(props: TagContentProps) {
  const { isClipped, text, translation, autoFocus } = props;
  const textInputRef = useRef<TagInputRef>(null);
  const translationInputRef = useRef<TagInputRef>(null);
  const textRef = useRef<HTMLDivElement>(null);
  const translationRef = useRef<HTMLDivElement>(null);

  const [editingRef, setEditingRef] = useState<RefObject<TagInputRef>>();
  const [inputWidth, setInputWidth] = useState<number>();

  useEffect(() => {
    if (autoFocus) {
      onFocus(textInputRef, textRef);
    }
  });

  const onFocus = (
    inputRef: RefObject<TagInputRef>,
    textRef: RefObject<HTMLDivElement>
  ) => {
    setInputWidth(textRef.current?.offsetWidth);
    setEditingRef(inputRef);

    props.onFocus();

    setTimeout(() => {
      inputRef.current?.focus();
    });
  };

  function onBlur(
    onChange: TagContentProps['onTextChange']
  ): (value: string) => void;
  function onBlur(
    onChange: TagContentProps['onTranslationChange']
  ): (value: string, text: string) => void;
  function onBlur(
    onChange:
      | TagContentProps['onTextChange']
      | TagContentProps['onTranslationChange']
  ) {
    return (value: string, originText: string = text) => {
      setEditingRef(undefined);
      props.onBlur();
      onChange?.(value, originText);
    };
  }

  return (
    <div
      className={classNames(styles.tagContent, isClipped && styles.warpContent)}
    >
      {editingRef === textInputRef ? (
        <TagInput
          ref={textInputRef}
          value={text}
          width={inputWidth}
          onBlur={onBlur(props.onTextChange)}
        />
      ) : (
        <div ref={textRef} onClick={onFocus.bind(null, textInputRef, textRef)}>
          {text}
        </div>
      )}
      {editingRef === translationInputRef ? (
        <TagInput
          ref={translationInputRef}
          value={translation ?? ''}
          width={inputWidth}
          onBlur={onBlur(props.onTranslationChange)}
        />
      ) : (
        <div
          ref={translationRef}
          onClick={onFocus.bind(null, translationInputRef, translationRef)}
        >
          {translation || '...'}
        </div>
      )}
    </div>
  );
}
