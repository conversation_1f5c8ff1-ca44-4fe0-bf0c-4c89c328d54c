@import '~@/styles/variables.less';

@tag-border-radius: calc(@size-sm / 2);
@tag-height: calc(@size-ms + @size-sm);
@tag-padding: calc(@size-md / 2);

.tag-wrap {
  max-width: 100%;
}

.tag:global(.@{ant-prefix}-tag) {
  position: relative;
  min-height: @tag-height;
  max-width: 100%;
  line-height: @tag-height;
  padding: 0 @tag-padding;
  margin-right: @size-sm;
  margin-bottom: @size-sm;
  border: 1px solid @base-black-opacity-4;
  border-radius: @tag-border-radius;
  font-size: @font-size;

  &.shadow-tag {
    visibility: hidden;
    position: absolute;
    top: 0;
    height: 0;
    max-width: calc(100% - @size-sm);
    overflow: hidden;
    padding: 0 @size-md;
  }

  &:hover {
    .close-icon {
      opacity: 1;
      z-index: 1;
    }
  }

  :global(.@{ant-prefix}-tag-close-icon) {
    position: absolute;
    right: 0;
    top: 0;
  }

  .close-icon {
    position: absolute;
    width: @font-size;
    height: @font-size;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: @size-xs;
    margin-top: -7px;
    margin-left: -7px;
    border-radius: 50%;
    background-color: @content-btn-transparency;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    z-index: -1;

    :global(path) {
      fill: @background-input;
    }
  }

  &.tag-priority-high {
    color: @base-white-opacity-100;
    background-color: @base-primary-light-90;
  }

  &.tag-priority-medium {
    color: @content-web-tag-major;
    background-color: @background-web-tag-major;

    :global(input) {
      color: @background-input;
      caret-color: @background-input;
    }
  }

  &.tag-priority-low {
    color: @content-web-tag-moderate;
    background-color: @background-web-tag-moderate;
  }

  &.tag-priority-none {
    color: @content-web-tag-minor;
    background-color: @background-web-tag-minor;
  }

  .tag-content {
    display: flex;

    & > div {
      max-width: 100%;
      min-width: @size-xxs;
      overflow: hidden;
      white-space: normal;
      word-break: break-word;
    }

    & > input {
      display: block;
    }

    & > div + div,
    & > div + input,
    & > input + div {
      margin-left: @tag-padding;
      padding-left: @tag-padding;
      border-left: 1px solid @base-black-opacity-4;
    }

    &.warp-content {
      display: block;

      & > div + div,
      & > div + input,
      & > input + div {
        margin-left: 0;
        padding-left: 0;
        border-left: none;
      }
    }
  }
}

.tags {
  position: relative;
  height: 100%;

  &-container {
    position: relative;
    width: 100%;
    padding: @size-sm 0;
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
  }

  &-placeholder {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: @border-radius;
    border: 2px dashed @color-border;
    color: @color-text-secondary;
    cursor: pointer;
  }
}
