import type { EditableTagProps } from './EditableTag';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactNode, ReactElement } from 'react';

import EditableTag from './EditableTag';
import { useState, useEffect, cloneElement } from 'react';
import { v4 as uuid } from 'uuid';

import styles from './styles.module.less';

export type Tag = Pick<
  EditableTagProps,
  'text' | 'translation' | 'priority'
> & {
  id: string;
};

interface EditableTagsProps {
  /** 标签数组 */
  tags: (string | Tag)[];
  empty: ReactNode;
  disabled?: boolean;
  /** 翻译 api */
  fetchTranslation?: (
    text: string,
    preText: string,
    preTranslation?: string
  ) => Promise<string>;
  onChange?: (tags: Tag[]) => void;
}

function generateTags(tags: EditableTagsProps['tags']): Tag[] {
  return tags.map((tag) =>
    typeof tag === 'string'
      ? {
          text: tag,
          id: uuid()
        }
      : tag
  );
}

export default function EditableTags(props: EditableTagsProps) {
  const { tags: originalTags, disabled, fetchTranslation, onChange } = props;
  const [tags, setTags] = useState<Tag[]>(() => generateTags(originalTags));
  const [adding, setAdding] = useState({
    isAdding: false,
    addingTime: 0
  });

  const resetAdding = () => {
    setAdding({
      addingTime: +new Date(),
      isAdding: false
    });
  };

  const onClose = (index: number) => {
    const nextTags = tags.filter((tag, tagIndex) => tagIndex !== index);
    setTags(nextTags);
    onChange?.(nextTags);
    resetAdding();
  };
  const onTextChange = (index: number, text: string) => {
    const nextTags = tags.map((tag, tagIndex) =>
      tagIndex === index ? Object.assign({}, tag, { text }) : tag
    );
    setTags(nextTags);
    onChange?.(nextTags);
    resetAdding();
  };
  const onTranslationChange = (
    index: number,
    translation: string,
    text?: string
  ) => {
    const nextTags = tags.map((tag, tagIndex) =>
      tagIndex === index ? Object.assign({}, tag, { translation, text }) : tag
    );
    setTags(nextTags);
    onChange?.(nextTags);
    resetAdding();
  };

  useEffect(() => {
    setTags(generateTags(originalTags));
  }, [originalTags]);

  /**
   * 点击空白处新建标签
   * @param event
   */
  const onClick: MouseEventHandler<HTMLDivElement> = (event) => {
    const target = event.target as Element;
    if (
      !disabled &&
      (target.matches(`.${styles.tags}`) ||
        target.matches(`.${styles.tagsContainer}`)) &&
      +new Date() - adding.addingTime > 512
    ) {
      setAdding((adding) => ({
        ...adding,
        isAdding: true
      }));
    }
  };

  const onAddTag = (text: string) => {
    const trimmedText = text.trim();

    resetAdding();

    if (!trimmedText) {
      return;
    }

    const nextTags = tags.concat({
      id: uuid(),
      text: trimmedText
    });
    setTags(nextTags);
    onChange?.(nextTags);
  };

  const translate =
    (preText: string, preTranslation?: string) => async (text: string) => {
      const translation = await fetchTranslation?.(
        text,
        preText,
        preTranslation
      );

      return translation ?? '';
    };

  return (
    <div className={styles.tags} onClick={onClick}>
      <div className={styles.tagsContainer}>
        {tags.map((tagProps, index) => (
          <EditableTag
            key={tagProps.id}
            {...tagProps}
            fetchTranslation={translate(tagProps.text, tagProps.translation)}
            onClose={onClose.bind(null, index)}
            onTextChange={onTextChange.bind(null, index)}
            onTranslationChange={onTranslationChange.bind(null, index)}
          />
        ))}
        {!disabled && adding.isAdding && (
          <EditableTag text="" autoFocus onTextChange={onAddTag} />
        )}
      </div>
      {!tags.length &&
        !adding.isAdding &&
        cloneElement(props.empty as ReactElement, {
          className: styles.tagsPlaceholder,
          onClick: () =>
            setAdding((adding) => ({
              ...adding,
              isAdding: true
            }))
        })}
    </div>
  );
}

EditableTags.defaultProps = {
  empty: <div>点击空白处新建标签</div>
};
