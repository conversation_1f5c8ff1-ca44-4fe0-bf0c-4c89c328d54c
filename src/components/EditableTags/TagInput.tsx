import type { InputProps, InputRef } from 'antd';
import type { ChangeEventHandler } from 'react';

import { Input } from 'antd';
import { forwardRef, useRef, useState, useImperativeHandle } from 'react';

import styles from './tagInput.module.less';
import { MAX_TEXT_LENGTH } from '@/constants/corpus';

function getStringWidth(str: string): number {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) {
    return 0;
  }

  context.font = '16px Microsoft YaHei';

  return context.measureText(str).width;
}

export interface TagInputRef {
  focus: () => void;
}

interface TagInputProps
  extends Omit<InputProps, 'value' | 'onBlur' | 'onChange' | 'onPressEnter'> {
  value: string;
  width?: number;
  onBlur: (value: string) => void;
}

export default forwardRef<TagInputRef, TagInputProps>(function TagInput(
  { width, value: originalValue, ...restProps },
  ref
) {
  const inputRef = useRef<InputRef>(null);
  const [value, setValue] = useState(originalValue);
  const [style, setStyle] = useState(() => (width ? { width } : {}));

  useImperativeHandle(ref, () => ({
    focus() {
      inputRef.current?.focus();
    }
  }));

  const onBlur = () => {
    restProps.onBlur?.(value);
  };

  const onChange: ChangeEventHandler<HTMLInputElement> = (event) => {
    const value = event.target.value;

    setValue(value);
    setStyle({
      width: getStringWidth(value)
    });
  };

  return (
    <Input
      ref={inputRef}
      size="small"
      bordered={false}
      {...restProps}
      value={value}
      maxLength={MAX_TEXT_LENGTH}
      className={styles.input}
      style={style}
      onBlur={onBlur}
      onPressEnter={onBlur}
      onChange={onChange}
    />
  );
});
