import type { DesignerConfig } from './types';
import type { ReactNode } from 'react';

import { DesignerConfigContext } from './context';

import { defaultDesignerConfig } from './constants';
import { useEffect, useState } from 'react';
import _ from 'lodash';

export * from './context';
export * from './types';
export * from './hooks';

export type DesignerConfigProviderProps = Partial<DesignerConfig> & {
  children?: ReactNode;
};

function getDesignerConfig(props: DesignerConfigProviderProps) {
  return Object.assign(
    {},
    defaultDesignerConfig,
    _.omitBy(_.omit(props, ['children']), _.isNil)
  );
}

export function DesignerConfigProvider(props: DesignerConfigProviderProps) {
  const [designerConfig, setDesignerConfig] = useState(
    getDesignerConfig.bind(null, props)
  );

  useEffect(() => {
    setDesignerConfig((prevDesignerConfig) => {
      const nextDesignerConfig = getDesignerConfig(props);

      return _.isEqual(nextDesignerConfig, prevDesignerConfig)
        ? prevDesignerConfig
        : nextDesignerConfig;
    });
  }, [props]);

  return (
    <DesignerConfigContext.Provider value={designerConfig}>
      {props.children}
    </DesignerConfigContext.Provider>
  );
}
