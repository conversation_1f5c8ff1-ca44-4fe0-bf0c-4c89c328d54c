import { DesignerConfigContext } from './context';

import { useContext } from 'react';

/**
 * 使用设计室前缀样式类
 * @param suffix 后缀
 * @param props 属性字典
 */
export function useDesignerPrefixCls<Props extends { prefixCls?: string }>(
  suffix?: string,
  props?: Props
) {
  const { prefixCls } = useContext(DesignerConfigContext);

  if (props?.prefixCls) {
    return props.prefixCls;
  }

  return suffix ? `${prefixCls}-${suffix}` : prefixCls;
}
