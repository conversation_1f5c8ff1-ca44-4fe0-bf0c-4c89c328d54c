import { SuggestPromptProps } from '@/api/types';
import { useLayoutEffect, useRef } from 'react';

type UseSliceType = {
  tags: SuggestPromptProps[];
  setTags: React.Dispatch<React.SetStateAction<SuggestPromptProps[]>>;
};

const tagClassName = 'association-tag';

export const useSlice = ({ tags, setTags }: UseSliceType) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (!containerRef.current) return;

    const containerWidth = containerRef.current.offsetWidth;
    const tagElements =
      containerRef.current.getElementsByClassName(tagClassName);

    let totalWidth = 0;
    let hiddenTags = 0;

    if (!tagElements?.length) return;

    // 裁剪成一行的tag
    for (const tag of tagElements) {
      const tagWidth = tag.getBoundingClientRect().width;

      totalWidth += tagWidth;

      if (totalWidth > containerWidth) {
        hiddenTags++;
        break;
      }
    }

    if (hiddenTags > 0) {
      setTags(tags.slice(0, tags.length - hiddenTags));
    }
  }, [setTags, tags]);

  return { containerRef, tagClassName };
};
