import { Tooltip, Typography } from 'antd';
import classNames from 'classnames';
import styles from './index.module.less';
import { ComponentPropsWithoutRef } from 'react';

export interface TagProps
  extends ComponentPropsWithoutRef<typeof Typography.Text> {
  disabled?: boolean;
  text: string;
}

export const Tag = ({ disabled, text }: TagProps) => {
  return (
    <Tooltip
      title="点击替换高亮的词"
      destroyTooltipOnHide
      mouseEnterDelay={2}
      placement="bottomLeft"
    >
      <Typography.Text
        className={classNames({
          [styles.disabled]: disabled
        })}
      >
        {text}
      </Typography.Text>
    </Tooltip>
  );
};
