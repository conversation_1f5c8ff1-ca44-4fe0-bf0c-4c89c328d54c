import { motion } from 'framer-motion';

export interface RefetchIconProps {
  disabled?: boolean;
  loading?: boolean;
  onClick?(): void;
  className?: string;
}

export const RefetchIcon = ({
  disabled,
  onClick,
  loading,
  className
}: RefetchIconProps) => {
  return (
    <motion.svg
      className={className}
      whileHover={{ scale: disabled ? 1 : 1.1 }}
      onClick={() => {
        if (disabled) return;
        onClick?.();
      }}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 48 48"
      width="1em"
      height="1em"
      fill="currentColor"
      aria-hidden="true"
    >
      <motion.path
        d="M4 39a2 2 0 110-4c4.78 0 7.91-.84 10.24-2.5 2.34-1.68 4.16-4.39 5.9-8.74 1.93-4.82 4.7-8.35 8.73-10.63 2.93-1.65 6.4-2.57 10.5-2.94L38.1 8.93v-.01h-.01a2 2 0 012.82-2.83l4.5 4.5a2 2 0 010 2.82l-4.47 4.48-.02.02a2 2 0 01-2.83-2.82v-.01h.01v-.01l.81-.8c-3.31.37-5.95 1.15-8.08 2.35-3.06 1.72-5.3 4.44-6.97 8.62-1.86 4.65-4.04 8.19-7.3 10.51C13.3 38.1 9.22 39 4 39zM28.87 34.87a17.83 17.83 0 01-3.06-2.18c.7-1.18 1.32-2.43 1.9-3.7.93.95 1.97 1.74 3.12 2.4 2.13 1.2 4.77 1.97 8.08 2.35l-.8-.8v-.01h-.01v-.01h-.01a2 2 0 112.82-2.83h.01l.02.02 4.47 4.48a2 2 0 010 2.82l-4.5 4.5a2 2 0 11-2.82-2.82l.01-.02 1.27-1.26c-4.1-.37-7.57-1.3-10.5-2.94zM17.12 18.44a26.92 26.92 0 012.29-3.56c-.86-1-1.8-1.88-2.85-2.63C13.3 9.9 9.22 9 4 9a2 2 0 000 4c4.78 0 7.91.84 10.24 2.5 1.05.75 2 1.71 2.88 2.94z"
        stroke="currentColor" // 设置描边颜色
        strokeWidth="2" // 设置描边宽度
        strokeLinecap="round"
        strokeLinejoin="round"
        initial={{ pathLength: 1, pathOffset: 1 }} // 初始状态
        animate={loading ? { pathLength: 1, pathOffset: 0 } : undefined} // 动画状态
        transition={{
          duration: 1,
          ease: 'easeInOut',
          repeat: loading ? Infinity : 0
        }}
      />
    </motion.svg>
  );
};
