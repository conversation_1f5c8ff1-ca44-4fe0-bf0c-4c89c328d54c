import { Col, Row } from 'antd';
import styles from './index.module.less';
import { useSlice } from './useSlice';
import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
  MouseEvent
} from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import classNames from 'classnames';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { promptSuggest } from '@/api';
import _ from 'lodash';
import axios from 'axios';
import { SuggestPromptProps } from '@/api/types';
import { RefetchIcon } from './RefetchIcon';
import { Tag } from './Tag';
import { useAbortController } from '@/hooks/useAbortController';

export interface AssociationProps {
  onClick?(tag: SuggestPromptProps): void;
  onMouseMove?(e: MouseEvent<HTMLDivElement>): void;
  onMouseOut?(e: MouseEvent<HTMLDivElement>): void;
  disabled?: boolean;
}

export interface AssociationRef {
  fetchSuggest(text: string): Promise<void>;
}

export const Association = forwardRef<AssociationRef, AssociationProps>(
  (props, ref) => {
    const [loading, setLoading] = useState(false);
    const [tags, setTags] = useState<SuggestPromptProps[]>([]);
    const { containerRef, tagClassName } = useSlice({ tags, setTags });
    const disabled = props.disabled || loading;

    const cachedTextRef = useRef<string>('');

    const { cancelRequest } = useAbortController();

    const fetchSuggest = useCallback(async (text: string, change?: boolean) => {
      if (!text) return;

      cachedTextRef.current = text;

      try {
        setLoading(true);
        const signal = cancelRequest();

        const res = await promptSuggest(
          { text, from: change ? 1 : undefined },
          { signal }
        );
        setLoading(false);
        setTags((prev) => {
          if (!res.suggestPrompt.length) return prev;
          return res.suggestPrompt;
        });
      } catch (error) {
        if (axios.isCancel(error)) {
          console.log('Request canceled');
        } else {
          setLoading(false);

          defaultErrorHandler(error);
        }
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useImperativeHandle(
      ref,
      () => ({
        fetchSuggest: _.debounce(
          (text: string) => fetchSuggest(text),
          1000
        ) as AssociationRef['fetchSuggest']
      }),
      [fetchSuggest]
    );

    if (!tags.length) return null;

    return (
      <AnimatePresence mode="popLayout">
        <motion.div
          key={tags[0].promptName}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2, ease: 'easeInOut' }}
          exit={{ opacity: 0 }}
        >
          <Row
            justify="space-between"
            align={'middle'}
            wrap={false}
            gutter={16}
            className={styles.association}
          >
            <Col flex={1}>
              <Row gutter={16} ref={containerRef}>
                {tags.map((tag, index) => (
                  <Col
                    key={tag.promptName + index}
                    className={tagClassName}
                    onClick={() => {
                      if (disabled) return;
                      props.onClick?.(tag);
                    }}
                    onMouseMove={(e) => {
                      if (disabled) return;
                      props.onMouseMove?.(e);
                    }}
                    onMouseOut={(e) => {
                      if (disabled) return;
                      props.onMouseOut?.(e);
                    }}
                  >
                    <Tag disabled={disabled} text={tag.promptName} />
                  </Col>
                ))}
              </Row>
            </Col>

            <Col className={classNames(styles.icon)}>
              <RefetchIcon
                disabled={disabled}
                loading={loading}
                onClick={() => {
                  fetchSuggest(cachedTextRef.current, true);
                }}
              />
            </Col>
          </Row>
        </motion.div>
      </AnimatePresence>
    );
  }
);
