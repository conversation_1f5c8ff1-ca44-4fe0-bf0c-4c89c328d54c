import { Input } from 'antd';

import {
  ComponentPropsWithoutRef,
  ComponentRef,
  useEffect,
  useRef,
  useState
} from 'react';
import { Complement, type TrackEventType } from './Complement';
import { TextArea } from '../TextArea';
import { RefetchIcon as RandomIcon } from './Association/RefetchIcon';
import styles from './index.module.less';
import { fetchRandomPrompt } from '@/api';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { AiSparkleBold } from '@meitu/candy-icons';
import { CorpusModal } from './CorpusModal';
import classNames from 'classnames';
import { trackEvent } from '@/services';

import { CorpusFilter } from '@/types';

const { TextArea: AntdTextArea } = Input;

type CorpusOption = {
  visible: boolean;
  corpusFilter: CorpusFilter;
  /**
   * 展开按钮的配置
   */
  expand?: {
    numbers: number;
  };
  /**
   * 触发按钮的文字 默认为“词库”
   * 如果哦设置了expand 该设置不生效
   */
  btnLabel?: string;
};
export interface TextAreaProps
  extends Omit<ComponentPropsWithoutRef<typeof AntdTextArea>, 'onChange'>,
    TrackEventType {
  // 智能补全textarea初始值
  complementInitValue?: string;
  onComplementChange?(value: string): void;
  onChange?(value: string, event?: any): void;
  /** 是否有智能补全按钮 操作按钮 */
  hasComplementAction?: boolean;
  /** 是否有随机 prompt 操作按钮 */
  hasRandomPromptAction?: boolean;
  /** 是否有词库 操作按钮 */
  corpusAction?: CorpusFilter | CorpusOption;
  /** 词库按钮展示的文字 */
  /** 是否展示原来的随机按钮 */
  showInitRandomPrompt?: boolean;
}

// TODO component separation, no longer coupled together.
export const SmartTextarea = ({
  className,
  complementInitValue,
  onComplementChange,
  onChange,
  hasRandomPromptAction,
  hasComplementAction = true,
  showInitRandomPrompt = true,
  corpusAction,
  onOpen,
  onClose,
  onStartComplement,
  onSuccess,
  onApply,
  onReGenerate,
  onCorpusVisibleChange,
  onCorpusLanguageChange,
  ...restProps
}: TextAreaProps) => {
  const textareaRef = useRef<ComponentRef<typeof AntdTextArea>>(null);
  const [prompts, setPrompts] = useState<Array<string>>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  /**
   * 随机生成 Prompt
   */
  const onRandomPrompt = () => {
    // 检查是否已经取完所有关键词，如果是，则重新从头开始
    if (currentIndex >= prompts.length) {
      setCurrentIndex(() => 0);
    }

    // 更新当前索引，以便下次点击可以获取下一个关键词
    setCurrentIndex((prevIndex) => prevIndex + 1);
    // 随机获取关键词
    const promptIndex = currentIndex >= prompts.length ? 0 : currentIndex;
    const prompt = prompts[promptIndex];
    // 获取是文生图还是图生图
    const isTextToImage = window.location.pathname.includes('text-to-image');
    trackEvent('inspiration_dice_click', {
      function: isTextToImage ? 'text_to_image' : 'image_to_image'
    });
    onChange?.(prompt);
  };

  useEffect(() => {
    if (
      (hasRandomPromptAction && prompts.length === 0) ||
      showInitRandomPrompt
    ) {
      // 有配置随机 prompt 功能
      fetchRandomPrompt()
        .then((prompts) => {
          setPrompts(prompts);
        })
        .catch(defaultErrorHandler);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasRandomPromptAction, showInitRandomPrompt]);

  function renderCorpus() {
    if (!corpusAction) {
      return;
    }

    let visible = true;
    let filter: null | CorpusOption['corpusFilter'] = null;
    let expand: CorpusOption['expand'] = undefined;
    let btnLabel: CorpusOption['btnLabel'] = undefined;

    if (typeof corpusAction === 'number') {
      filter = corpusAction;
    } else {
      filter = corpusAction.corpusFilter;
      visible = corpusAction.visible;
      expand = corpusAction.expand;
      btnLabel = corpusAction.btnLabel;
    }

    if (!visible || filter === null) {
      return;
    }

    if (!expand) {
      return (
        <CorpusModal
          prompt={restProps.value as string}
          maxLength={restProps.maxLength}
          corpusFilter={filter}
          className={styles.actionItem}
          onChange={onChange}
          onVisibleChange={onCorpusVisibleChange}
          onLanguageChange={onCorpusLanguageChange}
          triggerBtnLabel={btnLabel}
        />
      );
    }

    return (
      <CorpusModal.Expand
        prompt={restProps.value as string}
        maxLength={restProps.maxLength}
        corpusFilter={filter}
        className={styles.actionItem}
        onChange={onChange}
        onVisibleChange={onCorpusVisibleChange}
        onLanguageChange={onCorpusLanguageChange}
        expandNumbers={expand.numbers}
      />
    );
  }

  return (
    <TextArea
      ref={textareaRef}
      {...restProps}
      onChange={(e) => {
        onChange?.(e.target.value, e);
      }}
      extra={
        <section className={styles.actions}>
          {hasComplementAction ? (
            <Complement
              onFinish={(v) => {
                onChange?.(v);
              }}
              inputValue={restProps.value as string}
              onOpen={onOpen}
              onStartComplement={onStartComplement}
              onSuccess={onSuccess}
              onApply={onApply}
              onReGenerate={onReGenerate}
              onComplementChange={onComplementChange}
              complementInitValue={
                (complementInitValue || (restProps.value as string)) ?? ''
              }
              className={styles.actionItem}
            />
          ) : null}
          {hasRandomPromptAction ? (
            <div
              className={classNames(
                styles.actionItem,
                styles.onlyIcon,
                styles.button,
                styles.randomPrompt
              )}
              onClick={onRandomPrompt}
            >
              <AiSparkleBold />
              <span
                style={{
                  fontSize: '12px'
                }}
              >
                新灵感
              </span>
            </div>
          ) : null}
          {renderCorpus()}
          {showInitRandomPrompt ? (
            <RandomIcon
              className={classNames(styles.actionItem, styles.onlyIcon)}
              onClick={onRandomPrompt}
            />
          ) : null}
        </section>
      }
    />
  );
};
