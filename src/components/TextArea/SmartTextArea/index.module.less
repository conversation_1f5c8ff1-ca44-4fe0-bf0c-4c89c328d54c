@import '~@/styles/variables.less';

.actions {
  display: flex;
  align-items: center;

  .action-item {
    display: flex;
    align-items: center;
    margin-right: @size-xxs;
    cursor: pointer;

    & + .only-icon {
      margin-left: @size-xxs;
    }
  }
}
.button {
  padding: 4px 8px;
  height: 24px;
  border-radius: 6px;
  border: 1px solid var(--stroke-systemBorderOverlay, rgba(0, 0, 0, 0.08));
  background: #fff;
  z-index: 999;

  color: @content-input;
  font-style: @text-12;
  line-height: 16px;
  cursor: pointer;
  &:hover {
    background: #f5f5f5;
  }
}
.randomPrompt {
  font-style: @text-12;
}
