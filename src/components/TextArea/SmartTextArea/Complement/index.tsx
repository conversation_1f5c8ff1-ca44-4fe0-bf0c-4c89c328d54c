import type { Language } from '@/types';

import { Col, Modal, Row, Space } from 'antd';
import styles from './index.module.less';
import { AiSparkleBold, CrossBoldOutlined } from '@meitu/candy-icons';
import classNames from 'classnames';
import { useEffect, useRef, useState } from 'react';
import { TextArea } from '../..';
import { FullWidthSpace } from '@/components/FullWidthSpace';
import { useComplementFlow } from './useComplementFlow';
import { motion, AnimatePresence } from 'framer-motion';
import { flushSync } from 'react-dom';
import { Button } from '@/components';

export type TrackEventType = {
  onOpen?(): void;
  onClose?(resultPrompt: string): void;
  onStartComplement?(text: string): void;
  onSuccess?(text: string, resultPrompt: string): void;
  onApply?(resultPrompt: string): void;
  onReGenerate?(resultPrompt: string): void;
  onCorpusVisibleChange?(visible: boolean): void;
  onCorpusLanguageChange?(language: Language): void;
};
export interface ComplementProps extends TrackEventType {
  className?: string;
  style?: React.CSSProperties;
  onFinish?(value: string): void;
  // 补全初始值
  complementInitValue?: string;
  onComplementChange?(value: string): void;
  // 发布会需求 暂时将参数设置为文本框最新值
  inputValue?: string;
}

export const Complement = ({
  className,
  style,
  onFinish,
  complementInitValue = '',
  onComplementChange,

  onOpen,
  onClose: onCloseFromProps,
  onStartComplement,
  onSuccess,
  onApply,
  onReGenerate,
  inputValue
}: ComplementProps) => {
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState(inputValue);
  // const [value, setValue] = useState(complementInitValue);
  const [position, setPosition] = useState<{ x: number; y: number }>();

  const ref = useRef<HTMLDivElement>(null);

  // 发布会需求 暂时将参数设置为文本框最新值
  useEffect(() => {
    setValue(inputValue ?? '');
  }, [inputValue]);

  const {
    // backStep,
    nextStep,
    disabledButton,
    generatedStatus,
    initStatus,
    currentFlow,
    result,
    init
  } = useComplementFlow(value ?? '', {
    onFinish: (v) => {
      onFinish?.(v ?? '');
      onClose();
    },
    onStartComplement,
    onSuccess,
    onApply,
    onReGenerate,
    onReviewFailed: () => {
      onClose();
    }
  });

  // const onBack = () => {
  //   backStep();
  // };

  const onClose = () => {
    setOpen(false);
    init();
    onCloseFromProps?.(result);
  };

  const onChange = (value: string) => {
    setValue(value);
    // onComplementChange?.(value);
  };

  const onClickTrigger = () => {
    // if (!!complementInitValue && !value) {
    //   onChange(complementInitValue);
    // }
    // TODO if there is a need for custom location in the future, expose this parameter.
    if (ref.current) {
      flushSync(() => {
        const { x, y } = ref.current!.getBoundingClientRect();

        setPosition({ x: x + 296, y: Math.max(y - 128, 0) });
      });
    }

    onOpen?.();

    setOpen(true);

    nextStep();
  };

  return (
    <>
      <div
        ref={ref}
        onClick={onClickTrigger}
        className={classNames(styles.button, className)}
        style={style}
      >
        <Space className={styles.space} size={2}>
          <AiSparkleBold />
          智能联想
        </Space>
      </div>

      <Modal
        width={280}
        style={
          position
            ? { position: 'absolute', top: position.y, left: position.x }
            : undefined
        }
        open={open}
        footer={null}
        mask={false}
        maskClosable={false}
        className={styles.modal}
        title={
          <Space>
            智能联想
            <div className={styles.tag}>限免</div>
          </Space>
        }
        onCancel={onClose}
        closeIcon={<CrossBoldOutlined />}
      >
        <AnimatePresence initial={false} mode="wait">
          <motion.div
            key={currentFlow.status}
            initial={{ x: 10, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -10, opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <FullWidthSpace direction="vertical" size={16}>
              {!!currentFlow.transitionIcon && (
                <FullWidthSpace
                  className={styles.iconHolder}
                  align="center"
                  direction="vertical"
                  size={12}
                >
                  {currentFlow.transitionIcon}
                  {currentFlow.transitionText}
                </FullWidthSpace>
              )}

              {(initStatus || generatedStatus) && (
                <TextArea
                  placeholder={
                    initStatus
                      ? '请输入简单的描述，我们会为您补充完善提示词。'
                      : undefined
                  }
                  disabled={generatedStatus}
                  className={styles.textarea}
                  allowClear
                  maxLength={800}
                  showCount={initStatus}
                  value={generatedStatus ? result : value}
                  onChange={({ target: { value } }) => {
                    if (generatedStatus) return;
                    onChange(value);
                  }}
                />
              )}

              <Row gutter={12}>
                {/* {!!currentFlow.previous && (
                  <Col flex={1}>
                    <Button type="default" onClick={onBack} block>
                      {currentFlow.backButtonName}
                    </Button>
                  </Col>
                )} */}

                {!!currentFlow.action && (
                  <Col flex={1}>
                    <Button
                      type="primary"
                      disabled={disabledButton}
                      block
                      style={{ width: '100%' }}
                      onClick={nextStep}
                    >
                      {currentFlow.nextButtonName}
                    </Button>
                  </Col>
                )}
              </Row>
            </FullWidthSpace>
          </motion.div>
        </AnimatePresence>
      </Modal>
    </>
  );
};
