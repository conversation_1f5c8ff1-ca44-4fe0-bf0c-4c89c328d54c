import { CloudSyn } from '@meitu/candy-icons';
import { Loading3QuartersOutlined } from '@ant-design/icons';

import {
  ComplementFlowType,
  ComplementFlows,
  CompleteAction,
  NextWithLoaderAction
} from './type';

export * from './type';

export const loadingFlow: ComplementFlowType = {
  status: ComplementFlows.Loading,
  transitionIcon: (
    <Loading3QuartersOutlined spin style={{ fontSize: '40px' }} />
  ),
  transitionText: '生成中，请稍后'
};

export const errorFlow: ComplementFlowType = {
  status: ComplementFlows.Error,
  nextButtonName: '重新生成',
  action: () => new NextWithLoaderAction(generatedFlow, loadingFlow, errorFlow),
  transitionIcon: <CloudSyn style={{ fontSize: '40px' }} />,
  transitionText: '加载失败'
};

export const generatedFlow: ComplementFlowType = {
  status: ComplementFlows.Generated,
  nextButtonName: '使用该描述',
  backButtonName: '重新生成',
  action: new CompleteAction(),
  previous: () => initFlow
};

export const initFlow: ComplementFlowType = {
  status: ComplementFlows.Init,
  nextButtonName: '开始联想',
  action: new NextWithLoaderAction(generatedFlow, loadingFlow, errorFlow)
};

export const AllFlows = {
  initFlow,
  generatedFlow,
  errorFlow,
  loadingFlow
};
