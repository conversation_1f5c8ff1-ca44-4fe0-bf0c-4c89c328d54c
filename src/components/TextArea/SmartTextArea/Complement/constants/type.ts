import { ReactNode } from 'react';

export enum ComplementFlows {
  Init = 'init',
  Loading = 'loading',
  Generated = 'generated',
  Error = 'error'
}

/** actions */
export const Next = 'Next' as const;
export const NextWithLoader = 'NextWithLoader' as const;
export const Complete = 'Complete' as const;

export type ComplementActions =
  | NextAction
  | NextWithLoaderAction
  | CompleteAction;

export type ComplementFlowType = {
  status: ComplementFlows;
  // 下一步按钮名称
  nextButtonName?: string;
  // 上一步按钮名称
  backButtonName?: string;
  // 下一步动作
  action?: ComplementActions | (() => ComplementActions);
  // 上一步动作
  previous?: () => ComplementFlowType;
  // 过渡状态图标
  transitionIcon?: ReactNode;
  // 过渡状态文字
  transitionText?: string;
};

export class NextAction {
  type = Next;

  constructor(public nextFlow: ComplementFlowType) {
    this.nextFlow = nextFlow;
  }
}

export class NextWithLoaderAction {
  type = NextWithLoader;

  constructor(
    public nextFlow: ComplementFlowType,
    public loadingFlow: ComplementFlowType,
    public errorFlow: ComplementFlowType
  ) {}
}

export class CompleteAction {
  type = Complete;
}
