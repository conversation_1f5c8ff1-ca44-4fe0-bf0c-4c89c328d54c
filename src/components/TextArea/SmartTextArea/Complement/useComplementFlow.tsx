import { useState } from 'react';
import { AllFlows, ComplementFlows, NextWithLoaderAction } from './constants';
import { promptSearch } from '@/api';
import { type TrackEventType } from '.';
import { useAbortController } from '@/hooks/useAbortController';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { CONTENT_SENSITIVE_CODE } from '@/constants/errorCode';

export type UseComplementFlowType = TrackEventType & {
  onFinish?(v: string): void;
  onReviewFailed?(): void;
};

export const useComplementFlow = (
  req: string,
  {
    onFinish,
    onStartComplement,
    onSuccess,
    onApply,
    onReGenerate,
    onReviewFailed
  }: UseComplementFlowType
) => {
  const [currentFlow, setCurrentFlow] = useState(AllFlows.initFlow);
  const [result, setResult] = useState<string>('');
  const disabledButton = currentFlow.status === ComplementFlows.Init && !req;
  const generatedStatus = currentFlow.status === ComplementFlows.Generated;
  const initStatus = currentFlow.status === ComplementFlows.Init;
  const { cancelRequest } = useAbortController();

  const init = () => {
    cancelRequest();
    setCurrentFlow(AllFlows.initFlow);
  };

  const nextStep = () => {
    const action =
      typeof currentFlow.action === 'function'
        ? currentFlow.action()
        : currentFlow.action;

    if (!action) return;

    handleTrackEvent();

    switch (action.type) {
      case 'Complete':
        onFinish?.(result);
        setCurrentFlow(AllFlows.initFlow);
        break;
      case 'Next':
        setCurrentFlow(action.nextFlow);
        break;

      case 'NextWithLoader':
        fetch(action);

        break;
      default: {
        throw new Error('Unrecognized Action Type Received');
      }
    }
  };

  const backStep = () => {
    if (!currentFlow.previous) return;
    handleTrackEvent(true);
    setCurrentFlow(currentFlow.previous);
  };

  const fetch = async (action: NextWithLoaderAction) => {
    setCurrentFlow(action.loadingFlow);

    const toErrorStatus = () => {
      setTimeout(() => {
        setCurrentFlow(action.errorFlow);
      }, 1000);
    };
    const signal = cancelRequest();

    // TODO 暂时处理， 后续如果有多个NextWithLoaderAction， 状态机需要增加payload
    try {
      const res = await promptSearch({ text: req }, { signal });
      const text = res?.resultPrompt?.[0]?.promptName ?? '';
      if (!text) {
        toErrorStatus();
        return;
      }
      handleTrackEvent(false, text);
      setResult(text);
      setCurrentFlow(action.nextFlow);
    } catch (error: any) {
      if (error.code === 'ERR_CANCELED') return;

      defaultErrorHandler(error, (err) => {
        if (err?.code === CONTENT_SENSITIVE_CODE) {
          onReviewFailed?.();

          return err?.message ?? '请调整描述后再尝试';
        }
        return '';
      });

      toErrorStatus();
    }
  };

  const handleTrackEvent = (isBackEvent?: boolean, fetchedResult?: string) => {
    if (isBackEvent) {
      onReGenerate?.(result);
      return;
    }
    if (fetchedResult) {
      onSuccess?.(req, fetchedResult);
      return;
    }
    if (initStatus) {
      onStartComplement?.(req);
      return;
    }

    if (generatedStatus) {
      onApply?.(result);
    }
  };

  return {
    backStep,
    nextStep,
    disabledButton,
    generatedStatus,
    initStatus,
    currentFlow,
    result,
    init
  };
};
