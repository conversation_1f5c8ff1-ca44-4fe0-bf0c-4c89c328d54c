@import '~@/styles/variables.less';

.button {
  padding: 4px 8px;
  height: 24px;
  border-radius: 6px;
  border: 1px solid var(--stroke-systemBorderOverlay, rgba(0, 0, 0, 0.08));
  background: #fff;
  z-index: 999;

  color: @content-input;
  font-style: @text-12;
  line-height: 16px;

  cursor: pointer;
  &:hover {
    background: #f5f5f5;
  }
}

.modal:global(.ant-modal) {
  :global(.ant-modal-content) {
    padding: 16px;
    min-height: 216px;
    border-radius: 12px;
    box-shadow: @level-4;

    :global(.ant-modal-header) {
      margin-bottom: 12px;

      :global(.ant-modal-title) {
        font-style: normal;
        font-weight: 600;
        font-size: @text-14;
        line-height: 20px;
        color: @content-system-primary;
      }
    }

    .textarea {
      height: 104px;
      resize: none;

      &:global(.ant-input-affix-wrapper) {
        cursor: text !important;

        textarea {
          cursor: text !important;
        }
      }
    }
  }
}

.iconHolder {
  margin-top: 40px;
  color: @content-system-quinary;
  margin-bottom: 10px;
}

.tag {
  padding: 1px 6px;
  border-radius: 4px;
  background: @background-tag-flash-sale;
  color: @base-white-opacity-100;
  text-shadow: 0px 0px 1px rgba(0, 0, 0, 0.25);
  font-size: @text-12;
  font-style: normal;
  font-weight: 600;
  line-height: 16px;
}

.space {
  font-size: 12px !important;
}
