@import '~@/styles/variables.less';

.button:global(.@{ant-prefix}-btn.@{ant-prefix}-btn-sm) {
  padding: @size-xxs @size-xs;
  height: @size-lg;
  border-radius: 6px;
  border: 1px solid var(--stroke-systemBorder<PERSON>verlay, rgba(0, 0, 0, 0.08));
  background: #fff;
  font-style: @text-12;
  line-height: @size-ms;
  font-size: @font-size-sm;

  :global(.@{ant-prefix}-btn-icon) {
    margin-right: 2px !important;
  }
}

.modal:global(.@{ant-prefix}-modal) {
  :global {
    .@{ant-prefix}-modal-content {
      width: 520px;
      padding: @size-md;
      border-radius: @size-sm;
      box-shadow: @level-4;

      .@{ant-prefix}-modal-body {
        height: 75vh;
        max-height: 584px;
        min-height: 292px;
      }
    }

    .@{ant-prefix}-modal-close {
      margin-top: @size-xs;
    }
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-right: @size-xl;

    h3 {
      font-size: @font-size-lg;
      margin-bottom: 0;
    }

    &-extra {
      display: flex;
      align-items: center;

      & > * {
        margin-right: @size-xxs;
      }

      .switcher {
        margin-right: @size-md;
        color: @content-system-quaternary;
        font-size: @font-size;
        font-weight: 400;

        & > span {
          cursor: pointer;
          margin-left: calc(@size-xxs / 2);
          margin-right: calc(@size-xxs / 2);

          &.active {
            color: @content-web-primary;
            text-decoration-line: underline;
          }
        }
      }
    }
  }
}

.corpus-tabs {
  margin-right: -16px;
}

.major-tabs {
  :global .@{ant-prefix}-tabs-nav {
    .@{ant-prefix}-tabs-nav-wrap {
      transform: none !important;
    }

    .@{ant-prefix}-tabs-tab {
      font-size: @font-size !important;
    }
  }
}
