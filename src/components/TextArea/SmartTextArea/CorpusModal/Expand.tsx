import type { Corpus, CorpusWithPriority, Language } from '@/types';

import { Mo<PERSON>, Button, Image } from 'antd';
import {
  type CorpusTabsRef,
  CorpusTabsProvider,
  CorpusTabs,
  SearchInput
} from '@/components/CorpusTabs';
import { CrossBold } from '@meitu/candy-icons';
import { Fragment, useRef, useState } from 'react';
import { flushSync } from 'react-dom';
import { formatCorpusByText, formatTextByPriority } from '@/utils/corpus';
import classNames from 'classnames';
import _ from 'lodash';

import { CorpusFilter } from '@/types';
import styles from './index.module.less';
import { useCorpusCategoryState } from '@/components/CorpusTabs/hooks';
import { toAtlasImageView2URL } from '@meitu/util';

interface CorpusModalExpandedProps {
  prompt?: string;
  maxLength?: number;
  corpusFilter: CorpusFilter;
  className?: string;
  onChange?: (value: string) => void;
  onVisibleChange?: (visible: boolean) => void;
  onLanguageChange?: (language: Language) => void;
  expandNumbers?: number;
}

const getFormatTextByLanguage = (language: Language) => (corpus: Corpus) =>
  formatTextByPriority(
    corpus,
    (corpus: CorpusWithPriority) =>
      corpus.displayText ?? (corpus[language] as string)
  );

function CorpusModalContent(props: CorpusModalExpandedProps) {
  const [open, setOpen] = useState(false);
  const [language, setLanguage] = useState<Language>('text');
  const [positionY, setPositionY] = useState<number>();
  const corpusRef = useRef<CorpusTabsRef>(null);
  const [selectedCorpora, setSelectedCorpora] = useState<Corpus[]>([]);

  const btnsMapRef = useRef<Map<number, HTMLElement>>(new Map());
  const { categories } = useCorpusCategoryState();

  const onClose = () => {
    setOpen(false);
    props.onVisibleChange?.(false);
  };

  const sliceText: (text: string) => string = props.maxLength
    ? (text) => text.slice(0, props.maxLength)
    : _.identity;
  const onChange = (corpora: Corpus[]) => {
    setSelectedCorpora(corpora);
    const formatTextByLanguage = getFormatTextByLanguage(language);
    props.onChange?.(sliceText(corpora.map(formatTextByLanguage).join(',')));
  };

  const onClick = (id: number) => {
    const el = btnsMapRef.current.get(id);
    if (el) {
      flushSync(() => {
        const { y } = (el as HTMLElement).getBoundingClientRect();

        setPositionY(
          Math.min(
            window.innerHeight * 0.3,
            window.innerHeight - 438,
            Math.max(y - 128, 0)
          )
        );
      });
    }

    setOpen(true);
    props.onVisibleChange?.(true);

    setSelectedCorpora(formatCorpusByText(props.prompt ?? '', selectedCorpora));

    setTimeout(() => {
      corpusRef.current?.setSelectedCorpora(
        props.prompt ? formatCorpusByText(props.prompt, selectedCorpora) : []
      );

      corpusRef.current?.selectCategory(id);
    });
  };

  const onLanguageChange = (language: Language) => {
    setLanguage(language);
    const formatTextByLanguage = getFormatTextByLanguage(language);
    props.onChange?.(
      sliceText(selectedCorpora.map(formatTextByLanguage).join(','))
    );
    props.onLanguageChange?.(language);
  };

  function renderExpandBtns() {
    const expandNumbers = props.expandNumbers ?? 2;
    const expandBtnNumbers = Math.min(expandNumbers, categories.length);
    const moreBtnId =
      expandBtnNumbers < categories.length && categories[expandBtnNumbers].id;
    return (
      <Fragment>
        {categories.slice(0, expandBtnNumbers).map(({ id, name, icon }) => {
          const iconSize = 12;
          const imageSize = iconSize * window.devicePixelRatio;
          const iconImage = icon ? (
            <Image
              width={iconSize}
              height={iconSize}
              src={toAtlasImageView2URL(icon, {
                mode: 2,
                width: imageSize,
                height: imageSize
              })}
            />
          ) : null;

          return (
            <Button
              key={id}
              ref={(el) => el && btnsMapRef.current.set(id, el)}
              size="small"
              type="text"
              icon={iconImage}
              className={classNames(styles.button, props.className)}
              onClick={() => onClick(id)}
            >
              {name}
            </Button>
          );
        })}

        {moreBtnId && (
          <Button
            key={categories[expandBtnNumbers].id}
            ref={(el) => el && btnsMapRef.current.set(moreBtnId, el)}
            size="small"
            type="text"
            className={classNames(styles.button, props.className)}
            onClick={() => onClick(moreBtnId)}
          >
            更多
          </Button>
        )}
      </Fragment>
    );
  }

  return (
    <>
      {renderExpandBtns()}
      <Modal
        style={{
          position: 'absolute',
          top: positionY,
          left: 336
        }}
        open={open}
        footer={null}
        mask={false}
        closeIcon={<CrossBold />}
        className={styles.modal}
        title={<Title value={language} onChange={onLanguageChange} />}
        onCancel={onClose}
      >
        <CorpusTabs
          ref={corpusRef}
          searchVisible={false}
          majorClassName={styles.majorTabs}
          disabled={
            (props.prompt?.length ?? 0) >= (props.maxLength ?? Infinity)
          }
          className={styles.corpusTabs}
          onChange={onChange}
        />
      </Modal>
    </>
  );
}

export function CorpusModalExpanded(props: CorpusModalExpandedProps) {
  return (
    <CorpusTabsProvider corpusFilter={props.corpusFilter}>
      <CorpusModalContent {...props} />
    </CorpusTabsProvider>
  );
}

interface SwitcherProps {
  value: Language;
  onChange: (language: Language) => void;
}

function Title(switcherProps: SwitcherProps) {
  return (
    <div className={styles.title}>
      <h3>创意词库</h3>
      <div className={styles.titleExtra}>
        <Switcher {...switcherProps} />
        <SearchInput />
      </div>
    </div>
  );
}

function Switcher({ value, onChange }: SwitcherProps) {
  return (
    <div className={styles.switcher}>
      <span
        className={value === 'text' ? styles.active : ''}
        onClick={onChange.bind(null, 'text')}
      >
        中文
      </span>
      /
      <span
        className={value === 'translation' ? styles.active : ''}
        onClick={onChange.bind(null, 'translation')}
      >
        EN
      </span>
    </div>
  );
}
