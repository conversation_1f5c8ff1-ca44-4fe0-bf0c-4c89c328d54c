import type { Corpus, CorpusWithPriority, Language } from '@/types';

import { <PERSON><PERSON>, Button } from 'antd';
import {
  type CorpusTabsRef,
  CorpusTabsProvider,
  CorpusTabs,
  SearchInput
} from '@/components/CorpusTabs';
import { BookWithMarkBold, CrossBold } from '@meitu/candy-icons';
import { useRef, useState } from 'react';
import { flushSync } from 'react-dom';
import { formatCorpusByText, formatTextByPriority } from '@/utils/corpus';
import classNames from 'classnames';
import _ from 'lodash';
import { CorpusModalExpanded } from './Expand';
import { CorpusFilter } from '@/types';
import styles from './index.module.less';

interface CorpusModalProps {
  prompt?: string;
  maxLength?: number;
  corpusFilter: CorpusFilter;
  className?: string;
  onChange?: (value: string) => void;
  onVisibleChange?: (visible: boolean) => void;
  onLanguageChange?: (language: Language) => void;
  triggerBtnLabel?: string;
}

const getFormatTextByLanguage = (language: Language) => (corpus: Corpus) =>
  formatTextByPriority(
    corpus,
    (corpus: CorpusWithPriority) =>
      corpus.displayText ?? (corpus[language] as string)
  );

export function CorpusModal(props: CorpusModalProps) {
  const [open, setOpen] = useState(false);
  const [language, setLanguage] = useState<Language>('text');
  const [positionY, setPositionY] = useState<number>();
  const ref = useRef<HTMLDivElement>(null);
  const corpusRef = useRef<CorpusTabsRef>(null);
  const [selectedCorpora, setSelectedCorpora] = useState<Corpus[]>([]);

  const onClose = () => {
    setOpen(false);
    props.onVisibleChange?.(false);
  };

  const sliceText: (text: string) => string = props.maxLength
    ? (text) => text.slice(0, props.maxLength)
    : _.identity;
  const onChange = (corpora: Corpus[]) => {
    setSelectedCorpora(corpora);
    const formatTextByLanguage = getFormatTextByLanguage(language);
    props.onChange?.(sliceText(corpora.map(formatTextByLanguage).join(',')));
  };

  const onClick = () => {
    if (ref.current) {
      flushSync(() => {
        const { y } = ref.current!.getBoundingClientRect();

        setPositionY(
          Math.min(
            window.innerHeight * 0.3,
            window.innerHeight - 438,
            Math.max(y - 128, 0)
          )
        );
      });
    }
    setOpen(true);
    props.onVisibleChange?.(true);

    setSelectedCorpora(formatCorpusByText(props.prompt ?? '', selectedCorpora));

    setTimeout(() => {
      corpusRef.current?.setSelectedCorpora(
        props.prompt ? formatCorpusByText(props.prompt, selectedCorpora) : []
      );
    });
  };

  const onLanguageChange = (language: Language) => {
    setLanguage(language);
    const formatTextByLanguage = getFormatTextByLanguage(language);
    props.onChange?.(
      sliceText(selectedCorpora.map(formatTextByLanguage).join(','))
    );
    props.onLanguageChange?.(language);
  };

  return (
    <>
      <Button
        ref={ref}
        size="small"
        type="text"
        icon={<BookWithMarkBold />}
        className={classNames(styles.button, props.className)}
        onClick={onClick}
      >
        {props.triggerBtnLabel || '词库'}
      </Button>

      <CorpusTabsProvider corpusFilter={props.corpusFilter}>
        <Modal
          style={{
            position: 'absolute',
            top: positionY,
            left: 336
          }}
          open={open}
          footer={null}
          mask={false}
          closeIcon={<CrossBold />}
          className={styles.modal}
          title={<Title value={language} onChange={onLanguageChange} />}
          onCancel={onClose}
        >
          <CorpusTabs
            ref={corpusRef}
            searchVisible={false}
            majorClassName={styles.majorTabs}
            disabled={
              (props.prompt?.length ?? 0) >= (props.maxLength ?? Infinity)
            }
            className={styles.corpusTabs}
            onChange={onChange}
          />
        </Modal>
      </CorpusTabsProvider>
    </>
  );
}

CorpusModal.Expand = CorpusModalExpanded;

interface SwitcherProps {
  value: Language;
  onChange: (language: Language) => void;
}

function Title(switcherProps: SwitcherProps) {
  return (
    <div className={styles.title}>
      <h3>创意词库</h3>
      <div className={styles.titleExtra}>
        <Switcher {...switcherProps} />
        <SearchInput />
      </div>
    </div>
  );
}

function Switcher({ value, onChange }: SwitcherProps) {
  return (
    <div className={styles.switcher}>
      <span
        className={value === 'text' ? styles.active : ''}
        onClick={onChange.bind(null, 'text')}
      >
        中文
      </span>
      /
      <span
        className={value === 'translation' ? styles.active : ''}
        onClick={onChange.bind(null, 'translation')}
      >
        EN
      </span>
    </div>
  );
}
