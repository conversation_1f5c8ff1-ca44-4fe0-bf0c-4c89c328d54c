import { TextareaNoSpace } from '@/components/CreateNoSpace';
import classNames from 'classnames';
import styles from './index.module.less';
import { Divider, Form, Input } from 'antd';
import { ComponentPropsWithoutRef, ComponentRef, forwardRef } from 'react';
import { isBoolean } from 'lodash';
import { TrashCanBold } from '@meitu/candy-icons';
import { runes } from 'runes2';

const { TextArea: AntdTextArea } = Input;

export type TextareaRef = ComponentRef<typeof AntdTextArea>;

export interface TextareaProps
  extends ComponentPropsWithoutRef<typeof AntdTextArea> {
  extra?: React.ReactNode;
  headerExtra?: React.ReactNode;
  onClear?: () => void;

  wrapperClassName?: string;
}

export const TextArea = forwardRef<TextareaRef, TextareaProps>(
  (
    {
      className,
      extra,
      allowClear,
      headerExtra,
      onClear,
      wrapperClassName,
      maxLength,
      ...restProps
    },
    ref
  ) => {
    const hasRemoveIcon = !restProps.disabled && !!allowClear;

    const { status } = Form.Item.useStatus();

    const getAllowClear = () => ({
      clearIcon: (
        <div className={styles.remove}>
          {hasRemoveIcon && restProps.showCount && (
            <Divider className={styles.divider} type="vertical" />
          )}
          {hasRemoveIcon && (
            <div style={{ display: 'flex' }} onClick={onClear}>
              {isBoolean(allowClear) ? <TrashCanBold /> : allowClear?.clearIcon}
            </div>
          )}
        </div>
      )
    });

    return (
      <div
        className={classNames(
          styles.textareaWrapper,
          {
            [styles.paddingBottom]:
              !!extra || !!restProps.showCount || hasRemoveIcon,
            [styles.error]: status === 'error'
          },
          wrapperClassName
        )}
      >
        {headerExtra}

        <TextareaNoSpace
          ref={ref}
          className={classNames(styles.textarea, className, {
            [styles.layoutClear]: hasRemoveIcon
          })}
          {...restProps}
          allowClear={getAllowClear()}
          count={{
            max: maxLength,
            strategy: (txt: string) => runes(txt).length,
            exceedFormatter: (txt: string, { max }: { max: number }) => {
              return runes(txt).slice(0, max).join('');
            }
          }}
        />
        <div className={styles.extra}>{extra}</div>
      </div>
    );
  }
);
