@import '~@/styles/variables.less';

.textarea-wrapper {
  position: relative;
  border-radius: 8px;
  border: 1px solid #d0d2d6;
  transition: all 0.2s ease-in-out;
  padding: 8px !important;

  &:hover {
    border-color: #3549ff;
  }

  &.error {
    border-color: @color-error;
  }

  &:focus,
  &:focus-within {
    border-color: #3549ff;
  }

  &.padding-bottom {
    padding-bottom: 0px !important;

    :global(.ant-input-affix-wrapper) {
      padding-bottom: 32px !important;
    }
  }
}

.textarea {
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &:global(.ant-input-affix-wrapper) {
    border: none !important;
    border-radius: 0px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    overflow: hidden;

    &:focus-within {
      box-shadow: none !important;
    }

    textarea {
      border-radius: 0;
      padding: 0;
      padding-right: 0 !important;
      resize: none;
      font-weight: 400;
      font-size: @text-14;
      line-height: 20px;
      transition: all 0.3s ease-in-out;
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }

      // &::-webkit-scrollbar {
      //   width: 4px;
      //   height: 4px;
      // }

      // &::-webkit-scrollbar-track {
      //   background-color: transparent;
      // }
    }

    :global .@{ant-prefix}-input-suffix {
      .@{ant-prefix}-input-clear-icon {
        position: relative;
        bottom: 0;
        visibility: visible !important;
      }
    }

    :global(.ant-input-data-count) {
      bottom: 8px;
      right: 8px;
      font-weight: 400;
      font-size: @text-12;
      line-height: 14px;
      color: @content-input-lable;
    }
  }

  &:global(.@{ant-prefix}-input-affix-wrapper-disabled) {
    border: none;

    &:hover {
      border: none;
    }
  }

  :global(.@{ant-prefix}-input-disabled) {
    background-color: @background-input-not-editable !important;
    border: none;
    color: @content-input-not-editable !important;

    &:hover {
      border: none;
    }
  }

  &.layoutClear {
    :global(.ant-input-data-count) {
      right: 32px;
    }
  }
}

.remove {
  cursor: pointer;
  color: @content-input;
  bottom: 8px;
  right: 0px;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  min-height: 16px;

  &:hover {
    color: @content-input;
  }

  .divider {
    background: @stroke-input;
    top: 0;
    margin-right: @size-xxs;
  }

  svg {
    color: #abadb2;
  }
}

.extra {
  position: absolute;
  bottom: 8px;
  left: 8px;
  z-index: 20;
}
