import { TextArea as InternalTextarea } from './TextArea';
import { SmartTextarea } from './SmartTextArea';

type InternalTextareaType = typeof InternalTextarea;

interface TextareaProps extends InternalTextareaType {
  SmartTextarea: typeof SmartTextarea;
}

const TextArea = InternalTextarea as unknown as TextareaProps;

TextArea.SmartTextarea = SmartTextarea;

export type { TextareaRef, TextareaProps } from './TextArea';
// export type * from './SmartTextArea';
export { TextArea };
