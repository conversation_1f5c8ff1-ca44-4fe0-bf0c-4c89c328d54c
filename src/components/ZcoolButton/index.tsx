import styles from './index.module.less';
import { useState } from 'react';
import classNames from 'classnames';
import { getExternalLink } from '@/api';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { useParams } from 'react-router-dom';
import { useCommonConfigState } from '@/hooks/useCommonConfig';
import { PermissionEnvWrapper } from '../PermissionEnvWrapper';
import { AppOrigin } from '@/constants';
import { simulateLinkClick } from '@/utils/simulateLinkClick';
import { trackZcoolActivity } from '@/utils/track';

export interface ZcoolButtonProps {}

export const ZcoolButton = (props: ZcoolButtonProps) => {
  const [loading, setLoading] = useState(false);
  const { id = '' } = useParams();
  const [commonConfig] = useCommonConfigState();

  if (!commonConfig?.showJumpZcoolBtn || !id) return null;

  return (
    <PermissionEnvWrapper includesOrigin={[AppOrigin.Whee]}>
      <div
        style={{ backgroundImage: `url(${commonConfig.zcoolBtnPicUrl})` }}
        className={classNames(styles.zCool, {
          [styles.disabled]: loading
        })}
        onClick={async () => {
          try {
            if (loading) return;
            setLoading(true);
            trackZcoolActivity({
              activity_source: commonConfig?.activitySource
            });
            const { jumpUrl } = await getExternalLink({ key: 'zcool', id });

            !!jumpUrl && simulateLinkClick(jumpUrl);
          } catch (error) {
            defaultErrorHandler(error);
          } finally {
            setLoading(false);
          }
        }}
      ></div>
    </PermissionEnvWrapper>
  );
};
