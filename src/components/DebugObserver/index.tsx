import { isMobileV2 } from '@/utils/isMobile';
import { FloatButton } from 'antd';
import { useEffect } from 'react';
import { useRecoilCallback } from 'recoil';
import VConsole from 'vconsole';

function DebugButton() {
  const onClick = useRecoilCallback(
    ({ snapshot }) =>
      async () => {
        console.debug('Atom values:');
        for (const node of snapshot.getNodes_UNSTABLE()) {
          const value = await snapshot.getPromise(node);
          console.debug(node.key, value);
        }
      },
    []
  );

  useEffect(() => {
    if (!isMobileV2()) return;

    const vConsole = new VConsole({ theme: 'dark' });

    return () => {
      vConsole.destroy();
    };
  }, []);

  return (
    <FloatButton
      tooltip="打印recoil数据"
      // 和回到顶部按钮错开
      style={{ bottom: 98 }}
      onClick={onClick}
    />
  );
}
export { DebugButton };
