import {
  ErrorBoundary as SentryErrorBoundary,
  ErrorBoundaryProps
} from '@sentry/react';
import { Button, Space, Typography } from 'antd';
import { ReactNode } from 'react';

export const ErrorBoundary = ({ children }: { children?: ReactNode }) => {
  return (
    <SentryErrorBoundary showDialog fallback={Fallback}>
      {children}
    </SentryErrorBoundary>
  );
};

const Fallback: ErrorBoundaryProps['fallback'] = ({ resetError }) => {
  return (
    <Space
      align="center"
      direction="vertical"
      style={{ padding: '80px', width: '100%', display: 'flex' }}
    >
      <Typography.Title style={{ textAlign: 'center' }}>
        You have encountered an error
      </Typography.Title>
      <Button
        type="primary"
        onClick={() => {
          resetError();
        }}
      >
        Click here to reset!
      </Button>
    </Space>
  );
};
