import styles from './index.module.less';
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react';
import { Button, ConfigProvider, Input, InputProps, InputRef } from 'antd';
import { CrossCircleFillBold, SearchBold } from '@meitu/candy-icons';
import classNames from 'classnames';
import { EditorUsableAuthorized } from '@/components';
import { getLocalStorageItem, setLocalStorageItem } from '@meitu/util';
import produce from 'immer';
import { useLocation, useNavigate } from 'react-router-dom';
import { AppModule, generateRouteTo, getAppModulePath } from '@/services';

const SEARCH_HISTORY_LIST = 'art:search-history-list';

export interface SearchInputRefType {
  isFocused: boolean;
}
interface SearchInputType {
  className?: string;
  searchBtnNeedAuthor?: boolean;
  getResultList?: (keyword: string) => void;
  autoFocus?: boolean;

  onPressEnter?: (keyword: string) => void;
  onSearch?: (keyword: string) => void;
}

export const SearchInput = forwardRef<SearchInputRefType, SearchInputType>(
  (props, ref) => {
    const {
      className,
      searchBtnNeedAuthor,
      getResultList,
      autoFocus = false
    } = props;
    const navigate = useNavigate();
    const { pathname } = useLocation();
    const inputRef = useRef<InputRef>(null);

    const [prompt, setPrompt] = useState('');
    const [isFocused, setIsFocused] = useState(false);
    const [historyList, setHistoryList] = useState<Array<string>>([]);
    const [showBtn, setShowBtn] = useState(false);

    useImperativeHandle(
      ref,
      () => ({
        isFocused
      }),
      [isFocused]
    );

    useEffect(() => {
      setHistoryList(getLocalStorageItem(SEARCH_HISTORY_LIST) || []);

      // 灵感顶部的输入框，输入内容了才展示搜索按钮
      if (searchBtnNeedAuthor) {
        setShowBtn(prompt !== '');
      } else {
        setShowBtn(true);
      }
    }, [isFocused, prompt, searchBtnNeedAuthor]);

    // 结果页回填关键词
    useEffect(() => {
      if (pathname === getAppModulePath(AppModule.SearchResult)) {
        setPrompt(getLocalStorageItem(SEARCH_HISTORY_LIST)?.[0]);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pathname]);

    // 输入搜索词
    const onInputChange: InputProps['onChange'] = (event) => {
      setPrompt(event.target.value);
    };

    // 开始搜索
    const handleSearchResult = (prompt: string) => {
      const trimmedPrompt = prompt.trim();
      if (!trimmedPrompt) {
        setPrompt(trimmedPrompt);
        navigate(generateRouteTo(AppModule.Art));
        return;
      }
      // 保留十个，重复的放前边
      const temp = produce(historyList, (draft) => {
        const index = draft.indexOf(trimmedPrompt);
        if (index !== -1) {
          draft.splice(index, 1);
        }
        draft.unshift(trimmedPrompt);
      });
      const firstTen = temp.slice(0, 10);
      setHistoryList(firstTen);
      setLocalStorageItem(SEARCH_HISTORY_LIST, firstTen);

      // 跳转结果页
      if (pathname === getAppModulePath(AppModule.SearchResult)) {
        getResultList?.(trimmedPrompt);
      } else {
        navigate(generateRouteTo(AppModule.SearchResult));
      }
    };

    // 清空
    const clearList = () => {
      setHistoryList([]);
      setLocalStorageItem(SEARCH_HISTORY_LIST, []);
    };

    return (
      <div className={styles.inputBox}>
        <Input
          ref={inputRef}
          prefix={<SearchBold />}
          value={prompt}
          maxLength={40}
          allowClear={{
            clearIcon: <CrossCircleFillBold />
          }}
          placeholder="可搜索作品或模型"
          className={classNames(styles.topInput, className)}
          onChange={onInputChange}
          onPressEnter={() => {
            props.onSearch?.(prompt);
            handleSearchResult(prompt);
          }}
          autoFocus={autoFocus}
          onFocus={() => {
            setIsFocused(true);
          }}
          onBlur={() => {
            setTimeout(() => {
              setIsFocused(false);
            }, 500);
          }}
        />
        {showBtn && (
          <ConfigProvider autoInsertSpaceInButton={false}>
            <EditorUsableAuthorized>
              <Button
                className={styles.inputBtn}
                onClick={() => {
                  props.onSearch?.(prompt);
                  handleSearchResult(prompt);
                }}
              >
                搜索
              </Button>
            </EditorUsableAuthorized>
          </ConfigProvider>
        )}
        {isFocused && historyList.length > 0 && prompt === '' && (
          <div className={styles.historyBox}>
            <div className={styles.titleBox}>
              <span className={styles.title}>历史记录</span>
              <span className={styles.clear} onClick={clearList}>
                清空
              </span>
            </div>
            <div className={styles.listBox}>
              {historyList.map((item) => {
                return (
                  <span
                    className={styles.listItem}
                    onMouseDown={() => {}}
                    onClick={() => {
                      setPrompt(item);

                      handleSearchResult(item);
                    }}
                  >
                    {item}
                  </span>
                );
              })}
            </div>
          </div>
        )}
      </div>
    );
  }
);

export { SearchInput as Component };
