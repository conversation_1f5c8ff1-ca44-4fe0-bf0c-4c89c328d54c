@import '~@/styles/variables.less';

.input-box {
  position: relative;
  width: 440px;

  .top-input {
    width: 440px;
    height: 48px;
    border-radius: 12px;
    box-shadow: 0px 12px 48px 0px rgba(97, 134, 163, 0.12);
    border: none;
    padding-right: 72px;

    :global .@{ant-prefix}-input::placeholder {
      font-size: 16px;
      color: @content-input-lable;
    }

    :global .@{ant-prefix}-input-clear-icon {
      height: 18px;

      svg {
        width: 18px;
        height: 18px;
        color: @content-input-lable;
      }
    }
  }

  .input-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 56px;
    height: 32px;
    border-radius: 10px;
    background: @background-btn-ai;
    border: none;
    color: @content-btn-primary;
    font-size: 12px;
    font-weight: 500;
    z-index: 6;

    &:hover {
      color: @content-btn-primary !important;
    }
  }

  .history-box {
    position: absolute;
    top: 60px;
    left: 0;
    width: 440px;
    padding: 20px;
    background: @background-web-menu;
    border-radius: 12px;
    box-shadow: 0px 6px 24px 0px rgba(0, 0, 0, 0.06),
      0px 0px 2px 0px rgba(0, 0, 0, 0.08);
    z-index: 99;

    .title-box {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        color: @content-web-menu-tertiary;
      }

      .clear {
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        color: @content-system-lock;
        cursor: pointer;
      }
    }

    .list-box {
      margin-top: 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 8px 20px;

      .list-item {
        max-width: 156px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
      }
    }
  }

  .search {
    background: #f6f7fa;
    box-shadow: none;

    :global .@{ant-prefix}-input {
      background: #f6f7fa;
    }
  }
}
