import { Rate, Input } from 'antd';
import { RateProps } from '@/types/antd';
import styles from './styles.module.less';
import { useEffect, useState, useRef } from 'react';
import classNames from 'classnames';
import RateDefaultIcon from '@/icons/rate-default.png';
import RateActiveIcon from '@/icons/rate-active.png';
import { Button } from '../Button';
import { debounce } from 'lodash';
import { fetcRateList, submitRate } from '@/api/aiMaterialZcool';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { RateData, RateSubmitParams, RateType } from '@/api/types';
import { produce } from 'immer';
import {
  CustomerServiceBoldOutlined,
  FeedbackBoldOutlined
} from '@meitu/candy-icons';
import { useApp } from '@/App';
import { useLooper } from '@/hooks';
import { trackEvent } from '@/services';
import { getSource } from '@/utils';

interface RateActionProps {
  activeGraphSrc?: string;
  taskId: string;
  onSuccess?(): void;
  rateType: RateType;
  showRate: boolean;
}

const RateAction = (props: RateActionProps) => {
  const { activeGraphSrc, taskId, onSuccess, rateType, showRate } = props;
  const timer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const { openFeedbackModal, openOnlineConsultationModal } = useApp();
  const [showModal, setShowModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [rateVal, setRateVal] = useState(0);
  const [rateData, setRateData] = useState<RateData>();
  const [textVal, setTextVal] = useState('');
  const [hasRateClicked, setHasRateClicked] = useState<boolean>(false);

  useEffect(() => {
    // 兼容ipad mouseleave事件不触发问题
    document.addEventListener('click', handleDomClick);
    return destroy;
  });

  useEffect(() => {
    init();
  }, []);

  useLooper({
    callback: async () => {
      setShowSuccessModal(false);
    },
    duration: 3000,
    trigger: () => showSuccessModal
  });

  const init = async () => {
    try {
      let data = await fetcRateList();
      setRateData(data);
    } catch (e) {
      defaultErrorHandler(e);
    }
  };

  const handleDomClick = (e: Event) => {
    if (
      !document.getElementById('rate-container')?.contains(e?.target as Node)
    ) {
      toggleModal(false);
      setHasRateClicked(false);
    }
  };

  const destroy = () => {
    document.removeEventListener('click', handleDomClick);
    timer.current && clearTimeout(timer.current);
  };

  const toggleModal = debounce((flag: boolean) => {
    !flag && setRateVal(0);
    setShowModal(flag);
  }, 200);

  const getCustomIcons = (arg: RateProps) => {
    const { value = 0, index = 0 } = arg;
    return value > index ? (
      <img alt="" className={styles.rateIcon} src={RateActiveIcon} />
    ) : (
      <img alt="" className={styles.rateIcon} src={RateDefaultIcon} />
    );
  };

  const selectTags = (index: number) => {
    const nextRateData = produce(rateData, (draft) => {
      Object.assign(draft?.[rateVal]?.tags[index] ?? {}, {
        active: !draft?.[rateVal]?.tags[index].active
      });
    });
    setRateData(nextRateData);
  };

  const submit = debounce(async () => {
    trackEvent('whee_satisfaction_submit_click', {
      source: getSource(),
      messageId: taskId
    });
    let cateIds: Number[] = [];
    rateData?.[rateVal]?.tags?.map((tag) => {
      tag.active && cateIds?.push(tag.id);
      return false;
    });
    let params: RateSubmitParams = {
      cateId: cateIds.join(','),
      content: textVal,
      pic: activeGraphSrc ?? '',
      msgId: taskId,
      type: rateType,
      score: String(rateVal)
    };
    try {
      let { result } = await submitRate(params);
      if (result) {
        toggleModal(false);
        setShowSuccessModal(true);
        onSuccess && onSuccess();
      }
    } catch (e) {
      defaultErrorHandler(e);
    }
  }, 200);

  if (!showRate && !showSuccessModal) return null;

  return (
    <div className={styles.rateContainer} id="rate-container">
      {showRate && (
        <>
          <span className={styles.rateLable}>效果评分</span>
          <div
            className={styles.rateBox}
            onMouseOver={() => !showSuccessModal && toggleModal(true)}
          >
            <Rate
              value={rateVal}
              allowClear={false}
              onHoverChange={(value) => {
                value && setRateVal(value);
              }}
              onChange={(value) => {
                setHasRateClicked(true);
                value && setRateVal(value);
              }}
              character={(arg: RateProps) => getCustomIcons(arg)}
            />
            <div
              style={{
                display: showModal ? 'block' : 'none'
              }}
              className={styles.ratePopupBox}
              onMouseLeave={() => {
                const hasSelectedTags = rateData?.[rateVal]?.tags.some(
                  (item) => item.active
                );
                // 没有主动操作过，移出才消失
                if (!hasRateClicked && textVal === '' && !hasSelectedTags) {
                  toggleModal(false);
                }
              }}
            >
              <div className={styles.popTitleBox}>
                <span
                  className={classNames(styles.popTitleText, {
                    [styles.popTitleIconVeryDiscontent]: rateVal === 1,
                    [styles.popTitleIconDiscontent]: rateVal === 2,
                    [styles.popTitleIconCommon]: rateVal === 3,
                    [styles.popTitleIconGood]: rateVal === 4,
                    [styles.popTitleIconVeryGood]: rateVal === 5
                  })}
                >
                  {rateData?.[rateVal]?.title}
                </span>
                <Button onClick={submit}>提交</Button>
              </div>
              <div className={styles.popTagList}>
                {rateData?.[rateVal]?.tags.map((tag, index) => {
                  return (
                    <div
                      key={tag.id}
                      className={classNames(styles.popTagItem, {
                        [styles.active]: tag.active
                      })}
                      onClick={() => selectTags(index)}
                    >
                      {tag.name}
                    </div>
                  );
                })}
              </div>
              <Input
                value={textVal}
                placeholder="其他"
                onChange={(e) => setTextVal(e.target.value)}
                maxLength={200}
              />
            </div>
          </div>
        </>
      )}
      {showSuccessModal ? (
        <div className={styles.rateSuccessModal}>
          <span className={styles.rateSuccessText}>感谢反馈！</span>
          <div className={styles.rateSuccessOperate}>
            <Button
              type="default"
              icon={<CustomerServiceBoldOutlined />}
              onClick={() => {
                trackEvent('whee_feedback_more_click', {
                  feedback_more: 'customer_service'
                });
                openOnlineConsultationModal();
              }}
            >
              在线客服
            </Button>
            <Button
              type="default"
              icon={<FeedbackBoldOutlined />}
              onClick={() => {
                trackEvent('whee_feedback_more_click', {
                  feedback_more: 'feedback'
                });
                openFeedbackModal();
              }}
            >
              意见反馈
            </Button>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default RateAction;
