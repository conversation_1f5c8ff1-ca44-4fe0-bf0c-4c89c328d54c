import { useDarkModeState } from '@/hooks';
import { matchBrowserTheme } from '@/utils/matchBrowserTheme';
import { useLayoutEffect, useRef } from 'react';

export interface BrowserThemeProps {}

export const BrowserTheme = (props: BrowserThemeProps) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setIsDarkMode] = useDarkModeState();

  const darkThemeMatch = useRef<MediaQueryList>();

  useLayoutEffect(() => {
    if (!darkThemeMatch.current) {
      darkThemeMatch.current = matchBrowserTheme('dark');
    }

    const callBack = () => {
      setIsDarkMode(!!matchBrowserTheme('dark')?.matches);
    };

    darkThemeMatch.current?.addEventListener('change', callBack);

    return () => {
      darkThemeMatch.current?.removeEventListener('change', callBack);
    };
  }, [setIsDarkMode]);

  return null;
};
