import React from 'react';
import styles from './index.module.less';
import classNames from 'classnames';

import { Row, Col, Tooltip, Space } from 'antd';
import type { InputNumberProps as AntdInputNumberProps } from 'antd';
import { InputNumber, InputNumberProps } from '@/components/InputNumber';
import _ from 'lodash';
import { QuestionMarkCircleBold } from '@meitu/candy-icons';

import { isNumber } from 'lodash';
import { Slider } from '@/components/Slider';

export interface RangeSliderInputProps
  extends Pick<
    InputNumberProps,
    'controls' | 'suffix' | 'disabled' | 'formatter'
  > {
  value?: [number, number];

  /** 刻度标记个数 */
  markNum?: number;

  /** 最小值 */
  min?: number;

  /** 最大值 */
  max?: number;

  /** 步长 */
  step?: number;

  /** 区间的最小长度 */
  minInterval?: number;

  className?: string;

  onChange?: (value: number[]) => void;

  /** 左上角标题 仅 `direction = 'vertical'` 时生效*/
  title?: React.ReactNode;

  /** 提示文本 */
  tooltip?: React.ReactNode;
}

/** 标题和提示 */
export function Title({
  title,
  tooltip
}: Pick<RangeSliderInputProps, 'title' | 'tooltip'>) {
  return (
    <section>
      <span className={styles.title}>{title}</span>
      {!!tooltip ? (
        <Tooltip title={tooltip}>
          <QuestionMarkCircleBold className={styles.tooltip} />
        </Tooltip>
      ) : null}
    </section>
  );
}

export function RangeSliderInput(props: RangeSliderInputProps) {
  const {
    value: values = [0, 0],
    minInterval,
    className,
    controls,
    suffix,
    title,
    tooltip,
    min = 0,
    max = 100,
    step = 1,
    disabled,
    onChange
  } = props;

  const minLength = minInterval ?? step ?? 0.01;

  function handleLeftChange(left: AntdInputNumberProps['value']) {
    if (!isNumber(left)) {
      return;
    }

    const right = values[1];
    if (right - left < minLength) {
      left = right - minLength;
    }
    onChange?.([left, right]);
  }

  function handelRightChange(right: AntdInputNumberProps['value']) {
    if (!isNumber(right)) {
      return;
    }

    const left = values[0];
    if (right - left < minLength) {
      right = left + minLength;
    }
    onChange?.([left, right]);
  }

  const onSliderChange = (values: number[]) => {
    const [left, right] = values;
    if (right - left < minLength) {
      return;
    }

    onChange?.(values);
  };

  const left = values[0] ?? 0;
  const right = values[1] ?? left + minLength;
  /** 输入框 */
  const inputNodes = (
    <Space>
      <InputNumber
        precision={step.toString().split('.')[1]?.length ?? 0}
        value={left}
        disabled={disabled}
        onChange={handleLeftChange}
        {..._.omitBy({ controls, suffix, max, min, step }, _.isUndefined)}
      />

      <InputNumber
        precision={step.toString().split('.')[1]?.length ?? 0}
        value={right}
        disabled={disabled}
        onChange={handelRightChange}
        {..._.omitBy({ controls, suffix, max, min, step }, _.isUndefined)}
      />
    </Space>
  );

  /** 滑杆 */
  const sliderProps = {
    min,
    max,
    step
  };

  return (
    <section className={classNames(styles.sliderInput, className)}>
      <Row>
        <Col span={24} className={styles.header}>
          {' '}
          <Title title={title} tooltip={tooltip} />
          {inputNodes}
        </Col>
        <Col span={24}>
          <Slider
            {...sliderProps}
            range
            value={[left, right]}
            disabled={disabled}
            onChange={onSliderChange}
            tooltip={{
              open: false
            }}
          />
        </Col>
      </Row>
    </section>
  );
}
