import { useLongPress } from 'react-use';
import { EditorRefType, getStore } from '../Editor';
import { Latest_IMAGE_GROUP_ID } from './constants';
import { Actions } from '../ImagesContainer/Actions';

export interface CompareButtonProps {
  editorRef: React.RefObject<EditorRefType | null>;
}

export const CompareButton = ({ editorRef }: CompareButtonProps) => {
  const showOriginImage = (show: boolean) => {
    const { updateShape } = getStore(editorRef) ?? {};
    updateShape?.({
      id: Latest_IMAGE_GROUP_ID,
      visible: !show
    });
  };

  const {
    onMouseUp: _onMouseUp,
    onTouchEnd: _onTouchEnd,
    ...longPressEvent
  } = useLongPress(
    () => {
      showOriginImage(true);
    },
    {
      isPreventDefault: true,
      delay: 150
    }
  );

  const onMouseUp = () => {
    showOriginImage(false);
    _onMouseUp();
  };

  const onTouchEnd = () => {
    showOriginImage(false);
    _onTouchEnd();
  };

  const fixturesEvent = { ...longPressEvent, onMouseUp, onTouchEnd };

  return <Actions.CompareButton {...fixturesEvent} />;
};
