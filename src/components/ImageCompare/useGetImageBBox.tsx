import { ImageCompareProps } from '.';
import { getImageDimensions } from '@/utils/blob';
import { useEffect } from 'react';
import { BBoxType } from '../Editor/utils/fitBounds';
import { EditorRefType, adaptiveImageByContainer, getStore } from '../Editor';

export interface useGetImageBBoxProps
  extends Pick<
    ImageCompareProps,
    'latestImage' | 'originImage' | 'onFetchLoading'
  > {
  onSuccess?(dimensions: BBoxType): void;
  onError?(err: any): void;
  editorRef: React.RefObject<EditorRefType>;
}

export const useGetImageBBox = ({
  latestImage,
  originImage,
  onSuccess,
  onFetchLoading,
  onError,
  editorRef
}: useGetImageBBoxProps) => {
  const initializeStage = async () => {
    if (!latestImage || !originImage) return;

    onFetchLoading?.(true);

    try {
      const { width, height } = await getImageDimensions(latestImage);
      await getImageDimensions(originImage);
      const bbox = { x: 0, y: 0, width, height };

      const { stageRef } = getStore(editorRef) ?? {};

      const containerWidth = stageRef?.current?.width();
      const containerHeight = stageRef?.current?.height();
      if (!containerWidth || !containerHeight) return;
      // 适配屏幕视口容器

      const fixturesDimensions = adaptiveImageByContainer(
        { width: containerWidth, height: containerHeight },
        bbox.width / bbox.height,
        containerWidth / containerHeight
      );

      onSuccess?.({ ...bbox, ...fixturesDimensions });
    } catch (error) {
      //TODO  handle fetch error, Tips or modal ?
      onError?.(error);
    } finally {
      onFetchLoading?.(false);
    }
  };

  useEffect(() => {
    initializeStage();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [latestImage, originImage]);

  return {};
};
