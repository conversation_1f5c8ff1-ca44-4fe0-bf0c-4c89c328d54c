// 居中撑满屏幕 标识符
export const AUTO_FIT_BOUNDS_NODE_NAME = 'autoFitBoundsNodeName';

export const ORIGIN_IMAGE_GROUP_ID = 'originImageGroupId';
export const Latest_IMAGE_GROUP_ID = 'LatestImageGroupId';
export const ORIGIN_IMAGE_TEXT_ID = 'originImageTextId';
export const LATEST_IMAGE_TEXT_ID = 'LatestImageTextId';

export const DRAGGER_GROUP_ID = 'draggerGroupId';
export const DRAGGER_REC_ID = 'draggerRectId';
export const DRAGGER_LINE_ID = 'draggerLineId';

export const DRAGGER_LINE_WIDTH = 2;
export const DRAGGER_RECT_SIZE = 40;
export const TEXT_RECT_WIDTH = 60;
export const TEXT_GROUP_PADDING = 12;

const triangleString = `<svg width="20" height="20" viewBox="-8 -8 40 40" fill="black" xmlns="http://www.w3.org/2000/svg">
<path
    d="M21.9948 13.2376L17.567 16.5726C16.7425 17.1936 16.3303 17.5041 15.9845 17.5C15.6836 17.4964 15.4006 17.3599 15.2142 17.1286C15 16.8627 15 16.3535 15 15.335V8.66504C15 7.64652 15 7.13727 15.2142 6.87142C15.4005 6.64014 15.6836 6.50365 15.9845 6.50004C16.3303 6.49589 16.7425 6.80639 17.567 7.42739L21.9948 10.7624C22.5539 11.1835 22.8335 11.3941 22.934 11.6507C23.022 11.8756 23.022 12.1244 22.934 12.3493C22.8335 12.6059 22.5539 12.8165 21.9948 13.2376Z"
    fill="black" />
<path
    d="M1.06603 11.6507C1.1665 11.3941 1.44607 11.1835 2.0052 10.7624L6.43298 7.42739C7.25748 6.80639 7.66972 6.49589 8.01552 6.50004C8.31637 6.50365 8.59945 6.64014 8.78581 6.87142C9 7.13726 9 7.64652 9 8.66504L9 15.335C9 16.3535 9 16.8627 8.78581 17.1286C8.59945 17.3599 8.31637 17.4964 8.01552 17.5C7.66972 17.5041 7.25748 17.1936 6.43298 16.5726L2.0052 13.2376C1.44607 12.8165 1.1665 12.6059 1.06603 12.3493C0.977991 12.1244 0.977991 11.8756 1.06603 11.6507Z"
    fill="black" />
</svg>`;

function svgToURL(s: any) {
  const uri = window.btoa(decodeURIComponent(encodeURIComponent(s)));
  return 'data:image/svg+xml;base64,' + uri;
}

export const triangleUrl = svgToURL(triangleString);
