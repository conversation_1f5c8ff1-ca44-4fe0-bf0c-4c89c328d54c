import Konva from 'konva';
import {
  BBoxType,
  EditorRefType,
  getStore,
  viewportToCanvasBox
} from '../Editor';
import {
  AUTO_FIT_BOUNDS_NODE_NAME,
  DRAGGER_GROUP_ID,
  DRAGGER_LINE_ID,
  DRAGGER_LINE_WIDTH,
  DRAGGER_RECT_SIZE,
  DRAGGER_REC_ID,
  LATEST_IMAGE_TEXT_ID,
  Latest_IMAGE_GROUP_ID,
  ORIGIN_IMAGE_GROUP_ID,
  ORIGIN_IMAGE_TEXT_ID,
  TEXT_GROUP_PADDING,
  TEXT_RECT_WIDTH,
  triangleUrl
} from './constants';

export type InitShapeParamsType = {
  // latestImage尺寸
  bbox: BBoxType;
  originImage: string;
  latestImage: string;
  // 画布尺寸
  scale?: number;
};

export interface useShapesOperatorProps {
  editorRef: React.RefObject<EditorRefType | null>;
}

Konva.pixelRatio = 2;

export const useShapesOperator = ({ editorRef }: useShapesOperatorProps) => {
  const initShapes = (props: InitShapeParamsType) => {
    const { bbox, originImage, latestImage, scale = 1 } = props;

    const { addShapes, changeCursor, stageRef, updateShape, removeAllShapes } =
      getStore(editorRef) ?? {};

    // 分割线初始位置居中
    const INIT_LINE_DRAGGER_X = bbox.width / 2;

    removeAllShapes?.();
    stageRef?.current?.draggable(true);
    changeCursor?.('move');

    addShapes?.([
      /** 旧图  */
      {
        id: ORIGIN_IMAGE_GROUP_ID,
        type: 'group',
        ...bbox,
        draggable: false,
        // 点击触发裁剪区域， 分割线和分割矩形移动
        onClick: (e) => {
          const pos = e.target.getRelativePointerPosition();

          updateShape?.([
            {
              id: Latest_IMAGE_GROUP_ID,
              clipX: pos.x,
              clipWidth: bbox.width - pos.x
            },
            { id: DRAGGER_GROUP_ID, x: pos.x },
            { id: DRAGGER_REC_ID, y: pos.y }
          ]);
        },
        elements: [
          {
            type: 'image',
            src: originImage,
            ...bbox,
            draggable: false,
            isSelected: false
          },
          {
            type: 'group',
            x: TEXT_GROUP_PADDING,
            y: TEXT_GROUP_PADDING,
            listening: false,
            scaleX: 1 / scale,
            scaleY: 1 / scale,

            id: ORIGIN_IMAGE_TEXT_ID,

            elements: [
              {
                type: 'rect',
                width: TEXT_RECT_WIDTH,
                height: 24,
                cornerRadius: 4,
                x: 0,
                y: 0,
                fill: 'rgba(0, 0, 0, 0.50)'
              },
              {
                type: 'text',
                text: '处理前',
                width: TEXT_RECT_WIDTH,
                fontSize: 12,
                fill: '#fff',
                padding: 7,
                align: 'center',
                x: 0,
                y: 0
              }
            ]
          }
        ]
      },
      /** 新图  */
      {
        type: 'group',
        id: Latest_IMAGE_GROUP_ID,
        name: AUTO_FIT_BOUNDS_NODE_NAME, // 标识符, 当window resize时fit bounds
        ...bbox,
        draggable: false,
        // 点击触发裁剪区域， 分割线和分割矩形移动
        onClick: (e) => {
          const pos = e.target.getRelativePointerPosition();
          updateShape?.([
            {
              id: Latest_IMAGE_GROUP_ID,
              clipX: pos.x,
              clipWidth: bbox.width - pos.x
            },
            { id: DRAGGER_GROUP_ID, x: pos.x },
            { id: DRAGGER_REC_ID, y: pos.y }
          ]);
        },
        elements: [
          {
            type: 'image',
            src: latestImage,
            canCache: false,
            ...bbox,
            draggable: false,
            isSelected: false
          },
          {
            type: 'group',
            x: bbox.width - TEXT_RECT_WIDTH - TEXT_GROUP_PADDING,
            y: TEXT_GROUP_PADDING,
            listening: false,
            scaleX: 1 / scale,
            scaleY: 1 / scale,
            id: LATEST_IMAGE_TEXT_ID,

            elements: [
              {
                type: 'rect',
                width: TEXT_RECT_WIDTH,
                height: 24,
                cornerRadius: 4,
                x: 0,
                y: 0,
                fill: 'rgba(0, 0, 0, 0.50)'
              },
              {
                type: 'text',
                text: '处理后',
                width: TEXT_RECT_WIDTH,
                fontSize: 12,
                fill: '#fff',
                padding: 7,
                align: 'center',
                x: 0,
                y: 0
              }
            ]
          }
        ]
      },
      /** 分割线  */
      {
        visible: false,
        type: 'group',
        id: DRAGGER_GROUP_ID,
        draggable: false,
        x: INIT_LINE_DRAGGER_X,
        y: 0,
        elements: [
          {
            id: DRAGGER_LINE_ID,
            type: 'rect',
            x: 0,
            // x: -(DRAGGER_LINE_WIDTH / scale / 2),
            y: 0,
            fill: '#fff',
            width: DRAGGER_LINE_WIDTH,
            height: bbox.height,
            isSelected: false,
            draggable: false,
            //屏幕视口下的大小在画布缩放时保持一致
            scaleX: 1 / scale,
            scaleY: 1
          },
          {
            type: 'image',
            src: triangleUrl,
            id: DRAGGER_REC_ID,
            onDragEnd(evt) {
              evt.cancelBubble = true;
            },
            onUpdateShape(conf) {
              const moveX = stageRef?.current
                ?.findOne(`#${DRAGGER_GROUP_ID}`)
                .getAttr('tempX');
              if (isNaN(moveX)) return;
              updateShape?.([
                {
                  id: Latest_IMAGE_GROUP_ID,
                  clipX: moveX,
                  clipWidth: Math.max(1, bbox.width - moveX)
                },
                conf,
                { id: DRAGGER_GROUP_ID, x: moveX }
              ]);
            },
            // 拖拽边界限制在latestImageGroup里面
            dragBoundFunc(pos) {
              const stage = this.getStage();

              const latestImageGroupNode = stage?.findOne(
                `#${Latest_IMAGE_GROUP_ID}`
              ) as Konva.Group;

              const draggerGroup = stage?.findOne(
                `#${DRAGGER_GROUP_ID}`
              ) as Konva.Group;

              if (!latestImageGroupNode || !stage) return pos;
              //  屏幕视口bbox
              const viewportBox = latestImageGroupNode.getClientRect({});

              const minX =
                viewportBox.x - DRAGGER_LINE_WIDTH - DRAGGER_RECT_SIZE / 2;
              const maxX = minX + viewportBox.width + DRAGGER_LINE_WIDTH;
              const minY = viewportBox.y;
              const maxY = minY + viewportBox.height - DRAGGER_RECT_SIZE;
              const fixturesX = Math.max(minX, Math.min(pos.x, maxX));
              const fixturesY = Math.max(minY, Math.min(pos.y, maxY));

              // 画布视口bbox
              const canvasBox = viewportToCanvasBox(
                { y: fixturesY, x: fixturesX },
                stage
              );

              const fixturesDraggerGroupX =
                canvasBox.x + DRAGGER_RECT_SIZE / stage.scaleX() / 2;

              // 暂时同步裁剪区域
              latestImageGroupNode.clipX(fixturesDraggerGroupX);
              latestImageGroupNode.clipWidth(
                Math.max(1, bbox.width - fixturesDraggerGroupX)
              );

              // 移动的时候暂时同步x轴分割线，实际生效还是在onUpdateShape
              draggerGroup.x(fixturesDraggerGroupX);
              draggerGroup?.setAttrs({ tempX: fixturesDraggerGroupX });
              return {
                x: this.absolutePosition().x,
                y: fixturesY
              };
            },
            x: -(DRAGGER_RECT_SIZE / scale / 2),
            y: (bbox.height + DRAGGER_RECT_SIZE) / 2,
            width: DRAGGER_RECT_SIZE,
            height: DRAGGER_RECT_SIZE,
            isSelected: false,
            draggable: true,
            stroke: '#fff',
            strokeWidth: 2,
            fill: '#fff',
            // opacity: 0.6,
            cornerRadius: DRAGGER_RECT_SIZE / 2,

            //屏幕视口下的大小在画布缩放时保持一致
            scaleX: 1 / scale,
            scaleY: 1 / scale
          }
        ]
      }
    ]);
  };

  return {
    initShapes
  };
};
