import {
  GlobalCtxType,
  useGlobalStoreRefSubscription,
  fitBoundsByStage,
  EditorRefType,
  BBoxType
} from '@/components';
import { useEffect, useState } from 'react';
import { AUTO_FIT_BOUNDS_NODE_NAME } from './constants';

export const useResizeStage = (
  editorRef: React.RefObject<EditorRefType | null>,
  onResize?: (resizeInfo: ReturnType<typeof fitBoundsByStage>) => void
) => {
  const [canvasSize, setCanvasSize] =
    useState<GlobalCtxType['canvasSize']>(null);

  useGlobalStoreRefSubscription(editorRef, (v) => {
    setCanvasSize(v.canvasSize);
  });

  const resize = (props?: { targetBBox?: BBoxType; zoomPreset?: number }) => {
    const res = fitBoundsByStage({
      editorRef,
      targetBBoxOrName: props?.targetBBox ?? AUTO_FIT_BOUNDS_NODE_NAME,
      padding: { left: 0, right: 0, top: 0, bottom: 0 },
      // TODO 联动Editor组件传入最大最小zoom值
      minZoom: 1,
      maxZoom: 5,
      zoomPreset: props?.zoomPreset === 1 ? 'auto' : props?.zoomPreset
    });

    onResize?.(res);

    return res;
  };

  useEffect(() => {
    resize();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [canvasSize]);

  return { resize };
};
