import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { Editor, EditorRefType } from '../Editor';

import styles from './index.module.less';
import { useResizeStage } from './useResizeStage';

import { useShapesOperator } from './useShapesOperator';
import { useOnWheeEnd } from './useOnWheelEnd';
import { useGetImageBBox } from './useGetImageBBox';
import { useModeChange } from './useModeChange';
import { Actions } from '../ImagesContainer/Actions';
import { CompareButton } from './CompareButton';

export enum COMPARE_MODE {
  Split = 'Split',
  LongPress = 'LongPress'
}

export interface ImageCompareProps {
  /** 原始图片地址 */
  originImage?: string;
  /** 最新图片地址 */
  latestImage?: string;
  /** 对比模式 */
  mode: COMPARE_MODE;

  /**  */
  scale?: number;

  onFetchLoading?(loading: boolean): void;
}

export interface ImageCompareRefProps {
  resize: (scale?: number) => void;
}

export const ImageCompare = forwardRef<ImageCompareRefProps, ImageCompareProps>(
  ({ scale, originImage, latestImage, mode, onFetchLoading }, ref) => {
    const containerRef = useRef(null);
    const editorRef = useRef<EditorRefType>(null);
    const { initShapes } = useShapesOperator({ editorRef });

    const { fixDraggerScale } = useOnWheeEnd({ editorRef, mode });

    const { resize } = useResizeStage(editorRef, (scaleInfo) => {
      fixDraggerScale(scaleInfo?.scale.x ?? 1);
    });

    const { onChangeMode } = useModeChange({ mode, editorRef });

    // 获取图片bbox
    useGetImageBBox({
      onFetchLoading,
      originImage,
      latestImage,
      editorRef,
      onSuccess: (bbox) => {
        // 撑满居中(by bbox)
        const res = resize({ targetBBox: bbox });

        initShapes({
          bbox,
          originImage: originImage!,
          latestImage: latestImage!,
          scale: res?.scale.x
        });

        onChangeMode(mode);
      }
    });

    useEffect(() => {
      resize({ zoomPreset: scale });
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [scale]);

    useImperativeHandle(
      ref,
      () => ({
        resize: (scale?: number) => {
          resize({ zoomPreset: scale });
        }
      }),
      [resize]
    );

    return (
      <div ref={containerRef} className={styles.editor}>
        <Editor
          containerRef={containerRef}
          ref={editorRef}
          maxZoom={5}
          minZoom={1}
        ></Editor>

        <Actions className={styles.actions}>
          {mode === COMPARE_MODE.LongPress && (
            <CompareButton editorRef={editorRef} />
          )}
        </Actions>
      </div>
    );
  }
);
