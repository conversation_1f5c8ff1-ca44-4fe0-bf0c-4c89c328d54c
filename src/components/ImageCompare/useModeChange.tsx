import { COMPARE_MODE, ImageCompareProps } from '.';
import { EditorRefType, getStore } from '../Editor';
import {
  DRAGGER_GROUP_ID,
  Latest_IMAGE_GROUP_ID,
  ORIGIN_IMAGE_GROUP_ID
} from './constants';
import { useUpdateEffect } from 'react-use';

export interface useModeChangeProps extends Pick<ImageCompareProps, 'mode'> {
  editorRef: React.RefObject<EditorRefType | null>;
}

// 模式切换
export const useModeChange = ({ mode, editorRef }: useModeChangeProps) => {
  const onChangeMode = (mode: COMPARE_MODE) => {
    console.log('mode change');

    const { stageRef, updateShape } = getStore(editorRef) ?? {};

    if (mode === COMPARE_MODE.LongPress) {
      updateShape?.([
        { id: DRAGGER_GROUP_ID, visible: false }, // 去除分割线
        { id: ORIGIN_IMAGE_GROUP_ID, listening: false },
        {
          // 去除latestImage的裁剪区域
          id: Latest_IMAGE_GROUP_ID,
          listening: false,
          clipX: undefined,
          clipY: undefined,
          clipWidth: undefined,
          clipHeight: undefined
        }
      ]);
      return;
    }

    if (mode === COMPARE_MODE.Split) {
      const draggerGroupNode = stageRef?.current?.findOne(
        `#${DRAGGER_GROUP_ID}`
      );
      const latestImageGroupNode = stageRef?.current?.findOne(
        `#${Latest_IMAGE_GROUP_ID}`
      );

      if (!draggerGroupNode || !latestImageGroupNode) return;

      const draggerGroupNodeX = draggerGroupNode.x();
      const draggerGroupNodeY = draggerGroupNode.y();

      const latestImageGroupNodeWidth = latestImageGroupNode.width();
      const latestImageGroupNodeHeight = latestImageGroupNode.height();

      updateShape?.([
        { id: DRAGGER_GROUP_ID, visible: true }, // 显示分割线
        { id: ORIGIN_IMAGE_GROUP_ID, listening: true },
        {
          // 修正新旧图对比位置
          id: Latest_IMAGE_GROUP_ID,
          listening: true,
          clipX: draggerGroupNodeX,
          clipY: draggerGroupNodeY,
          clipWidth: Math.max(1, latestImageGroupNodeWidth - draggerGroupNodeX),
          clipHeight: latestImageGroupNodeHeight
        }
      ]);

      return;
    }
  };

  useUpdateEffect(() => {
    onChangeMode(mode);
  }, [mode, editorRef]);

  return { onChangeMode };
};
