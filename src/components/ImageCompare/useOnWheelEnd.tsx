import { useEffect, useRef } from 'react';
import { EditorRefType, getStore } from '../Editor';
import {
  DRAGGER_GROUP_ID,
  DRAGGER_LINE_ID,
  DRAGGER_RECT_SIZE,
  DRAGGER_REC_ID,
  LATEST_IMAGE_TEXT_ID,
  Latest_IMAGE_GROUP_ID,
  ORIGIN_IMAGE_TEXT_ID,
  TEXT_GROUP_PADDING,
  TEXT_RECT_WIDTH
} from './constants';
import { COMPARE_MODE } from '.';

export interface UseOnWheeEndProps {
  editorRef: React.RefObject<EditorRefType | null>;
  mode: COMPARE_MODE;
}

/**
 * 整个画布允许滚轮双指缩放，缩放的实现又是针对于stage的scale
 * 为了在缩放进行时 实时保持指定图形大小在屏幕视口下一致是一件不太容易办到的事情
 * 所以onWheel得时候会隐藏目标图形，onWheelEnd才重新计算他的屏幕视口大小
 */
export const useOnWheeEnd = ({ editorRef, mode }: UseOnWheeEndProps) => {
  const timerRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    const { stageRef } = getStore(editorRef) ?? {};

    stageRef?.current?.off('wheel.updateScale');

    stageRef?.current?.on('wheel.updateScale', (e) => {
      const stage = e.target.getStage();
      const draggerGroup = stage?.findOne(`#${DRAGGER_GROUP_ID}`);
      const originImageTextNode = stage?.findOne(`#${ORIGIN_IMAGE_TEXT_ID}`);
      const latestImageTextNode = stage?.findOne(`#${LATEST_IMAGE_TEXT_ID}`);

      clearTimeout(timerRef.current);

      draggerGroup?.visible(false);
      originImageTextNode?.visible(false);
      latestImageTextNode?.visible(false);

      timerRef.current = setTimeout(function () {
        const stageScaleX = stage?.scaleX() ?? 1;
        // 获取节点在屏幕视口下的位置
        fixDraggerScale(stageScaleX);

        originImageTextNode?.visible(true);
        latestImageTextNode?.visible(true);
        if (mode === COMPARE_MODE.Split) draggerGroup?.visible(true);
      }, 400);
    });

    return () => {
      stageRef?.current?.off('wheel.updateScale');
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editorRef, mode]);

  const fixDraggerScale = (stageScaleX: number) => {
    const { updateShape, stageRef } = getStore(editorRef) ?? {};

    const latestImage = stageRef?.current?.findOne(`#${Latest_IMAGE_GROUP_ID}`);

    updateShape?.([
      {
        id: LATEST_IMAGE_TEXT_ID,
        scaleX: 1 / stageScaleX,
        scaleY: 1 / stageScaleX,
        x:
          (latestImage?.width() ?? 1) -
          (TEXT_RECT_WIDTH + TEXT_GROUP_PADDING) / stageScaleX,
        y: 12 / stageScaleX
      },
      {
        id: ORIGIN_IMAGE_TEXT_ID,
        scaleX: 1 / stageScaleX,
        scaleY: 1 / stageScaleX,
        y: 12 / stageScaleX
      },
      {
        id: DRAGGER_LINE_ID,
        scaleX: 1 / stageScaleX,
        scaleY: 1
      },
      {
        id: DRAGGER_REC_ID,
        x: -(DRAGGER_RECT_SIZE / stageScaleX / 2),
        scaleX: 1 / stageScaleX,
        scaleY: 1 / stageScaleX
      }
    ]);
  };

  return { fixDraggerScale };
};
