import { CorpusPriorityLevel } from '@/types';

import { Card, Button } from 'antd';
import { PlusBold, MinusBold } from '@meitu/candy-icons';
import { useState, useEffect } from 'react';
import { increasePriority, decreasePriority } from '@/utils/corpus';

import styles from './styles.module.less';
import classNames from 'classnames';

export interface CorpusCardProps {
  /** 标题 - 原文 */
  text: string;
  /** 描述 - 译文 */
  translation?: string;
  /** 权重 */
  priority?: CorpusPriorityLevel;
  disabled?: boolean;
  onChange?: (params: Omit<CorpusCardProps, 'onChange'>) => void;
}

interface CorpusCardActionsProps {
  priority?: CorpusPriorityLevel;
  onChange: (priority: CorpusPriorityLevel) => void;
}

function Actions({ priority, onChange }: CorpusCardActionsProps) {
  /** 增加权重 */
  const onPlus = () => onChange(increasePriority(priority));
  /** 减少权重 */
  const onMinus = () => onChange(decreasePriority(priority));

  return (
    <div
      className={styles.corpusCardActions}
      onClick={(event) => {
        if (priority) {
          event.stopPropagation();
          event.preventDefault();
        }
      }}
    >
      {priority && (
        <>
          <Button
            type="text"
            icon={<PlusBold />}
            disabled={priority === CorpusPriorityLevel.High}
            onClick={onPlus}
          />
          <Button
            type="text"
            icon={<MinusBold />}
            disabled={priority === CorpusPriorityLevel.None}
            onClick={onMinus}
          />
        </>
      )}
    </div>
  );
}

export default function CorpusCard(props: CorpusCardProps) {
  const {
    text,
    translation,
    disabled,
    priority: priorityFromProps,
    onChange
  } = props;
  const [priority, setPriority] = useState(priorityFromProps);

  useEffect(
    () => {
      if (priority !== priorityFromProps) {
        setPriority(priorityFromProps);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [priorityFromProps]
  );

  const onClick = () => {
    if (disabled) {
      return;
    }

    const nextPriority = !!priority ? undefined : CorpusPriorityLevel.None;

    setPriority(nextPriority);
    onChange?.({
      text,
      translation,
      priority: nextPriority
    });
  };

  const onPriorityChange = (priority: CorpusPriorityLevel) => {
    setPriority(priority);
    onChange?.({
      text,
      translation,
      priority
    });
  };

  return (
    <Card
      className={classNames(
        styles.corpusCard,
        !!priority && styles.active,
        disabled && styles.disabled
      )}
      onClick={onClick}
    >
      <div className={styles.corpusCardContent}>
        <b>{text}</b>
        <p>{translation}</p>
      </div>
      <Actions priority={priority} onChange={onPriorityChange} />
    </Card>
  );
}
