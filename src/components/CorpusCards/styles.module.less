@import '~@/styles/variables.less';

.corpus-card:global(.@{ant-prefix}-card-bordered) {
  margin-bottom: @size-ms;
  margin-right: @size-ms;
  border-color: transparent;
  border-width: 2px;
  outline: 1px solid @stroke-system-border-overlay;
  cursor: pointer;

  &.active,
  &:hover {
    background-color: @background-card-hover;
  }

  &.disabled {
    cursor: not-allowed;
  }

  :global .@{ant-prefix}-card-body {
    display: flex;
    padding: 0;
    border-radius: @border-radius;
  }

  :local .corpus-card-content {
    padding: calc(@size-sm - 3px) @size calc(@size-sm - 3px) calc(@size - 3px);
    user-select: none;

    b {
      line-height: @size-md;
      font-weight: 500;
    }

    p {
      margin-bottom: 0;
      margin-top: @size-xxs;
      font-size: @font-size-sm;
      font-weight: 400;
      line-height: @size-ms;
      color: @base-natural-light-50;
    }
  }

  :local .corpus-card-actions {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: @size-md;
    margin-right: @size-xs;

    :global .@{ant-prefix}-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: @size-md;
      height: @size-md;
      padding: 0;
      font-size: @font-size-sm;
      background-color: @background-tag;

      & + .@{ant-prefix}-btn {
        margin-top: @size-xs;
      }
    }
  }
}

.corpus-card-container {
  display: flex;
  flex-wrap: wrap;
}
