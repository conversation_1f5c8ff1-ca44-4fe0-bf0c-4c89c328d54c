import type { CorpusCardProps } from './CorpusCard';

import CorpusCard from './CorpusCard';
import { useState, useEffect } from 'react';
import _ from 'lodash';

import styles from './styles.module.less';

type Card = Omit<CorpusCardProps, 'onChange'> & {
  id: string;
};

interface CorpusCardsProps {
  list: Card[];
  /** 禁用状态 */
  disabled?: boolean | ((card: Card) => boolean);
  onChange?: (selected: Card[]) => void;
}

function getSelected(list: Card[]): Record<string, Card> {
  return list.reduce(
    (selectedCard, card) =>
      card.priority
        ? Object.assign(selectedCard, {
            [card.id ?? card.text]: card
          })
        : selectedCard,
    {}
  );
}

export default function CorpusCards(props: CorpusCardsProps) {
  const { list, disabled: disabledFromProps, onChange } = props;
  const [selected, setSelected] = useState(() => getSelected(list));
  const disabled = _.isFunction(disabledFromProps)
    ? disabledFromProps
    : () => disabledFromProps;

  useEffect(
    () => {
      setSelected(getSelected(list));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [list]
  );

  const onCardChange = (
    id: string,
    card: Omit<CorpusCardProps, 'onChange'>
  ) => {
    const { priority } = card;

    if (!priority) {
      const nextSelected = _.omit(selected, id);

      setSelected(nextSelected);
      onChange?.(Object.values(nextSelected));
      return;
    }

    const nextSelected = Object.assign(selected, {
      [id]: Object.assign({ id }, card)
    });

    setSelected(nextSelected);
    onChange?.(Object.values(nextSelected));
  };

  return (
    <div className={styles.corpusCardContainer}>
      {list.map((cardProps) => (
        <CorpusCard
          key={cardProps.id ?? cardProps.text}
          disabled={disabled(cardProps)}
          {...cardProps}
          onChange={onCardChange.bind(null, cardProps.id ?? cardProps.text)}
        />
      ))}
    </div>
  );
}
