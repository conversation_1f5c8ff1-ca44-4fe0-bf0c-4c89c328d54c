import {
  CSSProperties,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react';
import * as PIXI from 'pixi.js';
import { Spin, message } from 'antd';

export default forwardRef(
  (
    {
      className,
      style,
      width,
      height,
      srcL,
      srcR,
      scale
    }: {
      srcL: string;
      srcR: string;
      className?: string;
      style?: CSSProperties;
      width: number;
      height: number;
      scale: number;
    },
    ref
  ) => {
    const INIT_X = width / 2;
    const scaleRef = useRef(scale);
    const containerElRef = useRef<HTMLDivElement>(null);
    const appRef = useRef<PIXI.Application>();
    const imageLRef = useRef<PIXI.Sprite>();
    const imageRRef = useRef<PIXI.Sprite>();
    const imageContainerRef = useRef<PIXI.Container>();
    const imageLContainerRef = useRef<PIXI.Container>();
    const imageRContainerRef = useRef<PIXI.Container>();
    const isOperating = useRef(false);
    const isDragging = useRef(false);
    const isZooming = useRef(false);
    const operatorRef = useRef<PIXI.Container>();
    const mousePositionRef = useRef({
      x: -1,
      y: -1
    });
    const operateInfoRef = useRef({
      lineX: INIT_X,
      scaleNumber: {
        current: 1,
        original: 1
      },
      originalPosition: {
        x: 0,
        y: 0
      }
    });
    const beginX = useRef(INIT_X);
    const currentX = useRef(INIT_X);
    const sizeRef = useRef({
      width: width,
      height: height
    });
    const [spinning, setSpinning] = useState(false);
    useImperativeHandle(ref, () => ({
      resetZooming: resetZooming
    }));

    const init = () => {
      const containerEl = containerElRef.current!;
      const app = new PIXI.Application({
        // backgroundColor:
        //   window.matchMedia &&
        //   window.matchMedia('(prefers-color-scheme: dark)').matches
        //     ? '#000'
        //     : '#FFF',
        backgroundColor: '#FFF',
        width: width,
        height: height
      });
      appRef.current = app;
      containerEl.appendChild(app.view as HTMLCanvasElement);
      // imageContainer
      const imageContainer = new PIXI.Container();
      imageContainer.interactive = true;
      imageContainerRef.current = imageContainer;
      app.stage.addChild(imageContainer as any);
      const imageLContainer = new PIXI.Container();
      const imageRContainer = new PIXI.Container();
      imageContainer.addChild(imageRContainer as any);
      imageContainer.addChild(imageLContainer as any);
      imageLContainerRef.current = imageLContainer;
      imageRContainerRef.current = imageRContainer;
      // mask
      const mask = new PIXI.Graphics();
      imageLContainer.addChild(mask as any);
      imageLContainer.mask = mask;
      // operator
      const operatorContainer = new PIXI.Container();
      operatorContainer.interactive = true;
      operatorContainer.cursor = 'col-resize';
      operatorRef.current = operatorContainer;
      app.stage.addChild(operatorContainer as any);
      const roundBox = new PIXI.Graphics();
      roundBox.x = 0;
      operatorContainer.addChild(roundBox as any);
      resize();
    };

    const updateOperatorDrawing = () => {
      const operateInfo = operateInfoRef.current;
      const operator = operatorRef.current!;
      const imageLContainer = imageLContainerRef.current!;
      const mask = imageLContainer.mask! as PIXI.Graphics;
      // set
      mask.x = operateInfo.lineX - sizeRef.current.width;
      operator.x = operateInfo.lineX;
      // render
      // appRef.current?.render();
    };

    const onOperatorBarMouseDown = () => {
      isOperating.current = true;
      beginX.current = operateInfoRef.current.lineX;
      currentX.current = operateInfoRef.current.lineX;
    };

    const resetZooming = () => {
      const imageL = imageLRef.current;
      const imageR = imageRRef.current;
      if (!imageL || !imageR) {
        return;
      }
      isZooming.current = false;
      const operateInfo = operateInfoRef.current;
      const targetScale = operateInfo.scaleNumber.original;
      const lrRate = imageR.scale.x / imageL.scale.x;
      imageL.scale.set(targetScale, targetScale);
      imageR.scale.set(targetScale * lrRate, targetScale * lrRate);
      operateInfo.scaleNumber.current = targetScale;
      imageL.position.set(
        operateInfo.originalPosition.x,
        operateInfo.originalPosition.y
      );
      imageR.position.set(
        operateInfo.originalPosition.x,
        operateInfo.originalPosition.y
      );
    };

    const onImageClick = (event: PIXI.FederatedPointerEvent) => {
      const imageL = imageLRef.current;
      const imageR = imageRRef.current;
      if (!imageL || !imageR) {
        return;
      }
      isZooming.current = !isZooming.current;
      const operateInfo = operateInfoRef.current;
      if (isZooming.current) {
        const targetScale = operateInfo.scaleNumber.original * scaleRef.current;
        operateInfo.scaleNumber.current = targetScale;

        const localPos = event.data.getLocalPosition(
          imageContainerRef.current!.parent as any
        );
        const targetX =
          localPos.x - (localPos.x - imageL.position.x) * scaleRef.current;
        const targetY =
          localPos.y - (localPos.y - imageL.position.y) * scaleRef.current;
        imageL.position.set(targetX, targetY);
        imageR.position.set(targetX, targetY);
        const lrRate = imageR.scale.x / imageL.scale.x;
        imageL.scale.set(targetScale, targetScale);
        imageR.scale.set(targetScale * lrRate, targetScale * lrRate);
      } else {
        resetZooming();
      }
    };

    const onImageMouseDown = (event: PIXI.FederatedPointerEvent) => {
      mousePositionRef.current.x = event.clientX;
      mousePositionRef.current.y = event.clientY;
      const imageL = imageLRef.current;
      const imageR = imageRRef.current;
      if (!isZooming.current || !imageL || !imageR) {
        return;
      }
      document.body.style.cursor = 'move';
      containerElRef.current!.style.cursor = 'move';
      isDragging.current = true;
    };

    const onImageMouseUp = (event: PIXI.FederatedPointerEvent) => {
      const newX = event.clientX;
      const newY = event.clientY;
      const lastX = mousePositionRef.current.x;
      const lastY = mousePositionRef.current.y;
      if (
        lastX >= 0 &&
        lastY >= 0 &&
        Math.pow(lastX - newX, 2) + Math.pow(lastY - newY, 2) < 25
      ) {
        onImageClick(event);
      }
    };

    const onMouseMove = (event: MouseEvent) => {
      if (isOperating.current) {
        var targetX = (currentX.current = currentX.current + event.movementX);
        const pic = imageLRef.current || imageRRef.current;
        const minX = pic ? pic.x : 0;
        const maxX = pic ? pic.x + pic.width : sizeRef.current.width;
        targetX = Math.max(minX, targetX);
        targetX = Math.min(targetX, maxX);
        operateInfoRef.current.lineX = targetX;
        updateOperatorDrawing();
      }
      if (isDragging.current) {
        const imageL = imageLRef.current!;
        const imageR = imageRRef.current!;
        const targetX = imageL.position.x + event.movementX;
        const targetY = imageL.position.y + event.movementY;
        imageL.position.set(targetX, targetY);
        imageR.position.set(targetX, targetY);
      }
    };

    const onMouseUp = () => {
      if (isOperating.current) {
        isOperating.current = false;
      }
      if (isDragging.current) {
        isDragging.current = false;
        document.body.style.cursor = 'default';
        containerElRef.current!.style.cursor = 'zoom-in';
      }
      mousePositionRef.current.x = -1;
      mousePositionRef.current.y = -1;
    };

    const loadImagesAsync = async (srcL: string, srcR: string) => {
      setSpinning(true);
      try {
        await loadImageAsync(srcL, 'l');
        await loadImageAsync(srcR, 'r');
      } catch (e) {
        message.error('图片加载失败。');
      } finally {
        setSpinning(false);
      }
    };

    const loadImageAsync = async (url: string, side: 'l' | 'r') => {
      const app = appRef.current;
      if (!app) {
        return;
      }
      const texture = await PIXI.Assets.load(url);
      const pic = new PIXI.Sprite(texture);
      // remove
      if (side === 'l' && imageLRef.current) {
        imageLContainerRef.current!.removeChild(imageLRef.current as any);
      } else if (side === 'r' && imageRRef.current) {
        imageRContainerRef.current!.removeChild(imageRRef.current as any);
      }
      // add
      if (side === 'l') {
        imageLRef.current = pic;
        imageLContainerRef.current!.addChild(pic as any);
      } else {
        imageRRef.current = pic;
        imageRContainerRef.current!.addChild(pic as any);
      }
      resize();
      if (side === 'l') {
        const operateInfo = operateInfoRef.current;
        operateInfo.scaleNumber.original = pic.scale.x;
        operateInfo.scaleNumber.current = pic.scale.x;
        operateInfo.originalPosition.x = pic.x;
        operateInfo.originalPosition.y = pic.y;
      }
      // app.render();
    };

    const resize = () => {
      const app = appRef.current;
      if (!app) {
        return;
      }
      const newWidth = sizeRef.current.width;
      const newHeight = sizeRef.current.height;
      app.renderer.resize(newWidth, newHeight);
      app.renderer.view.style!.width = newWidth + 'px';
      app.renderer.view.style!.height = newHeight + 'px';
      //pic
      const pics = [imageLRef.current, imageRRef.current];
      for (var i = 0; i < pics.length; i++) {
        const pic = pics[i];
        if (pic) {
          const picRate = pic.width / pic.height;
          const isXAlign = picRate > newWidth / newHeight;
          if (isXAlign) {
            pic.width = newWidth;
            pic.height = newWidth / picRate;
            pic.y = (height - pic.height) / 2;
          } else {
            pic.width = newHeight * picRate;
            pic.height = newHeight;
            pic.x = (newWidth - pic.width) / 2;
          }
        }
      }
      const mask = imageLContainerRef.current?.mask as PIXI.Graphics;
      if (mask) {
        //mask
        mask.clear();
        mask.beginFill('#000', 1);
        mask.drawRect(0, 0, newWidth, newHeight); // 初始遮罩大小
        mask.endFill();
        mask.x = operateInfoRef.current.lineX - newWidth;
      }
      //operator
      const operateInfo = operateInfoRef.current;
      const operatorContainer = operatorRef.current;
      if (operatorContainer) {
        operatorContainer.x = operateInfo.lineX;
        const roundBox = operatorContainer.children[0] as PIXI.Graphics;
        roundBox.beginFill('#fff', 0.6);
        // roundBox.drawRoundedRect(-16, 0, 32, 46, 10);
        roundBox.drawRoundedRect(-2.5, -newHeight / 2, 5, newHeight, 10);
        roundBox.endFill();
        drawSlideButtonGraphics(roundBox, 0, 0, 16);
        roundBox.y = newHeight / 2;
      }
      app.render();
    };

    // init
    useEffect(() => {
      init();
      const app = appRef.current!;
      const containerEl = containerElRef.current!;
      // addListener
      operatorRef.current!.on('mousedown', onOperatorBarMouseDown);
      imageContainerRef.current!.on('mousedown', onImageMouseDown);
      imageContainerRef.current!.on('mouseup', onImageMouseUp);
      window.addEventListener('mousemove', onMouseMove);
      window.addEventListener('mouseup', onMouseUp);
      window.addEventListener('mouseupoutside', onMouseUp);
      // render
      // appRef.current!.render();
      return () => {
        containerEl.removeChild(app.view as HTMLCanvasElement);
        // removeListener
        imageContainerRef.current!.off('mousedown', onImageMouseDown);
        imageContainerRef.current!.off('mouseup', onImageMouseUp);
        operatorRef.current?.off('mousedown', onOperatorBarMouseDown);
        window.removeEventListener('mousemove', onMouseMove);
        window.removeEventListener('mouseup', onMouseUp);
        window.removeEventListener('mouseupoutside', onMouseUp);
        app.destroy();
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    //loadImage
    useEffect(() => {
      loadImagesAsync(srcL, srcR);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [srcL, srcR]);

    // scale
    useEffect(() => {
      scaleRef.current = scale;
    }, [scale]);

    useEffect(() => {
      sizeRef.current.width = width;
      sizeRef.current.height = height;
      operateInfoRef.current.lineX = width / 2;
      resize();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [width, height]);

    return (
      <Spin spinning={spinning}>
        <div
          ref={containerElRef}
          className={className}
          style={{
            ...style,
            cursor: 'zoom-in',
            width: `${width}px`,
            height: `${height}px`
          }}
        ></div>
      </Spin>
    );
  }
);

function drawSlideButtonGraphics(
  graphics: PIXI.Graphics,
  centerX: number,
  centerY: number,
  radius: number
): void {
  const baseLength = radius * 0.8;
  const height = radius * 0.5;
  graphics.beginFill(0xffffff);
  graphics.drawCircle(centerX, centerY, radius);
  graphics.endFill();
  const lt = drawSlideButtonGraphicsTriangle(
    centerX - radius / 6,
    centerY,
    baseLength,
    height,
    '#000'
  );
  lt.rotation = -Math.PI / 2;
  const rt = drawSlideButtonGraphicsTriangle(
    centerX + radius / 6,
    centerY,
    baseLength,
    height,
    '#000'
  );
  rt.rotation = Math.PI / 2;
  graphics.addChild(lt as any);
  graphics.addChild(rt as any);
}

function drawSlideButtonGraphicsTriangle(
  baseMidX: number,
  baseMidY: number,
  baseLength: number,
  height: number,
  fillColor: string
): PIXI.Graphics {
  const graphics = new PIXI.Graphics();
  const rotation = 0;
  const topX = 0;
  const topY = -height;
  const baseLeftX = -baseLength / 2;
  const baseLeftY = 0;
  const baseRightX = baseLength / 2;
  const baseRightY = 0;
  const cornerRadius = baseLength / 10;
  const cos = Math.cos(rotation);
  const sin = Math.sin(rotation);
  const rotatedTopX = cos * topX - sin * topY + baseMidX;
  const rotatedTopY = sin * topX + cos * topY + baseMidY;
  const rotatedBaseLeftX = cos * baseLeftX - sin * baseLeftY + baseMidX;
  const rotatedBaseLeftY = sin * baseLeftX + cos * baseLeftY + baseMidY;
  const rotatedBaseRightX = cos * baseRightX - sin * baseRightY + baseMidX;
  const rotatedBaseRightY = sin * baseRightX + cos * baseRightY + baseMidY;
  //draw
  graphics.beginFill(fillColor);
  graphics.moveTo(rotatedBaseLeftX, rotatedBaseLeftY);
  graphics.arcTo(
    rotatedTopX,
    rotatedTopY,
    rotatedBaseRightX,
    rotatedBaseRightY,
    cornerRadius
  );
  graphics.arcTo(
    rotatedBaseRightX,
    rotatedBaseRightY,
    rotatedBaseLeftX,
    rotatedBaseLeftY,
    cornerRadius
  );
  graphics.arcTo(
    rotatedBaseLeftX,
    rotatedBaseLeftY,
    rotatedTopX,
    rotatedTopY,
    cornerRadius
  );
  graphics.endFill();
  graphics.pivot.x = baseMidX;
  graphics.pivot.y = baseMidY;
  graphics.position.x = baseMidX;
  graphics.position.y = baseMidY;
  return graphics;
}
