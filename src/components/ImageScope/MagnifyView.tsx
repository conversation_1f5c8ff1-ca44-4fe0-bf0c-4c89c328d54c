import {
  CSSProperties,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react';
import * as PIXI from 'pixi.js';
import { Button, Spin, message } from 'antd';
import { CompareBold } from '@meitu/candy-icons';

export default forwardRef(
  (
    {
      className,
      style,
      width,
      height,
      src1,
      src2,
      scale
    }: {
      src1: string;
      src2: string;
      className?: string;
      style?: CSSProperties;
      width: number;
      height: number;
      scale: number;
    },
    ref
  ) => {
    const scaleRef = useRef(scale);
    const containerElRef = useRef<HTMLDivElement>(null);
    const appRef = useRef<PIXI.Application>();
    const imageLRef = useRef<PIXI.Sprite>();
    const imageRRef = useRef<PIXI.Sprite>();
    const imageContainerRef = useRef<PIXI.Container>();
    const imageLContainerRef = useRef<PIXI.Container>();
    const imageRContainerRef = useRef<PIXI.Container>();
    const isSwitching = useRef(false);
    const isDragging = useRef(false);
    const isZooming = useRef(false);
    const mousePositionRef = useRef({
      x: -1,
      y: -1
    });
    const operateInfoRef = useRef({
      scaleNumber: {
        current: 1,
        original: 1
      },
      originalPosition: {
        x: 0,
        y: 0
      }
    });
    const sizeRef = useRef({
      width: width,
      height: height
    });
    const [spinning, setSpinning] = useState(false);

    useImperativeHandle(ref, () => ({
      resetZooming: resetZooming
    }));

    const init = () => {
      const containerEl = containerElRef.current!;
      const app = new PIXI.Application({
        // backgroundColor:
        //   window.matchMedia &&
        //   window.matchMedia('(prefers-color-scheme: dark)').matches
        //     ? '#000'
        //     : '#FFF',
        backgroundColor: '#FFF',
        width: width,
        height: height
      });
      appRef.current = app;
      containerEl.appendChild(app.view as HTMLCanvasElement);
      // imageContainer
      const imageContainer = new PIXI.Container();
      imageContainer.interactive = true;
      imageContainerRef.current = imageContainer;
      app.stage.addChild(imageContainer as any);
      const imageLContainer = new PIXI.Container();
      const imageRContainer = new PIXI.Container();
      imageContainer.addChild(imageRContainer as any);
      imageContainer.addChild(imageLContainer as any);
      imageLContainerRef.current = imageLContainer;
      imageRContainerRef.current = imageRContainer;
      // mask
      const mask = new PIXI.Graphics();
      imageLContainer.addChild(mask as any);
      imageLContainer.mask = mask;
      resize();
    };

    const onSwitchBtnMouseDown = () => {
      isSwitching.current = true;
      const mask = imageLContainerRef.current!.mask as PIXI.Graphics;
      mask.width = 0;
    };

    const onImageMouseDown = (event: PIXI.FederatedPointerEvent) => {
      mousePositionRef.current.x = event.clientX;
      mousePositionRef.current.y = event.clientY;
      const imageL = imageLRef.current;
      const imageR = imageRRef.current;
      if (!isZooming.current || !imageL || !imageR) {
        return;
      }
      document.body.style.cursor = 'move';
      containerElRef.current!.style.cursor = 'move';
      isDragging.current = true;
    };

    const resetZooming = () => {
      const imageL = imageLRef.current;
      const imageR = imageRRef.current;
      if (!imageL || !imageR) {
        return;
      }
      isZooming.current = false;
      const operateInfo = operateInfoRef.current;
      const targetScale = operateInfo.scaleNumber.original;
      const lrRate = imageR.scale.x / imageL.scale.x;
      imageL.scale.set(targetScale, targetScale);
      imageR.scale.set(targetScale * lrRate, targetScale * lrRate);
      operateInfo.scaleNumber.current = targetScale;
      imageL.position.set(
        operateInfo.originalPosition.x,
        operateInfo.originalPosition.y
      );
      imageR.position.set(
        operateInfo.originalPosition.x,
        operateInfo.originalPosition.y
      );
    };

    const onImageClick = (event: PIXI.FederatedPointerEvent) => {
      const imageL = imageLRef.current;
      const imageR = imageRRef.current;
      if (!imageL || !imageR) {
        return;
      }
      isZooming.current = !isZooming.current;
      const operateInfo = operateInfoRef.current;
      if (isZooming.current) {
        const targetScale = operateInfo.scaleNumber.original * scaleRef.current;
        operateInfo.scaleNumber.current = targetScale;

        const localPos = event.data.getLocalPosition(
          imageContainerRef.current!.parent as any
        );
        const targetX =
          localPos.x - (localPos.x - imageL.position.x) * scaleRef.current;
        const targetY =
          localPos.y - (localPos.y - imageL.position.y) * scaleRef.current;
        imageL.position.set(targetX, targetY);
        imageR.position.set(targetX, targetY);
        const lrRate = imageR.scale.x / imageL.scale.x;
        imageL.scale.set(targetScale, targetScale);
        imageR.scale.set(targetScale * lrRate, targetScale * lrRate);
      } else {
        resetZooming();
      }
    };

    const onMouseMove = (event: MouseEvent) => {
      if (isDragging.current) {
        const imageL = imageLRef.current!;
        const imageR = imageRRef.current!;
        const targetX = imageL.position.x + event.movementX;
        const targetY = imageL.position.y + event.movementY;
        imageL.position.set(targetX, targetY);
        imageR.position.set(targetX, targetY);
      }
    };

    const onImageMouseUp = (event: PIXI.FederatedPointerEvent) => {
      const newX = event.clientX;
      const newY = event.clientY;
      const lastX = mousePositionRef.current.x;
      const lastY = mousePositionRef.current.y;
      if (
        lastX >= 0 &&
        lastY >= 0 &&
        Math.pow(lastX - newX, 2) + Math.pow(lastY - newY, 2) < 25
      ) {
        onImageClick(event);
      }
    };

    const onMouseUp = () => {
      if (isSwitching.current) {
        isSwitching.current = false;
        const mask = imageLContainerRef.current!.mask as PIXI.Graphics;
        mask.width = sizeRef.current.width;
      }
      if (isDragging.current) {
        isDragging.current = false;
        document.body.style.cursor = 'default';
        containerElRef.current!.style.cursor = 'zoom-in';
      }
      mousePositionRef.current.x = -1;
      mousePositionRef.current.y = -1;
    };

    const loadImagesAsync = async (src1: string, src2: string) => {
      setSpinning(true);
      try {
        await loadImageAsync(src1, 'l');
        await loadImageAsync(src2, 'r');
      } catch (e) {
        message.error('图片加载失败。');
      } finally {
        setSpinning(false);
      }
    };

    const loadImageAsync = async (url: string, side: 'l' | 'r') => {
      const app = appRef.current;
      if (!app || !url) {
        return;
      }
      const texture = await PIXI.Assets.load(url);
      const pic = new PIXI.Sprite(texture);
      // remove
      if (side === 'l' && imageLRef.current) {
        imageLContainerRef.current!.removeChild(imageLRef.current as any);
      } else if (side === 'r' && imageRRef.current) {
        imageRContainerRef.current!.removeChild(imageRRef.current as any);
      }
      // add
      if (side === 'l') {
        imageLRef.current = pic;
        imageLContainerRef.current!.addChild(pic as any);
      } else {
        imageRRef.current = pic;
        imageRContainerRef.current!.addChild(pic as any);
      }
      resize();
      if (side === 'l') {
        const operateInfo = operateInfoRef.current;
        operateInfo.scaleNumber.original = pic.scale.x;
        operateInfo.scaleNumber.current = pic.scale.x;
        operateInfo.originalPosition.x = pic.x;
        operateInfo.originalPosition.y = pic.y;
      }
      // app.render();
    };

    const resize = () => {
      const app = appRef.current;
      if (!app) {
        return;
      }
      const newWidth = sizeRef.current.width;
      const newHeight = sizeRef.current.height;
      app.renderer.resize(newWidth, newHeight);
      app.renderer.view.style!.width = newWidth + 'px';
      app.renderer.view.style!.height = newHeight + 'px';
      //pic
      const pics = [imageLRef.current, imageRRef.current];
      for (var i = 0; i < pics.length; i++) {
        const pic = pics[i];
        if (pic) {
          const picRate = pic.width / pic.height;
          const isXAlign = picRate > newWidth / newHeight;
          if (isXAlign) {
            pic.width = newWidth;
            pic.height = newWidth / picRate;
            pic.y = (height - pic.height) / 2;
          } else {
            pic.width = newHeight * picRate;
            pic.height = newHeight;
            pic.x = (newWidth - pic.width) / 2;
          }
        }
      }
      const mask = imageLContainerRef.current?.mask as PIXI.Graphics;
      if (mask) {
        //mask
        mask.clear();
        mask.beginFill('#000', 1);
        mask.drawRect(0, 0, newWidth, newHeight); // 初始遮罩大小
        mask.endFill();
        mask.width = isSwitching.current ? 0 : newWidth;
      }
      app.render();
    };

    // init
    useEffect(() => {
      init();
      const app = appRef.current!;
      const containerEl = containerElRef.current!;
      // addListener
      imageContainerRef.current!.on('mousedown', onImageMouseDown);
      imageContainerRef.current!.on('mouseup', onImageMouseUp);
      window.addEventListener('mousemove', onMouseMove);
      window.addEventListener('mouseup', onMouseUp);
      window.addEventListener('mouseupoutside', onMouseUp);
      // render
      // appRef.current!.render();
      return () => {
        containerEl.removeChild(app.view as HTMLCanvasElement);
        // removeListener
        imageContainerRef.current!.off('mousedown', onImageMouseDown);
        imageContainerRef.current!.off('mouseup', onImageMouseUp);
        window.removeEventListener('mousemove', onMouseMove);
        window.removeEventListener('mouseup', onMouseUp);
        window.removeEventListener('mouseupoutside', onMouseUp);
        app.destroy();
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    //loadImage
    useEffect(() => {
      loadImagesAsync(src1, src2);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [src1, src2]);

    // scale
    useEffect(() => {
      scaleRef.current = scale;
    }, [scale]);

    useEffect(() => {
      sizeRef.current.width = width;
      sizeRef.current.height = height;
      resize();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [width, height]);

    return (
      <Spin spinning={spinning}>
        <Button
          onMouseDown={onSwitchBtnMouseDown}
          style={{
            width: '45px',
            height: '45px',
            position: 'absolute',
            right: '16px',
            top: '16px'
          }}
          icon={<CompareBold width={45} height={45} />}
        />
        <div
          ref={containerElRef}
          className={className}
          style={{
            ...style,
            cursor: 'zoom-in',
            width: `${width}px`,
            height: `${height}px`
          }}
        ></div>
      </Spin>
    );
  }
);
