import type { ReactNode } from 'react';

import classNames from 'classnames';
import styles from './styles.module.less';

interface LoadingProps {
  type?: 'light' | 'dark' | 'default';
  children?: ReactNode;
  className?: string;
}

/** 加载中 */
export function Loading(props: LoadingProps) {
  return (
    <div className={classNames(styles.loading, 'loading', props.type)}>
      <div className={classNames(styles.loadingJumping, props.className)}>
        <span />
        <span />
        <span />
      </div>
      {props.children}
    </div>
  );
}
