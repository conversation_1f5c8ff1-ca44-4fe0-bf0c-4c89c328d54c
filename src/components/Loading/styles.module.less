@import '~@/styles/variables.less';

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: transparent;
  color: @content-system-primary;

  &-jumping {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: calc(@size-xxl * 2);

    &:global(.@{ant-prefix}-spin-dot) {
      min-width: 96px;
    }
  }

  &-jumping,
  &-jumping * {
    box-sizing: border-box;
  }

  &-jumping span {
    display: inline-block;
    height: @size-ms;
    width: @size-ms;
    background: @color-fill;
    border-radius: 100%;
    background-clip: padding-box;
  }

  &-jumping span:nth-child(1) {
    animation: scale 1.15s 0.12s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
  }

  &-jumping span:nth-child(2) {
    animation: scale 1.15s 0.23s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
  }

  &-jumping span:nth-child(3) {
    animation: scale 1.15s 0.35s infinite cubic-bezier(0.6, -0.28, 0.735, 0.045);
  }

  @keyframes scale {
    0% {
      transform: scale(0);
      background: @color-fill-quaternary;
    }

    25% {
      transform: scale(0.9, 0.9);
      background: @color-fill-secondary;
    }

    50% {
      transform: scale(1, 1);
      margin: 0 3px;
      background: @color-fill;
    }

    100% {
      transform: scale(0);
      background: @color-fill-quaternary;
    }
  }
}

.loading:global(.light) {
  .loading-jumping {
    span:nth-child(1) {
      animation: scale-light 1.15s 0.12s infinite
        cubic-bezier(0.6, -0.28, 0.735, 0.045);
    }

    span:nth-child(2) {
      animation: scale-light 1.15s 0.23s infinite
        cubic-bezier(0.6, -0.28, 0.735, 0.045);
    }

    span:nth-child(3) {
      animation: scale-light 1.15s 0.35s infinite
        cubic-bezier(0.6, -0.28, 0.735, 0.045);
    }
  }

  @keyframes scale-light {
    0% {
      transform: scale(0);
      background: @base-white-opacity-100;
    }

    25% {
      transform: scale(0.9, 0.9);
      background: @base-white-opacity-100;
    }

    50% {
      transform: scale(1, 1);
      margin: 0 3px;
      background: @base-white-opacity-100;
    }

    100% {
      transform: scale(0);
      background: @base-white-opacity-100;
    }
  }
}

.loading:global(.dark) {
  .loading-jumping {
    span:nth-child(1) {
      animation: scale-dark 1.15s 0.12s infinite
        cubic-bezier(0.6, -0.28, 0.735, 0.045);
    }

    span:nth-child(2) {
      animation: scale-dark 1.15s 0.23s infinite
        cubic-bezier(0.6, -0.28, 0.735, 0.045);
    }

    span:nth-child(3) {
      animation: scale-dark 1.15s 0.35s infinite
        cubic-bezier(0.6, -0.28, 0.735, 0.045);
    }
  }

  @keyframes scale-dark {
    0% {
      transform: scale(0);
      background: @base-black-opacity-65;
    }

    25% {
      transform: scale(0.9, 0.9);
      background: @base-black-opacity-65;
    }

    50% {
      transform: scale(1, 1);
      margin: 0 3px;
      background: @base-black-opacity-65;
    }

    100% {
      transform: scale(0);
      background: @base-black-opacity-65;
    }
  }
}

.count-up {
  position: absolute;
  left: @size-xs;
  bottom: @size-xs;
}
