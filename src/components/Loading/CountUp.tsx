import { useEffect, useState, useRef } from 'react';
import moment from 'moment';

interface CountUpProps {
  startValue: number;
  className?: string;
}

// 每1000ms更新value
const STEP = 1000;

function formatCount(seconds: number) {
  const duration = moment.duration(seconds, 'seconds');
  const days = Math.floor(duration.asDays());
  const hours = duration.hours();
  const minutes = duration.minutes();

  return [
    days > 0 && `${days}天`,
    (days > 0 || hours > 0) && `${hours}小时`,
    (days > 0 || hours > 0 || minutes > 0) && `${minutes}分钟`,
    `${duration.seconds()}秒`
  ]
    .filter(Boolean)
    .join(' ');
}

export default function CountUp({ startValue, className }: CountUpProps) {
  const [value, setValue] = useState(startValue);
  // 动画开始时间
  const [startTime, setStartTime] = useState<number | null>(null);
  const frameId = useRef<number | null>(null);

  useEffect(
    () => {
      function countUp(timestamp: number) {
        if (!startTime) {
          setStartTime(timestamp);
          return;
        }

        // 经过的时间
        const elapsedTime = timestamp - startTime!;

        // 保留小数点后一位
        setValue(startValue + Math.round(elapsedTime / STEP));

        frameId.current = requestAnimationFrame(countUp);
      }

      // 开始动画
      frameId.current = requestAnimationFrame(countUp);

      // 在组件卸载时停止动画
      return () => cancelAnimationFrame(frameId.current as number);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [startTime]
  );

  return <span className={className}>{formatCount(value)}</span>;
}
