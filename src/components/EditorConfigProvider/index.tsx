import { type ThemeConfig, ConfigProvider } from 'antd';
import { ReactNode } from 'react';

interface EditorConfigProviderProps extends ThemeConfig {
  theme?: ThemeConfig;

  children?: ReactNode;
}

export function EditorConfigProvider(props: EditorConfigProviderProps) {
  const theme = Object.assign(
    {},
    {
      components: {
        Select: {
          controlHeight: 26,
          colorBgContainer: 'transparent'
        },
        InputNumber: {
          controlHeight: 26
        },
        Button: {
          controlHeight: 32
        },
        Switch: {
          fontSize: 12
        }
      }
    },
    props.theme ?? {}
  );

  return <ConfigProvider theme={theme}>{props.children}</ConfigProvider>;
}
