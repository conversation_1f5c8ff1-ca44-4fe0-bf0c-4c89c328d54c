@import '~@/styles/variables';

.slider-input {
  transition: color @motion-duration-mid;
  // margin-bottom: 20px;
  // &:focus-within {
  //   .title {
  //     color: @color-primary;
  //   }
  // }
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: @size-xs;
  }

  .title {
    color: @color-text-secondary;
  }

  .tooltip {
    color: @content-input-lable;
    margin-left: @size-xxs;
    font-size: @font-size-sm;
    cursor: pointer;
  }

  .label {
    margin-right: @size-sm;
    color: @content-system-secondary;
  }

  .slider {
    flex: 1 0;
  }

  .input {
    margin-left: @size-sm;
  }

  .horizontal {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
  }
}
