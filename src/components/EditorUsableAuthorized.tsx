import type { ReactElement, MouseEvent } from 'react';

import { App } from 'antd';

import {
  useEditorModuleAccessibility,
  useAccountProfile,
  useAccount
} from '@/hooks';
import { useEffect, cloneElement, useRef } from 'react';
import { useApp } from '@/App';
import { useNavigate } from 'react-router-dom';
import { isUndefined } from 'lodash';

export interface EditorUsableAuthorizedProps {
  /**
   * 验证等级
   * @description `button` 级验证会推迟到触发子元素的 `onClick` 时进行，`layout` 级验证则会立刻进行
   * @default "button"
   */
  level?: 'layout' | 'button';

  children: ReactElement;

  /**
   * @description  是否有可用的授权（没有传代表不需要权限校验）
   */
  hasUsableAuthorized?: boolean;
}

/**
 * 编辑器可用验证器
 * @param props 必须传入单一节点的 children
 * @constructor
 */
export function EditorUsableAuthorized(props: EditorUsableAuthorizedProps) {
  const {
    level: authorizeLevel = 'button',
    children: element,
    hasUsableAuthorized
  } = props;

  const { accountProfile, loading: isFetchingAccountProfile } =
    useAccountProfile();

  const { message } = App.useApp();
  const { openBetaInvitationModal } = useApp();
  const { login } = useAccount();
  const navigate = useNavigate();

  // `layout` 级别验证时在全局将仅触发一次验证
  const authorizedRef = useRef(false);

  // `layout` 验证时立即校验编辑器是否可用
  useEffect(
    () => {
      if (authorizeLevel !== 'layout' || authorizedRef.current) {
        return;
      }

      // 登录中
      if (isFetchingAccountProfile) {
        return;
      }

      if (accountProfile) {
        authorizedRef.current = true;
      }

      if (!accountProfile) {
        login();
        return;
      }

      // 没有获取的内测权限
      if (!isUndefined(hasUsableAuthorized) && !hasUsableAuthorized) {
        openBetaInvitationModal();
        navigate('/');
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      isFetchingAccountProfile,
      openBetaInvitationModal,
      accountProfile,
      authorizeLevel,
      hasUsableAuthorized,
      login
    ]
  );

  if (authorizeLevel === 'layout') {
    return accountProfile ? element : null;
  }

  if (
    isFetchingAccountProfile ||
    !accountProfile ||
    (accountProfile &&
      !isUndefined(hasUsableAuthorized) &&
      !hasUsableAuthorized)
  ) {
    return cloneElement(element, {
      onClick(event: MouseEvent<HTMLElement>) {
        event.stopPropagation();
        event.preventDefault();

        // 登录中
        if (isFetchingAccountProfile) {
          message.warning('登录中，请稍后重试');
          return;
        }

        // 未登录
        if (!accountProfile) {
          login();
          return;
        }

        // 没有获取的内测权限
        if (!hasUsableAuthorized) {
          openBetaInvitationModal();
        }
      }
    });
  }

  return element;
}

interface EditorUsableAuthorizedDecoratorProps
  extends EditorUsableAuthorizedProps {
  children: ReactElement;
}

/**
 * 风格模型装饰器
 */
export function TrainingAccessAuthorized(
  props: EditorUsableAuthorizedDecoratorProps
) {
  const editorModuleAccessibility = useEditorModuleAccessibility();
  const hasTrainingAccess = editorModuleAccessibility?.hasTrainingAccess;
  const { children, ...restProps } = props;

  return (
    <EditorUsableAuthorized
      hasUsableAuthorized={hasTrainingAccess}
      {...restProps}
    >
      {props.children}
    </EditorUsableAuthorized>
  );
}
