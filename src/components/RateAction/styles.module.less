@import '~@/styles/variables.less';

.rate-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 264px;
  height: 56px;

  .rate-lable {
    font-size: 14px;
    color: @content-system-tertiary;
    margin-right: 20px;
  }

  .rate-icon {
    width: 24px;
    height: 24px;
  }

  .rate-box {
    height: 24px;

    :global .ant-rate {
      height: 100%;

      .ant-rate-star-first,
      .ant-rate-star-second {
        display: flex;
      }
    }

    .rate-popup-box {
      width: 264px;
      min-height: 254px;
      box-shadow: 0px 10px 40px 0px rgba(0, 0, 0, 0.1),
        0px 0px 2px 0px rgba(0, 0, 0, 0.08);
      position: absolute;
      bottom: 0;
      right: 0;
      z-index: -1;
      background: @background-system-frame-floatpanel;
      border-radius: 8px;
      padding: 16px;
      padding-bottom: 60px;

      .pop-title-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .pop-title-text {
          font-size: 16px;
          font-weight: 500;
          line-height: 22px;
          color: @content-system-primary;
          display: flex;
          align-items: center;

          &::before {
            content: ' ';
            display: inline-block;
            width: 22px;
            height: 22px;
            background-repeat: no-repeat;
            background-size: contain;
            margin-right: 4px;
          }

          &.pop-title-icon-very-discontent::before {
            background-image: url('../../icons/rate-score-very-discontent.png');
          }

          &.pop-title-icon-discontent::before {
            background-image: url('../../icons/rate-score-discontent.png');
          }

          &.pop-title-icon-common::before {
            background-image: url('../../icons/rate-score-common.png');
          }

          &.pop-title-icon-very-good::before {
            background-image: url('../../icons/rate-score-very-good.png');
          }

          &.pop-title-icon-good::before {
            background-image: url('../../icons/rate-score-good.png');
          }
        }

        button {
          width: 56px;
          height: 26px;
          font-size: 12px;
          color: @content-btn-primary;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }

      .pop-tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 12px;

        .pop-tag-item {
          display: flex;
          align-items: center;
          height: 26px;
          padding: 0px 8px;
          cursor: pointer;
          border-radius: 6px;
          border: 1px solid @stroke-btn-secondary;
          font-size: 12px;
          color: @content-btn-secondary;

          &:not(.active):hover {
            background: @background-btn-hover;
          }

          &.active {
            border-color: @stroke-web-tag-selected;
            color: @content-web-tag-selected;
          }
        }
      }

      :global .ant-input {
        font-size: 12px;
      }
    }
  }

  .rate-success-modal {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 264px;
    height: 104px;
    padding: 16px;
    background: @background-system-frame-floatpanel;
    border-radius: 8px;
    box-shadow: 0px 10px 40px 0px rgba(0, 0, 0, 0.1),
      0px 0px 2px 0px rgba(0, 0, 0, 0.08);

    .rate-success-text {
      color: @content-system-primary;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
    }

    .rate-success-operate {
      margin-top: 20px;
      display: flex;

      button {
        margin-right: 12px;
        height: 26px;
        display: flex;
        align-items: center;
        padding: 0 8px;
        font-size: 12px;

        :global .anticon {
          font-size: 14px;

          & + span {
            margin-left: 4px;
          }
        }
      }
    }
  }
}
