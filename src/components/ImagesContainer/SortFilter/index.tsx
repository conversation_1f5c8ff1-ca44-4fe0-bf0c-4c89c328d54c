import { type RefSelectProps, Select } from 'antd';
import { ChevronDownBlack } from '@meitu/candy-icons';
import { useRef, useState } from 'react';

import { SortFilterType } from '@/types';
import styles from './index.module.less';

export interface SortFilterProps {
  value: SortFilterType;
  onChange: (value: SortFilterType) => void;
}

const options = [
  {
    value: SortFilterType.DESC,
    label: <span>时间倒序</span>
  },
  {
    value: SortFilterType.ASC,
    label: <span>时间正序</span>
  }
];

export function SortFilter({ value, onChange }: SortFilterProps) {
  const selectRef = useRef<RefSelectProps>(null);
  const [open, setOpen] = useState(false);

  return (
    <Select
      ref={selectRef}
      value={value}
      options={options}
      suffixIcon={
        <ChevronDownBlack
          onClick={() => {
            setOpen(!open);
          }}
        />
      }
      open={open}
      className={styles.imageFilter}
      popupClassName={styles.imageFilterPopup}
      onDropdownVisibleChange={setOpen}
      onChange={(value) => {
        onChange(value);
        selectRef.current?.blur();
      }}
    />
  );
}
