@import '~@/styles/variables.less';
.close-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 14px;
  height: 14px;
}
.title {
  padding-top: 8px;
}
.content {
  margin-top: 12px;
  width: 100%;
}
.remark {
  margin-top: 12px;
  // color: @content-system-quaternary #abadb2;
  color: #abadb2;
  font-family: PingFang SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 133.333% */
}
.button-container {
  margin-top: 20px;
  width: 100%;
  display: flex;
  justify-content: center;
}
.commit-button {
  border-radius: 8px;
  width: 180px;
  height: 36px;
  text-align: center;
  background-color: '#3549FF';
}
.text-area {
  height: 120px;
  resize: 'none';
}
.commit-button span {
  color: @content-btn-primary #fff;
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 142.857% */
}
.commit-button .price {
  color: @content-btn-primary #fff;
  font-family: PingFang SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 133.333% */
}
