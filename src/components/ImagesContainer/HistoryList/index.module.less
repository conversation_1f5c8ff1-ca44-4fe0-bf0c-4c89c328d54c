@import '~@/styles/variables.less';
.history-list {
  position: absolute;
  top: 0;
  right: 0;
  width: 160px;
  height: calc(100vh - 72px);
  z-index: 2;
  border-left: 1px solid @stroke-system-border-overlay;
  .history-title {
    color: @content-system-primary;
    font-size: 14px;
    width: 160px;
    font-style: normal;
    padding-left: @size;
    padding-top: @size;
    font-weight: 600;
    line-height: 20px;
  }
  .list-box {
    position: relative;
    margin-top: 12px;
    padding-bottom: 26px;
    overflow-x: hidden;
    height: 100%;
    overflow-y: auto;
    // scrollbar-width: none;
    -ms-overflow-style: none;

    // &::-webkit-scrollbar {
    //   // display: none;
    // }
  }

  .history-item {
    width: 100%;
    height: 100%;
    background-color: #f6f7fa;
    // margin-bottom: 14px;
    border: 1px solid @stroke-system-border-overlay;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &-only {
      width: 128px;
      height: 128px;
      background-color: #f6f7fa;
      margin-bottom: 14px;
      margin-left: 16px;
      border: 1px solid @stroke-system-border-overlay;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      :global .@{ant-prefix}-image {
        width: 100%;
        height: 100%;
        border-radius: 4px;
        object-fit: cover;
      }
      .item-img {
        width: 100%;
        height: 100%;
        border-radius: 6px;
        object-fit: cover;
      }
    }
    &-more {
      width: 128px;
      cursor: pointer;
      height: 128px;
      background-color: #f6f7fa;
      margin-bottom: 14px;
      margin-left: 16px;
      border: 1px solid @stroke-system-border-overlay;
      border-radius: 4px;
      padding: 2px;
      display: flex;
      flex-wrap: wrap;
      :global .@{ant-prefix}-image {
        width: 56px;
        height: 56px;
        object-fit: cover;
        border-radius: 4px;
        margin: 2px;
      }
      .item-img {
        width: 100%;
        height: 100%;
        border-radius: 6px;
        object-fit: cover;
      }
      .item-img-err {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 56px;
        height: 56px;
        object-fit: cover;
        border-radius: 4px;
        margin: 2px;
        background-image: url('~@/assets/images/invisible-graph.jpg');
        background-size: cover;
        background-position: center center;
        color: @color-white;
        line-height: @line-height-lg;

        :global .@{ant-prefix}-image {
          margin-bottom: @size;
        }

        &-icon {
          font-size: calc(26px);

          :global path {
            fill: @color-white;
          }
        }
      }
    }
  }
  .active {
    // padding: 2px;
    border: 2px solid @stroke-input-selected;
    border-radius: 6px;
    padding: 2px;
  }

  .empty {
    position: absolute;
    top: 50%;
    left: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transform: translate(-60%, -60%);

    :global .@{ant-prefix}-empty-image {
      width: 84px;
      height: 84px;
    }

    :global .@{ant-prefix}-empty-description {
      color: @content-system-quaternary;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
    }
  }
  .no-image {
    display: none;
  }
}
