import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';
import type { CanvasSize } from '@/types';
import type { Graph } from '@/types/draft';

import { LoadingStatus, GraphStatus } from '@/types/draft';
import {
  SlashCircle,
  TrashCanBold,
  RedrawBold,
  PublishBold,
  DownloadBold,
  UltraHdBoldFill
} from '@meitu/candy-icons';
import { Image, Button, Tooltip, Tag } from 'antd';
import CountUp from '@/components/Loading/CountUp';
import { CollectionSwitch } from '@/components/CollectionSwitch';
import { Loading, PermissionEnvWrapper } from '@/components';

import { toAtlasImageMogr2URL } from '@meitu/util';
import styles from './GraphBatch.module.less';
import { defaultBackgroundTagAmount } from '@meitu/candy-theme/dist/variables.mjs';
import { useMemo, useState, Dispatch, SetStateAction } from 'react';
import classNames from 'classnames';
import _ from 'lodash';
import { type initialValues } from './PublishModal';
import { UpscalerTaskStatus } from '@/api/types';
import { AppOrigin, RANDOM_SEED_VALUE } from '@/constants';
import { useReEditParams } from './Hooks';
import { useApp } from '@/App';
import { isTimestampExpired } from '@/utils/isTimestampExpired';
import imageError from '@/assets/images/image-error.png';

export interface GraphBatchProps {
  /** 同一批次唯一标识 id */
  id: string;
  /** 批次中图片集合 */
  batch: Graph[];
  /** 图片尺寸 */
  size: CanvasSize;
  /** 加载状态 */
  loadingStatus?: LoadingStatus;
  /** 加载进度 */
  loadingProgress?: number;
  /** 生成时间 */
  time?: number;

  // 是否已发布
  isPublish?: boolean;

  /** 是否收藏 */
  isCollect?: boolean;

  /** 点击批次 */
  onClick?: (id: GraphBatchProps['id']) => void;
  /** 删除批次 */
  onRemove?: (id: GraphBatchProps['id']) => void;
  /** 再次编辑 */
  onReEdit?: (id: GraphBatchProps['id']) => void;

  /** 发布作品 */
  onPublish?: (id: GraphBatchProps['id']) => void;

  onPublishSuccess?: (values: initialValues) => void;

  /** 下载作品 */
  onDownload?: (
    id: GraphBatchProps['id'],
    setDownloading: Dispatch<SetStateAction<boolean>>
  ) => void;

  // 是否有超分图
  isUpscalerSuccess?: boolean;
  /** 收藏/取消收藏 作品 */
  onCollect?: (id: GraphBatchProps['id'], collected: boolean) => void;
  /** 隐藏tools项 */
  hiddenActionKeys?: (
    | 'remove'
    | 'reEdit'
    | 'publish'
    | 'download'
    | 'collect'
    | 'deeperDesign'
  )[];
}
/** 图片生成失败 区分敏感、超时情况*/
export function LoadFailed(props: {
  type?: LoadingStatus;
  showText?: boolean;
}) {
  const getClassNames = (type: LoadingStatus | undefined) => {
    switch (type) {
      case LoadingStatus.IRREGULARITY:
        return 'irregularity';

      case LoadingStatus.TIMEOUT:
        return 'timeout';

      default:
        return '';
    }
  };
  const showText = props.showText ?? true;

  return (
    <div
      className={classNames(
        styles.invisible,
        styles[getClassNames(props.type)]
      )}
    >
      <SlashCircle className={styles.invisibleIcon} />
      {showText && (
        <>
          <span>当前图片生成出错</span>
          <span>请调整参数或再次生成</span>
        </>
      )}
    </div>
  );
}

/** 图片不可见 */
export function Invisible() {
  return (
    <div className={classNames(styles.invisible, 'invisible')}>
      <SlashCircle className={styles.invisibleIcon} />
      <span>图片不适合展示</span>
      <span>请修改提示词再次生成</span>
    </div>
  );
}

function getDisplayGraph(batch: Graph[]): Graph | undefined {
  return batch.find(({ status }) => status === GraphStatus.SUCCESS);
}

const popEvent: (fn?: () => void) => MouseEventHandler = (fn) => (event) => {
  event.preventDefault();
  event.stopPropagation();

  fn?.();
};
const getSeed = (batch: Graph[]) => {
  return (
    batch.find(({ status }) => status === GraphStatus.SUCCESS)?.seed ??
    batch[0]?.seed ??
    RANDOM_SEED_VALUE
  );
};

function GraphBatchMask({
  id,
  isPublish,
  isCollect,
  batch,
  loadingStatus,
  batchLength,
  onRemove,
  onReEdit,
  onPublish,
  onDownload,
  isUpscalerSuccess,
  onCollect,
  hiddenActionKeys: hiddenKeys = []
}: Omit<GraphBatchProps, 'size'> & {
  batchLength: number;
}) {
  const [downloading, setDownloading] = useState(false);
  const hasValidGraph = batch?.some(
    (graph) => graph?.status === GraphStatus.SUCCESS
  );
  const isSuccess = loadingStatus === LoadingStatus.SUCCESS;
  const isLoading = loadingStatus === LoadingStatus.LOADING;

  const { reEdit, loading } = useReEditParams();

  return (
    <>
      {isSuccess && (
        <section className={styles.graphBatchMultipleIcon}>
          {batchLength > 1 && (
            <Tag color={defaultBackgroundTagAmount} className={styles.tag}>
              {batchLength}张
              {isUpscalerSuccess && <UltraHdBoldFill className={styles.icon} />}
            </Tag>
          )}
          {batchLength === 1 && isUpscalerSuccess && (
            <Tag color={defaultBackgroundTagAmount} className={styles.tag}>
              <UltraHdBoldFill
                className={styles.icon}
                style={{ marginLeft: 0 }}
              />
            </Tag>
          )}
          {isPublish && (
            <Tag color={defaultBackgroundTagAmount} className={styles.tag}>
              已发布
            </Tag>
          )}
        </section>
      )}
      <div className={styles.graphBatchMask}>
        <PermissionEnvWrapper
          includesOrigin={[AppOrigin.Whee]}
          extraPermission={
            isSuccess && hasValidGraph && !hiddenKeys.includes('collect')
          }
        >
          <CollectionSwitch
            isCollected={isCollect ?? false}
            className={classNames(styles.maskBtn, styles.collectionSwitch)}
            onChange={(collected, event) => {
              event.preventDefault();
              event.stopPropagation();

              onCollect?.(id, collected);
            }}
          />
        </PermissionEnvWrapper>
        <div className={styles.actions}>
          <PermissionEnvWrapper
            includesOrigin={[AppOrigin.Whee]}
            extraPermission={
              isSuccess &&
              hasValidGraph &&
              !isPublish &&
              !hiddenKeys.includes('publish')
            }
          >
            <Tooltip title="发布">
              <Button
                icon={<PublishBold />}
                type="text"
                className={styles.maskBtn}
                onClick={popEvent(onPublish?.bind(null, id))}
              />
            </Tooltip>
          </PermissionEnvWrapper>
          {!hiddenKeys.includes('reEdit') && (
            <Tooltip title="再次编辑">
              <Button
                loading={loading}
                icon={<RedrawBold />}
                type="text"
                className={styles.maskBtn}
                onClick={popEvent(() => {
                  if (loading) return;
                  reEdit(id, { seed: getSeed(batch) });
                  onReEdit?.bind(null, id);
                })}
              />
            </Tooltip>
          )}

          {!hiddenKeys.includes('download') && isSuccess && hasValidGraph && (
            <Tooltip title="下载">
              <Button
                icon={<DownloadBold />}
                type="text"
                className={styles.maskBtn}
                loading={downloading}
                onClick={popEvent(onDownload?.bind(null, id, setDownloading))}
              />
            </Tooltip>
          )}
          {!hiddenKeys.includes('remove') && !isLoading && (
            <Tooltip title="删除">
              <Button
                icon={<TrashCanBold />}
                type="text"
                className={styles.maskBtn}
                onClick={popEvent(onRemove?.bind(null, id))}
              />
            </Tooltip>
          )}
        </div>
      </div>
    </>
  );
}

export default function GraphBatch(props: GraphBatchProps) {
  const { id, batch, time, loadingStatus } = props;
  const [isError, setIsError] = useState(false);

  const { displaySrc, isUpscalerIng, isUpscalerSuccess } = useMemo(() => {
    return {
      // 展示用图片地址
      displaySrc: getDisplayGraph(batch)?.src,
      // 是否有正在超分
      isUpscalerIng: batch.some((item) => {
        return item.upscalerInfo?.status === UpscalerTaskStatus.GENERATING;
      }),
      // 是否有超分成功的
      isUpscalerSuccess: batch.some((item) => {
        return item.upscalerInfo?.status === UpscalerTaskStatus.SUCCESS;
      })
    };
  }, [batch]);

  const thumbnail = useMemo(
    () =>
      displaySrc &&
      toAtlasImageMogr2URL(displaySrc, {
        thumbnail: { type: 'size', width: 350, height: 350 }
      }),
    [displaySrc]
  );

  const { openRefreshModal } = useApp();

  const timestamp = thumbnail?.split('&t=')[1];

  const handleError = () => {
    setIsError(true);
    // 图片过期弹窗
    if (isTimestampExpired(timestamp || '')) {
      openRefreshModal();
    }
  };

  // 图片加载错误，只显示底图，无提示语
  if (isError) {
    return (
      <div className={styles.graphBatch}>
        <div className={classNames(styles.invisible, 'invisible')}>
          <Image src={imageError} preview={false} width={68} height={60} />
          <span>图片加载失败</span>
          <span>请刷新页面</span>
        </div>
      </div>
    );
  }

  if (loadingStatus === LoadingStatus.LOADING) {
    return (
      <div className={styles.graphBatch}>
        <Loading>
          <CountUp
            startValue={time ? Math.round(+new Date() / 1000) - time : 0}
            className={styles.countUp}
          />
        </Loading>
        <GraphBatchMask
          {..._.pick(props, [
            'id',
            'batch',
            'loadingStatus',
            'isPublish',
            'onRemove',
            'onReEdit',
            'hiddenActionKeys'
          ])}
          batchLength={batch.length}
        />
      </div>
    );
  }

  if (
    loadingStatus === LoadingStatus.FAILED ||
    loadingStatus === LoadingStatus.IRREGULARITY ||
    loadingStatus === LoadingStatus.TIMEOUT
  ) {
    return (
      <div className={styles.graphBatch}>
        <LoadFailed type={loadingStatus} />
        <GraphBatchMask
          {..._.pick(props, [
            'id',
            'batch',
            'loadingStatus',
            'onRemove',
            'onReEdit',
            'hiddenActionKeys'
          ])}
          batchLength={batch.length}
        />
      </div>
    );
  }

  return (
    <div className={styles.graphBatch}>
      <div
        className={styles.graphBatchContent}
        // HACK 没有可展示的图片则不可点击进入
        onClick={() => displaySrc && props.onClick?.(id)}
      >
        {!displaySrc ? (
          <LoadFailed />
        ) : !isUpscalerIng ? (
          <Image
            src={thumbnail}
            onError={handleError}
            preview={{ visible: false, mask: null }}
            className={styles.graphBatchImage}
          />
        ) : (
          <Image
            src={thumbnail}
            style={{ filter: 'blur(12px)' }}
            preview={{
              visible: false,
              mask: (
                <div className="upscaler-mask">
                  <Loading
                    type="light"
                    children={
                      <div className="upscaler-text">
                        云端算力正在努力计算，请耐心等待・・・
                      </div>
                    }
                  />
                </div>
              )
            }}
            onError={handleError}
            className={styles.graphBatchImage}
          />
        )}
        {/* 超分生成中不展示操作 */}
        {!isUpscalerIng && (
          <GraphBatchMask
            {..._.pick(props, [
              'id',
              'isPublish',
              'isCollect',
              'batch',
              'loadingStatus',
              'onRemove',
              'onReEdit',
              'onPublish',
              'onDownload',
              'onCollect',
              'hiddenActionKeys'
            ])}
            // HACK 没有可展示的图片则不显示标签
            batchLength={displaySrc ? batch.length : 0}
            isUpscalerSuccess={isUpscalerSuccess}
          />
        )}
      </div>
    </div>
  );
}
