import { useSyncMemberDescErrorHandler } from '@/hooks/useMember';
import { atom, useRecoilState, useResetRecoilState } from 'recoil';
import {
  createUpscalerImageTask as createUpscalerImageTaskApi,
  fetchUpscalerTaskByIds
} from '@/api';
import {
  CreateUpscalerImageTaskBody,
  MediaType,
  MtccFuncCode,
  UpscalerTaskStatus
} from '@/api/types';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { useContext, useState } from 'react';
import { useLooper } from '@/hooks';
import { GraphBatch } from '@/types';
import { ImagesContainerContext } from '../context';
import produce from 'immer';

/**
 * 超分任务的 id
 */
const imageUpscalerTaskIds = atom<string[]>({
  key: 'imageUpscalerTaskIds',
  default: []
});

export function useImageUpscalerState() {
  const [upscalerLoadingIds, setUpscalerLoadingIds] =
    useRecoilState(imageUpscalerTaskIds);
  const resetUpscalerLoadingIds = useResetRecoilState(imageUpscalerTaskIds);
  const handleError = useSyncMemberDescErrorHandler(
    undefined,
    MtccFuncCode.FuncCodeImageMagicsrHDWithRight
  );
  const { updateMeiDouBalance } = useMeiDouBalance();

  /** 创建超分任务 */
  const createUpscalerImageTask = async (
    createParams: CreateUpscalerImageTaskBody
  ) => {
    try {
      const { id } = await createUpscalerImageTaskApi({
        ...createParams,
        functionName: MtccFuncCode.FuncCodeImageMagicsrHDWithRight,
        mediaType: MediaType.Photo,
        resMediaType: MediaType.Photo
      });
      const nextIds = produce(upscalerLoadingIds, (draft) => {
        draft.push(id);
      });
      setUpscalerLoadingIds(nextIds);

      return { id };
    } catch (error) {
      handleError(error);
    } finally {
      updateMeiDouBalance();
    }
  };

  return {
    upscalerLoadingIds,
    setUpscalerLoadingIds,
    resetUpscalerLoadingIds,
    createUpscalerImageTask
  };
}

/**
 * 轮询获取超分任务
 */
export function useFetchUpscalerTaskLooper() {
  const { graphBatch, setGraphBatch } = useContext(ImagesContainerContext);
  const [upscalerLoadingIds, setUpscalerLoadingIds] =
    useRecoilState(imageUpscalerTaskIds);
  const [completedTask, setCompletedTask] =
    useState<Record<string, GraphBatch>>();
  const { updateMeiDouBalance } = useMeiDouBalance();

  useLooper({
    callback: async () => {
      const result = await fetchUpscalerTaskByIds({
        ids: upscalerLoadingIds.join(',')
      });
      // 已经处理完的任务，替换原数据
      const doneTask = result.filter(
        ({ status }) =>
          status === UpscalerTaskStatus.SUCCESS ||
          status === UpscalerTaskStatus.FAILURE
      );

      // 如果有失败的，更新美豆
      if (
        doneTask.some(({ status }) => status === UpscalerTaskStatus.FAILURE)
      ) {
        updateMeiDouBalance();
      }

      const nextGraphBatchData = produce(graphBatch, (draft) => {
        doneTask.forEach((item) => {
          // 通过超分id匹配 要更新的batch
          let doneIndex = draft[item.wheeMsgId].batch.findIndex((batchItem) => {
            return batchItem.upscalerInfo?.id === item.id;
          });

          if (item.status === UpscalerTaskStatus.SUCCESS) {
            draft[item.wheeMsgId].batch[doneIndex].src = item.resultImage.url;
            draft[item.wheeMsgId].batch[doneIndex].urlWatermark =
              item.resultImage?.urlWatermark;
            draft[item.wheeMsgId].batch[doneIndex].hdOriginUrl =
              item.hdOriginUrl;
            draft[item.wheeMsgId].batch[doneIndex].hadSatisfied =
              item.resultImage.hadSatisfied;
          }

          draft[item.wheeMsgId].batch[doneIndex].upscalerInfo!.status =
            item.status;
        });
      });
      setGraphBatch(nextGraphBatchData);
      setCompletedTask(nextGraphBatchData);

      // 未完成的 同步更新LoadingIds
      const loadingTask = result.filter(
        ({ status }) => status === UpscalerTaskStatus.GENERATING
      );
      const loadingIds = loadingTask.map(({ id }) => id);

      setUpscalerLoadingIds(loadingIds);
    },
    trigger: () => upscalerLoadingIds.length > 0,
    duration: 4096
  });

  return completedTask;
}
