import type { GraphBatch } from '@/types/draft';

import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import _ from 'lodash';

import { FilterType, SortFilterType, Graph } from '@/types';
import { AppOrigin, appOrigin } from '@/constants';

interface ImagesContainerInitialProps {
  graphBatch: Record<string, GraphBatch>;
  loadingIds: Set<string>;
  ids: string[];
  imageWatermark: boolean;
}

export function useImagesContainerProvider(
  initialState: ImagesContainerInitialProps
) {
  const isWhee = appOrigin === AppOrigin.Whee;
  const [cursor, setCursor] = useState<string | null | undefined>(null);
  const [graphBatch, setGraphBatch] = useState(initialState.graphBatch);
  const [loadingIds, setLoadingIds] = useState(initialState.loadingIds);
  const [ids, setIds] = useState(initialState.ids);
  const [scrollTop, setScrollTop] = useState(0);
  const { id: previewId } = useParams();
  const [imageWatermark, setImageWatermark] = useState(
    initialState.imageWatermark
  );
  // 图片源字段区分
  const [imageSourceKey, setImageSourceKey] = useState<'src' | 'urlWatermark'>(
    'src'
  );

  const [filterType, setFilterType] = useState<FilterType>(FilterType.ALL);
  const [keyword, setKeyword] = useState<string>('');
  const [batchOrigin, setBatchOrigin] = useState<Graph[]>([]);
  const [historyActiveIndex, setHistoryActiveIndex] = useState<number>(0);
  const [sortFilterType, setSortFilterType] = useState<SortFilterType>(
    SortFilterType.DESC
  );

  useEffect(() => {
    setImageSourceKey(isWhee && imageWatermark ? 'urlWatermark' : 'src');
  }, [imageWatermark, isWhee]);

  return {
    cursor,
    setCursor,

    imageWatermark,
    setImageWatermark,

    imageSourceKey,

    graphBatch,
    setGraphBatch: (currentGraphBatches: Record<string, GraphBatch>) => {
      setGraphBatch((graphBatch) =>
        Object.assign({}, graphBatch, currentGraphBatches)
      );
    },
    updateGraphBatch: (graphBatch: GraphBatch) => {
      setGraphBatch((graphBatches) =>
        Object.assign({}, graphBatches, {
          [graphBatch.id]: graphBatch
        })
      );
    },

    ids,
    addIds: (currentIds: string[]) => {
      setIds((ids) => _.union(ids, currentIds));
    },
    insertIds: (currentIds: string[]) => {
      setIds((ids) => _.union(currentIds, ids));
    },
    removeId: (id: string) => {
      setIds((ids) => ids.filter((graphBatchId) => id !== graphBatchId));
    },

    loadingIds,
    addLoadingIds: (currentLoadingIds: string[]) => {
      setLoadingIds(
        (loadingIds) =>
          new Set([...currentLoadingIds, ...Array.from(loadingIds)])
      );
    },
    setLoadingIds: (loadingIds: string[]) => {
      setLoadingIds(new Set(loadingIds));
    },

    previewId,

    scrollTop,
    setScrollTop,

    filterType,
    setFilterType,

    sortFilterType,
    setSortFilterType,

    keyword,
    setKeyword,

    clearGraphBatch: () => {
      setIds([]);
    },
    batchOrigin,
    setBatchOrigin,
    historyActiveIndex,
    setHistoryActiveIndex
  };
}
