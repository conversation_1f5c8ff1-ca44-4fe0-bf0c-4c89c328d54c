@import '~@/styles/variables.less';

.draft-container {
  position: relative;
  z-index: 2;
  height: calc(100vh - @layout-header-height - @size);
  overflow: hidden;
  border: none;
  border-radius: @border-radius-lg;
  background: @background-system-frame-floatpanel;
  margin-right: @size;
  box-shadow: @level-2;

  :global(.@{ant-prefix}-spin-blur) {
    &::after {
      opacity: 0 !important;
    }
  }

  .upscale-box {
    position: relative;

    .upscaleCorner {
      position: absolute;
      top: -4px;
      right: -4px;
    }
  }

  .badge-icon {
    width: 28px;
    height: 16px;
    border-radius: 4px;
    background: linear-gradient(91deg, #ffebc2 1.27%, #ffbfff 98.73%);
    margin-left: 3px;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      color: var(--content-navBarTag, #1c1d1f);
      font-size: 12px;
      transform: scale(0.84);
      font-style: normal;
      font-weight: 600;
    }
  }

  .pop-box {
    position: absolute;
    left: -162px;
    width: 148px;
    height: 89px;
    border-radius: 10px;
    background: @base-black-opacity-50;
    padding: 4px;

    .pop-item-box {
      display: flex;
      padding: 2px 0 2px 10px;
      height: 40px;
      border-radius: 10px;
      cursor: pointer;

      &:hover {
        background: var(--_base-black_opacity_50, rgba(0, 0, 0, 0.5));
      }

      .item-icon {
        svg {
          width: 22px;
          height: 22px;
          color: #fff;
        }
      }

      .item-content {
        margin-left: 6px;

        .item-title {
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 16px;
          color: @content-btn-primary;
          display: flex;
          justify-content: flex-start;
          align-items: center;
        }

        .item-meidou {
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .icon {
            width: 10px;
            height: 10px;
          }

          .desc {
            // 有些游览器不支持小于12px的字体大小 使用scale将12缩小到9
            font-size: 12px;
            transform: scale(0.75);
            font-style: normal;
            font-weight: 300;
            color: @content-btn-primary;
            transform-origin: 6px center;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .scroll {
    margin: @size-md;
    margin-top: @size-sm;
    margin-right: 0;
    padding-right: 10px;
    overflow: auto;
    height: calc(100% - @size-lg);
    display: flex;
    flex-direction: column;

    &::-webkit-scrollbar-thumb {
      background-color: transparent;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    &:hover {
      &::-webkit-scrollbar-thumb {
        background-color: @background-system-scrollbar;

        &:hover {
          background: @background-system-scrollbar-active;
        }
      }
    }
  }

  &-header {
    position: sticky;
    top: 0;
    background-color: @background-system-frame-floatpanel;
    padding-bottom: @size-sm;
    z-index: 2;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex: 0 0 auto;
  }

  &-waterfall {
    position: relative;
    min-height: calc(100% - 45px);
  }

  .filter {
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
    height: 20px;
    z-index: 1000;
    background: @background-system-frame-floatpanel;
    margin: 0 @size-sm;
    // box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    filter: blur(4px);
  }

  &-back:global(.@{ant-prefix}-btn) {
    position: absolute;
    top: @size;
    left: @size;
  }

  &-actions {
    position: absolute;
    top: @size;
    right: @size;

    :global .@{ant-prefix}-btn.@{ant-prefix}-btn-icon-only {
      display: flex;
      align-items: center;
      justify-content: center;
      width: calc(@size + @size-sm);
      height: calc(@size + @size-sm);
      background-color: @background-hover-tips;
      color: @content-btn-primary;
      border: none;

      path {
        fill: @content-btn-primary;
      }
    }
  }

  &-rate {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    width: calc(100% - 200px);
    z-index: 5;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :global(.@{ant-prefix}-image-mask) {
    &:hover {
      :local .comparing-action {
        opacity: 1;
      }
    }
  }

  &-placeholder:global(.@{ant-prefix}-empty) {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -54%);

    :global .@{ant-prefix}-empty-image {
      width: 144px;
      height: 144px;
    }

    :global .@{ant-prefix}-empty-description {
      color: @content-system-tertiary;
    }
  }
}

.tag {
  position: absolute;
  left: @size-xs;
  top: @size-xs;
}

.comparing-action {
  position: absolute !important;
  right: @size-xs;
  bottom: @size-xs;
  opacity: 0;
}

.comparing-mask {
  background-color: @background-system-frame-floatpanel;
}

.no-transition {
  transition: none;
}

.upscaler-corner {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  background: @background-tag-amount;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  color: @base-white-opacity-100;
  z-index: 3;

  .close-btn {
    width: 12px;
    height: 12px;
    margin-left: 4px;
    cursor: pointer;
  }
}

.confirm-modal {
  :global {
    .ant-modal-body > .ant-modal-confirm-body-wrapper {
      .anticon {
        color: @background-btn-ai;
      }

      .footer-box {
        margin-top: 12px;
        display: flex;
        justify-content: flex-end;
      }

      .ok-btn {
        margin-left: 8px;
        background: @background-btn-ai;
      }
    }
  }
}

.label-loading {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9;
  width: 100%;
  height: 100%;
  background: transparent;

  .jumping {
    span {
      width: 12px;
      height: 12px;
    }
  }
}

.box-padding {
  padding: @size 44px;
}

.action-button:global(.@{ant-prefix}-btn):not(.@{ant-prefix}-btn-icon-only) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 68px;
  height: 56px;
  border: 0;
  padding: 0;
  border-radius: @size-xs;
  color: #fff;
  background: rgba(0, 0, 0, 0.5);
  gap: @size-xxs;

  & > span {
    flex: 0 0 auto;

    &:not(:global(.@{ant-prefix}-btn-icon)) {
      // 有些游览器不支持小于12px的字体大小 使用scale将12缩小到10
      font-size: 12px;
      transform: scale(0.8333);
    }
  }

  & > :global(.@{ant-prefix}-btn-icon) {
    position: relative;
    top: 2px;
    font-size: @size-ms;

    &:not(:last-child) {
      margin-right: 0;
    }
  }

  &:not(:disabled):not(.ant-btn-disabled):hover {
    color: #fff;
  }
}

.search {
  width: 214px !important;
  height: 32px;
}

.ultra_clear_body {
  :global(.ant-modal-content) {
    padding: 24px 16px 16px 16px !important;
  }

  :global(.ant-modal-body) {
    color: #1c1d1f;

    h3 {
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      margin-bottom: 16px;
    }

    video {
      width: 220px;
      border-radius: 8px;
      margin: 0 auto;
      display: block;
      margin-bottom: 20px;
    }

    .detailBtnS {
      margin-top: 24px;

      :global {
        .footer-box {
          display: flex;
          justify-content: space-between;

          .ok-btn {
            background: @background-btn-ai;

            &:hover {
              background: @background-btn-ai-hover;
            }
          }

          .ant-btn {
            width: 148px;
            height: 36px;
          }
        }
      }
    }

    .detailBox {
      text-align: center;
      font-size: 16px;
      font-weight: 500;

      .detailCheck {
        color: #616366;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        margin-top: 12px;
        /* 142.857% */
      }
    }
  }
}
