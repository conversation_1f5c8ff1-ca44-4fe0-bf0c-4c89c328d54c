import type { Graph, GraphBatch, To3DTaskStatus } from '@/types/draft';
import type { initialValues } from './PublishModal';

import GraphPreview from '@/components/GraphPreview';
import RateAction from '@/components/RateAction';
import PublishModal, { PublishModalRef } from './PublishModal';
import {
  Button,
  Modal,
  message,
  App,
  type ButtonProps,
  Spin,
  Tooltip,
  Checkbox
} from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import {
  useRemoveImage,
  useReEditParams,
  useGetImagePreview,
  useFetchTaskLooper,
  useCreateLoadingTask,
  ImagesContainerContext,
  useImageUpscalerState,
  useFetchUpscalerTaskLooper
} from '.';
import { useEffect, useRef, useState, useContext } from 'react';
import { CompareGraphAction } from '@/components/GraphPreview/CompareGraphAction';
import { Actions, CommonButton } from './Actions';
import { ReportAction } from './ReportAction';
import { AppModule, generateRouteTo, trackEvent } from '@/services';
import { useImageExtension } from '@/hooks/useImageExtension';

import styles from './styles.module.less';
import { GraphStatus, LoadingStatus } from '@/types/draft';
import { appOrigin, AppOrigin, RANDOM_SEED_VALUE } from '@/constants';
import produce from 'immer';
import { downloadFile } from '@/utils/blob';
import {
  DraftType,
  ImageExtensionDoRequest,
  MediaType,
  MtccFuncCode,
  RateType,
  UpscalerTaskStatus
} from '@/api/types';
import {
  useMembershipDesc,
  useSyncMemberDescErrorHandler
} from '@/hooks/useMember';
import {
  BackArrowBold,
  CrossBold,
  LightingCircleBoldFill,
  ProCircleBoldFill,
  UltraHdBoldFill
} from '@meitu/candy-icons';
import { Loading } from '@/components';
import classNames from 'classnames';
import { closeFailureTip, createDeeperDesignTask, downloadImage } from '@/api';
import { useImagePartialRepaint } from '@/hooks/useImagePartialRepaint';
import { ImagePartialRepaintDoRequest } from '@/api/types/imagePartialRepain';
import { PermissionEnvWrapper } from '../PermissionEnvWrapper';
import {
  useCachedImageUpscale,
  useCachedImgToImgFields
} from '@/hooks/useCachedPageFields';
import { ControlNetZoomMode } from '@/types';
import { getSource } from '@/utils';
import DeeperDesignModal from './DeeperDesignModal';
import { useApp } from '@/App';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import AutoSizer from 'react-virtualized-auto-sizer';
import { useImageEraser } from '@/hooks/useImageEraser';
import HistoryList from './HistoryList';
import { Meidou } from '@/icons';
import { IA_ULTRA_CLEAR_MESSAGE_KEY } from '@/services/account/constants';
import { getLocalStorageItem, setLocalStorageItem } from '@meitu/util';
import { UpscaleCompare } from '../GraphPreview/UpscaleCompare';

export const useDraftGraph = () => {
  const getDraftGraph = useGetImagePreview();

  return {
    batch: (getDraftGraph?.batch ?? []) as Graph[],
    isPublish: getDraftGraph?.isPublish ?? false,
    size: getDraftGraph?.size,
    canUseSame: true,
    initImage: getDraftGraph?.params?.initImages?.[0],
    zoomMode: getDraftGraph?.params?.resizeMode,
    taskCategory: getDraftGraph?.taskCategory
  };
};

/** 跳往AI扩图事件 !! 多个地方用到*/
export const useToImageExtension = () => {
  const navigate = useNavigate();
  const path = generateRouteTo(AppModule.ImageExtension, {
    source: getSource()
  });
  const { goEditorStep } = useImageExtension();
  const { message } = App.useApp();

  return async (src?: string, msgId?: string) => {
    if (!src) {
      return;
    }
    try {
      await goEditorStep(src, { msgId } as ImageExtensionDoRequest);
      navigate(path);
    } catch (error) {
      message.error('AI扩图失败, 图片可能过期！');
    }
  };
};

export const useToImageEraser = () => {
  const navigate = useNavigate();
  const path = generateRouteTo(AppModule.ImageEraser, {
    source: getSource()
  });
  const { goEditorStepBySrc } = useImageEraser();

  return (src?: string, msgId?: string) => {
    if (!src) {
      return;
    }
    try {
      goEditorStepBySrc({ src, msgId });
      navigate(path);
    } catch (error) {
      console.log(error);
    }
  };
};

export const useToImg2Img = () => {
  const navigate = useNavigate();
  const path = generateRouteTo(AppModule.ImageToImage);
  const { setFlushParamsEditorCached, setParamsEditorCached } =
    useCachedImgToImgFields();

  return async (src?: string, msgId?: string) => {
    if (!src) return;

    setFlushParamsEditorCached({
      image: { key: src, strength: 80, zoomMode: ControlNetZoomMode.Cropping }
    });

    setParamsEditorCached({
      msgId
    });

    navigate(path);
  };
};

/** 跳往AI改图事件  !! 多个地方用到 */
export const useToImagePartialRepaint = () => {
  const navigate = useNavigate();
  const path = generateRouteTo(AppModule.ImagePartialRepaint, {
    source: getSource()
  });
  const { goEditorStep } = useImagePartialRepaint();
  const { message } = App.useApp();

  return async (
    src?: string,
    apiParams?: ImagePartialRepaintDoRequest | undefined
  ) => {
    if (!src) {
      return;
    }
    try {
      await goEditorStep(src, apiParams);
      navigate(path);
    } catch (error) {
      message.error('AI扩图失败, 图片可能过期！');
    }
  };
};

// 跳转AI超清
export const useToImageUpscale = () => {
  const navigate = useNavigate();
  const path = generateRouteTo(AppModule.ImageUpscale, {
    source: getSource()
  });
  const { setParamsEditorCached } = useCachedImageUpscale();

  return (src?: string, msgId?: string) => {
    if (!src) {
      return;
    }
    try {
      setParamsEditorCached({ imageFile: src, msgId });
      navigate(path);
    } catch (error) {
      message.error('AI超清失败, 图片可能过期！');
    }
  };
};

interface GraphPreviewContainerProps {
  /** 是否有同步至画廊操作按钮的权限 */
  hasPublishGalleryControlAccess?: boolean;

  /** 是否有允许使用画面控制图片操作按钮的权限 */
  hasControlnetImageControlAccess?: boolean;

  /** 下载事件 */
  onDownload?: (id: string, extra?: { downloadPicType?: string }) => void;
  /** 预览事件 */
  onPreview?: (id: string) => void;
  /** 打开发布弹窗事件 */
  onOpenPublishModal?: (id: string) => void;
  /** 发布事件 */
  onPublish?: (value: initialValues) => void;
  /** 发布成功事件 */
  onPublishSuccess?: (value: initialValues) => void;
  /** 重新编辑事件 */
  onReEditParams?: (id: string) => void;
  /** 点击AI改图 */
  onImagePartialRepaintClick?: (id: string) => void;
  /** 点击AI无痕消除 */
  onImageEraserClick?: (id: string) => void;
  /** 点击图像扩展 */
  onImageExtensionClick?: (id: string) => void;
  /** 删除 */
  onRemove?: (id: string) => void;
  /** 超分 */
  onUpscaler?: (type: string, id: string) => void;
  /** 图生图 */
  onImageToImage?: (id: string) => void;
  /** 图片创建成功 */
  onImageCreated?: (images: GraphBatch[]) => void;

  hiddenActionKeys?: (
    | 'remove' // 删除
    | 'reEdit' // 再次编辑
    | 'publish' // 发布
    | 'download' // 下载
    | 'highDefinition' // 超分
    | 'repaint' // AI改图
    | 'eraser' // AI无痕消除
    | 'expand' // AI扩图
    | 'img2img' //图生图
    | 'deeperDesign'
  )[]; //延伸创作 // 图生图
  /** 额外节点 */
  extra?: React.ReactNode;

  graphPreviewClassName?: string;
}

export function GraphPreviewContainer({
  hiddenActionKeys: hiddenKeys = [],
  ...props
}: GraphPreviewContainerProps) {
  const { id = '' } = useParams();

  const {
    batch,
    isPublish,
    size,
    canUseSame,
    initImage,
    zoomMode,
    taskCategory
  } = useDraftGraph();

  const publishModalRef = useRef<PublishModalRef>(null);
  const [activeGraph, setActiveGraph] = useState<[Graph, number | undefined]>();
  const [downloading, setDownloading] = useState(false);
  const [isDeeperDesignModalOpen, setDeeperDesignModalOpen] = useState(false);
  const [isOrigin, setIsOrigin] = useState(false);

  const {
    goBack,
    graphBatch,
    updateGraphBatch,
    insertIds,
    imageSourceKey,
    batchOrigin,
    setBatchOrigin
  } = useContext(ImagesContainerContext);
  const [deeperPrompt, setDeeperPrompt] = useState('');
  const { updateMeiDouBalance } = useMeiDouBalance();
  const navigate = useNavigate();
  const [messageApi, messageContextHolder] = message.useMessage();
  const [modal, contextHolder] = Modal.useModal();
  const removeDraft = useRemoveImage();
  const { reEdit, loading } = useReEditParams();

  const [showPop, setShowPop] = useState(false);

  const completedTask = useFetchUpscalerTaskLooper();
  useCreateLoadingTask();
  const completedLooperTask = useFetchTaskLooper();

  const { isVipCurrent } = useMembershipDesc();
  const { createUpscalerImageTask } = useImageUpscalerState();

  const { openRefreshModal } = useApp();
  const handleError = useSyncMemberDescErrorHandler();

  const [showAIClearModal, setShowAIClearModal] = useState(false);
  const [isCheckIAClearMessage, setIsCheckIAClearMessage] = useState(false);
  const [upscaleLoading, setUpscaleLoading] = useState(false);

  const GraphSuccess = activeGraph?.[0]?.status === GraphStatus.SUCCESS;

  // 及时同步更新
  useEffect(
    () => {
      if (!id || !activeGraph?.[0]?.src || !completedTask) {
        return;
      }
      let activeIndex = activeGraph[1] ?? 0;
      setActiveGraph([completedTask[id].batch[activeIndex], activeIndex]);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [completedTask]
  );
  useEffect(() => {
    if (completedLooperTask?.length) {
      props.onImageCreated?.(completedLooperTask);
      // onImageCreatedByCtx?.(completedLooperTask);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [completedLooperTask]);

  const confirmRemove = () => {
    props?.onRemove?.(id);
    if (!id || !activeGraph?.[0].src) {
      return;
    }

    modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确认删除当前图片',
      okButtonProps: {
        danger: true
      },
      okText: '删除',
      cancelText: '取消',
      onOk: async () => {
        const res = await removeDraft(
          id,
          activeGraph[0].src as string,
          activeGraph[1]
        );

        if (res) {
          const activeIndex = activeGraph[1] ?? 0;
          const index = activeIndex === res.nextBatch.length ? 0 : activeIndex;
          setActiveGraph([res.nextBatch[index], index]);
        }
      }
    });
  };

  // 分辨率提升
  const confirmUpscaler = () => {
    if (!id || !activeGraph?.[0].src) {
      return;
    }
    const tokenViaLegacyCache = getLocalStorageItem<string>(
      IA_ULTRA_CLEAR_MESSAGE_KEY
    );
    // 选择了不再提示，直接扣豆生成
    if (!tokenViaLegacyCache) {
      setShowAIClearModal(true);
    } else {
      clearHandle();
    }
    setShowPop(false);
  };
  const clearHandle = async () => {
    props?.onUpscaler?.('ai_ultra_hd_quick', id);
    setUpscaleLoading(true);

    // 超分接口，根据code判断是否充值弹窗
    const res = await createUpscalerImageTask({
      msgId: id,
      imageFile: String(activeGraph?.[0].src),
      functionName: MtccFuncCode.FuncCodeImageMagicsrHDWithRight,
      mediaType: MediaType.Photo,
      resMediaType: MediaType.Photo
    });

    setShowAIClearModal(false);
    setUpscaleLoading(false);

    if (!res) return;

    // 设置超分状态ing，给当前图片绑定超分id
    let activeIndex = activeGraph?.[1] ?? 0;
    let nextGraphBatchData = produce(graphBatch, (draft) => {
      draft[id].batch[activeIndex].upscalerInfo!.status =
        UpscalerTaskStatus.GENERATING;
      draft[id].batch[activeIndex].upscalerInfo!.id = String(res?.id);
    });
    updateGraphBatch(nextGraphBatchData[id]);

    setActiveGraph([nextGraphBatchData[id].batch[activeIndex], activeIndex]);
  };
  const Footer = (_props: { cancel: () => void; isCheck?: boolean }) => {
    return (
      <div className="footer-box">
        <Button
          onClick={() => {
            trackEvent('quick_hd_popup_click', {
              click_type: 'cancel',
              is_check_box: isCheckIAClearMessage ? 1 : 0
            });

            _props.cancel();
          }}
          disabled={upscaleLoading}
        >
          取消
        </Button>
        <Spin spinning={upscaleLoading} size="small">
          <Button
            type="primary"
            className="ok-btn"
            onClick={async () => {
              trackEvent('quick_hd_popup_click', {
                click_type: 'confirm',
                is_check_box: isCheckIAClearMessage ? 1 : 0
              });

              clearHandle();
              if (_props.isCheck)
                setLocalStorageItem(IA_ULTRA_CLEAR_MESSAGE_KEY, 'true');
            }}
          >
            确定
          </Button>
        </Spin>
      </div>
    );
  };

  const onDownload = async () => {
    if (!activeGraph?.[0].src) {
      return;
    }
    // 如果超分后，选中的对比是原图。那么下载的图也是超分前的原图
    const url = isOrigin
      ? activeGraph?.[0]?.hdOriginUrl ?? ''
      : activeGraph[0]?.[imageSourceKey] || activeGraph[0].src;

    setDownloading(true);
    try {
      await downloadFile(url);
      messageApi.success('下载成功');
      downloadImage({ msgId: id, imageUrl: url });
    } catch (error) {
      messageApi.error('下载失败');
    }
    setDownloading(false);

    props.onDownload?.(
      id,
      // 只有超清过的，才会区分此字段
      upscalerSuccess
        ? {
            downloadPicType: isOrigin ? 'original' : 'results'
          }
        : {}
    );
  };

  const onChange = (graph: Graph, index?: number) => {
    // 切换的时候重置原图对比状态
    setIsOrigin(false);
    setActiveGraph([graph, index]);
  };

  const onDeeperDesignCancel = () => {
    setDeeperDesignModalOpen(false);
  };

  const onDeeperdesignOpen = () => {
    setDeeperPrompt(graphBatch[id].params?.prompt || '');
    setDeeperDesignModalOpen(true);
  };

  const onDeeperDesignCommit = async (): Promise<void> => {
    const activeGraphInfo = activeGraph?.[0];
    if (!id || !activeGraphInfo || !activeGraphInfo.src) {
      message.error('未选择图片。');
      return;
    }
    try {
      const res = await createDeeperDesignTask({
        msgId: id,
        image: activeGraphInfo.src,
        imageSeed: activeGraphInfo.seed ?? RANDOM_SEED_VALUE,
        prompt: deeperPrompt,
        functionName: MtccFuncCode.FuncCodeText2Image,
        mediaType: MediaType.Text,
        resMediaType: MediaType.Photo,
        customBatchSize: '4'
      });

      if (!res) return;
      updateMeiDouBalance();
      const previewId = res?.[0]?.id ?? '';
      reEdit(id, { prompt: deeperPrompt });
      // addGraph
      const newGraphBatch: GraphBatch = {
        id: previewId,
        batch: [
          {
            status: GraphStatus.AUDITING
          }
        ],
        size: [
          graphBatch[id].params?.width ?? 0,
          graphBatch[id].params?.height ?? 0
        ],
        loadingStatus: LoadingStatus.LOADING
      };
      let nextGraphBatchData = produce(graphBatch, (draft) => {
        return {
          [previewId]: newGraphBatch,
          ...draft
        };
      });
      updateGraphBatch(nextGraphBatchData[previewId]);
      insertIds([previewId]);
      // routerTo
      setDeeperDesignModalOpen(false);
      navigate(
        generateRouteTo(AppModule.TextToImagePreviewerLoading, previewId),
        {
          replace: true
        }
      );
    } catch (e: any) {
      handleError(e);
    }
  };

  /** 评分成功后，更新缓存中的数据 */
  const onRateSuccess = async () => {
    if (!activeGraph) return;
    let activeIndex = activeGraph[1] ?? 0;
    let nextGraphBatchData = produce(graphBatch, (draft) => {
      draft[id].batch[activeIndex].hadSatisfied = true;
    });
    updateGraphBatch(nextGraphBatchData[id]);

    // 更新当前active的状态值，控制是否显示评分组件
    setActiveGraph([nextGraphBatchData[id].batch[activeIndex], activeIndex]);
  };

  const upscalerStatus = activeGraph?.[0]?.upscalerInfo?.status;

  const upscalerSuccess = upscalerStatus === UpscalerTaskStatus.SUCCESS;
  const upscalerFailure = upscalerStatus === UpscalerTaskStatus.FAILURE;
  // 超分过程中 再次编辑、下载可用
  const upscalerIng = upscalerStatus === UpscalerTaskStatus.GENERATING;

  // 超分失败后，点击关闭提示，重置超分状态
  const upscalerFailureClicked = async () => {
    await closeFailureTip({
      id: activeGraph?.[0].upscalerInfo?.id || ''
    });

    if (!id || !activeGraph?.[0].src) {
      return;
    }
    let activeIndex = activeGraph[1] ?? 0;
    let nextGraphBatchData = produce(graphBatch, (draft) => {
      draft[id].batch[activeIndex].upscalerInfo!.status =
        UpscalerTaskStatus.INITIAL;
    });

    updateGraphBatch(nextGraphBatchData[id]);
    setActiveGraph([nextGraphBatchData[id].batch[activeIndex], activeIndex]);
  };

  /**
   * 发布按钮 HACK: 设计室暂时隐藏发布按钮
   */
  function PublishAction() {
    const publishVisible = batch.some(
      (graph) => graph?.status === GraphStatus.SUCCESS
    );

    return (
      <PermissionEnvWrapper
        includesOrigin={[AppOrigin.Whee]}
        extraPermission={publishVisible && !isPublish && !upscalerIng}
      >
        <Actions.PublishButton
          onClick={() => {
            props.onOpenPublishModal?.(id);
            publishModalRef.current?.open({
              id,
              // 不发布正在超分的
              graph: batch.filter(
                (item) =>
                  item.upscalerInfo?.status !== UpscalerTaskStatus.GENERATING
              ),
              canUseSame,
              canPostToGallery: props.hasPublishGalleryControlAccess,
              hasPublishGalleryControlAccess:
                props.hasPublishGalleryControlAccess,
              hasControlnetImageControlAccess:
                props.hasControlnetImageControlAccess
            });
          }}
        >
          {isPublish ? '已发布' : '发布'}
        </Actions.PublishButton>
      </PermissionEnvWrapper>
    );
  }

  useEffect(
    () => {
      props.onPreview?.(id);
      setActiveGraph([batch[0], 0]);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [id]
  );

  const toImageExtension = useToImageExtension();
  const toImageEraser = useToImageEraser();
  const toImg2Img = useToImg2Img();
  const toImageUpscale = useToImageUpscale();

  const hasGraphViewable = batch.some(
    ({ status }) => status === GraphStatus.SUCCESS
  );

  useEffect(() => {
    const batchList = hasGraphViewable ? batch : batch?.[0] ? [batch?.[0]] : [];

    const result = batchList.map((item) => {
      return {
        ...item,
        urlWatermark: item?.[imageSourceKey] || item.src
      };
    });
    setBatchOrigin(result);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasGraphViewable, batch, imageSourceKey]);

  // 生成的列表变化时，设置当前activeGraph
  useEffect(() => {
    let activeIndex = activeGraph?.[1] ?? 0;
    setActiveGraph([graphBatch?.[id]?.batch[activeIndex], activeIndex]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [graphBatch]);

  // 图生图的超分 对比的是用户原图与超分结果图
  // const originImg =
  //   activeGraph?.[0]?.upscalerInfo?.id &&
  //   activeGraph?.[0]?.hdOriginUrl &&
  //   [DraftType.TEXT_TO_IMAGE, DraftType.AI_MODEL_IMAGE].includes(
  //     taskCategory as DraftType
  //   )
  //     ? activeGraph?.[0]?.hdOriginUrl
  //     : initImage;
  const originImg = initImage;
  // 是否展示历史列表组件 仅文生图图生图展示历史列表
  const showHistoryList = [
    DraftType.TEXT_TO_IMAGE,
    DraftType.IMAGE_TO_IMAGE
  ].includes(taskCategory as DraftType)
    ? true
    : false;

  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 鼠标移入事件处理函数
  const handleMouseEnter = () => {
    setShowPop(true);
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
  };

  // 鼠标移出事件处理函数
  const handleMouseLeave = () => {
    // 设置延迟隐藏子菜单
    timerRef.current = setTimeout(() => {
      setShowPop(false);
    }, 200);
  };

  // 清除定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // AI超清浮窗
  const renderPop = () => {
    return (
      <div
        className={styles.popBox}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div className={styles.popItemBox} onClick={confirmUpscaler}>
          <LightingCircleBoldFill className={styles.itemIcon} />
          <div className={styles.itemContent}>
            <div className={styles.itemTitle}>
              快捷AI超清
              <div className={styles.badgeIcon}>
                <span>上新</span>
              </div>
            </div>
            <div className={styles.itemMeidou}>
              <Meidou className={styles.icon} />
              <div className={styles.desc}>
                消耗{isVipCurrent ? '1' : '2'}美豆
              </div>
            </div>
          </div>
        </div>
        <div
          className={styles.popItemBox}
          onClick={() => {
            props?.onUpscaler?.('ai_ultra_hd_pro', id);

            toImageUpscale(activeGraph?.[0].src, id);
          }}
        >
          <ProCircleBoldFill className={styles.itemIcon} />
          <div className={styles.itemContent}>
            <div className={styles.itemTitle}>专业AI超清</div>
            <div className={styles.itemMeidou}>
              <Meidou className={styles.icon} />
              <div className={styles.desc}>
                消耗{isVipCurrent ? '2～4' : '4～8'}美豆
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.draftContainer}>
      <Button
        icon={<BackArrowBold />}
        className={styles.draftContainerBack}
        onClick={goBack}
      >
        返回
      </Button>
      {id && (
        <AutoSizer>
          {({ height }) => {
            return (
              <Actions
                buttonRender={({
                  children,
                  className,
                  ...buttonProps
                }: ButtonProps) => {
                  return (
                    <Button
                      {...buttonProps}
                      className={classNames(className, styles.actionButton)}
                    >
                      {children}
                    </Button>
                  );
                }}
                limitHeightWithScale={{
                  // 最高的高度=容器高度-内边距-底部留白
                  maxHeight: height ? height - 70 : 0,
                  transformOrigin: 'right top'
                }}
                showHistoryList={
                  appOrigin === AppOrigin.Whee && showHistoryList
                }
              >
                {!hiddenKeys.includes('publish') && hasGraphViewable && (
                  <PublishAction />
                )}
                <ReportAction id={id} />
                {/* 再次编辑 */}
                {!hiddenKeys.includes('reEdit') && (
                  <Actions.RefreshButton
                    loading={loading}
                    onClick={() => {
                      if (loading) return;
                      reEdit(id, {
                        seed: activeGraph?.[0].seed ?? RANDOM_SEED_VALUE
                      });

                      props.onReEditParams?.(id);
                    }}
                  />
                )}

                {/* 超分*/}
                <PermissionEnvWrapper
                  includesOrigin={[AppOrigin.Whee]}
                  extraPermission={
                    !hiddenKeys.includes('highDefinition') &&
                    !upscalerIng &&
                    !upscalerSuccess &&
                    GraphSuccess
                  }
                >
                  <Spin spinning={upscaleLoading}>
                    <div
                      onMouseEnter={handleMouseEnter}
                      onMouseLeave={handleMouseLeave}
                      className={styles.upscaleBox}
                    >
                      {showPop && renderPop()}
                      <CommonButton icon={<UltraHdBoldFill />}>
                        AI超清
                        <div
                          className={classNames(
                            styles.badgeIcon,
                            styles.upscaleCorner
                          )}
                        >
                          <span>上新</span>
                        </div>
                      </CommonButton>
                    </div>
                  </Spin>
                </PermissionEnvWrapper>

                {/* AI改图 */}
                <PermissionEnvWrapper
                  includesOrigin={[AppOrigin.Whee]}
                  extraPermission={
                    !hiddenKeys.includes('repaint') &&
                    GraphSuccess &&
                    !upscalerIng
                  }
                >
                  <Actions.RepaintButton
                    onClick={() => {
                      props?.onImagePartialRepaintClick?.(id);
                    }}
                    editorImageUrl={activeGraph?.[0]?.src ?? ''}
                  />
                </PermissionEnvWrapper>

                {/* AI无痕消除 */}
                <PermissionEnvWrapper
                  includesOrigin={[AppOrigin.Whee]}
                  extraPermission={
                    !hiddenKeys.includes('eraser') &&
                    GraphSuccess &&
                    !upscalerIng
                  }
                >
                  <Actions.Eraser
                    onClick={() => {
                      props?.onImageEraserClick?.(id);
                      toImageEraser(activeGraph?.[0].src, id);
                    }}
                  />
                </PermissionEnvWrapper>

                {/* AI扩图 */}
                <PermissionEnvWrapper
                  includesOrigin={[AppOrigin.Whee]}
                  extraPermission={
                    !hiddenKeys.includes('expand') &&
                    GraphSuccess &&
                    !upscalerIng
                  }
                >
                  <Actions.ExtensionButton
                    onClick={() => {
                      props?.onImageExtensionClick?.(id);
                      toImageExtension(activeGraph?.[0].src, id);
                    }}
                  />
                </PermissionEnvWrapper>

                {/* 图生图 */}
                <PermissionEnvWrapper
                  includesOrigin={[AppOrigin.Whee]}
                  extraPermission={
                    !hiddenKeys.includes('img2img') &&
                    GraphSuccess &&
                    !upscalerIng
                  }
                >
                  <Actions.ImgToImg
                    onClick={() => {
                      props.onImageToImage?.(id);
                      toImg2Img(activeGraph?.[0].src, id);
                    }}
                  />
                </PermissionEnvWrapper>

                {/* 延伸创作 */}
                <PermissionEnvWrapper
                  includesOrigin={[AppOrigin.Whee]}
                  extraPermission={
                    !hiddenKeys.includes('deeperDesign') &&
                    GraphSuccess &&
                    !upscalerIng
                  }
                >
                  <Actions.DeeperDesignButton onClick={onDeeperdesignOpen} />
                </PermissionEnvWrapper>

                {/* 下载 */}
                {!hiddenKeys.includes('download') &&
                  hasGraphViewable &&
                  GraphSuccess && (
                    <Tooltip
                      title={
                        upscalerSuccess
                          ? isOrigin
                            ? '下载原图'
                            : '下载超清图'
                          : ''
                      }
                    >
                      <Actions.DownloadButton
                        loading={downloading}
                        onClick={onDownload}
                      />
                    </Tooltip>
                  )}
                {/* 删除 */}
                {!hiddenKeys.includes('remove') &&
                  !upscalerIng &&
                  GraphSuccess && (
                    <Actions.RemoveButton onClick={confirmRemove} />
                  )}
              </Actions>
            );
          }}
        </AutoSizer>
      )}
      {appOrigin === AppOrigin.Whee && showHistoryList && (
        <HistoryList taskCategory={taskCategory as DraftType} />
      )}

      <GraphPreview
        size={size}
        // HACK 临时处理全部失败时不显示缩略图，只显示第一张
        batch={batchOrigin}
        mask={
          <>
            {upscalerIng && (
              <div className={styles.upscalerCorner}>
                云端算力正在努力计算，请耐心等待...
              </div>
            )}
            {upscalerFailure && (
              <div className={styles.upscalerCorner}>
                分辨率提升失败，已返还相应美豆至您的账户
                <CrossBold
                  className={styles.closeBtn}
                  onClick={(e) => {
                    e.stopPropagation();
                    upscalerFailureClicked();
                  }}
                />
              </div>
            )}
            {/* 超分过的展示此对比 */}
            {upscalerSuccess && (
              <UpscaleCompare
                key={activeGraph?.[1]}
                originGraph={activeGraph?.[0]?.hdOriginUrl ?? ''}
                zoomMode={zoomMode}
                isOrigin={isOrigin}
                setIsOrigin={(val: boolean) => {
                  setIsOrigin(val);
                }}
              />
            )}
            {/* 没有超分过的才展示此对比 */}
            {originImg && !upscalerSuccess && (
              <CompareGraphAction
                originGraph={originImg}
                zoomMode={zoomMode}
                className={styles.comparingAction}
                maskClassName={classNames(
                  styles.comparingMask,
                  taskCategory === DraftType.TEXT_TO_IMAGE
                    ? styles.noTransition
                    : ''
                )}
              />
            )}
          </>
        }
        labelGraphPreviewLoading={(
          status?: UpscalerTaskStatus | To3DTaskStatus,
          index?: number
        ) => {
          const activeIndex = activeGraph?.[1] ?? 0;

          return (
            status === UpscalerTaskStatus.GENERATING &&
            index !== activeIndex && (
              <div className={styles.labelLoading}>
                <Loading type="light" className={styles.jumping} />
              </div>
            )
          );
        }}
        maxProportion={1}
        onChange={onChange}
        handleError={() => {
          openRefreshModal();
        }}
        graphPreviewClassName={classNames(
          props.graphPreviewClassName,
          styles.boxPadding
        )}
        needLargeImagePreview={true}
        isOrigin={isOrigin}
      />
      {/* 评分组件，只能评一次，评分成功后，则不展示。组件内包含成功弹窗，showRate内部分开控制 */}
      {appOrigin === AppOrigin.Whee && (
        <div className={styles.draftContainerRate}>
          <RateAction
            key={activeGraph?.[1] ?? 0}
            activeGraphSrc={activeGraph?.[0]?.src}
            taskId={id}
            onSuccess={onRateSuccess}
            rateType={RateType.TextOrImg}
            showRate={
              !activeGraph?.[0]?.hadSatisfied &&
              activeGraph?.[0]?.status === GraphStatus.SUCCESS
            }
          />
        </div>
      )}
      {contextHolder}
      {messageContextHolder}

      <PublishModal
        ref={publishModalRef}
        onPublish={props.onPublish}
        onPublishSuccess={props.onPublishSuccess}
      />
      {graphBatch[id] ? (
        <DeeperDesignModal
          value={deeperPrompt}
          open={isDeeperDesignModalOpen}
          onCancel={onDeeperDesignCancel}
          onChange={setDeeperPrompt}
          onCommit={onDeeperDesignCommit}
        />
      ) : null}
      {props.extra}

      <Modal
        open={showAIClearModal}
        wrapClassName={styles.ultra_clear_body}
        centered={true}
        width={340}
        footer={null}
        closable={true}
        mask={false}
        onOk={() => {}}
        onCancel={() => {
          setShowAIClearModal(false);
        }}
      >
        <h3>快捷AI超清</h3>
        <video
          src="https://wheeai.meitudata.com/static/66828435935773197YnlPKVh9L6417.mp4"
          loop
          muted
          autoPlay
          playsInline
          preload="auto"
          controls={false}
          disablePictureInPicture
        ></video>
        <div className={styles.detailBox}>
          <div className={styles.detailCenter}>
            快捷AI超清将消耗{isVipCurrent ? '1' : '2'}美豆，是否继续？
          </div>
          {/* {IA_ULTRA_CLEAR_MESSAGE_KEY} */}
          <p className={styles.detailCheck}>
            <Checkbox
              onChange={(data) => {
                const checked = data.target.checked;
                setIsCheckIAClearMessage(checked);
              }}
            ></Checkbox>{' '}
            本次登录不再提示
          </p>
          <div className={styles.detailBtnS}>
            <Footer
              isCheck={isCheckIAClearMessage}
              cancel={() => {
                setShowAIClearModal(false);
                // upscaleModal.destroy();
              }}
            />
          </div>
        </div>
      </Modal>
    </div>
  );
}
