@import '~@/styles/variables.less';

.publish-modal:global(.@{ant-prefix}-modal) {
  border-radius: @border-radius-lg;
  overflow: hidden;

  :global .@{ant-prefix}-modal-content {
    padding: 0;
    background-color: @color-bg-base;
    overflow: hidden;
  }
}

.publish-container {
  display: flex;

  &-preview {
    flex: 2.4;
    padding: 0;
    max-width: 674px;
    height: 520px;
    background-color: @color-bg-layout;

    &-content {
      height: 100% !important;

      :global .graph-preview-content {
        padding: 0;
      }
    }

    &-tabs {
      :global .@{ant-prefix}-tabs {
        height: 100%;

        &-nav {
          margin-bottom: @size-sm !important;

          &-wrap {
            justify-content: flex-start !important;
          }

          .@{ant-prefix}-tabs-tab {
            width: 80px !important;
            height: 80px !important;
          }
        }

        &-content {
          height: 100%;
          display: flex;
          align-items: center;
        }

        &-tabpane {
          height: 100%;
        }
      }
    }
  }

  :global(.@{ant-prefix}-image-mask) {
    &:hover {
      :local .comparing-action {
        opacity: 1;
      }
    }
  }

  &-form:global(.@{ant-prefix}-form) {
    position: relative;
    flex: 1;
    padding: @size-lg;
    background-color: @color-white;

    :global(.@{ant-prefix}-tag-checkable) {
      border-color: @color-border;
    }

    :global(.@{ant-prefix}-input) {
      background-color: @background-input;
    }

    :global(.@{ant-prefix}-form-item-label) {
      font-weight: 600;
    }

    :local .publish-container-form-actions {
      display: flex;
      position: absolute;
      bottom: @size-lg;
      width: calc(100% - @size-xxl);

      .confirm-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: @size-xl;
        transition: none;

        &:disabled {
          color: white;
          border: none;

          &::before {
            opacity: 1;
            background: @background-btn-ai-disable !important;
            transition: none;
          }
        }
      }

      :global .@{ant-prefix}-btn {
        flex: 1;

        &:not(:first-child) {
          margin-left: @size-sm;
        }
      }
    }

    :local .horizontal-form-item {
      margin-bottom: 0;

      :global {
        .@{ant-prefix}-form-item-row {
          flex-direction: row;
        }

        .@{ant-prefix}-form-item-label {
          display: flex;
          align-items: center;
          padding-bottom: 0;

          span {
            margin-left: @size-xxs;
          }
        }

        .@{ant-prefix}-form-item-control {
          width: auto;
          text-align: right;
        }
      }
    }

    :local .name-text-area {
      height: calc(@size-xl * 2);
      resize: none;
    }

    :local .description-text-area {
      height: calc(@size-xxl * 3);
      resize: none;
    }
  }
}

.comparing-action {
  position: absolute !important;
  right: @size-xs;
  bottom: @size-xs;
  opacity: 0;
}

.comparing-mask {
  background-color: @color-bg-layout;
}

.set-cover {
  position: absolute !important;
  bottom: 12px;
  left: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 82px;
  height: 28px;
  background: @background-tag-amount;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  border-radius: 8px;
  cursor: pointer;
  z-index: 2;

  span {
    display: inline-block;
    width: 14px;
    height: 14px;
  }

  .cover-title {
    margin-left: 4px;
  }
}
