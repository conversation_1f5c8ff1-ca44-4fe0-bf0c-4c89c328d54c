import type { Graph } from '@/types/draft';
import type { PublishFormValue } from './PublishForm';
import type { CanvasSize } from '@/types';
import { CanUse } from '@/api/types/draft';

import { Modal, message } from 'antd';
import GraphPreview from '@/components/GraphPreview';
import PublishForm from './PublishForm';
import { forwardRef, useState, useContext, useImperativeHandle } from 'react';
import { publishDraft } from '@/api';
import { ImagesContainerContext } from '..';
import { CompareGraphAction } from '@/components/GraphPreview/CompareGraphAction';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import _ from 'lodash';

import { GraphStatus } from '@/types/draft';
import styles from './styles.module.less';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { CrossBoldOutlined, PictureBold } from '@meitu/candy-icons';
import produce from 'immer';
import { AxiosError } from 'axios';
import { useCustomVerify, VerifyType } from '@/hooks/useCustomVerify';

interface OpenPublishModalProps extends Partial<PublishFormValue> {
  id: string;
  graph?: Graph[];

  /** 是否有同步至画廊操作按钮的权限 */
  hasPublishGalleryControlAccess?: boolean;

  /** 是否有允许使用画面控制图片操作按钮的权限 */
  hasControlnetImageControlAccess?: boolean;
}

export interface PublishModalRef {
  open: (props: OpenPublishModalProps) => void;
}

interface PublishModalProps {
  /** 发布事件 */
  onPublish?: (value: initialValues) => void;
  /** 发布成功事件 */
  onPublishSuccess?: (value: initialValues) => void;
}

export type initialValues = {
  id: string;
  graph?: Graph[];
  formValues: Partial<PublishFormValue>;
};

function polyfillCanUseSame(canUseSame?: boolean) {
  if (_.isBoolean(canUseSame)) {
    return canUseSame ? CanUse.ALLOW : CanUse.FORBIDDEN;
  }

  return canUseSame;
}

type InitImage = {
  imageUrl?: string;
  zoomMode?: number;
};

export default forwardRef<PublishModalRef, PublishModalProps>(
  function PublishModal(props, ref) {
    const [visible, setVisible] = useState(false);
    const [initialValues, setInitialValues] = useState<initialValues>();
    const [messageApi, contextHolder] = message.useMessage();
    const [loading, setLoading] = useState(false);
    const { graphBatch, updateGraphBatch } = useContext(ImagesContainerContext);
    const [initImage, setInitImage] = useState<InitImage>();
    const [size, setSize] = useState<CanvasSize>();
    const [galleryControlAccess, setGalleryControlAccess] = useState(true); // 默认是同步至画廊
    const [controlnetImageControlAccess, setControlnetImageControlAccess] =
      useState(false); // 默认是不带 controlnet 的图片
    const { updateMeiDouBalance } = useMeiDouBalance();

    // 服务端违规校验相关
    const {
      setTitleVerify,
      setDescriptionVerify,
      titleVerify,
      descriptionVerify,
      resetVerify,
      setImageVerify,
      imageVerify
    } = useCustomVerify();

    useImperativeHandle(ref, () => ({
      open({
        graph,
        id,
        hasPublishGalleryControlAccess = true,
        hasControlnetImageControlAccess = false,
        ...values
      }) {
        setVisible(true);

        const nextBatchData = produce(graph, (draft) => {
          return draft?.reduce((pre, cur) => {
            return cur.status === GraphStatus.SUCCESS
              ? pre.concat(Object.assign({}, cur, { checked: true }))
              : pre;
          }, [] as Graph[]);
        });

        setInitialValues({
          id,
          graph: nextBatchData,
          formValues: values
        });

        const activeGraph = graphBatch[id];

        setInitImage({
          imageUrl: graphBatch[id]?.params?.initImages?.[0],
          zoomMode: graphBatch[id]?.params?.resizeMode
        });
        setSize(activeGraph?.size);
        setGalleryControlAccess(hasPublishGalleryControlAccess);
        setControlnetImageControlAccess(hasControlnetImageControlAccess);
      }
    }));

    const close = () => {
      setVisible(false);
      resetVerify();
    };

    const verifyErrorhandler = (message: string, code?: string) => {
      switch (code) {
        case VerifyType.TITLE:
          setTitleVerify('请调整创意名信息');
          setDescriptionVerify('');
          setImageVerify('');
          break;
        case VerifyType.DESCRIPTION:
          setDescriptionVerify('请调整简介信息');
          setTitleVerify('');
          setImageVerify('');
          break;
        case VerifyType.IMAGE:
          setImageVerify('请重新上传合规的图片');
          setTitleVerify('');
          setDescriptionVerify('');
          break;
        case VerifyType.DESCRIPTION_AND_TITLE:
          setTitleVerify('请调整创意名信息');
          setDescriptionVerify('请调整简介信息');
          setImageVerify('');
          break;
        case VerifyType.DESCRIPTION_AND_IMAGE:
          setDescriptionVerify('请调整简介信息');
          setImageVerify('请重新上传合规的图片');
          setTitleVerify('');
          break;
        case VerifyType.TITLE_AND_IMAGE:
          setTitleVerify('请调整创意名信息');
          setImageVerify('请重新上传合规的图片');
          setDescriptionVerify('');
          break;
        case VerifyType.DESCRIPTION_AND_IMAGE_AND_TITLE:
          setTitleVerify('请调整创意名信息');
          setDescriptionVerify('请调整简介信息');
          setImageVerify('请重新上传合规的图片');
          break;
      }
    };

    const onFinish = async (formValues: PublishFormValue) => {
      const { canUseSame, canPostToGallery, canUseControlnetImage, ...values } =
        formValues;
      const { id = '', graph = [] } = initialValues ?? {};
      const nextInitialValues = Object.assign({}, initialValues, {
        formValues
      });

      try {
        setLoading(true);
        props.onPublish?.(nextInitialValues);

        // 封面图放到第一项
        const graphCopy = produce(graph, (draft) => {
          draft.unshift(draft.splice(coverIndex, 1)[0]);
        });

        const nextBatchData = graphCopy.reduce((pre, cur) => {
          return cur.checked ? pre.concat(cur.src || '') : pre;
        }, [] as string[]);

        const { result, showAwardTip, awardTipTitle } = await publishDraft(
          Object.assign(
            {
              id,
              images: nextBatchData,
              canUseSame: polyfillCanUseSame(canUseSame),
              publishGallery:
                _.isUndefined(canPostToGallery) ||
                (canPostToGallery && _.isBoolean(canPostToGallery))
                  ? CanUse.ALLOW
                  : CanUse.FORBIDDEN,
              canUseControlnetImage: canUseControlnetImage
                ? CanUse.ALLOW
                : CanUse.FORBIDDEN,
              tags: []
            },
            values
          )
        );

        if (result) {
          messageApi.success(showAwardTip ? awardTipTitle : '已提交发布作品');
          showAwardTip && updateMeiDouBalance();
          props.onPublishSuccess?.(nextInitialValues);

          updateGraphBatch(
            Object.assign({}, graphBatch[id], {
              isPublish: true
            })
          );

          close();
        } else {
          messageApi.error('发布失败');
        }
      } catch (error) {
        const { code, message } = error as AxiosError;
        verifyErrorhandler(message, code);
        defaultErrorHandler(error);
      } finally {
        setLoading(false);
      }
    };

    const [coverIndex, setCoverIndex] = useState(0);
    const [graphIndex, setGraphIndex] = useState(0);

    // 切换图片
    const onChange = (graph: Graph, index?: number) => {
      if (index === undefined) {
        return;
      }
      setGraphIndex(index);
    };

    // 更新选择状态
    const updateBatch = (batch: Graph[], checkIndex: number) => {
      const nextBatchData = produce(batch, (draft) => {
        const batchFilter = draft.filter((item) => item.checked);
        draft.forEach((item, index) => {
          if (checkIndex === index) {
            // 只剩一张的时候，选中的无法取消，未选中的还能选
            if (batchFilter.length <= 1) {
              if (!item.checked) {
                item.checked = !item.checked;
              }
            } else {
              item.checked = !item.checked;
            }
          }
        });
      });

      setInitialValues((initialValues) =>
        Object.assign({}, initialValues, {
          graph: nextBatchData
        })
      );

      // 当设为封面的图片被去选时，自动把当前第一张选中的设为封面
      if (checkIndex === coverIndex) {
        const firstCheckedIndex = nextBatchData.findIndex(
          (item) => item.checked
        );
        setCoverIndex(firstCheckedIndex);
      }
    };

    return (
      <>
        {contextHolder}
        <Modal
          open={visible}
          width={970}
          maskClosable={false}
          footer={null}
          destroyOnClose
          className={styles.publishModal}
          onCancel={close}
          closeIcon={<CrossBoldOutlined />}
          centered
        >
          <div className={styles.publishContainer}>
            <div className={styles.publishContainerPreview}>
              <GraphPreview
                size={size}
                batch={initialValues?.graph ?? []}
                tabsClassName={styles.publishContainerPreviewTabs}
                className={styles.publishContainerPreviewContent}
                checkable={true}
                updateBatch={updateBatch}
                onChange={onChange}
                validateStatus={imageVerify ? 'error' : undefined}
                coverCornerMark={(index: number) => {
                  return (
                    coverIndex === index && (
                      <div className="preview-cover">封面</div>
                    )
                  );
                }}
                mask={
                  <>
                    {initImage?.imageUrl && (
                      <CompareGraphAction
                        originGraph={initImage.imageUrl}
                        zoomMode={initImage.zoomMode}
                        className={styles.comparingAction}
                        maskClassName={styles.comparingMask}
                      />
                    )}
                    {initialValues?.graph![graphIndex].checked &&
                      Number(initialValues?.graph?.length) > 1 && (
                        <div
                          className={styles.setCover}
                          onClick={() => {
                            setCoverIndex(graphIndex);
                          }}
                        >
                          <PictureBold />
                          <div className={styles.coverTitle}>设置封面</div>
                        </div>
                      )}
                  </>
                }
              />
            </div>
            {/* TODO:LYZ custom verify */}
            <PublishForm
              onFinish={onFinish}
              onCancel={close}
              loading={loading}
              initialValues={initialValues?.formValues}
              hasPublishGalleryControlAccess={galleryControlAccess}
              hasControlnetImageControlAccess={controlnetImageControlAccess}
              titleVerify={titleVerify}
              descriptionVerify={descriptionVerify}
            />
          </div>
        </Modal>
      </>
    );
  }
);
