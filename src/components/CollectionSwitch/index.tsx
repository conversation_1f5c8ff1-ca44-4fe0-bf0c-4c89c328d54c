import type { MouseEvent } from 'react';

import {
  FivePointedStarBold,
  FivePointedStarBoldFill
} from '@meitu/candy-icons';
import { EditorUsableAuthorized } from '@/components';

import styles from './styles.module.less';
import classNames from 'classnames';

interface CollectionSwitchProps {
  /** 已收藏 */
  isCollected: boolean;

  /** 收藏数量 */
  count?: number;

  className?: string;

  onChange: (isCollected: boolean, event: MouseEvent) => void;
}

export function CollectionSwitch({
  isCollected,
  count,
  className,
  onChange
}: CollectionSwitchProps) {
  return (
    <EditorUsableAuthorized>
      <div
        className={classNames(
          styles.collectionSwitch,
          isCollected && styles.collected,
          className
        )}
        onClick={onChange.bind(null, !isCollected)}
      >
        <div className={styles.collectionSwitchStar}>
          {isCollected ? <FivePointedStarBoldFill /> : <FivePointedStarBold />}
        </div>
        {count !== undefined && (
          <div className={styles.collectionSwitchCount}>{count}</div>
        )}
      </div>
    </EditorUsableAuthorized>
  );
}
