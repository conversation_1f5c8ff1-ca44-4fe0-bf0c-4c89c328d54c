@import '~@/styles/variables.less';

.collection-switch {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: @size-lg;
  font-size: @size-sm;
  color: @content-system-primary;
  line-height: @size-md;
  cursor: pointer;
  padding: 0 @size-xxs;
  background-color: @background-switch;
  border-radius: @size-xxs;
  user-select: none;

  &-star {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: @size-md;
    font-size: @font-size;
    transition: all 0.3s ease;
  }

  &-count {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: @size-sm;
  }

  &:hover {
    .collection-switch-star {
      color: @background-collection;
    }
  }

  &:active {
    .collection-switch-star {
      &::after {
        box-shadow: none;
      }
    }
  }

  &.collected {
    background-color: @background-tag;

    &:hover {
      .collection-switch-star {
        &::after {
          box-shadow: none;
        }
      }
    }

    path {
      fill: @background-collection;
    }
  }
}
