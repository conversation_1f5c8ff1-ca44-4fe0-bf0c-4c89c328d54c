import type { TrainingFailedResult, TrainingSuccessResult } from '@/api/types';

import { AppModule, generateRouteTo, trackEvent } from '@/services';
import { Button, message, Modal, Space, Typography } from 'antd';
import { CheckCircleBoldFilled, CrossBoldOutlined } from '@meitu/candy-icons';

import { fetchTrainingStatus, fetchTrainingTotal } from '@/api/training';
import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { useInterval } from 'react-use';
import { useAccount } from '@/hooks';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { EditorMode } from '@/constants';
import { useRecoilValue } from 'recoil';
import { trainingIsCreatedState } from '@/hooks/useTrainingConfig';

export const TrainingNotification = () => {
  const [successQueue, setSuccessQueue] = useState<TrainingSuccessResult[]>([]);
  const [failedQueue, setFailedQueue] = useState<TrainingFailedResult[]>([]);
  const { isLogin } = useAccount();
  const { updateMeiDouBalance } = useMeiDouBalance();
  const [trainingTotal, setTrainingTotal] = useState(0);

  const trainingIsCreated = useRecoilValue(trainingIsCreatedState);

  const isOpen = !!successQueue.length;
  const navigate = useNavigate();
  // 有训练成功的弹窗时，非登录时，正在训练任务为0  不轮询
  const delay = isOpen || !isLogin || trainingTotal === 0 ? null : 1000 * 15;

  useInterval(async () => {
    try {
      const res = await fetchTrainingStatus();

      if (res?.successList?.length > 0) {
        updateMeiDouBalance();

        setSuccessQueue(res.successList);

        res?.successList.forEach((successItem) => {
          const { advanced, status, ...rest } = successItem;
          trackEvent('model_training_success', { ...rest, ...advanced });
        });
      }

      if (res?.failList?.length > 0) {
        updateMeiDouBalance();

        setFailedQueue((prev) => (!!prev.length ? prev : res.failList));

        res?.failList.forEach((failedItem) => {
          const { advanced, status, ...rest } = failedItem;
          trackEvent('model_training_fail', { ...rest, ...advanced });
        });
      }
    } catch (error) {
      console.log(error);
    }
  }, delay);

  const { token } = useAccount();

  useEffect(() => {
    if (!token) return;
    // 查询是否有正在生成中的任务
    fetchTrainingTotal().then((res) => {
      setTrainingTotal(res.doing);
    });
    // 点击了立即生成，有训练结果返回
  }, [trainingIsCreated, successQueue, failedQueue, token]);

  useEffect(() => {
    if (!failedQueue.length) return;

    message.error('风格模型训练失败，请重新训练');

    setFailedQueue([]);
  }, [failedQueue.length]);

  const onClose = () => {
    setSuccessQueue([]);
  };

  const onLeave = (isApply?: boolean) => {
    const queue = successQueue;
    onClose();

    const toView = () => {
      navigate(generateRouteTo(AppModule.Personal), { replace: true });
    };

    const toApply = () => {
      if (queue[0]?.trainingModelId) {
        navigate(
          generateRouteTo(AppModule.TextToImage, {
            styleModelId: String(queue[0]?.trainingModelId),
            editorMode: EditorMode.Advanced
          }),
          { replace: true }
        );
      }
    };

    const finalEffect = isApply ? toApply : toView;

    finalEffect();
  };

  return (
    <>
      <Modal
        centered
        open={isOpen}
        bodyStyle={{ paddingRight: 20 }}
        onCancel={onClose}
        width={360}
        footer={
          <Space>
            <Button type="primary" onClick={() => onLeave()}>
              去查看
            </Button>
            <Button onClick={() => onLeave(true)}>去应用</Button>
          </Space>
        }
        closeIcon={<CrossBoldOutlined />}
      >
        <Space>
          <CheckCircleBoldFilled
            style={{ fontSize: '25px', color: '#52c41a' }}
          />
          <Typography.Text>
            {successQueue[0]?.name ?? ''}
            模型训练已完成
          </Typography.Text>
        </Space>
      </Modal>
    </>
  );
};
