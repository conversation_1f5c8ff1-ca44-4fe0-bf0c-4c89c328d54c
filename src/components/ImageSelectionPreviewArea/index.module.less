@import '~@/styles/variables.less';

.slidesPerView {
  z-index: 99;
  max-width: 664px;
  height: 124px;
  padding: 16px;
  background-color: @background-system-frame-floatpanel;
  border-radius: 8px;
  box-shadow: @level-2;
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  gap: 16px;

  .swiper {
    flex: 1;
  }

  &.loading {
    .item {
      background: linear-gradient(
        90deg,
        #ebedf2 0%,
        rgba(255, 255, 255, 0) 66%
      );
      background-position: 100% 0;
      animation: gradient 1.5s infinite ease-in-out alternate;
    }

    @keyframes gradient {
      0% {
        background-size: 100% 100%;
      }
      50% {
        background-position: 50% 0;
        background-size: 200% 100%;
      }
      100% {
        background-position: 0 0;
        background-size: 300% 100%;
      }
    }
  }

  .item {
    width: 92px;
    cursor: pointer;
    height: 92px;
    border-radius: 4px;
    border-radius: 4px;
    border: 1px solid @stroke-system-border-overlay;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    opacity: 0.5;
    position: relative;

    &.active {
      opacity: 1;
    }

    &.effect {
      opacity: 1;
      cursor: unset;
      border: none;
      background: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;

      :global {
        .ant-btn {
          &:hover {
            background: @background-btn-hover;
          }
          border-radius: 8px;

          width: 92px;
          height: 92px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          color: @content-system-primary;

          &:disabled {
            cursor: not-allowed;
            color: @content-system-quinary;
          }

          .@{ant-prefix}icon {
            font-size: @size-md;
          }
        }
      }

      // .effect-icon {
      //   color: @content-system-primary;
      //   &.disabled {
      //     color: @content-system-quinary;
      //   }
      // }
    }

    &.error {
      &::after {
        content: '生成失败';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        text-align: center;
        color: @base-white-opacity-100;
        font-size: @text-14;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }
}
