import classNames from 'classnames';
import styles from './index.module.less';
import { Button } from '../Button';
import { CheckBold, UndoBold } from '@meitu/candy-icons';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/less';
import { forwardRef, useImperativeHandle, useState } from 'react';
import { ImageStatus, ResultImage } from '@/api/types';
import ErrorImageSrc from '@/assets/images/invisible-graph.jpg';
import { useKey } from 'react-use';

export type ResultListItemType = ResultImage | undefined;

export interface ImageSelectionPreviewAreaProps {
  className?: string;
  loading?: boolean;
  onClickPrev?: () => void;
  onClickNext?: () => void;

  resultList: ResultListItemType[];
  onSelect?: (res: ResultListItemType, index: number) => void;

  prevTitle?: string;
  nextTitle?: string;

  prevIcon?: React.ReactNode;
  nextIcon?: React.ReactNode;

  prevShow?: boolean;
  nextShow?: boolean;
}

export type ImageSelectionPreviewAreaRef = {
  setSelected: (v: ResultListItemType, list?: ResultListItemType[]) => void;
};

export const ImageSelectionPreviewArea = forwardRef<
  ImageSelectionPreviewAreaRef,
  ImageSelectionPreviewAreaProps
>(
  (
    {
      className,
      loading,
      onClickPrev,
      resultList,
      onSelect,
      onClickNext,
      prevTitle,
      nextTitle,
      prevIcon,
      nextIcon,
      prevShow = true,
      nextShow = true
    },
    ref
  ) => {
    const [selected, _setSelected] = useState<ResultListItemType>();
    const hasResult = !!resultList.filter(Boolean).length;

    const changeImageByKeys = (key: 'prev' | 'next') => {
      if (loading || !selected || !hasResult) return;
      const index = resultList.findIndex((item) => item!.url === selected.url);
      let nextIndex = 0;
      if (key === 'next') {
        nextIndex = index + 1 > resultList.length - 1 ? 0 : index + 1;
      } else if (key === 'prev') {
        nextIndex = index - 1 < 0 ? resultList.length - 1 : index - 1;
      }

      const next = resultList[nextIndex];
      if (!next) return;
      _setSelected(next);
      onSelect?.(next, nextIndex);
    };

    useKey('ArrowLeft', changeImageByKeys.bind(null, 'prev'), {}, [
      changeImageByKeys
    ]);
    useKey('ArrowRight', changeImageByKeys.bind(null, 'next'), {}, [
      changeImageByKeys
    ]);

    useImperativeHandle(
      ref,
      () => ({
        setSelected: (v, list) => {
          // resultList 无值到有值（渲染的时候）ref的setSelected 是找不到值的
          // 可以直接把list带进来或者宏任务delay call
          const fixturesList = list || resultList;

          const index = fixturesList.findIndex((item) => item?.url === v?.url);

          if (index === -1) return;

          _setSelected(v);
          onSelect?.(v, index);
        }
      }),
      [onSelect, resultList]
    );

    return (
      <div
        className={classNames(styles.slidesPerView, className, {
          [styles.loading]: loading
        })}
      >
        {prevShow && (
          <div className={classNames(styles.item, styles.effect)}>
            <Button
              disabled={loading}
              onClick={onClickPrev}
              type="text"
              size="small"
              icon={prevIcon ?? <UndoBold />}
            >
              {prevTitle ?? '上一步'}
            </Button>
          </div>
        )}

        <Swiper
          className={styles.swiper}
          spaceBetween={16}
          slidesPerView="auto"
        >
          {resultList.map((result, index) => (
            <SwiperSlide
              key={(result?.url ?? '') + index}
              onClick={() => {
                if (!result || selected?.url === result?.url) return;
                _setSelected(result);
                onSelect?.(result, index);
              }}
              className={classNames(styles.item, {
                [styles.active]: selected?.url === result?.url,
                [styles.error]: result?.imageStatus === ImageStatus.DELETED
              })}
              style={
                (!!result && {
                  backgroundImage: `url(${
                    result.imageStatus === ImageStatus.NORMAL
                      ? result.url
                      : ErrorImageSrc
                  })`
                }) ||
                {}
              }
            />
          ))}
        </Swiper>

        {nextShow && (
          <div className={classNames(styles.item, styles.effect)}>
            <Button
              disabled={
                loading || selected?.imageStatus === ImageStatus.DELETED
              }
              onClick={onClickNext}
              type="text"
              size="small"
              icon={nextIcon ?? <CheckBold />}
            >
              {nextTitle ?? '继续修改'}
            </Button>
          </div>
        )}
      </div>
    );
  }
);
