import { type CanvasSize, type SwitchType } from '@/types';
import type { ReactNode } from 'react';
import type { Graph, To3DTaskStatus } from '@/types/draft';
import { GraphStatus, LoadingStatus } from '@/types/draft';

import { LoadFailed } from '@/components/ImagesContainer/GraphBatch';
import { Loading } from '@/components';
import { Button, Image, Tabs, Empty } from 'antd';
import {
  CheckBlack,
  ChevronLeftBlack,
  ChevronRightBlack
} from '@meitu/candy-icons';
import React, { useState, useEffect, useRef } from 'react';
import { useProportion } from './useProportion';
import { useImageSize } from '@/hooks';

import styles from './styles.module.less';
import classNames from 'classnames';
import invisible from '@/assets/images/irregularity.png';
import empty from '@/assets/images/empty.jpg';
import { UpscalerTaskStatus } from '@/api/types';
import { isTimestampExpired } from '@/utils/isTimestampExpired';
import {
  LargeImagePreviewModalHandle,
  LargeImagePreviewModal
} from './LargeImagePreviewModal';
import produce from 'immer';
import { generateImagePreviewUrl } from '@/utils/cropImage';

interface GraphPreviewProps {
  /** 批次图片 */
  batch: Graph[];
  /** 画布尺寸 */
  size?: CanvasSize;
  /** 覆盖样式 */
  className?: string;
  tabsClassName?: string;
  /** 遮罩层 */
  mask?: ReactNode;
  /** 预览图限制最大比例 */
  maxProportion?: number;
  /** 图片切换事件 */
  onChange?: (graph: Graph, index?: number, switchType?: SwitchType) => void;
  // 是否可选择
  checkable?: boolean;
  // 封面角标
  coverCornerMark?: (index: number) => {};
  // 更新选择状态
  updateBatch?: (batch: Graph[], checkIndex: number) => void;
  // tab Loading
  labelGraphPreviewLoading?: (
    status?: UpscalerTaskStatus | To3DTaskStatus,
    index?: number
  ) => {};
  handleError?: () => void;
  // 特定的盒子className
  graphPreviewClassName?: string;
  // 是否需要图片放大
  needLargeImagePreview?: boolean;
  // 图片审核异常交互
  validateStatus?: 'success' | 'warning' | 'error' | 'validating' | '';
  // 当前点击大图预览的是否是原图
  isOrigin?: boolean;
  /** batch长度为0时的占位组件 */
  emptyPlaceholder?: React.ReactElement;
  // 是否需要预览图的背景
  needPreviewBackground?: boolean;
}

function GraphPreviewContent(
  props: Graph & {
    mask?: ReactNode;
    size?: CanvasSize;
    loading?: boolean;
    handleError?: () => void;
    onClick?: () => void;
    needLargeImagePreview?: boolean;
    needPreviewBackground?: boolean;
    // 图片审核异常交互
    validateStatus?: 'success' | 'warning' | 'error' | 'validating' | '';
  }
) {
  const {
    status,
    src,
    urlWatermark,
    mask,
    size,
    loading,
    onClick,
    needLargeImagePreview,
    validateStatus,
    needPreviewBackground
  } = props;

  const [isError, setIsError] = useState(false);

  const timestamp = src?.split('&t=')[1];

  const handleError = () => {
    setIsError(true);
    // 图片过期弹窗
    if (isTimestampExpired(timestamp || '')) {
      props.handleError?.();
    }
  };

  if (loading) {
    return <Loading />;
  }

  // 图片加载错误，显示底图
  if (isError) {
    return (
      <div
        className={classNames(styles.errInvisible, 'errInvisible')}
        style={{ width: size?.[0], height: size?.[1] }}
      ></div>
    );
  }

  if (status === GraphStatus.SUCCESS) {
    const url: string = (needLargeImagePreview ? urlWatermark : src) || '';
    return (
      <Image
        src={generateImagePreviewUrl(url, 1024)}
        width={size?.[0]}
        height={size?.[1]}
        preview={{ visible: false, mask }}
        onError={handleError}
        onClick={onClick}
        className={classNames(
          needLargeImagePreview && 'enable-large-image-preview'
        )}
        rootClassName={classNames(
          validateStatus ? styles.error : '',
          needPreviewBackground && 'image-background'
        )}
      />
    );
  } else if (status === GraphStatus.AUDITING) {
    return (
      <Image
        width={size?.[0]}
        height={size?.[1]}
        preview={{
          visible: false,
          mask: (
            <div className={styles.loadingMask}>
              <Loading />
            </div>
          )
        }}
        onError={handleError}
        onClick={onClick}
      />
    );
  } else {
    return (
      <Image
        src={invisible}
        width={size?.[0]}
        height={size?.[1]}
        preview={{
          visible: false,
          mask: (
            <>
              <LoadFailed type={LoadingStatus.IRREGULARITY} />
            </>
          )
        }}
        onError={handleError}
        onClick={onClick}
      />
    );
  }
}

const defaultEmptyPlaceholder = (
  <Empty
    image={empty}
    description=""
    className={styles.graphPreviewPlaceholder}
  />
);

/**此组件禁止依赖除props外其他外部状态 */
export default function GraphPreview(props: GraphPreviewProps) {
  const {
    batch,
    className,
    tabsClassName,
    size,
    mask,
    maxProportion = Infinity,
    onChange,
    checkable,
    coverCornerMark,
    updateBatch,
    labelGraphPreviewLoading,
    handleError,
    graphPreviewClassName,
    needLargeImagePreview,
    needPreviewBackground,
    validateStatus,
    isOrigin,
    emptyPlaceholder = defaultEmptyPlaceholder
  } = props;
  const [activeGraphIndex, setActiveGraphIndex] = useState(0);

  const sizeFromImage = useImageSize(batch[0]?.src);
  const graphSize = size?.some((edge) => !edge) ? sizeFromImage : size;
  const [proportion, nodeRef] = useProportion(graphSize);
  const initial = useRef(false);

  const largePreviewRef = useRef<LargeImagePreviewModalHandle>(null);

  const onTabChange = (activeKey: string) => {
    onGraphChange(+activeKey, 'click');
  };

  const onGraphChange = (index: number, switchType?: SwitchType) => {
    setActiveGraphIndex(index);
    onChange?.(batch[index], index, switchType);
  };

  useEffect(
    () => {
      const index = batch.findIndex(
        ({ status }) => status === GraphStatus.SUCCESS
      );

      const currentActiveGraphIndex = index > -1 ? index : 0;

      if (batch.length < 1) return;
      // 初始化或者当前图片状态不可用的时候，设置active
      if (
        !initial.current ||
        batch[activeGraphIndex]?.status !== GraphStatus.SUCCESS
      ) {
        onGraphChange(currentActiveGraphIndex);
      }

      initial.current = true;
      // setActiveGraphIndex(0);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [batch]
  );

  if (!batch.length) {
    return emptyPlaceholder;
  }

  const CheckableGraphPreviewContent = (
    graphProps: Graph & {
      mask?: ReactNode;
      size?: CanvasSize;
      loading?: boolean;
      coverCornerMark?: (index: number) => {};
      index: number;
    }
  ) => {
    const { coverCornerMark, index, checked } = graphProps;

    return (
      <>
        {coverCornerMark && coverCornerMark(index)}
        <div
          className="check-icon"
          onClick={(e) => {
            e.stopPropagation();
            updateBatch?.(batch, index);
          }}
        >
          {checked ? (
            <div className="checked">
              <CheckBlack />
            </div>
          ) : (
            <div className="no-check"></div>
          )}
        </div>
        <GraphPreviewContent {...graphProps} />
      </>
    );
  };

  const LabelGraphPreviewContent = (
    graphProps: Graph & {
      mask?: ReactNode;
      size?: CanvasSize;
      loading?: boolean;
      index: number;
      labelGraphPreviewLoading?: (
        status?: UpscalerTaskStatus | To3DTaskStatus,
        index?: number
      ) => {};
    }
  ) => {
    const { index, upscalerInfo, planeto3DInfo } = graphProps;

    const getStatus = () => {
      if (upscalerInfo && upscalerInfo.status) {
        return upscalerInfo.status;
      } else if (planeto3DInfo && planeto3DInfo.status) {
        return planeto3DInfo.status;
      }
    };

    return (
      <>
        {labelGraphPreviewLoading?.(getStatus(), index)}

        <GraphPreviewContent {...graphProps} />
      </>
    );
  };

  function handelLargeImagePreview(indexInBatch: number) {
    let temp: Graph[] = Object.assign([], batch);
    // 如果是原图，放大后的预览列表url变成原图的
    if (isOrigin) {
      temp = produce(batch, (draft) => {
        draft[indexInBatch].src =
          draft[indexInBatch]?.hdOriginUrl ||
          draft[indexInBatch]?.planeto3DOriginUrl;
        draft[indexInBatch].urlWatermark =
          draft[indexInBatch]?.hdOriginUrl ||
          draft[indexInBatch]?.planeto3DOriginUrl;
      });
    }

    const clickUrl = temp[indexInBatch]?.urlWatermark || temp[indexInBatch].src;
    if (!clickUrl) {
      return;
    }

    const largePreviewList = temp
      .filter(
        (b) => (b?.urlWatermark || b.src) && b.status === GraphStatus.SUCCESS
      )
      .map((b) => ({
        src: b?.urlWatermark || b.src || '',
        size: size as CanvasSize
      }));

    // 点击的图在过滤后数组的下标位置
    const openIndex = largePreviewList.findIndex((item) => {
      return item.src === clickUrl;
    });
    if (openIndex < 0) return;

    largePreviewRef.current?.open(largePreviewList, openIndex);
  }
  return (
    <div className={classNames(styles.graphPreview, graphPreviewClassName)}>
      <Button
        disabled={activeGraphIndex === 0}
        icon={<ChevronLeftBlack />}
        type="text"
        onClick={() => onGraphChange(activeGraphIndex - 1, 'last')}
      />
      <div
        ref={nodeRef}
        className={classNames(styles.graphPreviewContainer, className)}
      >
        <Tabs
          activeKey={activeGraphIndex.toString()}
          tabPosition="bottom"
          animated
          items={batch.map((graphProps, index) => ({
            key: index.toString(),
            forceRender: true,
            label: checkable ? (
              <CheckableGraphPreviewContent
                {...{ ...graphProps, index, coverCornerMark }}
              />
            ) : (
              <LabelGraphPreviewContent
                {...{
                  ...graphProps,
                  index,
                  labelGraphPreviewLoading
                }}
              />
            ),
            children: (
              <div
                className={classNames(
                  styles.graphPreviewContent,
                  'graph-preview-content',
                  validateStatus ? styles.error : ''
                )}
              >
                <GraphPreviewContent
                  {...graphProps}
                  loading={!proportion && !!graphSize}
                  size={
                    graphSize?.map(
                      (offset) =>
                        offset * Math.min(proportion ?? 1, maxProportion)
                    ) as CanvasSize
                  }
                  mask={mask}
                  handleError={handleError}
                  onClick={() => {
                    if (graphProps.status !== GraphStatus.SUCCESS) {
                      return;
                    }

                    handelLargeImagePreview(index);
                  }}
                  needLargeImagePreview={needLargeImagePreview}
                  needPreviewBackground={needPreviewBackground}
                />
              </div>
            )
          }))}
          className={classNames(styles.graphPreviewTabs, tabsClassName, {
            [styles.hiddenTabs]: batch.length < 2
          })}
          onChange={onTabChange}
        />
      </div>
      <Button
        disabled={activeGraphIndex === batch.length - 1}
        icon={<ChevronRightBlack />}
        type="text"
        onClick={() => onGraphChange(activeGraphIndex + 1, 'next')}
      />

      {needLargeImagePreview && (
        <LargeImagePreviewModal ref={largePreviewRef}></LargeImagePreviewModal>
      )}
    </div>
  );
}
