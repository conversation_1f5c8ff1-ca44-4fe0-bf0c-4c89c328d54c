import { useLayoutEffect, useRef, useState } from 'react';
import styles from './index.module.less';
import classNames from 'classnames';
import { Button } from '@/components/Button';
import { Image } from 'antd';
import {
  ChevronLeftBold,
  ChevronRightBold,
  CrossBold,
  DownloadBold
} from '@meitu/candy-icons';
import { message } from 'antd';
import { downloadFile } from '@/utils/blob';
import ResizeObserver from 'resize-observer-polyfill';
import { generateImagePreviewUrl } from '@/utils/cropImage';
import { CanvasSize } from '@/types';
import { toAtlasImageView2URL } from '@meitu/util';

type LargeImagePreviewProps = {
  images: Array<{
    src: string;
    size: CanvasSize;
  }>;

  activeIndex: number;
  onNextPage: () => void;
  onPrevPage: () => void;
  onClose: () => void;
};

export function LargeImagePreview(props: LargeImagePreviewProps) {
  const { images, activeIndex, onClose, onPrevPage, onNextPage } = props;
  const activeImage = images[activeIndex];

  function createPageSwitch(preOrNext: 'pre' | 'next') {
    return function (e: React.MouseEvent<HTMLElement, MouseEvent>) {
      e.stopPropagation();

      if (!images.length) {
        return;
      }

      switch (preOrNext) {
        case 'pre': {
          onPrevPage();
          break;
        }
        case 'next': {
          onNextPage();
          break;
        }
      }
    };
  }

  return (
    <div className={styles.largeImagePreviewContainer}>
      <div
        className={classNames(styles.switchPageContainer, 'prev')}
        onClick={onClose}
      >
        <Button
          icon={<ChevronLeftBold />}
          type="text"
          onClick={createPageSwitch('pre')}
          className={styles.switchPageButton}
        />
      </div>
      <div className={styles.imageContainer}>
        <AutoFillImage
          url={activeImage.src}
          imageSize={activeImage.size}
          previewUrl={generateImagePreviewUrl(activeImage.src, 2048)}
          onClose={onClose}
        ></AutoFillImage>
      </div>
      <div
        className={classNames(styles.switchPageContainer, 'next')}
        onClick={onClose}
      >
        <Button
          icon={<ChevronRightBold />}
          type="text"
          onClick={createPageSwitch('next')}
          className={styles.switchPageButton}
        />
      </div>
    </div>
  );
}

type AutoFIllImageProps = {
  url: string;
  onClose: () => void;
  previewUrl?: string;
  imageSize: CanvasSize;
};
function AutoFillImage({
  url,
  onClose,
  imageSize,
  previewUrl = url
}: AutoFIllImageProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    function resize() {
      if (imageSize[0] === 0 || imageSize[1] === 0) {
        return;
      }

      const container = containerRef.current!;

      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;

      const containerAspect = containerWidth / containerHeight;
      const imageAspect = imageSize[0] / imageSize[1];

      let imageWidth = 0;
      let imageHeight = 0;

      // 图片的宽高比 > 容器宽高比
      // 说明图片比容器更“扁”
      if (imageAspect > containerAspect) {
        imageWidth = containerWidth;
        imageHeight = containerWidth / imageAspect;
      } else {
        // 图片比容器更“高”
        imageWidth = containerHeight * imageAspect;
        imageHeight = containerHeight;
      }

      container.style.setProperty('--image-width', `${imageWidth}px`);
      container.style.setProperty('--image-height', `${imageHeight}px`);
    }

    resize();

    const observer = new ResizeObserver(resize);
    containerRef.current && observer.observe(containerRef.current);

    return () => {
      observer.disconnect();
    };
  }, [imageSize]);

  return (
    <div
      className={styles.autoFillContainer}
      ref={containerRef}
      onClick={onClose}
    >
      <ImageCard url={url} previewUrl={previewUrl} onClose={onClose} />
    </div>
  );
}

type ImageCardProps = {
  url: string;
  onClose: () => void;
  previewUrl?: string;
};
function ImageCard({ url, onClose, previewUrl = url }: ImageCardProps) {
  const [downloading, setDownloading] = useState(false);

  async function handleDownload() {
    setDownloading(true);
    try {
      await downloadFile(url);
      message.success('下载成功');
    } catch (error) {
      message.error('下载失败');
    }
    setDownloading(false);
  }
  // const atlasImageUrl = generateImagePreviewUrl(previewUrl, 100);
  const atlasImageUrl = toAtlasImageView2URL(url, {
    mode: 2,
    width: 80,
    height: 80
  });
  // console.log(atlasImageUrl)

  return (
    <section
      className={styles.imageCardContainer}
      onClick={(e) => e.stopPropagation()}
    >
      {/* <img src={previewUrl} alt="" /> */}
      <Image
        // key={previewUrl}
        src={previewUrl}
        style={{
          width: 'var(--image-width)',
          height: 'var(--image-height)'
        }}
        placeholder={
          <Image
            key={atlasImageUrl}
            src={atlasImageUrl}
            style={{
              width: 'var(--image-width)',
              height: 'var(--image-height)'
            }}
          ></Image>
        }
        onLoad={() => {}}
      ></Image>
      {/* <ProgressiveImage
        src={previewUrl}
        placeholder={_url}
        
      >
        {(src) => <img src={src} width={300} height={300} alt="an alternative text" />}
      </ProgressiveImage> */}

      <div className="mask">{/* <Spin /> */}</div>
      <header>
        <Button
          icon={<DownloadBold />}
          type="text"
          onClick={handleDownload}
          className={styles.imageCardHeaderButton}
          loading={downloading}
        />

        <Button
          icon={<CrossBold />}
          type="text"
          onClick={onClose}
          className={styles.imageCardHeaderButton}
        />
      </header>
    </section>
  );
}
