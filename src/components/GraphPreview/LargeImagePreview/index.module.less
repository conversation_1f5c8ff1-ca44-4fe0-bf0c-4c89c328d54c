.auto-fill-container {
  width: 100%;
  height: 100%;
  overflow: hidden;

  display: flex;
  align-items: center;
  justify-content: center;
}

.large-image-preview-container {
  width: 100%;
  height: 100%;
  overflow: hidden;

  display: flex;

  .switch-page-container {
    flex: 0 0 auto;
    border: none;
    background: transparent;

    display: flex;
    align-items: center;

    &:global(.prev) {
      padding: 0 16px 0 32px;
      justify-content: flex-end;
    }

    &:global(.next) {
      padding: 0 32px 0 16px;
      justify-content: flex-start;
    }

    & > .switch-page-button {
      color: #fff;
      &:hover {
        color: #fff !important;
      }

      &:focus-visible {
        outline: none;
      }
    }
  }

  .image-container {
    flex: 8 8 0;
  }
}

.image-card-container {
  position: relative;
  width: var(--image-width);
  height: var(--image-height);
  overflow: hidden;

  & > header {
    position: absolute;
    left: 0;
    top: 0;
    box-sizing: border-box;
    width: 100%;
    padding: 16px 16px 0 16px;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    z-index: 100;

    transform: translateY(-100%);
    transition: transform 0.5s;
    &:hover {
      transform: translateY(0%);
    }

    & > .image-card-header-button {
      background: rgba(0, 0, 0, 0.5);
      color: #fff;
      &:hover {
        color: #fff !important;
        background: rgba(0, 0, 0, 0.5) !important;
      }
    }
  }

  & > img {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    user-select: none;
  }

  & > :global.mask {
    position: absolute;
    width: 100%;
    height: 100%;
    background: transparent;
    opacity: 1;
    left: 0;
    top: 0;
  }

  & > :global.mask:hover {
    & + header {
      transform: translateY(0);
    }
  }
}
