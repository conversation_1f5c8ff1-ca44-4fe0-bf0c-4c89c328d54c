@import '~@/styles/variables.less';
.error {
  border-radius: 8px;
  border: 1.2px solid @base-red-light-50;
  padding: 2px;
}
.graph-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: @size;

  &-container {
    height: calc(100% - @size-xl - @size - 56px);
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex: 1;

    :global .@{ant-prefix}-tabs {
      height: 100%;
      width: 100%;
      justify-content: center;

      &-content {
        height: 100%;
        display: flex;
        align-items: center;

        &-holder {
          width: 100%;
          height: 100%;
        }

        .invisible {
          background-size: auto 100%;
          background-repeat: no-repeat;
          border-radius: @size-xxs;
          overflow: hidden;
        }

        .@{ant-prefix}-tabs-tabpane {
          height: 100%;
          width: 100%;
          flex: 1;

          .@{ant-prefix}-image {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            border-radius: @size-xxs;

            &.image-background {
              background-image: url('../../assets/images/transparent-bg.png');
              background-size: 20px 20px;
            }

            &-mask {
              opacity: 1;
              background: transparent;
              cursor: auto;
            }

            &-img {
              max-width: 100%;
              max-height: 100%;
              width: auto;
              height: auto;
              border-radius: @size-xxs;
              // object-fit: contain;
              user-select: none;
            }

            .enable-large-image-preview {
              & + .@{ant-prefix}-image-mask {
                cursor: zoom-in;
              }
            }
          }
        }
      }
    }
  }

  &-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    max-width: calc(100vw - 456px);
    max-height: 100%;
    padding: @size-xl @size-xl 0;
    overflow: hidden;

    :global(.invisible) {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: @color-white;
      font-size: @size;
      line-height: @line-height-lg;
      background-image: none;

      &-icon {
        font-size: calc(@size-xl * 2);
        margin-bottom: @size;

        :global path {
          fill: @color-white;
        }
      }
    }
  }

  &-tabs:global(.@{ant-prefix}-tabs) {
    align-items: center;

    :global .@{ant-prefix}-tabs-nav {
      justify-content: center;
      max-width: 640px;
      margin-top: @size-sm;

      .preview-cover {
        position: absolute;
        top: 0;
        left: 0;
        width: 36px;
        height: 18px;
        border-radius: 6px 6px 6px 0px;
        color: @base-white-opacity-100;
        background: @background-tag-amount;
        font-size: 12px;
        font-style: normal;
        line-height: 16px;
        z-index: 1;
        text-align: center;
      }

      .check-icon {
        position: absolute;
        top: 0;
        right: 0;
        width: 16px;
        height: 16px;
        border-radius: 0px 4px;
        z-index: 1;

        .checked {
          text-align: center;
          background: @background-checkbox-selected;
          width: 100%;
          height: 100%;
          border-radius: 0px 4px;
          line-height: 12px;

          svg {
            color: @content-checkbox-selected;
            width: 10px;
            height: 10px;
          }
        }

        .no-check {
          border-radius: 0px 4px;
          width: 100%;
          height: 100%;
          border: 1px solid @stroke-checkbox;
          background: @background-checkbox;
        }
      }

      &-wrap {
        justify-content: center;
      }

      &::before {
        content: none !important;
      }

      .@{ant-prefix}-tabs-tab {
        position: relative;
        width: 120px;
        height: 120px;
        border: 1px solid @background-btn-hover;
        border-radius: @size-xxs;
        background-color: @color-bg-layout;
        opacity: 0.5;
        padding: 0;
        overflow: hidden;

        &-active {
          opacity: 1;
        }

        &:not(:first-child) {
          margin-left: @size-sm;
        }

        &-btn {
          width: 100%;
          height: 100%;
          display: flex;
          color: transparent !important;

          .@{ant-prefix}icon {
            margin-right: 0;
          }

          .@{ant-prefix}-image {
            display: flex;
            align-items: center;
            justify-content: center;

            &-img {
              object-fit: cover;
              min-height: 100%;
              min-width: 101%;
            }

            &-mask {
              display: none;
            }
          }
        }
      }

      .@{ant-prefix}-tabs-nav-operations {
        display: none !important;
      }
    }

    :global .@{ant-prefix}-tabs-ink-bar {
      display: none;
    }
  }

  .hidden-tabs {
    :global(.@{ant-prefix}-tabs-nav) {
      display: none;
    }
  }

  &-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.loading-mask {
  width: 100%;
  height: 100%;
  background-color: @background-system-space-holder;
}

.err-invisible {
  width: 100%;
  height: 100%;
  background-image: url('~@/assets/images/invisible-graph.jpg');
  background-size: cover;
  background-position: center center;
}
