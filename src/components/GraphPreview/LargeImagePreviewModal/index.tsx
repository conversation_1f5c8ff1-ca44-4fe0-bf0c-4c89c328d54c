import { Image } from 'antd';
import { LargeImagePreview } from '../LargeImagePreview';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useState
} from 'react';
import { CanvasSize } from '@/types';

enum PageControlsKey {
  ArrowLeft = 'ArrowLeft',
  ArrowRight = 'ArrowRight',
  ArrowDown = 'ArrowDown',
  ArrowUp = 'ArrowUp'
}

type ImageItem = { src: string; size: CanvasSize };

export type LargeImagePreviewModalHandle = {
  open: (imageUrls: Array<ImageItem>, defaultIndex: number) => void;
};

type LargeImagePreviewModalProps = {};

export const LargeImagePreviewModal = forwardRef<
  LargeImagePreviewModalHandle,
  LargeImagePreviewModalProps
>((props, ref) => {
  const [isOpen, setIsOpen] = useState(false);

  const [images, setImages] = useState<Array<ImageItem>>([]);
  const [activeIndex, setActiveIndex] = useState(0);

  function open(images: Array<ImageItem>, index: number) {
    setIsOpen(true);
    setActiveIndex(index);
    setImages(images);
  }

  function close() {
    setIsOpen(false);
  }

  useImperativeHandle(ref, () => ({
    open
  }));

  const nums = images.length;
  const goNext = useCallback(
    (index: number) => (nums === 0 ? 0 : (index + 1) % nums),
    [nums]
  );
  const goPrev = useCallback(
    (index: number) => (nums === 0 ? 0 : (index - 1 + nums) % nums),
    [nums]
  );

  useEffect(() => {
    if (!isOpen) {
      return;
    }

    function handleKeydown(e: KeyboardEvent) {
      switch (e.key) {
        case PageControlsKey.ArrowLeft:
        case PageControlsKey.ArrowUp:
          setActiveIndex(goPrev);
          break;
        case PageControlsKey.ArrowRight:
        case PageControlsKey.ArrowDown:
          setActiveIndex(goNext);
          break;
      }
    }
    window.addEventListener('keydown', handleKeydown);
    return () => {
      window.removeEventListener('keydown', handleKeydown);
    };
  }, [isOpen, goPrev, goNext]);

  return (
    <Image
      style={{ display: 'none' }}
      preview={{
        visible: isOpen,
        styles: {
          mask: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(37px)'
          }
        },
        toolbarRender: () => null,
        closeIcon: null,
        destroyOnClose: true,
        imageRender() {
          return (
            <LargeImagePreview
              activeIndex={activeIndex}
              images={images}
              onClose={close}
              onPrevPage={setActiveIndex.bind(null, goPrev)}
              onNextPage={setActiveIndex.bind(null, goNext)}
            />
          );
        }
      }}
    />
  );
});
