import { Button } from 'antd';
import { Compare } from '@meitu/candy-icons';
import { useState, useEffect, useRef } from 'react';

import classNames from 'classnames';
import styles from './compareGraphAction.module.less';

interface CompareGraphActionProps {
  /** 原始图片路径 */
  originGraph: string;

  /** 缩放模式 */
  zoomMode?: number;

  className?: string;

  maskClassName?: string;

  /** 对比时触发 */
  onCompare?: (originGraph: string) => void;
  /** 获取对比状态，是否在对比 */
  getCompareStatus?: (status: boolean) => void;
}

function getBackgroundSize(zoomMode?: number) {
  switch (zoomMode) {
    case 1:
      return 'cover';
    case 0:
      return '100% 100%';
    default:
      return 'contain';
  }
}

export function useOnMouseDown(mouseDownHandler: () => void) {
  const [comparing, setComparing] = useState(false);
  // 防止单击触发
  const isMouseDown = useRef<ReturnType<typeof setTimeout> | null>(null);

  const onMouseUp = () => {
    setComparing(false);
    isMouseDown.current && clearTimeout(isMouseDown.current);

    window.removeEventListener('mouseup', onMouseUp);
  };

  const onMouseDown = () => {
    isMouseDown.current = setTimeout(() => {
      !comparing && mouseDownHandler?.();

      setComparing(true);
    }, 128);

    window.addEventListener('mouseup', onMouseUp);
  };

  useEffect(
    () => () => {
      window.removeEventListener('mouseup', onMouseUp);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  return { comparing, onMouseDown };
}

export function CompareGraphAction(props: CompareGraphActionProps) {
  const {
    originGraph,
    className,
    maskClassName,
    zoomMode,
    onCompare,
    getCompareStatus
  } = props;
  const { comparing, onMouseDown } = useOnMouseDown(() => {
    onCompare?.(originGraph);
  });

  useEffect(() => {
    getCompareStatus?.(comparing);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [comparing]);

  return (
    <>
      <div
        className={classNames(
          styles.comparingMask,
          maskClassName,
          comparing && styles.active
        )}
        style={{
          backgroundImage: `url(${originGraph})`,
          backgroundSize: getBackgroundSize(zoomMode)
        }}
      />
      <Button
        icon={<Compare />}
        onMouseDown={onMouseDown}
        className={classNames(styles.comparingAction, className)}
        onClick={(e) => e.stopPropagation()}
      />
    </>
  );
}
