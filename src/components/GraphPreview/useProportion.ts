import type { CanvasSize } from '@/types';

import { useState, useLayoutEffect, useRef } from 'react';
import ResizeObserver from 'resize-observer-polyfill';
import _ from 'lodash';

export function useProportion(size?: CanvasSize) {
  const [proportion, setProportion] = useState<number>();
  const containerRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(
    () => {
      const container = containerRef.current;

      if (!container || !size || size.some((edge) => edge === 0)) {
        return;
      }

      const observer = new ResizeObserver(
        _.throttle(() => {
          const containerSize = [
            container.offsetWidth - 64,
            container.offsetHeight - 164
          ];

          // HACK 容器未渲染完成
          if (containerSize.some((offset) => offset <= 0)) {
            return;
          }

          setProportion(
            Math.min(containerSize[0] / size[0], containerSize[1] / size[1])
          );
        }, 512)
      );

      observer.observe(container);

      return () => {
        // 移除 observer
        observer.disconnect();
      };
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [containerRef.current, size]
  );

  return [proportion, containerRef] as const;
}
