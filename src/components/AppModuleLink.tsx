import type { PropsWithChildren } from 'react';

import { AppModule, generateRouteTo, getAppModule } from '@/services';
import { Link } from 'react-router-dom';
import { addQueryParams, getSource } from '@/utils';

export interface AppModuleLinkProps {
  appModule: AppModule;
}

/**
 * 应用模块链接
 */
export function AppModuleLink(props: PropsWithChildren<AppModuleLinkProps>) {
  const { appModule, children } = props;
  const module = getAppModule(appModule);
  const { title } = module;

  switch (appModule) {
    case AppModule.ImageEditor: {
      return (
        <Link
          to={addQueryParams(generateRouteTo(AppModule.ImageEditor) as string, {
            source: getSource()
          })}
          target="_blank"
        >
          {children ?? title}
        </Link>
      );
    }
    // 海报 需要重载页面
    case AppModule.Poster: {
      return (
        <Link
          to={addQueryParams(generateRouteTo(AppModule.Poster) as string, {
            source: getSource()
          })}
          reloadDocument
        >
          {children ?? title}
        </Link>
      );
    }
    case AppModule.Tutorial: {
      return (
        <Link to={generateRouteTo(AppModule.Tutorial)} target="_blank">
          {children ?? title}
        </Link>
      );
    }

    default: {
      return (
        <Link
          to={addQueryParams(generateRouteTo(appModule) as string, {
            source: getSource()
          })}
        >
          {children ?? (appModule === AppModule.Overview ? '首页' : title)}
        </Link>
      );
    }
  }
}
