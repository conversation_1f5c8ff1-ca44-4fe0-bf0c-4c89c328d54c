@import '~@/styles/variables.less';

.mei-dou {
  padding: 6px 16px 10px 16px;
  background-color: #fff;
  position: fixed;
  z-index: calc(@z-index-popup-base - 128);
  bottom: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 320px;
  background: @base-white-opacity-100;
  border-radius: 10px;
  box-shadow: @level-2;

  &.has-border {
    bottom: 0;
    border-radius: 0;
    box-shadow: @separator;
  }

  :global(.ant-row) {
    width: 100%;
  }
  .iconZcoolMeidou {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 2px;
  }

  .count {
    color: @content-system-primary;
    font-size: @text-12;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
  }

  .before-discount {
    color: @content-system-quaternary;
    font-size: @text-12;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    text-decoration: line-through;
  }

  .icon {
    font-size: 15px;

    &:global(.ant-image-img) {
      width: 22px;
      height: 22px;
      margin-top: -4px;
    }
  }

  .icon2 {
    font-size: 13px;
    color: @content-system-quaternary;
    cursor: pointer;
  }

  .details-trigger {
    color: @content-system-quaternary;
    font-size: @text-12;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    cursor: pointer;
  }

  :global(.ant-btn) {
    margin: 7px 0 6px 0;
    border: none;
  }

  .skeleton {
    height: 22px;

    h3 {
      margin-bottom: 0 !important;
    }
  }
}

.details-popover :global(.ant-popover) {
  &-title {
    margin-bottom: 12px !important;
  }

  &-inner-content {
    .details {
      display: flex;
      justify-content: space-between;

      span {
        color: @content-system-primary;
        font-size: @text-12;
        font-weight: 400;
        line-height: 16px;

        &.before-discount {
          color: @content-system-quaternary;
          text-decoration: line-through;
        }
      }
    }
  }
}

.mei-dou :global(.@{ant-prefix}-btn.@{ant-prefix}-btn-primary) {
  background: #fff200 !important;
  color: #1c1d1f !important;
  &:hover {
    background: #ffd100 !important;
  }
  &:after {
    background: rgb(255, 209, 0) !important;
    color: #1c1d1f !important;
  }
  &:before {
    background: rgb(255, 209, 0) !important;
    color: #1c1d1f !important;
  }
}

.tooltip :global(.ant-tooltip-inner) {
  font-size: @text-12 !important;
}

.butContainer {
  display: flex;
  justify-content: end;
}

.butCancel {
  display: flex;
  width: 108px;
  padding: 11px 20px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: var(--radius-m, 8px);
  border: 1px solid var(--text-text-secondary, #555);
  background: var(--color-grey-white-opacity-100, #fff);
  cursor: pointer;
  &:hover {
    background: #ededed;
  }
}
.butOK {
  display: flex;
  width: 108px;
  padding: 11px 20px;
  justify-content: center;
  align-items: center;
  margin-left: 16px;
  gap: 10px;
  border-radius: var(--radius-m, 8px);
  background: var(--button-button-primary-default, #fff200);
  cursor: pointer;
  &:hover {
    background: rgb(255, 209, 0) !important;
  }
}

.Stylemodal {
  :global(.ant-modal-content) {
    border-radius: 12px !important;
  }
}
