// 站酷小素材 余额 不显示任务中心
import { Popover, Space, Modal } from 'antd';
import styles from './index.module.less';
import { ReactComponent as ZcoolMeidou } from '@/icons/zcoolMeidou.svg';
import { Button } from '@/components';
import { useAccount } from '@/hooks';
import { useMeiDouBalanceZcool } from '@/hooks/useMeiDouZcool';
import { trackMeidouEvent } from './trackMeidouEvent';
import { AppOrigin, appOrigin } from '@/constants';
import { useEffect, useRef, useState } from 'react';
import { encode } from 'js-base64';
import { useAccountProfile } from '@/hooks';
import { zcoolUniversalInterface } from '@/api/aiMaterialZcool';
import { getSessionStorageItem } from '@meitu/util';

export interface MeidouBalanceProps {}

export const MeidouBalance = (props: MeidouBalanceProps) => {
  const { isLogin } = useAccount();
  const { accountProfile } = useAccountProfile();

  const {
    availableAmount,
    updateMeiDouBalance
    // tips,
    // benefitsDescription,
    // detailTitle,
    // detailDesc,
    // benefitsDetail
  } = useMeiDouBalanceZcool();

  // const openSubscribePopup = useOpenSubscribePopup();
  // const { isVipCurrent } = useMembershipDesc();
  const containerRef = useRef<HTMLDivElement>(null);
  const [open, setOpen] = useState(false);
  const [giftOpen, setGiftOpen] = useState(false);
  const [giftNum, setGiftNum] = useState<string | number>(0);

  useEffect(() => {
    let timer: ReturnType<typeof setTimeout>;
    let isCancelled = false;

    const mtUid = getSessionStorageItem('meituUid') || '';
    if (!mtUid) {
      return;
    }

    async function poll() {
      try {
        const res = await zcoolUniversalInterface({
          uri: '/p1/wheeSubscribe/points/grantTrialPoints',
          params: {
            uid: mtUid
          }
        });

        if (isCancelled) return;

        if (res?.success) {
          // 停止轮询
          timer && clearTimeout(timer);
          // 更新美豆数据
          if (updateMeiDouBalance) {
            updateMeiDouBalance();
          }
          // 展示弹窗
          setGiftNum(res?.data || ' ');
          setGiftOpen(true);
        } else if (!res?.success && res?.errorCode === '1000') {
          // 继续轮询
          timer = setTimeout(poll, 10000);
        } else {
          // 停止轮询
          timer && clearTimeout(timer);
        }
      } catch (e) {
        if (!isCancelled) {
          // 出错也继续轮询
          timer = setTimeout(poll, 10000);
        }
      }
    }

    poll(); // 初始化立即触发

    return () => {
      isCancelled = true;
      timer && clearTimeout(timer);
    };
  }, []);

  if (!isLogin || appOrigin !== AppOrigin.Whee) return null;

  const meituUid = encode(String(accountProfile?.id));

  const showModal = () => {
    setOpen(true);
  };

  const handleOk = () => {
    setOpen(false);
    if (updateMeiDouBalance) {
      updateMeiDouBalance();
    }
  };

  const handleCancel = () => {
    setOpen(false);
    if (updateMeiDouBalance) {
      updateMeiDouBalance();
    }
  };

  const zcoolLink = {
    development: 'https://pre.zcool.com.cn',
    pre: 'https://pre.zcool.com.cn',
    beta: 'https://beta.zcool.com.cn',
    release: 'https://www.zcool.com.cn'
  };
  const zcoolMyLink = {
    development: 'https://pre-my.zcool.com.cn',
    pre: 'https://pre-my.zcool.com.cn',
    beta: 'https://beta-my.zcool.com.cn',
    release: 'https://my.zcool.com.cn'
  };

  return (
    <>
      <Popover
        overlayClassName={styles.popoverOverlay}
        placement="bottomLeft"
        arrow={false}
        onOpenChange={(open) => {
          if (!open) {
            // setDetailModalOpen(false);
            return;
          }

          trackMeidouEvent('beauty_coin_floating_layer_exp');
        }}
        content={
          <>
            <Button
              key="submit"
              type="primary"
              className={styles.buttonZcool}
              onClick={() => {
                window.open(
                  `${
                    zcoolLink[process.env.REACT_APP_ENV]
                  }/subscription/pro?m=3&z_source=5&meituUid=${meituUid}`,
                  '_blank'
                );
                showModal();
              }}
            >
              获取Z豆
            </Button>
          </>
        }
        title={
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}
          >
            <Space size={4} className={styles.title}>
              <span ref={containerRef} className={styles.container}>
                <ZcoolMeidou />
              </span>
              {availableAmount}
            </Space>

            <a
              href={`${
                zcoolMyLink[process.env.REACT_APP_ENV]
              }/retouching/manage?type=points&meituUid=${meituUid}`}
              target="_blank"
              className={styles.linkzcool}
            >
              Z豆明细
            </a>
          </div>
        }
      >
        <div className={styles.trigger}>
          <span className={styles.icon}>
            <ZcoolMeidou />
          </span>
          {availableAmount}
        </div>
        <Modal
          open={open}
          title="获取Z豆"
          onOk={handleOk}
          onCancel={handleCancel}
          footer={null}
          width={400}
          style={{
            borderRadius: '12px'
          }}
          className={styles.Stylemodal}
        >
          <p style={{ marginTop: 12, marginBottom: 20 }}>
            开通会员/会员充值可获得Z豆，立即生成精美素材
          </p>

          <div className={styles.butContainer}>
            <div
              className={styles.butCancel}
              onClick={() => {
                handleCancel();
              }}
            >
              取消
            </div>
            <div
              className={styles.butOK}
              onClick={() => {
                handleOk();
              }}
            >
              购买完成
            </div>
          </div>
        </Modal>
      </Popover>

      <Modal
        open={giftOpen}
        title={
          <div
            style={{
              textAlign: 'center',
              fontSize: '16px',
              fontWeight: 600,
              fontFamily: 'PingFang SC',
              lineHeight: '22px',
              color: '#1C1D1F'
            }}
          >
            惊喜Z豆到账
          </div>
        }
        onCancel={() => setGiftOpen(false)}
        footer={null}
        width={420}
        style={{
          borderRadius: '12px'
        }}
        className={styles.giftModal}
        centered
      >
        <div
          style={{
            padding: '20px 0 24px',
            color: '#1C1D1F',
            fontSize: '16px',
            lineHeight: '22px',
            fontFamily: 'PingFang SC',
            textAlign: 'center'
          }}
        >
          恭喜获得 {giftNum} Z豆，仅限今日使用！马上去生成你的专属素材吧～
        </div>

        <div style={{ textAlign: 'center' }}>
          <span
            className={styles.giftModalOk}
            onClick={() => {
              setGiftOpen(false);
            }}
          >
            立即体验
          </span>
        </div>
      </Modal>
    </>
  );
};

export type MeidouBalanceWithMissionCenterEntryProps = {
  /**
   * 自定义点击“任务中心”入口的行为
   */
  onClickEntry?: (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => void;
};

export function MeidouBalanceWithMissionCenterEntry({}: MeidouBalanceWithMissionCenterEntryProps) {
  const { isLogin } = useAccount();
  /**
   * 在没有登录或者在大模型官网时 不展示
   */
  if (!isLogin || appOrigin !== AppOrigin.Whee) return null;

  return (
    <div className={styles.balance}>
      <MeidouBalance />
    </div>
  );
}
