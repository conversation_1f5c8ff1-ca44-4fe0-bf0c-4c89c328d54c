@import '~@/styles/variables.less';
@background-editor-hover-overlay: rgba(20, 31, 51, 0.08);

.popoverOverlay :global(.@{ant-prefix}-popover) {
  &-content {
    max-width: 288px;

    :global(.@{ant-prefix}-popover-inner) {
      padding: 20px;

      &-content {
        color: @content-system-tertiary;
        font-size: @text-12 !important;
        font-weight: 400;
        line-height: 16px;

        .contents {
          font-size: @text-12 !important;
          white-space: pre-wrap;
          margin-bottom: 16px;
        }

        .benefits-desc {
          font-size: @text-12;
          color: @content-system-primary;
          margin-bottom: 8px;
        }
      }
    }
  }

  &-title {
    .title {
      color: @content-system-primary;
      font-size: @text-18;
      font-weight: 700;
      line-height: 20px;

      .container {
        position: relative;
        top: 1px;
      }

      .info-circle {
        cursor: pointer;
        color: @content-system-quaternary;
      }
    }
  }
}
.linkzcool {
  color: #939599 !important;
  font-weight: 400;

  &:hover {
    color: #ff7300 !important;
  }
}
.buttonZcool {
  margin-top: 12px;
  width: 248px;
  background-color: #fff200 !important;
  color: #1c1d1f !important;
  box-shadow: none !important;
  &:after {
    background: rgb(255, 209, 0) !important;
    color: #1c1d1f !important;
  }
}

.trigger {
  box-sizing: content-box;
  min-width: 70px;
  height: 36px;
  border: none;
  background: transparent;
  color: @content-system-primary;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  box-shadow: none;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none !important;
  cursor: pointer;
  .icon {
    // color: transparent;
    font-size: 18px;
    margin-right: 4px;
  }
  &:hover {
    background: rgba(20, 31, 51, 0.08);
  }
}

.trigger:global(
    .@{ant-prefix}-btn.@{ant-prefix}-btn-primary:not(:disabled):not(
        .ant-btn-circle
      )
  ) {
  box-sizing: content-box;
  min-width: 51px;
  height: 36px;
  border: none;
  background: transparent;
  color: @content-system-primary;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  box-shadow: none;
  padding: 0 10px 0 8px;
  display: flex;
  align-items: center;
  box-shadow: none !important;

  div {
    display: none; // 关闭ant design的Button组件的点击动画
  }

  .icon {
    color: transparent;
    font-size: 18px;
    margin-right: 2px;
  }

  &:hover {
    background-color: rgba(20, 31, 51, 0.08);
  }

  &::after,
  &::before {
    content: none !important;
  }
}

.detail {
  left: -316px !important;
  top: -20px !important;
  position: absolute;
  width: 288px;

  .title {
    position: relative;
    color: @content-system-primary;
    font-size: 16px;
    font-weight: 600 !important;
    height: 22px;
    line-height: 22px !important;
    text-align: center;
    margin-bottom: 6px;

    .detail-trigger {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
      color: @content-system-quaternary;
      font-size: @text-12;
      font-weight: 400;

      &:hover {
        color: @content-system-link;
      }
    }
  }

  .description {
    text-align: center;
    margin-bottom: 20px;
    white-space: pre-wrap;
    color: @content-system-quaternary;
  }

  .container {
    margin-bottom: 8px;
    max-height: 124px;
    overflow-y: auto;
    padding-right: 10px;
    margin-right: -10px;

    .content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      color: @content-system-primary;
      margin-bottom: 8px;
      line-height: 20px;

      .type {
        font-size: 14px;

        &.bold {
          font-weight: 600;
        }
      }

      .desc {
        font-size: 11px;
      }

      .amount {
        font-size: 14px;
        font-weight: 700;
      }
    }
  }
}

.balance {
  height: 38px;
  border-radius: 100px;
  border: 1px solid #e9ebf0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  font-size: 14px;
  user-select: none;

  :global(.mission-entry) {
    width: 76px;
    height: 100%;
    display: flex;
    align-items: center;
    padding-left: 12px;
    box-sizing: border-box;
    justify-content: space-between;
    color: @content-system-primary;

    &::after {
      content: '';
      display: block;
      width: 1px;
      height: 20px;
      background: #e9ebf0;
    }

    &:hover {
      color: @content-system-primary;
      background-color: @background-editor-hover-overlay;
    }
  }

  &
    > .trigger:global(
      .@{ant-prefix}-btn.@{ant-prefix}-btn-primary:not(:disabled)
    ) {
    padding: 0;
    padding-left: 8px;
    padding-right: 10px;
    border: none;
    height: 100%;
    border-radius: 0;
  }
}

.butContainer {
  display: flex;
  justify-content: end;
}

.butCancel {
  display: flex;
  width: 108px;
  padding: 11px 20px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: var(--radius-m, 8px);
  border: 1px solid var(--text-text-secondary, #555);
  background: var(--color-grey-white-opacity-100, #fff);
  cursor: pointer;
  &:hover {
    background: #ededed;
  }
}
.butOK {
  display: flex;
  width: 108px;
  padding: 11px 20px;
  justify-content: center;
  align-items: center;
  margin-left: 16px;
  gap: 10px;
  border-radius: var(--radius-m, 8px);
  background: var(--button-button-primary-default, #fff200);
  cursor: pointer;
  &:hover {
    background: rgb(255, 209, 0) !important;
  }
}

.Stylemodal {
  :global(.ant-modal-content) {
    border-radius: 12px !important;
  }
}

.giftModal {
  :global(.ant-modal-header) {
    margin-bottom: 0px !important;
  }
  :global(.ant-modal-content) {
    border-radius: 12px !important;
    padding: 20px 20px 24px !important;
  }
}

.giftModalOk {
  width: 220px;
  height: 40px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background: #fff200;
  color: #222;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;

  &:hover {
    cursor: pointer;
    background: #ffd100;
  }
}
