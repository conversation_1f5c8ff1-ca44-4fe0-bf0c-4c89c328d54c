import { Col, Popover, Row, Space, Tooltip, Modal } from 'antd';
import styles from './meiDouButton.module.less';
import { ChevronDownBold, ExclamationMarkCircleBold } from '@/icons';
import { ComponentPropsWithoutRef, MouseEvent, useState } from 'react';
import { Disclaimer } from '../Disclaimer';
import { MeiDouFetchPriceDescResponse } from '@/api/types/meidou';
import classNames from 'classnames';
import { useMembershipDesc } from '@/hooks/useMemberZcool';
import { AppOrigin, appOrigin } from '@/constants';
import { VipButton } from '../VipButton';
import { useMeiDouBalanceZcool } from '@/hooks/useMeiDouZcool';
import { encode } from 'js-base64';
import { useAccountProfile } from '@/hooks';
import { ReactComponent as ZcoolMeidou } from '@/icons/zcoolMeidou.svg';

export interface MeiDouButtonProps
  extends ComponentPropsWithoutRef<typeof VipButton> {
  price: MeiDouFetchPriceDescResponse | undefined;
  buttonText?: string;
  fetchPriceLoading?: boolean;
  hasBorder?: boolean;
  functionId?: string;
  updateMeiDouBalance?: () => void;
}
// ab测试code
// const abTestCode = process.env.REACT_APP_ABTEST_CODE;
// const abTestMeidouCode = process.env.REACT_APP_ABTEST_CODE_MEIDOU;

export const MeiDouButton = ({
  price,
  buttonText: buttonTextFromProps,
  fetchPriceLoading,
  hasBorder = true,
  onClick: onClickProp,
  className,
  functionId,
  updateMeiDouBalance,
  ...restProps
}: MeiDouButtonProps) => {
  const { isVipCurrent } = useMembershipDesc();
  const { availableAmount } = useMeiDouBalanceZcool();
  const { accountProfile } = useAccountProfile();

  const [open, setOpen] = useState(false);

  const showModal = () => {
    setOpen(true);
  };

  const handleOk = () => {
    setOpen(false);
    if (updateMeiDouBalance) {
      updateMeiDouBalance();
    }
  };

  const handleCancel = () => {
    setOpen(false);
    if (updateMeiDouBalance) {
      updateMeiDouBalance();
    }
  };

  const meituUid = encode(String(accountProfile?.id));

  // const { accountProfile } = useAccountProfile();

  const deficit = (availableAmount ?? 0) < (price?.amount ?? 0);
  const buttonText = deficit ? '余额不足' : buttonTextFromProps ?? '立即生成';

  const zcoolLink = {
    development: 'https://pre.zcool.com.cn',
    pre: 'https://pre.zcool.com.cn',
    beta: 'https://beta.zcool.com.cn',
    release: 'https://www.zcool.com.cn'
  };

  const onClick = (e: MouseEvent<HTMLAnchorElement & HTMLButtonElement>) => {
    if (restProps.loading) {
      e.preventDefault();
      return;
    }
    if (deficit) {
      e.preventDefault();
      // 余额不足时，打开站酷充值页
      window.open(
        `${
          zcoolLink[process.env.REACT_APP_ENV]
        }/subscription/pro?m=3&z_source=4&meituUid=${meituUid}`,
        '_blank'
      );

      showModal();
      return;
    }

    onClickProp?.(e);
  };

  return (
    <div
      className={classNames(
        styles.meiDou,
        {
          [styles.hasBorder]: hasBorder
        },
        className
      )}
    >
      {appOrigin === AppOrigin.Whee && (
        <Row justify="space-between" align="middle">
          <>
            <Col>
              <Space size={0}>
                <ZcoolMeidou className={styles.iconZcoolMeidou} />
                {/* <Image src={ZcoolMeidou} preview={false} className={styles.icon} /> */}
                <span className={styles.count}>
                  {price?.costPriceText ?? ''}
                </span>

                {price?.costPriceTextOrigin &&
                  '消耗 ' + price.costPriceTextOrigin !==
                    price.costPriceText && (
                    <span className={styles.beforeDiscount}>
                      {price.costPriceTextOrigin}
                    </span>
                  )}

                {price?.costPriceTips && (
                  <Tooltip
                    overlayClassName={styles.tooltip}
                    title={
                      <span
                        dangerouslySetInnerHTML={{
                          __html: price.costPriceTips
                        }}
                      />
                    }
                  >
                    <ExclamationMarkCircleBold className={styles.icon2} />
                  </Tooltip>
                )}
              </Space>
            </Col>

            {price?.priceDetail && (
              <Col>
                <Popover
                  overlayClassName={styles.detailsPopover}
                  title="明细"
                  arrow={false}
                  placement="topRight"
                  content={
                    <>
                      {price.priceDetail.map((detail) => (
                        <div key={detail.itemName} className={styles.details}>
                          <Space size={4}>
                            <span>{detail.itemName}</span>
                            <span>{detail.itemCount}</span>
                          </Space>
                          <Space>
                            {detail.priceOrigin !== detail.priceNow && (
                              <span className={styles.beforeDiscount}>
                                {detail.priceOrigin}
                              </span>
                            )}

                            <span>{detail.priceNow}</span>
                          </Space>
                        </div>
                      ))}
                    </>
                  }
                >
                  <Space
                    className={styles.detailsTrigger}
                    size={4}
                    align="center"
                  >
                    明细
                    <ChevronDownBold className={styles.icon2} />
                  </Space>
                </Popover>
              </Col>
            )}
          </>
        </Row>
      )}

      <VipButton
        isVip={price?.isVip || isVipCurrent}
        className={styles.zcoolButton}
        type="primary"
        {...restProps}
        onClick={onClick}
      >
        {buttonText}
      </VipButton>

      <Disclaimer />
      <Modal
        open={open}
        title="Z豆余额不足"
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
        width={400}
        style={{
          borderRadius: '12px'
        }}
        className={styles.Stylemodal}
      >
        {price?.isVip ? (
          <p style={{ marginTop: 12, marginBottom: 20 }}>
            立即充值，获得更多生成次数
          </p>
        ) : (
          <p style={{ marginTop: 12, marginBottom: 20 }}>
            开通会员可获得Z豆，体验更多高级功能~
          </p>
        )}
        <div className={styles.butContainer}>
          <div
            className={styles.butCancel}
            onClick={() => {
              handleCancel();
            }}
          >
            取消
          </div>
          <div
            className={styles.butOK}
            onClick={() => {
              handleOk();
            }}
          >
            {price?.isVip ? '我已充值' : '我已开通'}
          </div>
        </div>
      </Modal>
    </div>
  );
};
