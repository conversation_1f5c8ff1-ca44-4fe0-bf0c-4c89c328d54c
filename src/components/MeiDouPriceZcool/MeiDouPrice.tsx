import { fetchZcoolPriceDesc } from '@/api/aiMaterialZcool';
import { FunctionCode, MeiDouFetchPriceDescResponse } from '@/api/types/meidou';
import {
  MeiDouPriceRef,
  MeiDouPriceProps
} from '@/components/MeiDouPrice/MeiDouPrice';
import { AppOrigin, appOrigin } from '@/constants';
import { CustomEventNames } from '@/constants/customEventNames';
import { useRootStore } from '@/containers/Poster/store';
import { useCustomEvent } from '@/hooks';
import {
  useMembershipChanged,
  useMembershipDesc
} from '@/hooks/useMemberZcool';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';
import { toSnakeCase } from '@meitu/util';
import { Form } from 'antd';
import _ from 'lodash';
import {
  Ref,
  RefObject,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useState
} from 'react';
import { useDeepCompareEffect } from 'react-use';

const MeiDouPriceWithoutRef = <T extends FunctionCode>(
  { children, functionCode, getFunctionBody, fields = [] }: MeiDouPriceProps<T>,
  ref: Ref<MeiDouPriceRef>
) => {
  const [loading, setLoading] = useState(true);
  const [desc, setDesc] = useState<MeiDouFetchPriceDescResponse>();
  const { posterStore } = useRootStore();

  const { refreshMembershipDesc, isVipCurrent } = useMembershipDesc();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const getPrice = async () => {
    // FIXME 等待其他来源移出去后，这里的判断可以去掉
    if (appOrigin !== AppOrigin.Whee) return;

    setLoading(true);

    const fixtureParams = {
      functionCode,
      functionBody: JSON.stringify(toSnakeCase(getFunctionBody()))
    };
    try {
      const res = await fetchZcoolPriceDesc(fixtureParams);
      setDesc(res);

      if (functionCode === FunctionCode.aiPoster) {
        posterStore.setPriceInfo(res);
      }

      // 会员状态变化时刷新会员详情
      if (isVipCurrent !== res.isVip) refreshMembershipDesc();
    } catch (error) {
      defaultErrorHandler(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!!fields.length) return;

    getPrice();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fields.length]);

  // 会员变化时重新获取价格
  useMembershipChanged(getPrice);
  // 支付成功后，重新获取价格。中台下发限免张数会有延迟
  useCustomEvent(CustomEventNames.ReGetPrice, () => {
    setTimeout(() => {
      getPrice();
    }, 3000);
  });

  useImperativeHandle(ref, () => ({ getPrice, desc }), [getPrice, desc]);

  return (
    <>
      {children(desc, loading)}
      {!!fields.length && (
        <PriceChangeByFields fields={fields} getPrice={getPrice} />
      )}
    </>
  );
};

export const MeiDouPrice = forwardRef(MeiDouPriceWithoutRef) as <
  T extends FunctionCode
>(
  props: MeiDouPriceProps<T> & { ref?: RefObject<MeiDouPriceRef> }
) => ReturnType<typeof MeiDouPriceWithoutRef>;

export type PriceChangeByFieldsType = {
  fields: string[];
  getPrice: () => void;
};

export const PriceChangeByFields = ({
  fields,
  getPrice
}: PriceChangeByFieldsType) => {
  const form = Form.useFormInstance();
  const deps = Form.useWatch([], form);
  const fixturesDeps = _.pick(deps, fields);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceGetPrice = useCallback(_.debounce(getPrice, 500), [getPrice]);

  useDeepCompareEffect(() => {
    if (!Object.keys(fixturesDeps).length) return;
    if (Object.values(fixturesDeps).every((v) => v === undefined)) return;

    debounceGetPrice();

    return () => {
      debounceGetPrice.cancel();
    };
  }, [fixturesDeps]);

  return null;
};
