import { Segmented as AntdSegmented, Form } from 'antd';
import { ComponentPropsWithoutRef } from 'react';
import styles from './index.module.less';
import classNames from 'classnames';

export type SegmentedType = Omit<
  ComponentPropsWithoutRef<typeof AntdSegmented>,
  'size'
> & {
  large?: boolean;
};

export const Segmented = ({
  rootClassName,
  block = true,
  large = false,
  ...restProps
}: SegmentedType) => {
  const { status } = Form.Item.useStatus();

  return (
    <AntdSegmented
      size={large ? 'large' : 'middle'}
      rootClassName={classNames(styles.segmented, {
        [styles.error]: status === 'error'
      })}
      block={block}
      {...restProps}
    />
  );
};
