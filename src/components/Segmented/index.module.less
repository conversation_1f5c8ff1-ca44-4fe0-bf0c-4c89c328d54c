@import '~@/styles/variables.less';

.segmented {
  &.error {
    border: 1px solid @color-error;
  }

  &:global(.@{ant-prefix}-segmented) {
    border-radius: 10px !important;
    background-color: @background-tab;

    :global(.@{ant-prefix}-segmented) {
      &-item {
        border-radius: 8px !important;
        color: @content-tab !important;

        &:hover {
          opacity: 0.8;

          .ant-segmented-item-label {
            color: var(--content-tab, #616366) !important;
          }

          &::after {
            background: none !important;
          }
        }

        &-selected {
          background-color: @background-tab-selected;
          color: @content-tab-selected !important;
          box-shadow: @level-1;
          font-weight: 600;

          &:hover {
            opacity: 1;
          }
        }

        &-label {
          min-height: 24px;
          line-height: 24px;
          font-size: @text-12-bold;
          font-weight: 400;
        }
      }

      &-thumb {
        background-color: @background-tab-selected;
        border-radius: 8px;
      }
    }
  }

  &:global(.@{ant-prefix}-segmented-lg) {
    :global(.@{ant-prefix}-segmented-item) {
      &-label {
        min-height: 30px !important;
        line-height: 30px !important;
        font-size: @text-14-bold !important;
        font-weight: 400;
      }
    }
  }
}
