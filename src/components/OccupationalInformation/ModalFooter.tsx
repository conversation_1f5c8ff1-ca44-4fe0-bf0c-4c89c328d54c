import { Button, Flex } from 'antd';
import styles from './ModalFooter.module.less';
const boxStyle: React.CSSProperties = {
  width: '100%'
};

type Iporps = {
  buttonContext: string;
  stepHandler: () => void;
  step: 1 | 2;
  job: string;
  secondPageParams: string[];
  postHandler: () => Promise<void>;
};
export const ModalFooter = ({
  buttonContext,
  stepHandler,
  step,
  job,
  secondPageParams,
  postHandler
}: Iporps) => {
  return (
    <Flex
      gap="middle"
      style={boxStyle}
      justify={'space-between'}
      align={'center'}
    >
      <div className={styles.tipContainer}>提交成功送10美豆哦！</div>
      <Flex gap="middle" justify={'space-between'} align={'center'}>
        <Button
          className={styles.modalButton}
          onClick={stepHandler}
          disabled={step === 1 && !job}
        >
          {buttonContext}
        </Button>
        {step === 2 && (
          <Button
            type="primary"
            className={styles.modalButton}
            onClick={postHandler}
            disabled={secondPageParams.length < 1}
          >
            提交
          </Button>
        )}
      </Flex>
    </Flex>
  );
};
