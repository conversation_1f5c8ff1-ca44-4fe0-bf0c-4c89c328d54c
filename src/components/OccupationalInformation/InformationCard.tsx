import classnames from 'classnames';
import styles from './InformationCard.module.less';
import { Input } from 'antd';
import { useState } from 'react';
type CardProps = {
  img?: string;
  desc: string;
  params: string[] | string;
  clickItem: (key: string) => void;
  code: string;
};
export interface FormRef {
  output: (val: string) => void;
}
export const InformationCard = ({
  img,
  desc,
  clickItem,
  code,
  params
}: CardProps) => {
  const clickCurrent = () => {
    clickItem(code);
  };

  return (
    <div
      className={classnames(
        styles.cardBody,
        params.includes(code) ? styles.selected : ''
      )}
      onClick={clickCurrent}
    >
      <div className={styles.avatar}>
        <img src={img} alt="" />
      </div>
      <p className={styles.desc}>{desc}</p>
    </div>
  );
};

type FormProps = {
  clickItem: (key: string) => void;
  code: string;
  params: string[] | string;
  desc: string;
  inputBlurTrack: (val: string) => void;
  inputVal: string;
  inputHandler: (val: string) => void;
};

export const InformationForm = ({
  clickItem,
  code,
  params,
  desc,
  inputBlurTrack,
  inputVal,
  inputHandler
}: FormProps) => {
  const clickCurrent = () => {
    clickItem(code);
  };

  const [localInputVal, setLocalInputVal] = useState(inputVal);

  const handleInputBlur = (e: React.ChangeEvent<HTMLInputElement>) => {
    inputBlurTrack(e.target.value);
    inputHandler(localInputVal);
  };
  return (
    <div
      className={classnames(
        styles.cardBody,
        params.includes(code) ? styles.selected : ''
      )}
      onClick={clickCurrent}
    >
      {params.includes(code) ? (
        <div className={styles.inputContainer}>
          <Input
            placeholder="请填写"
            value={localInputVal}
            onChange={(e) => setLocalInputVal(e.currentTarget.value)}
            onBlur={handleInputBlur}
          ></Input>
        </div>
      ) : (
        <div className={styles.avatar}>
          <div className={styles.more}>
            <span />
            <span />
            <span />
          </div>
        </div>
      )}
      <p className={styles.desc}>{desc}</p>
    </div>
  );
};
