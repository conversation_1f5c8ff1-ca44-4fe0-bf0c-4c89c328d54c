.card-container {
  width: 160px;
  height: 120px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  cursor: pointer;
  border: 1px solid var(--stroke-border_overlay, rgba(0, 0, 0, 0.1));
  overflow: hidden;
}
.cover,
.infomation {
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  img {
    width: 158px;
    height: 100%;
    left: 0px;
    top: 0px;
    position: absolute;
  }
}
.cover {
  position: relative;
  box-sizing: content-box;
}
.padding8 {
  padding: 0 8px;
  align-items: flex-end;
  box-sizing: border-box;
}
.infomation {
  display: flex;
  flex-direction: column;
  padding-top: 4px;
  box-sizing: border-box;
  justify-content: flex-start;
}
.title {
  color: var(--content-primary, #242526);
  text-align: center;
  margin: 0;

  /* text_14_medium */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 142.857% */
}
.desc {
  color: var(--content-secondary, #787b80);
  text-align: center;
  margin: 0;

  /* text_12 */
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 133.333% */
}
.selected {
  border: 2px solid var(--stroke-webTagSelected, #3549ff);
}
.more {
  width: 22.67px;
  height: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  span {
    display: block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #939599;
  }
}
.imgBorder {
  left: -1px !important;
  top: -1px !important;
}
