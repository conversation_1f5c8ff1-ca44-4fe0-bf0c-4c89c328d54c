.card-body {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  row-gap: 8px;
  border-radius: 12px;
  border: 1px solid var(--stroke-border_overlay, rgba(0, 0, 0, 0.1));
  width: 160px;
  height: 120px;
  cursor: pointer;
  padding: 0 8px;
  .avatar {
    width: 54px;
    height: 54px;
    background-color: var(--backfround-container, #f5f5fa);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 30px;
      height: 30px;
    }
    .more {
      width: 22.67px;
      height: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        display: block;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: #939599;
      }
    }
  }
  .desc {
    color: var(--content-primary, #242526);
    text-align: center;

    /* text_14_medium */
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px; /* 142.857% */
  }
}
.selected {
  border: 2px solid var(--stroke-webTagSelected, #3549ff);
}
.inputContainer {
  width: 100%;
  height: 54px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}
