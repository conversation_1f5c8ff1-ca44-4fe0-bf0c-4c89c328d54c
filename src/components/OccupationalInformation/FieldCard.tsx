import classnames from 'classnames';
import styles from './FieldCard.module.less';
import { Input } from 'antd';
import { useState } from 'react';

type IProps = {
  title: string;
  desc?: string;
  img?: string;
  params: string[];
  code: string;
  clickItem: (key: string) => void;
};

export const FieldCard = ({
  title,
  img,
  desc,
  params,
  code,
  clickItem
}: IProps) => {
  return (
    <div
      className={classnames(
        styles.cardContainer,
        params.includes(code) && styles.selected
      )}
      onClick={() => clickItem(code)}
    >
      <div className={styles.cover}>
        <img
          src={img}
          alt=""
          className={classnames(params.includes(code) && styles.imgBorder)}
        />
      </div>
      <div className={styles.infomation}>
        <p className={styles.title}>{title}</p>
        <p className={styles.desc}>{desc}</p>
      </div>
    </div>
  );
};

type FormType = {
  title: string;
  desc?: string;
  params: string[];
  code: string;
  clickItem: (key: string) => void;
  inputBlurTrack: (code: string) => void;
  inputVal: string;
  inputHandler: (val: string) => void;
};

export const FieldForm = ({
  title,
  desc,
  params,
  code,
  clickItem,
  inputBlurTrack,
  inputVal,
  inputHandler
}: FormType) => {
  const [localInputVal, setLocalInputVal] = useState(inputVal);

  const handleInputBlur = (e: React.ChangeEvent<HTMLInputElement>) => {
    inputBlurTrack(e.target.value);
    inputHandler(localInputVal);
  };
  const defaultView = (
    <div className={styles.more}>
      <span />
      <span />
      <span />
    </div>
  );

  const activeView = (
    <Input
      placeholder="请输入"
      value={localInputVal}
      onChange={(e) => setLocalInputVal(e.target.value)}
      onBlur={handleInputBlur}
      onClick={(e) => e.stopPropagation()}
    />
  );

  const currentView = params.includes(code) ? activeView : defaultView;

  return (
    <div
      className={classnames(
        styles.cardContainer,
        params.includes(code) && styles.selected
      )}
      onClick={() => clickItem(code)}
    >
      <div className={classnames(styles.cover, styles.padding8)}>
        {currentView}
      </div>
      <div className={styles.infomation}>
        <p className={styles.title}>{title}</p>
        <p className={styles.desc}>{desc}</p>
      </div>
    </div>
  );
};
