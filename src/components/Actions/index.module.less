@import '~@/styles/variables.less';

.action:global(.@{ant-prefix}-dropdown) {
  :global .@{ant-prefix}-dropdown-menu {
    padding: 6px 0;
    background-color: @background-system-drop-list;

    :global(.@{ant-prefix}-dropdown-menu-item-divider) {
      margin: 0;
    }

    :global(.@{ant-prefix}-dropdown-menu-item) {
      border-radius: 0;
      padding: 0;
      font-weight: 400;
      font-size: @text-14;
      line-height: 20px;
      color: @content-list-primary;

      &:hover {
        background-color: @background-list-hover;
      }
    }
  }
}

.action-target:global(.@{ant-prefix}-btn.@{ant-prefix}-btn-icon-only) {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;

  > span {
    transform: scale(1);
  }

  // :global(.@{ant-prefix}-spin) {
  //   height: 100%;
  //   margin-top: 4px;
  // }

  :global(.@{ant-prefix}-spin-dot-spin) {
    font-size: 12px;
  }
}
