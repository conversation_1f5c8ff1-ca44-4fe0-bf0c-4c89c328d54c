import { Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { ComponentPropsWithoutRef, ReactNode, useMemo } from 'react';
import React from 'react';
import styles from './index.module.less';
import classNames from 'classnames';
import { EllipsisBlack } from '@meitu/candy-icons';
import _ from 'lodash';
import { Button } from '../Button';

export interface ActionsProps
  extends ComponentPropsWithoutRef<typeof Dropdown> {
  children: ReactNode[] | ReactNode;
  disabled?: boolean;
  triggerProps?: ComponentPropsWithoutRef<typeof Button>;
}

const Trigger = (props: ActionsProps['triggerProps']) => {
  if (props?.children && React.isValidElement(props.children)) {
    return React.cloneElement(props.children, _.omit(props, 'children'));
  }

  return <Button icon={<EllipsisBlack />} type="default" {...props} />;
};

export const Actions = ({
  children,
  triggerProps = {},
  overlayClassName,
  className,
  ...props
}: ActionsProps) => {
  // children convert to props
  const fixturesItems = useMemo(() => {
    let items: NonNullable<MenuProps['items']> = [];

    React.Children.forEach(children, (child, index) => {
      if (!React.isValidElement(child)) return child;

      items[index] = {
        label: React.cloneElement(child as React.ReactElement, {
          style: lockStyle
        }),
        key: child.key ?? index
      };
    });

    return items;
  }, [children]);

  return (
    <Dropdown
      menu={{ items: fixturesItems }}
      overlayClassName={classNames(styles.action, overlayClassName)}
      className={classNames(styles.actionTarget, className)}
      trigger={['click']}
      {...props}
    >
      <Trigger {...triggerProps} />
    </Dropdown>
  );
};

const lockStyle = {
  width: '100%',
  minWidth: '100px',
  minHeight: '30px',
  borderRadius: 0,
  textAlign: 'left',
  padding: '0 16px'
};
