import { CrossBold } from '@meitu/candy-icons';
import { Modal as AntdModal, ModalProps as AntdModalProps } from 'antd';
import classNames from 'classnames';
import { ReactNode, useContext, useEffect, useRef } from 'react';
import {
  createContext,
  forwardRef,
  useImperativeHandle,
  useState
} from 'react';
import styles from './index.module.less';

export interface ModalProps extends AntdModalProps {}

export interface ModalRef {
  setVisible: (visible: boolean) => void;
  visible: boolean;
}

export const Modal = forwardRef<ModalRef, ModalProps>(
  ({ open, className, ...props }, ref) => {
    const [visible, setVisible] = useState(false);

    useEffect(() => {
      setVisible(!!open);
    }, [open]);

    useImperativeHandle(ref, () => ({
      setVisible,
      visible
    }));

    return (
      <AntdModal
        open={visible}
        closeIcon={<CrossBold />}
        className={classNames(styles.modal, className)}
        {...props}
      />
    );
  }
);

export interface ModalRefCtxProps {
  ref: React.MutableRefObject<ModalRef | null>;
  changeModalVisible(visible: boolean): void;
}

export const ModalRefCtx = createContext<ModalRefCtxProps>({
  ref: { current: null },
  changeModalVisible: () => {}
});

export const ModalRefProvider = ({ children }: { children: ReactNode }) => {
  const ref = useRef<ModalRef>(null);

  const changeModalVisible = (visible: boolean) => {
    if (!ref.current) return;

    ref.current.setVisible(visible);
  };

  return (
    <ModalRefCtx.Provider value={{ ref, changeModalVisible }}>
      {children}
    </ModalRefCtx.Provider>
  );
};

export const useModalRef = () => useContext(ModalRefCtx);
