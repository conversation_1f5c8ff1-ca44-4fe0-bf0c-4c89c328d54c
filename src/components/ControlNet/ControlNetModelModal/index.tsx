import { CrossBold, SearchBold } from '@meitu/candy-icons';
import { Col, Empty, Input, Row, Typography, Modal } from 'antd';
import {
  type ReactElement,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
  createContext,
  startTransition
} from 'react';
import { FixedSizeGrid } from '../../Virtual/FixedSizeGrid';
import { ModelItem } from './ModelItem';
import empty from '@/assets/images/empty.jpg';
import {
  EditorConfigControlNetModel,
  EditorConfigModuleListResponse
} from '@/api/types/editorConfig';
import styles from './index.module.less';
import { Tabs } from '@/components';
import { produce } from 'immer';

const Ctx = createContext<{
  searchText: string;
}>({
  searchText: ''
});

type SearchTrigger = 'enter' | 'blur' | 'input';

export interface ControlNetModelModalProps {
  title: string;
  list: EditorConfigModuleListResponse[];

  onClose?: () => void;
  onModelClick?: (item: EditorConfigControlNetModel) => void;
  searchTrigger?: SearchTrigger[];
}

export type ControlNetModelModalHandle = {
  setVisible(visible: boolean): void;
};

export const ControlNetModelModal = forwardRef<
  ControlNetModelModalHandle,
  ControlNetModelModalProps
>(({ title, list, onClose, searchTrigger, onModelClick }, ref) => {
  const [searchValue, setSearchValue] = useState('');
  // searchTrigger 触发后将 searchValue 设为 searchText
  const [searchText, setSearchText] = useState('');
  const [activeTab, setActiveTab] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  const onSearch = useCallback(
    (value?: string) => {
      setSearchText(value ?? searchValue);
    },
    [searchValue]
  );

  const tabs = useMemo(() => {
    if (!list?.length || !isOpen) return [];

    return list.map((model) => {
      const filteredModel = produce(model, (draft) => {
        draft.list = filter(searchText, draft.list);
      });

      return {
        label: model.categoryName,
        key: String(model.categoryId),
        children: (
          <TabContainer
            model={filteredModel}
            key={model.categoryId}
            onModelClick={onModelClick}
          />
        )
      };
    });
  }, [list, isOpen, searchText, onModelClick]);

  // list如果为空 则将activeTab清空
  if (!list.length) {
    activeTab && setActiveTab('');
    // 如果当前的activeTab不在list中 则选中list的第一项
  } else if (!list.find((c) => String(c.categoryId) === activeTab)) {
    setActiveTab(String(list[0].categoryId));
  }

  const onCancel = useCallback(() => {
    onClose?.();
    setIsOpen(false);
    setSearchValue('');
    onSearch('');
  }, [onClose, setIsOpen, onSearch]);

  useImperativeHandle(
    ref,
    () => ({
      setVisible: (visible: boolean) => {
        if (!visible) {
          onCancel();
          return;
        }
        setIsOpen(true);
      }
    }),
    [onCancel]
  );

  const onPressEnter = searchTrigger?.includes('enter')
    ? () => {
        onSearch();
      }
    : undefined;

  const onSearchBlur = searchTrigger?.includes('blur')
    ? () => {
        onSearch();
      }
    : undefined;

  return (
    <Modal
      open={isOpen}
      className={styles.modelModal}
      width={896}
      footer={null}
      onCancel={onCancel}
      closeIcon={<CrossBold />}
      centered
      destroyOnClose
    >
      <Row gutter={[0, 8]}>
        <Col span={24} className={styles.pr40}>
          <Row justify="space-between">
            <Typography.Text className={styles.title} strong>
              {title}
            </Typography.Text>

            <Input
              className={styles.search}
              value={searchValue}
              allowClear
              onChange={({ target: { value }, type }) => {
                setSearchValue(value);
                if (searchTrigger?.includes('input')) {
                  onSearch(value);
                } else if (type === 'click') {
                  onSearch(value);
                }
              }}
              onPressEnter={onPressEnter}
              onBlur={onSearchBlur}
              placeholder="搜索模型"
              prefix={<SearchBold />}
            />
          </Row>
        </Col>

        <Col span={24} className={styles.pr6}>
          <Ctx.Provider value={{ searchText }}>
            <Tabs
              className={styles.tabs}
              onChange={(active) => {
                startTransition(() => {
                  setActiveTab(active);
                });
              }}
              activeKey={activeTab}
              type="no-line"
              size="small"
              items={tabs}
            />
          </Ctx.Provider>
        </Col>
      </Row>
    </Modal>
  );
});

export interface TabContainerProps {
  model: EditorConfigModuleListResponse;
  onModelClick?: (item: EditorConfigControlNetModel) => void;
  gridRender?: (grid: ReactElement) => ReactElement;
  onSearch?: (keyword: string) => void;
}

const filter = (keyword: string, list?: EditorConfigControlNetModel[]) => {
  if (!keyword) return list ?? [];

  return (
    list?.filter((item) => {
      return item.name.toLowerCase().includes(keyword.toLowerCase());
    }) ?? []
  );
};

export const TabContainer = ({
  model,
  onModelClick,
  gridRender
}: TabContainerProps) => {
  return (
    <Row
      className={styles.modelContainer}
      justify={!model.list.length ? 'center' : 'start'}
    >
      {!!model.list.length ? (
        <FixedSizeGrid
          rowKey={(item) => String(item.id)}
          dataSource={model.list}
          columnWidth={176}
          rowHeight={220}
          gutter={16}
          gridRender={gridRender}
          renderItem={(item) => (
            <ModelItem
              cornerLabelUrl={item?.tagUrl ?? ''}
              onClick={() => onModelClick?.(item)}
              item={item}
            />
          )}
        />
      ) : (
        <Empty
          className={styles.empty}
          image={empty}
          description={
            <Typography.Text type="secondary">暂无数据</Typography.Text>
          }
        />
      )}
    </Row>
  );
};

ControlNetModelModal.defaultProps = {
  searchTrigger: ['input']
};
