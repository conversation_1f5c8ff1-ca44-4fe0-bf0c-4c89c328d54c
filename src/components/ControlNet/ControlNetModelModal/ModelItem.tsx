import { Card, Image, Typography } from 'antd';
import styles from './modelItem.module.less';
import { EditorConfigControlNetModel } from '@/api/types/editorConfig';
import { Loading } from '@/components';
import React from 'react';
import { toAtlasImageMogr2URL } from '@meitu/util';
const { Meta } = Card;

export interface ModelItemProps {
  item: EditorConfigControlNetModel;
  onClick?(): void;
  cornerLabelUrl?: string;
}

export const ModelItem = ({
  item,
  onClick,
  cornerLabelUrl
}: ModelItemProps) => {
  const image = item?.coverPic ?? '';
  return (
    <Card
      onClick={onClick}
      className={styles.modelItem}
      cover={
        <>
          <Image
            placeholder={<Loading />}
            preview={false}
            alt={item.name}
            src={
              image
                ? toAtlasImageMogr2URL(image, {
                    thumbnail: { type: 'size', width: 204, height: 288 }
                  })
                : ''
            }
          />
        </>
      }
    >
      <Meta
        title={
          <Typography.Text
            ellipsis={{
              tooltip: {
                title: item.name,
                destroyTooltipOnHide: true
              }
            }}
            className={styles.f12}
          >
            {item.name}
          </Typography.Text>
        }
        description={
          <Typography.Text
            type="secondary"
            ellipsis={{
              tooltip: {
                title: item?.desc ?? '',
                destroyTooltipOnHide: true
              }
            }}
            className={styles.f12}
          >
            {item?.desc ?? ''}
          </Typography.Text>
        }
      />
      {cornerLabelUrl && (
        <span
          className={styles.cornerLabel}
          style={{ backgroundImage: `url(${cornerLabelUrl})` }}
        />
      )}
    </Card>
  );
};
