@import '~@/styles/variables.less';

.tooltip {
  color: @color-link;
  font-size: @size-sm;
  cursor: pointer;
}

.tooltip-popup {
  display: flex;

  .example-image {
    width: 100px;
    height: 100px;
    border-radius: @size-xxs;
    overflow: hidden;
  }

  .example-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: @size-ms;
    line-height: 2em;

    &:not(:first-child) {
      margin-left: @size-xs;
    }
  }
}

.overlay {
  &:global(.@{ant-prefix}-tooltip) {
    max-width: none;

    :global(.@{ant-prefix}-tooltip-inner) {
      padding: 16px 12px 12px 12px;
    }
  }
}
