import { EditorConfigControlNetModel } from '@/api/types';
import { Image, Tooltip } from 'antd';
import styles from './index.module.less';

export function ExampleTooltip(props: {
  className?: string;
  examples: EditorConfigControlNetModel['sampleTips'];
}) {
  return (
    <Tooltip
      className={props.className}
      overlayClassName={styles.overlay}
      title={
        <div className={styles.tooltipPopup}>
          {props.examples?.samplePic && (
            <ExampleImage label="参考图" src={props.examples.samplePic} />
          )}
          {props.examples?.prePic && (
            <ExampleImage label="预处理" src={props.examples.prePic} />
          )}
          {props.examples?.resultPic && (
            <ExampleImage label="结果图" src={props.examples.resultPic} />
          )}
        </div>
      }
    >
      <span className={styles.tooltip}>查看示例</span>
    </Tooltip>
  );
}

function ExampleImage(props: { label: string; src: string }) {
  return (
    <div className={styles.exampleItem}>
      <Image
        src={props.src}
        preview={false}
        wrapperClassName={styles.exampleImage}
      />
      <span>{props.label}</span>
    </div>
  );
}
