@import '~@/styles/variables.less';

.plus-block {
  background-color: #f6f7fa;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 26px;
  color: #d0d2d6;
}

.extra {
  position: absolute;
  top: @size;
  right: @size-sm;
}

.model-card {
  overflow: hidden;
  transition: background-color 0.3s ease;
  margin-bottom: @size-xs !important;
  :global(.model-card-extra) {
    background-color: transparent;
  }

  &:hover {
    border-color: @stroke-btn-secondary;
    box-shadow: none;

    :global(.@{ant-prefix}-card-body) {
      background-color: @background-card-hover;

      & > span {
        background-color: transparent;
      }
    }
  }

  .desc {
    width: 180px;
    font-size: @size-sm;
  }
}
