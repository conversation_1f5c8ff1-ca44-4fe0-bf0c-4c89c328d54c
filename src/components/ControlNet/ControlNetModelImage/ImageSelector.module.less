@import '~@/styles/variables.less';

.selector {
  display: flex;
  flex-direction: column;

  .select-row {
    &:not(:first-child) {
      margin-top: @size-xs;
    }
  }
}

.title-container {
  margin-bottom: 12px;
}

.selection {
  width: 51px;
  height: 51px;
  border-radius: @size-xxs;
  background: @background-system-main-background-flat;
  overflow: hidden;
  position: relative;
  opacity: 1;

  &:global(.upload) {
    background: transparent;
    opacity: 1;
  }

  &:global(.active) {
    box-shadow: 0 0 0 3px @stroke-system-selected;
    opacity: 1;
  }

  &:global(.empty) {
    opacity: 1;
    box-sizing: border-box;
    border: 2px dashed @background-system-main-background-default;
  }

  .upload {
    width: 100%;
    height: 100%;
    padding: 0 !important;
    min-width: 100%;
    border: none;

    .upload-button {
      width: 100%;
      height: 100%;
      inset: 0;
      position: absolute;
      background: transparent !important;
      border-radius: 0 !important;
      color: #abadb2 !important;

      &::before,
      &::after {
        background: transparent !important;
      }

      &:hover {
        color: @stroke-system-selected !important;
      }
    }

    :global(.@{ant-prefix}-upload-btn) {
      padding: 0 !important;
    }
  }
}

.uploader-selection {
  position: relative;

  .delete-uploaded-button {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    cursor: pointer;

    background-color: @background-system-main-background-default;
    font-size: @size-sm;
    border-radius: 0px 0px 0px @size-xxs;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
  }
}

.image-container {
  width: 100%;
  height: 100%;

  .image:global(.@{ant-prefix}-image-img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
