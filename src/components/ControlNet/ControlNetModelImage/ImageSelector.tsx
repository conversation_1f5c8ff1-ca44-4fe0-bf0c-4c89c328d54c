import { Col, Row, Image, Spin } from 'antd';
import classNames from 'classnames';
import styles from './ImageSelector.module.less';
import { useEffect, useState } from 'react';
import { Upload } from '@/components/Upload';
import { Button } from '@/components/Button';
import { PlusBold, TrashCan } from '@meitu/candy-icons';
import { EditorConfigControlNetModel } from '@/api/types';
import { ExampleTooltip } from '../ExampleTooltips';
import { containsUrlWithoutQuery } from '@/utils/controlNetUtils';

type ImageSelectorProps = SelectorProps & {
  examples?: EditorConfigControlNetModel['sampleTips'];
  taskCategory?: string;
};

export function ImageSelector({
  list,
  value,
  onChange,
  examples,
  taskCategory
}: ImageSelectorProps) {
  useEffect(() => {
    /**
     * 默认选中第一项
     */
    if (!value && list.length) {
      onChange?.(list[0]);
    }
  }, [list, value, onChange]);

  return (
    <>
      <Row className={styles.titleContainer}>
        <Col span={16}>
          <strong>上传或选择参考图</strong>
        </Col>
        <Col span={8}>
          <Row justify="end">
            {!!(examples && Object.keys(examples).length) && (
              <ExampleTooltip examples={examples} />
            )}
          </Row>
        </Col>
      </Row>

      <Row>
        <Col span={24}>
          <Selector
            list={list}
            value={value}
            onChange={onChange}
            taskCategory={taskCategory}
          />
        </Col>
      </Row>
    </>
  );
}

//#region 图片选择相关
type SelectorProps = {
  /**
   * 可以被选的图片
   * 1. controlnet模型相关
   * 2. 如果为图生图 还包括一张原图
   */
  list: Array<string>;
  value?: string;
  onChange?: (url: string) => void;
  taskCategory?: string;
};

const SELECTION_NUMS_IN_LINE = 5;
function Selector({ list, value, onChange, taskCategory }: SelectorProps) {
  const urls = ['upload', ...list];
  const rowNums = Math.ceil(urls.length / SELECTION_NUMS_IN_LINE);

  // 如果value不为空表示选中了一个图片
  // 如果在list中没找到value 则表示当前选中的value是上传的图片
  const valueIsUpload = !!(value && !containsUrlWithoutQuery(list, value));

  return (
    <section className={styles.selector}>
      {new Array(rowNums).fill(0).map((_, i) => {
        const urlsStart = i * SELECTION_NUMS_IN_LINE;
        const urlsEnd = Math.min(
          urlsStart + SELECTION_NUMS_IN_LINE,
          urls.length
        );
        return (
          <SelectRow
            key={i}
            firstRow={0 === i}
            urls={urls.slice(urlsStart, urlsEnd)}
            selectedUrl={value}
            valueIsUpload={valueIsUpload}
            onChange={onChange}
            taskCategory={taskCategory}
            list={list}
          />
        );
      })}
    </section>
  );
}

function SelectRow(props: {
  urls: Array<string>;
  firstRow?: boolean;
  selectedUrl?: string;
  valueIsUpload?: boolean;
  onChange?: (url: string) => void;
  taskCategory?: string;
  list: Array<string>;
}) {
  const urls = props.urls.slice(props.firstRow ? 1 : 0);

  function removeQuery(url: string) {
    return url.split('?').shift();
  }

  const selections = urls.map((url) => {
    return (
      <ImageSelection
        key={url}
        url={url}
        selected={
          !!props.selectedUrl &&
          removeQuery(props.selectedUrl) === removeQuery(url)
        }
        onChange={props.onChange}
      />
    );
  });
  const emptysNums = props.firstRow
    ? SELECTION_NUMS_IN_LINE - 1 - selections.length
    : SELECTION_NUMS_IN_LINE - selections.length;
  const emptys = new Array(emptysNums).fill(0).map((_, i) => {
    return <EmptySelection key={i} />;
  });

  return (
    <Row justify="space-between" className={styles.selectRow}>
      {props.firstRow && (
        <Uploader
          selected={props.valueIsUpload}
          onSelect={props.onChange}
          defaultValue={props.valueIsUpload ? props.selectedUrl : ''}
          taskCategory={props.taskCategory}
          list={props.list}
        />
      )}
      {selections}
      {emptys}
    </Row>
  );
}

function Uploader(props: {
  selected?: boolean;
  onSelect?: (url: string) => void;
  defaultValue?: string;
  taskCategory?: string;
  list: Array<string>;
}) {
  const [value, setValue] = useState(props.defaultValue ?? '');

  if (value && containsUrlWithoutQuery(props.list, value)) {
    setValue('');
  }

  function handleChange(url: string, previewUrl?: string) {
    setValue(previewUrl ?? '');
    previewUrl && props.onSelect?.(previewUrl);
  }

  function handleDelete() {
    setValue('');
    props.onSelect?.('');
  }

  return value ? (
    <div className={styles.uploaderSelection}>
      <ImageSelection
        url={value}
        selected={!!props.selected}
        onChange={props.onSelect}
      />
      <span className={styles.deleteUploadedButton} onClick={handleDelete}>
        <TrashCan />
      </span>
    </div>
  ) : (
    <div className={classNames(styles.selection, 'upload')}>
      <Upload.Dragger
        className={styles.upload}
        renderChildren={(loading: boolean) => {
          if (loading) {
            return <Spin spinning />;
          }

          return (
            <Button className={styles.uploadButton}>
              <PlusBold />
            </Button>
          );
        }}
        value={value}
        onChange={handleChange}
        taskCategory={props.taskCategory}
      />
    </div>
  );
}

function ImageSelection(props: {
  url: string;
  selected: boolean;
  onChange?: (url: string) => void;
}) {
  return (
    <div
      className={classNames(styles.selection, props.selected && 'active')}
      onClick={() => {
        props.onChange?.(props.url);
      }}
    >
      <Image
        src={props.url}
        preview={false}
        rootClassName={styles.imageContainer}
        className={styles.image}
      />
    </div>
  );
}

function EmptySelection() {
  return <div className={classNames(styles.selection, 'empty')}></div>;
}
//#endregion
