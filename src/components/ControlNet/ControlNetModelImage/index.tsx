import {
  EditorConfigControlNetModel,
  EditorConfigModuleListResponse
} from '@/api/types';
import {
  ControlNetModelCard,
  EmptyControlNetModelCard
} from '../ControlNetModelCard';
import {
  ControlNetModelModal,
  ControlNetModelModalHandle
} from '../ControlNetModelModal';
import { useEffect, useMemo, useRef } from 'react';
import { ImageSelector } from './ImageSelector';
import styles from './index.module.less';
import { containsUrlWithoutQuery } from '@/utils/controlNetUtils';

export interface ImageProcessingParams {
  image?: string;
  module?: string;
  model?: string;
  modelId?: number;

  modelConfig?: EditorConfigControlNetModel;
}

export interface ImageProcessingProps {
  /**
   * 可供选择的模型
   */
  moduleList: EditorConfigModuleListResponse[];
  /**
   * 向表单提交的值
   */
  value?: ImageProcessingParams;
  onChange?: (value: ImageProcessingParams) => void;
  /**
   * 选择图片时 可以向选项前放入其他图片
   */
  choosablePicBeforePreset?: string[];
  /**
   * 通过modal选择模型后触发
   */
  onSelectModel?: (model: EditorConfigControlNetModel) => void;
  /**
   * 删除模型触发
   * @param {model} 被删除的模型
   */
  onDeleteModel?: (model?: EditorConfigControlNetModel) => void;

  taskCategory?: string;

  hasTitle?: boolean;
}

export function ControlNetModelImage(props: ImageProcessingProps) {
  const {
    value,
    onChange,
    moduleList,
    choosablePicBeforePreset,
    onSelectModel,
    taskCategory,
    hasTitle = true,
    onDeleteModel
  } = props;
  const selectedModel = value?.modelConfig;

  const choosablePics = useMemo(() => {
    return [
      ...(choosablePicBeforePreset ?? []),
      ...(selectedModel?.choosablePics ?? [])
    ];
  }, [choosablePicBeforePreset, selectedModel]);

  const modelModalRef = useRef<ControlNetModelModalHandle>(null);
  function openSelectModal() {
    modelModalRef.current?.setVisible(true);
  }

  function closeModal() {
    modelModalRef.current?.setVisible(false);
  }

  function handleDelete() {
    onChange?.({});
    onDeleteModel?.(selectedModel);
  }

  return (
    <>
      <div className={styles.modelSelector}>
        {hasTitle && <strong className="title">参考类型</strong>}

        {selectedModel ? (
          <ControlNetModelCard
            onClick={openSelectModal}
            onDelete={handleDelete}
            model={selectedModel}
          />
        ) : (
          <EmptyControlNetModelCard onClick={openSelectModal} />
        )}
      </div>

      <ControlNetModelModal
        title="参考类型"
        list={moduleList}
        ref={modelModalRef}
        onModelClick={async (model) => {
          const preModel = value?.modelConfig;
          const imageIsUpload =
            // 1. 当前选中了图片
            value?.image &&
            // 2. 在候选的图片中没有找到选择的图片
            !containsUrlWithoutQuery(
              preModel?.choosablePics ?? [],
              value.image
            );

          onChange?.({
            ...value,
            // 切换controlnet模型时
            // 1. 如果图片为上传的图片，则保持不变
            // 2. 如果图片为预设的图片，则设置image为空，然后会默认选中第一项
            //    这里不默认选中model.choosablePics中的第一项
            //    因为在图生图中，存在原图应该选中原图，设置为空会自动处理这一逻辑
            image: imageIsUpload ? value.image : '',
            model: model.model,
            module: model.module,
            modelId: model.id,
            modelConfig: model
          });
          onSelectModel?.(model);
          closeModal();
        }}
      />

      {!!selectedModel && (
        <ImageSelector
          examples={selectedModel.sampleTips}
          list={choosablePics}
          onChange={(url: string) => {
            onChange?.({
              ...value,
              image: url
            });
          }}
          value={value?.image}
          taskCategory={taskCategory}
        />
      )}
    </>
  );
}
