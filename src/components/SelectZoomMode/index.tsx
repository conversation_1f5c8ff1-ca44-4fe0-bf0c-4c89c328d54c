import { Select } from 'antd';
import styles from './index.module.less';

/** 画面缩放 */
enum ZoomMode {
  /** 拉伸 */
  Stretching,

  /** 裁剪 */
  Cropping,

  /** 自动缩放 */
  Auto
}

const options = [
  // { label: '自动缩放', value: ZoomMode.Auto }, // diffusers暂时不支持 后期会开放
  { label: '拉伸', value: ZoomMode.Stretching },
  { label: '裁剪', value: ZoomMode.Cropping }
];
interface SelectZoomModeProps {
  value?: ZoomMode;
  onChange?: (value: ZoomMode) => void;
}

export function SelectZoomMode(props: SelectZoomModeProps) {
  return (
    <Select
      options={options}
      placeholder="请选择"
      value={props.value}
      onChange={props.onChange}
      className={styles.zoomMode}
    />
  );
}
