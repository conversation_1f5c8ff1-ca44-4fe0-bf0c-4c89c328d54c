@import '~@/styles/variables.less';

@designer-btn-border-radius: 8px;

.@{ant-prefix}-btn.@{designer-prefix}-btn.@{ant-prefix}-btn {
  &:not(.@{ant-prefix}-btn-sm):not(.@{ant-prefix}-btn-circle) {
    border-radius: @designer-btn-border-radius;
  }

  &-primary {
    &:not(:disabled) {
      border-color: transparent;

      &,
      &:hover,
      &:active {
        background: @background-btn-ai;
      }

      &:hover::after {
        opacity: 1;
      }

      &:active::after {
        opacity: 0.33;
      }
    }

    &:disabled {
      background: @color-white;

      &::before {
        opacity: 0.11;
        background: @content-btn-disable;
      }
    }

    > * {
      position: relative;
      z-index: 3;
    }

    &::before,
    &::after {
      content: '';
      opacity: 0;
      position: absolute;
      top: 0;
      left: 0;
      border-radius: @designer-btn-border-radius;
      width: 100%;
      height: 100%;
      transition: opacity @motion-duration-mid;
    }

    &::before {
      z-index: 2;
      background: @background-btn-ai;
    }

    &::after {
      z-index: 1;
      background: @background-btn-ai-hover;
    }

    &.@{ant-prefix}-btn-loading {
      opacity: 1;

      &::before {
        opacity: 0.65;
      }

      &:not(:disabled) {
        border: none;
        background: @color-white;

        &:hover,
        &:active {
          &::after {
            opacity: 0;
          }
        }
      }
    }

    &.@{ant-prefix}-btn-circle {
      &,
      &::after {
        border-radius: 50%;
      }
    }

    &.@{ant-prefix}-btn-round {
      &,
      &::after {
        border-radius: @control-height;
      }

      @button-sizes: {
        sm: @control-height-sm;
        lg: @control-height-lg;
      };

      each(@button-sizes, {
        &.@{ant-prefix}-btn-@{key} {
          &,
          &::after {
            border-radius: @value;
          }
        }
      });
    }
  }

  &-default {
    &:disabled {
      background: @color-white;

      &::before {
        content: '';
        opacity: 0.11;
        background: @content-btn-disable;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 8px;
        width: 100%;
        height: 100%;
      }
    }
  }
}
