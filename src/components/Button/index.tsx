import type { SeedTokens, ThemeTokens } from '@/styles';
import type { ButtonProps } from 'antd';

import { Button as AntdButton, ConfigProvider } from 'antd';

import {
  backgroundBtnAiHover,
  baseBrandLight50,
  baseBrandLight40,
  baseBrandLight30
} from '@meitu/candy-theme/dist/variables.mjs';

import { useDesignerPrefixCls } from '../DesignerConfigProvider';
import { useThemeTokenConfig } from '@/styles';
import classNames from 'classnames';
import './style.less';

export type { ButtonProps };

const seedTokens: SeedTokens<
  keyof ThemeTokens,
  ThemeTokens[keyof ThemeTokens]
> = [
  ['colorPrimaryHover', backgroundBtnAiHover],
  ['colorLink', baseBrandLight50],
  ['colorLinkHover', baseBrandLight40],
  ['colorLinkActive', baseBrandLight30]
];

export function Button(props: ButtonProps) {
  const prefixCls = useDesignerPrefixCls();
  const themeConfig = useThemeTokenConfig(seedTokens);

  return (
    <ConfigProvider theme={themeConfig}>
      <AntdButton
        {...props}
        type={props.type ?? 'primary'}
        className={classNames(`${prefixCls}-btn`, props.className)}
      />
    </ConfigProvider>
  );
}
