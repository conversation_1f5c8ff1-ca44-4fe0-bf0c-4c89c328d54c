import { createInstance } from './utils';
import {
  CommonPostResponse,
  CreateImageUpscaleTaskBody,
  CreateUpscalerImageTaskBody,
  DraftQuery,
  DraftRemoveBody,
  ImageStatus,
  ImageUpscaleTaskListResponse,
  TaskQuery,
  UpscalerTaskResponse,
  ImageUpscaleSizeBody,
  ImageUpscaleSizeResponse
} from './types';
import { toSnakeCase } from '@meitu/util';
import { Draft, GraphBatch, GraphStatus, LoadingStatus } from '@/types';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/image_upscaler' });

/**
 * 创建超分任务
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/134404 |接口文档}
 */
export function createUpscalerImageTask(data: CreateUpscalerImageTaskBody) {
  return instance.post<void, { id: string }>('/do.json', toSnakeCase(data));
}

/**
 * 图像超分 - 任务详情，支持批量获取
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/135158 |接口文档}
 */
export function fetchUpscalerTaskByIds(params: TaskQuery) {
  return instance.get<void, UpscalerTaskResponse[]>('/query.json', { params });
}

/**
 * 关闭超分失败提示
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/135581 |接口文档}
 */
export function closeFailureTip(data: { id: string }) {
  return instance.post<void>('/close_tip.json', toSnakeCase(data));
}

/**
 * AI 超清 - 任务提交
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/142697 |接口文档}
 */
export function createImageUpscaleTask(data: CreateImageUpscaleTaskBody) {
  return instance.post<void, { ids: Array<string> }>(
    '/generate.json',
    toSnakeCase(data)
  );
}

/**
 * AI 超清 - 任务结果计算
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/176125 |接口文档}
 */
export function fetchImageUpscaleSize(data: ImageUpscaleSizeBody) {
  return instance.post<void, ImageUpscaleSizeResponse>(
    '/count.json',
    toSnakeCase(data)
  );
}

function getLoadingStatus(status: number): LoadingStatus {
  switch (true) {
    case status < 90:
      return LoadingStatus.LOADING;
    case status === 92:
      return LoadingStatus.IRREGULARITY;
    case status === 99:
      return LoadingStatus.TIMEOUT;
    case status >= 90 && status < 100:
      return LoadingStatus.FAILED;
    case status === 100:
      return LoadingStatus.SUCCESS;
    default:
      return LoadingStatus.LOADING;
  }
}
// 转为草稿列表可用数据结构
function generateDraft({
  resultImage,
  id,
  originHeight,
  originWidth,
  taskStatus,
  hdOriginUrl,
  createdAt,
  params
}: UpscalerTaskResponse): GraphBatch {
  const loadingStatus = getLoadingStatus(taskStatus);

  return {
    id,
    batch: [
      {
        ...resultImage,
        src: resultImage?.url,
        status:
          resultImage?.imageStatus === ImageStatus.NORMAL
            ? GraphStatus.SUCCESS
            : GraphStatus.REJECTED,
        hadSatisfied: !!resultImage?.hadSatisfied
      }
    ],
    size: [originWidth || 0, originHeight || 0],
    loadingStatus,
    hdOriginUrl,
    time: createdAt,
    code: loadingStatus,
    params
  };
}
/**
 * AI 超清 - 列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/142701 |接口文档}
 */
export function fetchImageUpscaleTaskList(params: DraftQuery) {
  return instance
    .get<void, ImageUpscaleTaskListResponse>(
      '/list.json',
      toSnakeCase({ params })
    )
    .then<Draft>(({ list, cursor }) => ({
      total: list.length,
      cursor,
      list: list.map(generateDraft)
    }));
}

/**
 * AI 超清 - 任务详情，支持批量获取
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/135158 |接口文档}
 */
export function fetchImageUpscaleTaskByIds(params: TaskQuery) {
  return instance
    .get<void, UpscalerTaskResponse[]>('/query.json', { params })
    .then<GraphBatch[]>((data) => data.map(generateDraft));
}

/**
 * AI 超清 - 删除
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/142704 接口文档}
 */
export function removeImageUpscaleTask(data: DraftRemoveBody) {
  return instance.post<void, CommonPostResponse>(
    '/delete.json',
    toSnakeCase(data)
  );
}

/**
 * AI 超清 - 透明底图替换
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/153047 接口文档}
 */
export function replaceUpscaleImage(data: {
  id: string;
  resultImageUrl: string;
}) {
  return instance.post<void, CommonPostResponse>(
    '/replace_alpha_image.json',
    toSnakeCase(data)
  );
}
