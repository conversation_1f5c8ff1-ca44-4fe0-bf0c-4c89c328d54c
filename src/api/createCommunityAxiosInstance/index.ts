import type { CreateAxiosDefaults, AxiosResponse, AxiosError } from 'axios';
import type { CommunityResponseStructure } from './types';

import {
  type CreateAxiosInstanceOptions,
  createAxiosInstance,
  toCamelCase
} from '@meitu/util';

import { toAxiosResponseError, toCommunityAxiosError } from './utils';

export type { CommunityResponseStructure } from './types';

export interface createCommunityAxiosInstanceOptions<
  Data = any,
  ResData = CommunityResponseStructure<Data>,
  ReqData = any
> extends Pick<
    CreateAxiosInstanceOptions<Data, ResData, ReqData>,
    'beforeRequest'
  > {
  /**
   * 是否将响应数据转换成 `camelCase` 风格
   * @description 使用 `transformData` 时，该配置失效
   * @default true
   */
  camelCase?: boolean;

  /**
   * 数据转换
   * @param response 响应
   */
  transformData?: (
    data: Data,
    response: AxiosResponse<CommunityResponseStructure<Data>, ReqData>
  ) => any;

  /**
   * 抛出数据异常
   * @description 在 `response.data.ret` 不为 0 的时候抛出异常，可在此定制一些业务逻辑
   */
  throwException?: (
    axiosError: AxiosError<undefined, ReqData>,
    response: AxiosResponse<CommunityResponseStructure<Data>, ReqData>
  ) => never;
}

// TODO: 这块直接搬 @meitu/utils 里的 后续应该抽象到 @meitu/utils 里
/**
 * 创建社区 `axios` 实例
 */
export function createCommunityAxiosInstance<
  Data = any,
  ResData = CommunityResponseStructure<Data>,
  ReqData = any
>(
  config?: CreateAxiosDefaults<ReqData>,
  opts?: createCommunityAxiosInstanceOptions<Data, ResData, ReqData>
) {
  return createAxiosInstance<Data, ResData, ReqData>(config, {
    beforeRequest: opts?.beforeRequest,

    transformResponse(response) {
      if ((response.data as CommunityResponseStructure<Data>).ret === 0) {
        if (typeof opts?.transformData === 'function') {
          return opts.transformData(
            (response.data as unknown as CommunityResponseStructure<Data>).data,
            response as AxiosResponse
          );
        }

        return opts?.camelCase ?? true
          ? toCamelCase(
              (response.data as CommunityResponseStructure<Data>).data
            )
          : (response.data as CommunityResponseStructure<Data>).data;
      }

      return Promise.reject(toCommunityAxiosError(response));
    },

    /** 秀秀社区这边其实都是返回的 200 通过error_code做区分，理论是不用处理这种错误 */
    transformResponseError(error) {
      return new Promise((resolve, reject) => {
        const axiosError = toAxiosResponseError(error);

        if (typeof opts?.throwException === 'function') {
          try {
            opts.throwException(axiosError, error.response as AxiosResponse);
          } catch (err) {
            reject(err);
          }

          return;
        }

        reject(axiosError);
      });
    }
  });
}
