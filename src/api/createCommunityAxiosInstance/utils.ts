import type { CommunityResponseStructure } from './types';

import { type AxiosResponse, AxiosError } from 'axios';

/**
 * 将 `response` 转换为 `AxiosError`
 * @param error 错误
 * @param message 默认错误消息
 * @param code 默认错误编号
 */
export function toAxiosResponseError<
  Error extends { response?: AxiosResponse } = AxiosError
>(error: Error, message = 'unknown error', code = 500) {
  const internalError = error as unknown as AxiosError<
    CommunityResponseStructure<any>
  >;

  return new AxiosError<undefined>(
    internalError.response?.data?.msg ??
      internalError.message ??
      internalError.response?.statusText ??
      message,
    (
      internalError.response?.data?.error_code ??
      internalError.code ??
      internalError.response?.status ??
      code
    ).toString(),
    internalError.response?.config ?? internalError.config,
    internalError.response?.request ?? internalError.request,
    error.response
  );
}

/**
 * 转换为 `AxiosError`
 * @param response 响应
 */
export function toCommunityAxiosError(response: AxiosResponse) {
  return toAxiosResponseError({ response });
}
