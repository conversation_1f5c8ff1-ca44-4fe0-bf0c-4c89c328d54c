import { createMockInstance, createInstance } from './utils';
import {
  EditorConfigQuery,
  EditorConfigResponse,
  StyleModelResponse,
  StyleModelListQuery,
  StyleModelListResponse,
  EditorConfigModelListResponse
} from './types/editorConfig';
import { toSnakeCase } from '@meitu/util';
import { AIModelConfigResponse } from './types/imageAIModel';
import { ImageUpscaleConfigResponse, ImageEraserConfigResponse } from './types';
import { IPCharacterCustomizationConfig } from './types/ipCharacter/customization';
import { PosterConfigType, PosterFontType } from './types/poster';
import { ModelMvType } from '@/constants/model';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/editor');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/editor' });

/**
 * 服务端拉回来的配置中，对于flux模型，没有默认参数
 * 有些默认参数需要前端hard code
 * @param config
 * @returns
 */
export function appendDefaultParamsToFluxBaseModel(
  config: EditorConfigResponse
) {
  const { baseModel } = config;
  const nextBaseModel = baseModel.map((category) => {
    return {
      ...category,
      list: category.list.map((model) => {
        if (model.mvType !== ModelMvType.SpecialBeta) {
          return model;
        }

        return {
          ...model,
          defaultParams: {
            ...model.defaultParams,
            cfgScale: 3.5,
            samplerIndex: 'Euler FlowMatch'
          }
        };
      })
    };
  });

  return {
    ...config,
    baseModel: nextBaseModel
  };
}

/**
 * 获取模型列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120145 接口文档}
 */
export async function fetchEditorConfig(params: EditorConfigQuery) {
  const config = await instance.get<void, EditorConfigResponse>(
    '/config.json',
    toSnakeCase({ params })
  );

  return appendDefaultParamsToFluxBaseModel(config);
}

/**
 * 文生图-AI模特配置
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/139676 接口文档}
 */
export function fetchAIModelConfig() {
  return instance.get<void, AIModelConfigResponse>('/aimodel_config.json');
}

/**
 * 获取图生图编辑器配置列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120145 接口文档}
 */
export async function fetchImageToImageEditorConfig(
  params?: EditorConfigQuery
) {
  const config = await instance.get<void, EditorConfigResponse>(
    '/image2image_config.json',
    toSnakeCase({ params })
  );

  return appendDefaultParamsToFluxBaseModel(config);
}

/**
 * 获取图生图编辑器配置列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/140844 接口文档}
 */
export function fetchStyleModelList(params: StyleModelListQuery) {
  return instance
    .get<void, StyleModelListResponse>(
      '/style_model.json',
      toSnakeCase({ params })
    )
    .then(({ categoryList }) =>
      categoryList.map<StyleModelResponse>((category) =>
        Object.assign(category, {
          list:
            category.list?.map<EditorConfigModelListResponse>((item) =>
              Object.assign(item, {
                styleModelWeight: item.weight
              })
            ) ?? []
        })
      )
    );
}

/**
 * AI 超清 - 编辑器配置
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/142694 |接口文档}
 */
export function fetchImageUpscaleConfig() {
  return instance.get<void, ImageUpscaleConfigResponse>(
    '/image_upscaler_config.json'
  );
}

/**
 * AI 消除 - 配置接口
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/144522 |接口文档}
 */
export function fetchImageEraserConfig() {
  return instance.get<void, ImageEraserConfigResponse>(
    '/image_eraser_config.json'
  );
}

/**
 * 概念图设计 - 配置
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/167623 接口文档}
 */
export function fetchConceptConfig() {
  return instance.get<void, EditorConfigResponse>('/ip_concept_config.json');
}

/**
 * 获取形象定制的配置
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/167769 接口文档}
 */
export async function fetchIPFigureConfig() {
  const config = await instance.get<void, IPCharacterCustomizationConfig>(
    '/ip_figure_config.json'
  );

  // 将categoryId记录到lora中
  config.loraList.forEach((category) => {
    category.list.forEach((lora) => {
      lora.categoryId = category.categoryId;
    });
  });

  return config;
}

/**
 * AI 海报 - 配置
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/170204 接口文档}
 */
export async function fetchPosterConfig() {
  const config = await instance.get<void, PosterConfigType>(
    '/ai_poster_config.json'
  );

  // 将categoryId记录到lora中
  config.template.forEach((category) => {
    category.list.forEach((lora) => {
      lora.categoryId = category.categoryId;
    });
  });

  return config;
}

/**
 * AI海报-字体
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/170536 接口文档}
 */
export async function fetchPosterFont() {
  const config = await instance.get<void, PosterFontType>(
    '/ai_poster_font.json'
  );

  return config;
}
