import { createMockInstance, createInstance } from './utils';

import {
  ImageEditorBatchResponse,
  ImageEditorInferenceTaskRequest,
  ImageEditorInferenceTaskResponse,
  ImageEditorSaveRequest,
  ImageEditorSaveResponse,
  ImageEditorConfigResponse,
  ImageEditorProjectInfoRequest,
  ImageEditorProjectInfoResponse,
  TaskCategory,
  SmartSelectionRequestParams,
  SmartSelectionResponse,
  CancelTaskRequestParams,
  CancelTaskResponse,
  DeleteProjectRequestParams,
  DeleteProjectResponse,
  ImageEditorTaskConfigResponse,
  interactSelectionRequestParams,
  interactSelectionResponse,
  autoRecognitionSelectionRequestParams,
  autoRecognitionSelectionResponse
} from './types/imageEditor';
import { toSnakeCase } from '@meitu/util';
import { omitBy } from 'lodash';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/image_modify');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({
  baseURL: '/image_modify',
  timeout: 30 * 1000
});

/**
 * 改图编辑器-获取配置
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/146846 接口文档}
 */
export function getImageEditorConfig() {
  return instance.get<void, ImageEditorConfigResponse>('/config.json', {
    params: {
      // 时间戳 解决git 接口缓存问题
      _t: Date.now()
    }
  });
}
/**
 * 改图编辑器-保存
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/146844 接口文档}
 */
export function saveImageEditor(data: ImageEditorSaveRequest) {
  return instance.post<void, ImageEditorSaveResponse>(
    '/project/save.json',
    toSnakeCase(data)
  );
}
/**
 * 编辑器-发起AI推理任务
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/146705 接口文档}
 */
export function requestInferenceTask<T extends TaskCategory>(
  data: ImageEditorInferenceTaskRequest<T>,
  timeout?: number
) {
  const snakeData = toSnakeCase(data);

  return instance.post<void, ImageEditorInferenceTaskResponse>(
    '/task/do.json',
    {
      ...snakeData,
      params: JSON.stringify(snakeData.params)
    },
    {
      timeout
    }
  );
}
/**
 * 编辑器-批量查询任务详情
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/146711 接口文档}
 */
export function fetchHistoryList(params: { typeIds: string }) {
  return instance.get<void, ImageEditorBatchResponse>('/task/batch.json', {
    params: toSnakeCase(params)
  });
}
/**
 * 改图编辑器-根据项目ID获取project信息
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/146849 接口文档}
 */
export function getProjectInfo(data: ImageEditorProjectInfoRequest) {
  return instance.get<void, ImageEditorProjectInfoResponse>(
    '/project/detail.json',
    {
      params: toSnakeCase(data)
    }
  );
}

/**
 * 获取智能选择的选区
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/149103 接口文档}
 */
export function fetchSmartSelection(
  data: SmartSelectionRequestParams,
  timeout?: number
) {
  return instance.post<void, SmartSelectionResponse>(
    '/task/selection_interact.json',
    toSnakeCase(omitBy(data, (value) => value === undefined || value === null)),
    {
      timeout
    }
  );
}

/**
 * 取消任务
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/149103 接口文档}
 */
export function cancelTask(data: CancelTaskRequestParams) {
  return instance.post<void, CancelTaskResponse>(
    '/task/cancel.json',
    toSnakeCase(data)
  );
}

/**
 * 取消删除
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/148942 接口文档}
 */
export function deletePersonalProject(data: DeleteProjectRequestParams) {
  return instance.post<void, DeleteProjectResponse>(
    '/project/delete.json',
    toSnakeCase(data)
  );
}
/*
 * 编辑器-任务- 配置
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/151249 接口文档}
 */
export function fetchTaskConfigList(params: { taskCategory: TaskCategory }) {
  return instance.get<void, ImageEditorTaskConfigResponse>(
    '/task/config.json',
    {
      params: toSnakeCase(params)
    }
  );
}
/**
 * 交互选区
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/151248 接口文档}
 */
export function interactSelection(
  data: interactSelectionRequestParams,
  timeout?: number
) {
  return instance.post<void, interactSelectionResponse>(
    '/task/interact.json',
    toSnakeCase(omitBy(data, (value) => value === undefined || value === null)),
    {
      timeout
    }
  );
}

/**
 * 显著性检测
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/153015 接口文档}
 */
export function autoRecognitionSelection(
  data: autoRecognitionSelectionRequestParams,
  timeout?: number
) {
  return instance.post<void, autoRecognitionSelectionResponse>(
    '/task/sod.json',
    toSnakeCase(omitBy(data, (value) => value === undefined || value === null)),
    {
      timeout
    }
  );
}
