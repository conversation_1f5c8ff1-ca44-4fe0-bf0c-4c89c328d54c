import { zcoolcreateInstance } from './utils';
import {
  CreateText2ImageTaskBody,
  CreateText2ImageTaskResponse,
  TaskQuery,
  TaskResponse,
  DraftQuery,
  DraftResponse,
  DraftRemoveBody,
  CommonPostResponse,
  TaskInterface,
  DraftType,
  EditorConfigMattResponse,
  ImageUploadSignBody,
  RateListItem,
  RateData,
  RateSubmitParams,
  ImageUploadSignResponse
} from './types';
import type { Draft, GraphBatch } from '@/types/draft';
// import { zcoolgenerateDraft } from './draft';
import { toSnakeCase } from '@meitu/util';
import {
  AiMaterialTemplateListResponse,
  CreateMaterialGenerateTaskBody,
  AiMaterialTemplateListQuery
} from './types/aiMaterial';
import { GraphStatus, LoadingStatus } from '@/types/draft';
import {
  MeiDouFetchPriceDescResponse,
  MeidouFetchPriceDescRequest,
  MeidouQueryBalanceResponse
} from './types/meidou';
import { MeidouBalance } from '@/types';
import { EditorMode } from '@/constants';
import {
  StyleModelResponse,
  StyleModelListQuery,
  StyleModelListResponse,
  EditorConfigModelListResponse
} from './types/editorConfig';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = zcoolcreateInstance({ baseURL: '/api/sdk/ai_material' });
const instancesdk = zcoolcreateInstance({ baseURL: '/api/sdk' });
const instanceSub = zcoolcreateInstance({ baseURL: '/api/sdk/sub' });

// const instancesdk = zcoolcreateInstance({ baseURL: '/sdk/api/' });

/**
 * 获取任务，支持批量获取
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/169842|接口文档}
 */
export function fetchMaterialByIds(params: TaskQuery) {
  return instance
    .get<void, TaskResponse>('/query.json', { params })
    .then<GraphBatch[]>((data) => data.map(zcoolgenerateDraft));
}

/**
 * 创建素材转换
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120378 |接口文档}
 */
export function createMaterialTransformTask(data: CreateText2ImageTaskBody) {
  return instance.post<void, CreateText2ImageTaskResponse>(
    '/convert.json',
    toSnakeCase(data)
  );
}

/**
 * 创建素材生成
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/169652 |接口文档}
 */
export function createMaterialGenerateTask(
  data: CreateMaterialGenerateTaskBody
) {
  return instance.post<void, CreateText2ImageTaskResponse>(
    '/generate.json',
    toSnakeCase(data)
  );
}

function getLoadingStatus(
  processPercent: TaskInterface['processPercent'],
  status: TaskInterface['status']
): LoadingStatus {
  switch (true) {
    case status < 90:
      return LoadingStatus.LOADING;
    case status === 95:
      return LoadingStatus.IRREGULARITY;
    case status === 99:
      return LoadingStatus.TIMEOUT;
    case status >= 90 && status < 100:
      return LoadingStatus.FAILED;
    case status === 100:
      return LoadingStatus.SUCCESS;
  }

  return processPercent < 100 ? LoadingStatus.LOADING : LoadingStatus.SUCCESS;
}

/**
 * 获取草稿列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/169841 接口文档}
 */
export function fetchMaterialDraft(params: DraftQuery) {
  return instance
    .get<void, DraftResponse>('/list.json', { params: { ...params } })
    .then<Draft>(({ list, total, cursor }) => ({
      total,
      cursor,
      list: list.map(zcoolgenerateDraft)
    }));
}

/**
 * 移除草稿
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120372 接口文档}
 */
export function removeMaterialDraft(data: DraftRemoveBody) {
  return instance.post<void, CommonPostResponse>(
    '/delete.json',
    toSnakeCase(data)
  );
}

/**
 * 根据是否是推荐模式返回编辑模式
 * @param {number} simplifyVersion
 * @returns {EditorMode}
 */
export function getEditorMode(simplifyVersion?: number) {
  switch (simplifyVersion) {
    case 1:
      return EditorMode.Recommend;
    case 0:
      return EditorMode.Advanced;
    default:
      return undefined;
  }
}

/**
 * 获取图生图编辑器配置列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/170949 接口文档}
 */
export function fetchAiMaterialTemplateList(
  params: AiMaterialTemplateListQuery
) {
  return instance.get<void, AiMaterialTemplateListResponse>(
    '/template.json',
    toSnakeCase({ params })
  );
  // .then(({ list }) =>
  //   list.map((category) =>
  //     Object.assign(category, {
  //       list: category.list

  //     })
  //   )
  // );
}

/**
 * 风格许愿池接口
 */
export function fetchAiMaterialCollectStyleWish(params: {
  description: string;
  images: string[];
}) {
  return instancesdk.post<void, CommonPostResponse>(
    '/collect/style_wish.json',
    toSnakeCase(params)
  );
}

/**
 * 获取定价信息
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/132762 接口文档}
 */
export function fetchZcoolPriceDesc(data: MeidouFetchPriceDescRequest) {
  return instanceSub.post<void, MeiDouFetchPriceDescResponse>(
    '/get_zcool_price.json',
    toSnakeCase(data)
  );
}

/**
 * 美豆余额查询
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/132759 接口文档}
 */
export function checkZcoolMeidouBalance() {
  return instanceSub
    .post<void, MeidouQueryBalanceResponse>('/get_zcool_balance.json')
    .then<MeidouBalance>((response) => {
      const {
        availableAmount,
        tips,
        vipRightInfo,
        detailTitle,
        detailDesc,
        amountList = []
      } = response;

      return {
        availableAmount,
        tips,
        benefitsDescription: vipRightInfo,
        detailTitle,
        detailDesc,
        benefitsDetail: amountList.map(({ type, expireStr, desc, num }) => {
          return {
            type,
            key: expireStr,
            desc,
            value: num
          };
        })
      };
    });
}

/**
 * 获取用户再次编辑参数
 * @param {string} id
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/142276 接口文档}
 */
export function fetchDraftParamsById(id: string, idType: DraftType) {
  return instancesdk.get<
    void,
    Pick<TaskInterface, 'id' | 'params' | 'simplifyVersion'>
  >('/task/user_task_params.json', {
    params: toSnakeCase({
      id,
      idType
    })
  });
}

/**
 * 获取图生图编辑器配置列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/140844 接口文档}
 */
export function fetchStyleModelList(params: StyleModelListQuery) {
  return instancesdk
    .get<void, StyleModelListResponse>(
      '/editor/style_model.json',
      toSnakeCase({ params })
    )
    .then(({ categoryList }) =>
      categoryList.map<StyleModelResponse>((category) =>
        Object.assign(category, {
          list:
            category.list?.map<EditorConfigModelListResponse>((item) =>
              Object.assign(item, {
                styleModelWeight: item.weight
              })
            ) ?? []
        })
      )
    );
}

/**
 * 获取图片上传签名
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120393 接口文档}
 */
export function fetchImageUploadSign(params: ImageUploadSignBody) {
  return instancesdk
    .get<void, EditorConfigMattResponse>('/image/get_sign.json', {
      params
    })
    .then<ImageUploadSignResponse>((response) => {
      const { app, sig, sigTime, sigVersion, suffix, type, count } = response;

      return {
        app,
        sig,
        sigTime,
        sigVersion,
        suffix,
        type,
        count
      };
    });
}

/**
 * 获取满意度分类列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/133935 接口文档}
 */
export function fetcRateList() {
  return instancesdk
    .get<void, RateListItem[]>('/satisfied_category/list.json')
    .then<RateData>((response) => {
      return transformRate(response);
    });
}

/**
 * 提交评分
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/134348 接口文档}
 */
export function submitRate(data: RateSubmitParams) {
  return instancesdk.post<void, CommonPostResponse>(
    '/satisfied/submit.json',
    toSnakeCase(data)
  );
}

/**
 * 转换后端数据格式
 * @param params 接口原始数据
 * @returns
 */
function transformRate(params: RateListItem[]): RateData {
  let obj: RateData = {};
  params.map((item) => {
    obj[item.score] = item;
    return false;
  });

  return obj;
}

export function zcoolgenerateDraft({
  resultImages,
  id,
  processPercent,
  params,
  createdAt,
  isPublish,
  status,
  simplifyVersion,
  taskCategory,
  isCollect,
  wheeMsgId,
  disableEdit = false,
  inherit
}: TaskInterface): GraphBatch {
  const loadingStatus = getLoadingStatus(processPercent, status);

  return {
    id,
    batch: resultImages?.map((image) =>
      Object.assign(image, {
        status:
          (image.status ?? image.imageStatus) === 1
            ? GraphStatus.SUCCESS
            : loadingStatus === LoadingStatus.SUCCESS
            ? GraphStatus.REJECTED
            : GraphStatus.FAILED,
        src: image.url,
        seed: image.seed && image.seed > 0 ? image.seed : undefined,
        hadSatisfied: !!image.hadSatisfied,
        zcool_file_id: image.zcoolFileId
      })
    ) ?? [
      {
        src: '',
        status:
          loadingStatus === LoadingStatus.LOADING
            ? GraphStatus.AUDITING
            : GraphStatus.FAILED
      }
    ],
    size: [params.width, params.height],
    loadingStatus,
    loadingProgress: processPercent,
    time: createdAt,
    params,
    isPublish,
    code: loadingStatus,
    editorMode: getEditorMode(simplifyVersion),
    taskCategory,
    isCollect,
    wheeMsgId,
    disableEdit: !!disableEdit,
    inherit
  };
}

/**
 * 与zcool交互通用接口
 *
 */

export function zcoolUniversalInterface(data: {
  uri: string;
  params: { [key: string]: any };
}) {
  return instancesdk.post<void, any>(
    '/sub/forward_zcool_api.json',
    toSnakeCase(data)
  );
}
