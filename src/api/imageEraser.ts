import { toSnakeCase } from '@meitu/util';

import { createMockInstance, createInstance } from './utils';
import {} from './types/';
import {
  ImageEraserDoRequest,
  ImageEraserDoResponse,
  ImageEraserHistoryResponse,
  ImageEraserQueryRequest,
  ImageEraserQueryResponse
} from './types/imageEraser';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/image_eraser');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/image_eraser' });

/**
 * AI消除-任务创建
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/144236 接口文档}
 */
export function startImageEraser(data: ImageEraserDoRequest) {
  return instance.post<void, ImageEraserDoResponse>(
    '/do.json',
    toSnakeCase(data)
  );
}

/**
 * AI消除-任务详情查询
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/144238 接口文档}
 */
export function queryImageEraser(data: ImageEraserQueryRequest) {
  return instance.get<void, ImageEraserQueryResponse[]>('/query.json', {
    params: data
  });
}

/**
 * AI消除-任务列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/144240 接口文档}
 */
export function fetchImageEraserHistory(params: {
  count: string;
  cursor: string;
}) {
  return instance.get<void, ImageEraserHistoryResponse>('/list.json', {
    params
  });
}

/**
 * AI消除-删除历史记录
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/144242 接口文档}
 */
export function deleteImageEraserHistory(data: { id: string }) {
  return instance.post<void, { result: boolean }>(
    '/delete.json',
    toSnakeCase(data)
  );
}
