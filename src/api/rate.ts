import { createMockInstance, createInstance } from './utils';

import type {
  RateListItem,
  RateData,
  RateSubmitParams,
  CommonPostResponse
} from './types';
import { toSnakeCase } from '@meitu/util';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '' });

/**
 * 转换后端数据格式
 * @param params 接口原始数据
 * @returns
 */
function transformRate(params: RateListItem[]): RateData {
  let obj: RateData = {};
  params.map((item) => {
    obj[item.score] = item;
    return false;
  });

  return obj;
}

/**
 * 获取满意度分类列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/133935 接口文档}
 */
export function fetcRateList() {
  return instance
    .get<void, RateListItem[]>('/satisfied_category/list.json')
    .then<RateData>((response) => {
      return transformRate(response);
    });
}

/**
 * 提交评分
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/134348 接口文档}
 */
export function submitRate(data: RateSubmitParams) {
  return instance.post<void, CommonPostResponse>(
    '/satisfied/submit.json',
    toSnakeCase(data)
  );
}
