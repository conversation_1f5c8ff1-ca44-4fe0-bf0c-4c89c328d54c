import { toSnakeCase } from '@meitu/util';

import { createMockInstance, publicSdkInstance } from './utils';
import {
  ImagePartialRepaintDoRequest,
  ImagePartialRepaintDoResponse,
  ImagePartialRepaintHistoryResponse,
  ImagePartialRepaintQueryRequest,
  ImagePartialRepaintQueryResponse
} from './types/imagePartialRepain';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/image_inpaint');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = publicSdkInstance({ baseURL: '/sdk/image_inpaint' });

/**
 * AI改图-开始扩展
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/136405 接口文档}
 */
export function startImagePartialRepaint(data: ImagePartialRepaintDoRequest) {
  return instance.post<void, ImagePartialRepaintDoResponse>(
    '/do.json',
    toSnakeCase(data)
  );
}

/**
 * AI改图-查询详情
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/128951 接口文档}
 */
export function queryImagePartialRepaint(
  data: ImagePartialRepaintQueryRequest
) {
  return instance.get<void, ImagePartialRepaintQueryResponse[]>('/query.json', {
    params: data
  });
}

/**
 * AI改图-任务列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/136415 接口文档}
 */
export function fetchImagePartialRepaintHistory(params: {
  count: number;
  cursor: number;
}) {
  return instance.get<void, ImagePartialRepaintHistoryResponse>('/list.json', {
    params
  });
}

/**
 * AI改图-删除历史记录
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/128962 接口文档}
 */
export function deleteImagePartialRepaintHistory(data: {
  id: string;
  imageUrl: string;
}) {
  return instance.post<void, { result: boolean }>(
    '/delete.json',
    toSnakeCase(data)
  );
}
