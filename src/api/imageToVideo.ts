import { toSnakeCase } from '@meitu/util';

import { createMockInstance, createInstance } from './utils';
import {
  ImageToVideoDoRequest,
  ImageToVideoDoResponse,
  ImagePartialRepaintQueryRequest,
  ImageToVideoQueryResponse,
  ImageToVideoHistoryResponse,
  TaskStatus,
  ImageToVideoConfigResponse,
  ImageToVideoConfig,
  ImageToVideoDoRequestV2
} from './types';
import { Proportion } from '@/components/SizeSelector/constants';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/ai_video');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/ai_video' });

function getLoadingStatus(status: number): TaskStatus {
  switch (true) {
    case status < 90:
      return TaskStatus.GENERATING;

    case status >= 90 && status < 100:
      return TaskStatus.FAILURE;
    case status === 100:
      return TaskStatus.SUCCESS;

    default:
      return TaskStatus.GENERATING;
  }
}

function transformList(
  params: ImageToVideoQueryResponse
): ImageToVideoQueryResponse {
  return { ...params, status: getLoadingStatus(params.status) };
}

/**
 * AI 视频 - 创建
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/141843 接口文档}
 */
export function startImageToVideo(data: ImageToVideoDoRequest) {
  return instance.post<void, ImageToVideoDoResponse>(
    '/create.json',
    toSnakeCase(data)
  );
}

/**
 * AI 视频 (MVV5)- 创建
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/141843 接口文档}
 */
export function startImageToVideoV2(data: ImageToVideoDoRequestV2) {
  return instance.post<void, ImageToVideoDoResponse>(
    '/mvv/create.json',
    toSnakeCase(data)
  );
}

/**
 * AI 视频 - 查询详情
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/141864 接口文档}
 */
export function queryImageToVideo(params: ImagePartialRepaintQueryRequest) {
  return instance
    .get<void, ImageToVideoQueryResponse[]>('/query.json', {
      params
    })
    .then((res) => {
      const result = res.map(transformList);

      return result;
    });
}

/**
 * AI 视频 - 列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/141885 接口文档}
 */
export function fetchImageToVideoHistory(params: {
  count: number;
  cursor: string;
}) {
  return instance
    .get<void, ImageToVideoHistoryResponse>('/list.json', {
      params
    })
    .then((res) => {
      const { cursor, list = [] } = res;
      const result = list ? list.map(transformList) : [];

      return { cursor, list: result };
    });
}

/**
 * AI 视频 - 删除
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/141868 接口文档}
 */
export function deleteImagePartialRepaintHistory(data: { id: string }) {
  return instance.post<void, { result: boolean }>(
    '/delete.json',
    toSnakeCase(data)
  );
}

/**
 * AI 视频 - 配置
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/141855 接口文档}
 */
export function fetchImageToVideoConfig() {
  return instance
    .get<void, ImageToVideoConfigResponse>('/config.json')
    .then<ImageToVideoConfig>((res) => {
      const { imageBaseLength, sizeList = [] } = res;
      const list = sizeList.map((item) => ({
        ...item,
        // label:
        //   item.ratio === Proportion.ADAPTION
        //     ? item.sizeName
        //     : `${item.ratio} （${item.width}*${item.height}）`,
        // value: `${item.ratio}*${item.width}*${item.height}`
        label:
          item.ratio === Proportion.ADAPTION ? item.sizeName : `${item.ratio}`,
        value: `${item.ratio}`
      }));

      const imageDefaultSelectVal =
        list.find((item) => item.imageDefaultSelect)?.value || '';
      const textDefaultSelectVal =
        list.find((item) => item.textDefaultSelect)?.value || '';
      const hiddenScaleVal =
        list.find((item) => item.ratio === Proportion.ADAPTION)?.value || '';

      return {
        imageBaseLength,
        list,
        imageDefaultSelectVal,
        textDefaultSelectVal,
        hiddenScaleVal
      };
    });
}
