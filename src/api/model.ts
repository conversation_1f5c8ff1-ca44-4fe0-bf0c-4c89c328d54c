import { createMockInstance, createInstance } from './utils';
import { MarkModelBody } from './types/model';
import { CommonPostResponse } from './types';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/model');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/model' });

/**
 * 模型收藏
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120354 接口文档}
 */
export function markModel(data: MarkModelBody) {
  return instance.post<void, CommonPostResponse>('/collect.json', data);
}

/**
 * 取消模型收藏
 * TODO：与收藏合并为一个接口
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120357 接口文档}
 */
export function cancelMarkModel(data: MarkModelBody) {
  return instance.post<void, CommonPostResponse>('/cancel_collect.json', data);
}

/**
 * 查询模型是否为 flux 模型
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/177307 接口文档}
 */
export function getStyleType(styleModelId: string) {
  return instance.get<void, { isFlux: boolean }>('/get_style_type.json', {
    params: { style_model_id: styleModelId }
  });
}
