import { toSnakeCase } from '@meitu/util';
import {
  GenerateTemplateRequest,
  GenerateTemplateResponse,
  TemplateCategoryListResponse
} from './types/poster';
import { createInstance } from './utils';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/ai_poster/template' });

/**
 * 模版分类列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/174721 接口文档}
 */
export function fetchCategoryList() {
  return instance.get<void, TemplateCategoryListResponse>('/category.json');
}

/**
 * 提交模版
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/174721 接口文档}
 */
export function generateTemplate(params: GenerateTemplateRequest) {
  return instance.post<void, GenerateTemplateResponse>(
    '/generate.json',
    toSnakeCase(params)
  );
}
