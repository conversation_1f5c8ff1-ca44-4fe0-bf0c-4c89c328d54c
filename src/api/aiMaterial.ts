import { createInstance } from './utils';
import {
  CreateText2ImageTaskBody,
  CreateText2ImageTaskResponse,
  TaskQuery,
  TaskResponse,
  DraftQuery,
  DraftResponse,
  DraftRemoveBody,
  CommonPostResponse
} from './types';
import type { Draft, GraphBatch } from '@/types/draft';
import { generateDraft } from './draft';
import { toSnakeCase } from '@meitu/util';
import {
  AiMaterialTemplateListResponse,
  CreateMaterialGenerateTaskBody,
  AiMaterialTemplateListQuery
} from './types/aiMaterial';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/ai_material' });

/**
 * 获取任务，支持批量获取
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/169842|接口文档}
 */
export function fetchMaterialByIds(params: TaskQuery) {
  return instance
    .get<void, TaskResponse>('/query.json', { params })
    .then<GraphBatch[]>((data) => data.map(generateDraft));
}

/**
 * 创建素材转换
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120378 |接口文档}
 */
export function createMaterialTransformTask(data: CreateText2ImageTaskBody) {
  return instance.post<void, CreateText2ImageTaskResponse>(
    '/convert.json',
    toSnakeCase(data)
  );
}

/**
 * 创建素材生成
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/169652 |接口文档}
 */
export function createMaterialGenerateTask(
  data: CreateMaterialGenerateTaskBody
) {
  return instance.post<void, CreateText2ImageTaskResponse>(
    '/generate.json',
    toSnakeCase(data)
  );
}

/**
 * 获取草稿列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/169841 接口文档}
 */
export function fetchMaterialDraft(params: DraftQuery) {
  return instance
    .get<void, DraftResponse>('/list.json', { params: { ...params } })
    .then<Draft>(({ list, total, cursor }) => ({
      total,
      cursor,
      list: list.map(generateDraft)
    }));
}

/**
 * 移除草稿
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120372 接口文档}
 */
export function removeMaterialDraft(data: DraftRemoveBody) {
  return instance.post<void, CommonPostResponse>(
    '/delete.json',
    toSnakeCase(data)
  );
}

/**
 * 获取图生图编辑器配置列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/170949 接口文档}
 */
export function fetchAiMaterialTemplateList(
  params: AiMaterialTemplateListQuery
) {
  return instance.get<void, AiMaterialTemplateListResponse>(
    '/template.json',
    toSnakeCase({ params })
  );
  // .then(({ list }) =>
  //   list.map((category) =>
  //     Object.assign(category, {
  //       list: category.list

  //     })
  //   )
  // );
}
