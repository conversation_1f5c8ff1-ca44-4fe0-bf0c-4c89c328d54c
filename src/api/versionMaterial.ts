type VersionMaterialType = {
  version: string;
  images: {
    cover: string;
    link: string;
  }[];
  contentList: string[];
};

export function fetchVersionMaterial() {
  const templateUrl = `${process.env.PUBLIC_URL}/manifest.json?t=${Date.now()}`;

  return fetch(templateUrl).then((response) => {
    if (!response.ok) {
      console.log(response);
    }

    return response.json() as Promise<VersionMaterialType>;
  });
}
