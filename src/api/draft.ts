import type {
  DraftQuery,
  DraftResponse,
  DraftPublishBody,
  DraftRemoveBody,
  DraftCollectBody,
  CommonPostResponse,
  TaskInterface,
  PublishResponse
} from './types';
import type { Draft, GraphBatch } from '@/types/draft';
import { DraftType } from './types';
import { GraphStatus, LoadingStatus } from '@/types/draft';

import { createMockInstance, createInstance } from './utils';
import { toSnakeCase } from '@meitu/util';

import { EditorMode } from '@/constants';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/task');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/task' });

function getLoadingStatus(
  processPercent: TaskInterface['processPercent'],
  status: TaskInterface['status']
): LoadingStatus {
  switch (true) {
    case status < 90:
      return LoadingStatus.LOADING;
    case status === 95:
      return LoadingStatus.IRREGULARITY;
    case status === 99:
      return LoadingStatus.TIMEOUT;
    case status >= 90 && status < 100:
      return LoadingStatus.FAILED;
    case status === 100:
      return LoadingStatus.SUCCESS;
  }

  return processPercent < 100 ? LoadingStatus.LOADING : LoadingStatus.SUCCESS;
}

/**
 * 根据是否是推荐模式返回编辑模式
 * @param {number} simplifyVersion
 * @returns {EditorMode}
 */
export function getEditorMode(simplifyVersion?: number) {
  switch (simplifyVersion) {
    case 1:
      return EditorMode.Recommend;
    case 0:
      return EditorMode.Advanced;
    default:
      return undefined;
  }
}

export function generateDraft({
  resultImages,
  id,
  processPercent,
  params,
  createdAt,
  isPublish,
  status,
  simplifyVersion,
  taskCategory,
  isCollect,
  wheeMsgId,
  disableEdit = false
}: TaskInterface): GraphBatch {
  const loadingStatus = getLoadingStatus(processPercent, status);

  return {
    id,
    batch: resultImages?.map((image) =>
      Object.assign(image, {
        status:
          (image.status ?? image.imageStatus) === 1
            ? GraphStatus.SUCCESS
            : loadingStatus === LoadingStatus.SUCCESS
            ? GraphStatus.REJECTED
            : GraphStatus.FAILED,
        src: image.url,
        seed: image.seed && image.seed > 0 ? image.seed : undefined,
        hadSatisfied: !!image.hadSatisfied
      })
    ) ?? [
      {
        src: '',
        status:
          loadingStatus === LoadingStatus.LOADING
            ? GraphStatus.AUDITING
            : GraphStatus.FAILED
      }
    ],
    size: [params.width, params.height],
    loadingStatus,
    loadingProgress: processPercent,
    time: createdAt,
    params,
    isPublish,
    code: loadingStatus,
    editorMode: getEditorMode(simplifyVersion),
    taskCategory,
    isCollect,
    wheeMsgId,
    disableEdit: !!disableEdit
  };
}

/**
 * 获取草稿列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120156 接口文档}
 */
export function fetchDraft(type: DraftType, params: DraftQuery) {
  return instance
    .get<void, DraftResponse>('/list.json', { params: { ...params, type } })
    .then<Draft>(({ list, total, cursor }) => ({
      total,
      cursor,
      list: list.map(generateDraft)
    }));
}

/**
 * 发布草稿
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120375 接口文档}
 */
export function publishDraft(data: DraftPublishBody) {
  return instance.post<void, PublishResponse>(
    '/publish.json',
    toSnakeCase(data)
  );
}

/**
 * 移除草稿
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120372 接口文档}
 */
export function removeDraft(data: DraftRemoveBody) {
  return instance.post<void, CommonPostResponse>(
    '/delete.json',
    toSnakeCase(data)
  );
}

/**
 * 收藏草稿
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/135130 接口文档}
 */
export function collectDraft(data: DraftCollectBody) {
  return instance.post<void, CommonPostResponse>(
    '/collect.json',
    toSnakeCase(data)
  );
}

/**
 * 获取用户再次编辑参数
 * @param {string} id
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/142276 接口文档}
 */
export function fetchDraftParamsById(id: string, idType: DraftType) {
  return instance.get<
    void,
    Pick<TaskInterface, 'id' | 'params' | 'simplifyVersion'>
  >('/user_task_params.json', {
    params: toSnakeCase({
      id,
      idType
    })
  });
}
