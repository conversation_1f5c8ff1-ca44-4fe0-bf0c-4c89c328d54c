import { publicSdkInstance } from './utils';
import {
  CreateText2ImageTaskBody,
  CreateText2ImageTaskResponse,
  TaskQuery,
  TaskResponse,
  DraftQuery,
  DraftResponse,
  DraftRemoveBody,
  CommonPostResponse,
  TaskInterface,
  DraftType,
  EditorConfigMattResponse,
  ImageUploadSignBody,
  RateListItem,
  RateData,
  RateSubmitParams,
  ImageUploadSignResponse
} from './types';
import type { Draft, GraphBatch } from '@/types/draft';
// import { zcoolgenerateDraft } from './draft';
import { toSnakeCase } from '@meitu/util';
import {
  AiMaterialTemplateListResponse,
  CreateMaterialGenerateTaskBody,
  AiMaterialTemplateListQuery
} from './types/aiMaterial';
import { GraphStatus, LoadingStatus } from '@/types/draft';
import {
  MeiDouFetchPriceDescResponse,
  MeidouFetchPriceDescRequest,
  MeidouQueryBalanceResponse
} from './types/meidou';
import { MeidouBalance } from '@/types';
import { EditorMode } from '@/constants';
import {
  StyleModelResponse,
  StyleModelListQuery,
  StyleModelListResponse,
  EditorConfigModelListResponse
} from './types/editorConfig';

// eslint-disable-next-line @typescript-eslint/no-unused-vars

const instancesdk = publicSdkInstance({ baseURL: '/sdk' });

/**
 * 获取图片上传签名
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120393 接口文档}
 */
export function fetchImageUploadSign(params: ImageUploadSignBody) {
  return instancesdk
    .get<void, EditorConfigMattResponse>('/image/get_sign.json', {
      params
    })
    .then<ImageUploadSignResponse>((response) => {
      const { app, sig, sigTime, sigVersion, suffix, type, count } = response;

      return {
        app,
        sig,
        sigTime,
        sigVersion,
        suffix,
        type,
        count
      };
    });
}
