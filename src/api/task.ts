import { createMockInstance, createInstance } from './utils';
import {
  CreateText2ImageTaskBody,
  CreateText2ImageTaskResponse,
  TaskQuery,
  TaskResponse,
  createImageProcessingTaskBody,
  SimplifyCreateText2ImageTaskBody,
  DeeperDesignTaskBody,
  CreateTo3DImageTaskBody,
  MtccFuncCode
} from './types';
import type { GraphBatch } from '@/types/draft';

import { generateDraft } from './draft';
import { toSnakeCase } from '@meitu/util';
import { AIModelGenerateRequest } from './types/imageAIModel';
import { CreateIPCharacterImageParams } from './types/ipCharacter/customization';
import { isNil, omitBy } from 'lodash';
import { CreatePosterTaskBody } from './types/poster';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/task');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/task' });

/**
 * 获取任务，支持批量获取
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120381|接口文档}
 */
export function fetchTaskByIds(params: TaskQuery) {
  return instance
    .get<void, TaskResponse>('/query.json', { params })
    .then<GraphBatch[]>((data) => data.map(generateDraft));
}

/**
 * 创建文生图片任务
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120378 |接口文档}
 */
export function createText2ImageTask(data: CreateText2ImageTaskBody) {
  return instance.post<void, CreateText2ImageTaskResponse>(
    '/generate/text2image.json',
    toSnakeCase(data)
  );
}

/**
 * 任务-创建-AI模特图
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/139679 |接口文档}
 */
export function createAIModelTask(data: AIModelGenerateRequest) {
  return instance.post<void, CreateText2ImageTaskResponse[number]>(
    '/generate/aimodel_image.json',
    toSnakeCase(data)
  );
}

/**
 * 创建图生图片任务
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/126372 |接口文档}
 */
export function createImageToImageTask(data: CreateText2ImageTaskBody) {
  return instance.post<void, CreateText2ImageTaskResponse>(
    '/generate/image2image.json',
    toSnakeCase(data)
  );
}

/**
 * 创建延伸创作任务
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/142800 |接口文档}
 */
export function createDeeperDesignTask(data: DeeperDesignTaskBody) {
  return instance.post<void, CreateText2ImageTaskResponse>(
    '/generate/style_align.json',
    toSnakeCase(data)
  );
}

/**
 * 创建图片预处理任务
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/121438|接口文档}
 */
export function createImageProcessingTask(data: createImageProcessingTaskBody) {
  const { baseModelId, image, rgb2bgrMode, width, height, ...rest } = data;

  const finalData = {
    params: {
      baseModelId: 1,
      init_images: [image],
      width,
      height,
      controlnetUnits: [
        {
          enabled: true,
          inputImage: image,
          rgbbgrMode: rgb2bgrMode,
          ...rest
        }
      ]
    }
  };

  return instance.post<void, CreateText2ImageTaskResponse>(
    '/generate/preprocess.json',
    toSnakeCase(finalData)
  );
}

/**
 * 创建-首页简化文生图
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/134659 |接口文档}
 */
export function createSimplifyText2ImageTask(prompt: string) {
  return instance.post<void, CreateText2ImageTaskResponse>(
    '/generate/simplify_text2image.json',
    {
      params: { prompt }
    } as SimplifyCreateText2ImageTaskBody
  );
}

/**
 * 任务外跳链接生成
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/141267 |接口文档}
 */
export function getExternalLink(params: { key: string; id: string }) {
  return instance.get<void, { jumpUrl: string }>('/get_jump_url.json', {
    params
  });
}

/**
 * 概念图设计 - 任务 - 创建
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/167818 |接口文档}
 */
export function createConceptTask(data: CreateText2ImageTaskBody) {
  return instance.post<void, CreateText2ImageTaskResponse>(
    '/generate/ip_concept_image.json',
    toSnakeCase(data)
  );
}

/**
 * 创建-IP形象定制-形象定制
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/167869 |接口文档}
 */
export function createIPCharacterImage(params: CreateIPCharacterImageParams) {
  return instance.post<CreateIPCharacterImageParams, any>(
    '/generate/ip_figure_image.json',
    toSnakeCase({
      params: toSnakeCase(omitBy(params, isNil)),
      functionName: MtccFuncCode.FuncCodeIpImageDrawing
    })
  );
}

/**
 * 创建3d任务
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/167883 |接口文档}
 */
export function createTo3DImageTask(data: CreateTo3DImageTaskBody) {
  return instance.post<void, { id: string }>(
    '/template/do.json',
    toSnakeCase(data)
  );
}

/**
 * 关闭失败提示
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/169213 |接口文档}
 */
export function closeTaskFailureTip(data: { id: string }) {
  return instance.post<void>('/close_tip.json', toSnakeCase(data));
}

/** 创建AI 海报
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120378 |接口文档}
 */
export function createPosterTask(data: CreatePosterTaskBody) {
  return instance.post<void, CreateText2ImageTaskResponse>(
    '/generate/ai_poster.json',
    toSnakeCase(data)
  );
}

/**
 * 图片下载记录
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/169866 |接口文档}
 */
export function downloadImage(data: { msgId: string; imageUrl: string }) {
  return instance.post<void>('/download_image.json', toSnakeCase(data));
}
