import { toSnakeCase } from '@meitu/util';
import {
  TutorialDetailRes,
  TutorialListQuery,
  TutorialListRes
} from './types/tutorial';
import { createMockInstance, createInstance } from './utils';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/tutorial');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/tutorial' });

/**
 * 查询教程列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/146301|接口文档}
 */
export function fetchTutorialList(params: TutorialListQuery) {
  return instance.get<void, TutorialListRes>('/list.json', {
    params: toSnakeCase(params)
  });
}

/**
 * 查询教程文章详情
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/146304|接口文档}
 */
export function fetchTutorialDetail(params: { address: string }) {
  return instance.get<void, TutorialDetailRes>('/query.json', { params });
}
