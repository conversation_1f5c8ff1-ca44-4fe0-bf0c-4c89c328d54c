import type { OriginalHomeConfigResponse, CommonConfigResponse } from './types';
import type {
  OverviewBanner,
  CommonConfig,
  OverviewRecommendCard,
  OverviewOperation
} from '@/types';

import { createInstance, createMockInstance } from './utils';

import { FuncType } from './types';
import { addQueryParams, getSource } from '@/utils';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/home');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/home' });

/**
 * 获取首页配置
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/125550|接口文档}
 */
export function fetchHomeConfig() {
  return instance.get<void, OriginalHomeConfigResponse>('/config.json').then(
    (response) =>
      ({
        banners: response.bannerList.map<OverviewBanner>((originalBanner) => ({
          id: originalBanner.id.toString(),
          cover: originalBanner.picture,
          linkType: originalBanner.url ? originalBanner.type : undefined,
          link: originalBanner.url || undefined,
          title: originalBanner.content ?? ''
        })),
        // HACK 目前只支持单行
        recommendCards: response?.funcBarList
          .find(({ type }) => FuncType.Card === type)
          ?.funcList?.map<OverviewRecommendCard>((func) => ({
            id: func.id.toString(),
            title: func.name,
            description: func.title,
            cover: func.pic,
            hoverCover: func.checkPic,
            status: func.status,
            link: addQueryParams(func.url, { source: getSource() }),
            linkType: func.type,
            tag: func.tag,
            colspan: func.colspan
          })),
        operations: response.rightUpperOperateList?.map<OverviewOperation>(
          (originOperation) => ({
            id: originOperation.id.toString(),
            title: originOperation.name,
            cover: originOperation.pic,
            hoverCover: originOperation.checkPic,
            link: originOperation.url || undefined,
            linkType: originOperation.type,
            taskCategory: originOperation.taskCategory || undefined
          })
        )
      } as const)
  );
}

/**
 * 获取全局共用配置
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/127719|接口文档}
 */
export function fetchCommonConfig() {
  return instance
    .get<void, CommonConfigResponse>('/common.json')
    .then<CommonConfig>((response) => {
      const { id, title, subtitle, backImage, groupImage } =
        response?.addGroupPopup ?? {};
      return {
        showJumpZcoolBtn: response?.showJumpZcoolBtn,
        zcoolBtnPicUrl: response?.zcoolBtnPicUrl,
        activitySource: response?.activitySource,
        noticePopup: response?.noticePopup,
        groupPopupConfig: {
          id,
          title,
          description: subtitle,
          backgroundImage: backImage,
          qrcode: groupImage
        }
      };
    });
}
