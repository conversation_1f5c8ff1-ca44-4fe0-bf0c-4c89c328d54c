import type { InternalAxiosRequestConfig, CreateAxiosDefaults } from 'axios';
import { AppOrigin, appOrigin, WheeAppId } from '@/constants';
import {
  createPCBAxiosInstance,
  toSnakeCase
  // getSessionStorageItem
} from '@meitu/util';
import { accountClientId, getTraceChannelParams } from '@/services';
import { toSnakeDict } from '@meitu/util';
import { createCommunityAxiosInstance } from './createCommunityAxiosInstance';
import {
  removeAccountAccessToken,
  getAccountAccessToken,
  account
} from '@/services';
import mtstat from '@meitu/mtstat-sdk';
import { toMTCCRequestHeaders } from '@/utils/toMTCCRequestHeaders';
import { abTest } from '@/services';
import { v4 as uuid } from 'uuid';
import { MtccFuncCode } from './types';
import { message } from 'antd';

/** 业务响应码 */
enum BizCode {
  // 未登录
  Unauthorized = 10022,

  // 禁止访问
  Forbidden = 11004,

  // 权限不足
  NoPermission = 11020
}

const accountParams = toSnakeDict({
  clientId: accountClientId
});
const zcoolAccountParams = toSnakeDict({
  clientId: '**********'
});

const sdkAccountParams = toSnakeDict({
  clientId: '**********'
});
// 业务需求，需要获取客户端时区
const getLocalTimeZone = () => {
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  return timeZone;
};
const clientTimezone = getLocalTimeZone();

export function shimRequestGeneralParams(
  config: InternalAxiosRequestConfig,
  extra: Record<string, any> = {}
) {
  if (!config.method) {
    return config;
  }
  const gnum = mtstat.getDeviceId();

  switch (config.method.toUpperCase()) {
    case 'GET': {
      config.params = {
        ...config.params,

        ...accountParams,
        gnum,
        client_timezone: clientTimezone,
        ...extra
      };
      break;
    }

    default: {
      config.data = {
        ...config.data,
        ...accountParams,
        client_timezone: clientTimezone,
        gnum,
        ...extra
      };
    }
  }

  return config;
}

export function sdkShimRequestGeneralParams(
  config: InternalAxiosRequestConfig,
  extra: Record<string, any> = {}
) {
  if (!config.method) {
    return config;
  }
  const gnum = mtstat.getDeviceId();

  switch (config.method.toUpperCase()) {
    case 'GET': {
      config.params = {
        ...config.params,

        ...sdkAccountParams,
        gnum,
        client_timezone: clientTimezone,
        ...extra
      };
      break;
    }

    default: {
      config.data = {
        ...config.data,
        ...sdkAccountParams,
        client_timezone: clientTimezone,
        gnum,
        ...extra
      };
    }
  }

  return config;
}

/**
 * 傻逼的公共参数垫片
 * @param config 请求配置
 * @param extra 自定义额外的一个写公共参数
 */
export function shimWheeRequestGeneralParams(
  config: InternalAxiosRequestConfig,
  extra: Record<string, any> = {}
) {
  if (!config.method) {
    return config;
  }
  const gnum = mtstat.getDeviceId();

  // 获取生成张数
  const getBatchSize = () => {
    if (config?.data) {
      // 有传了自定义张数参数，优先取值（目前只针对延伸创作）
      if (config.data.custom_batch_size) {
        return config.data.custom_batch_size;
      }
      if (config?.data?.params && typeof config?.data?.params === 'string') {
        return (
          JSON.parse(config?.data?.params).batch_size ||
          JSON.parse(config?.data?.params).generate_num ||
          '1'
        );
      } else {
        return (
          config?.data?.params?.batch_size ||
          config?.data?.batch_size ||
          config?.data?.generate_num ||
          '1'
        );
      }
    }
  };

  switch (config.method.toUpperCase()) {
    case 'GET': {
      config.params = {
        ...config.params,
        ...accountParams,
        gnum,
        client_timezone: clientTimezone,
        ...extra
      };
      break;
    }

    default: {
      config.data = {
        ...config.data,
        ...accountParams,
        client_timezone: clientTimezone,
        gnum,
        ...extra
      };
    }
  }

  const mtccData = toMTCCRequestHeaders(
    WheeAppId,
    'CN',
    config?.data?.function_name === MtccFuncCode.FuncCodeModelTraining
      ? 'ModelTraining'
      : 'CreativeTools',
    {
      osType: 'web',
      gnum: mtstat.getDeviceId(),
      bizId: WheeAppId,
      orderId: uuid(),
      function: {
        name: config?.data?.function_name
      },
      mediaType: config?.data?.media_type,
      resMediaType: config?.data?.res_media_type,
      extInfo: toSnakeCase({
        number: getBatchSize(),
        modelName: config?.data?.model_name
      })
    }
  ) as any;

  config.headers = {
    ...config.headers,
    ...mtccData
  };

  return config;
}

export function zcoolshimWheeRequestGeneralParams(
  config: InternalAxiosRequestConfig,
  extra: Record<string, any> = {}
) {
  if (!config.method) {
    return config;
  }
  const gnum = mtstat.getDeviceId();

  // 获取生成张数
  const getBatchSize = () => {
    if (config?.data) {
      // 有传了自定义张数参数，优先取值（目前只针对延伸创作）
      if (config.data.custom_batch_size) {
        return config.data.custom_batch_size;
      }
      if (config?.data?.params && typeof config?.data?.params === 'string') {
        return (
          JSON.parse(config?.data?.params).batch_size ||
          JSON.parse(config?.data?.params).generate_num ||
          '1'
        );
      } else {
        return (
          config?.data?.params?.batch_size ||
          config?.data?.batch_size ||
          config?.data?.generate_num ||
          '1'
        );
      }
    }
  };

  switch (config.method.toUpperCase()) {
    case 'GET': {
      config.params = {
        ...config.params,
        ...zcoolAccountParams,
        gnum,
        client_timezone: clientTimezone,
        ...extra
      };
      break;
    }

    default: {
      config.data = {
        ...config.data,
        ...zcoolAccountParams,
        client_timezone: clientTimezone,
        gnum,
        ...extra
      };
    }
  }

  const mtccData = toMTCCRequestHeaders(
    WheeAppId,
    'CN',
    config?.data?.function_name === MtccFuncCode.FuncCodeModelTraining
      ? 'ModelTraining'
      : 'CreativeTools',
    {
      osType: 'web',
      gnum: mtstat.getDeviceId(),
      bizId: WheeAppId,
      orderId: uuid(),
      function: {
        name: config?.data?.function_name
      },
      mediaType: config?.data?.media_type,
      resMediaType: config?.data?.res_media_type,
      extInfo: toSnakeCase({
        number: getBatchSize(),
        modelName: config?.data?.model_name
      })
    }
  ) as any;

  config.headers = {
    ...config.headers,
    ...mtccData
  };

  return config;
}

export function createInstance(config?: CreateAxiosDefaults) {
  const [channelParams] = getTraceChannelParams();

  return createPCBAxiosInstance(
    {
      ...config,
      baseURL: `${process.env.PUBLIC_URL}/api${config?.baseURL}`
    },
    {
      async beforeRequest(config) {
        config.headers['X-App-Origin'] = appOrigin;

        let abInfo = await abTest.getABInfo();
        config.headers['ab_info'] = JSON.stringify(abInfo);

        // 设计室的应用源下不应该在 headers 中注入 `Access-Token`
        if (appOrigin !== AppOrigin.Designer) {
          config.headers['Access-Token'] = getAccountAccessToken();
        }

        // 渠道参数透传
        if (channelParams && channelParams?.channel) {
          Object.assign(config.headers, channelParams);
        }

        return shimWheeRequestGeneralParams(config);
      },

      throwException(axiosError, response) {
        const url = `${axiosError.config!.baseURL}${axiosError.config!.url}`;
        const status = response?.status;
        const code = response?.data?.code;

        // 未登录
        if (
          status === 401 ||
          (status === 400 && code === BizCode.Unauthorized)
        ) {
          // HACK: 不应当在此执行登录逻辑
          // 获取用户信息的接口不需要执行登录逻辑
          if (url === '/aigc/api/user/me.json') {
            // 获取个人信息接口不应当抛出错误，改为返回 `undefined`
            // eslint-disable-next-line no-throw-literal
            throw undefined;
          }
          // HACK: 不应当在此执行登录逻辑
          // 编辑器的接口不需要执行登录逻辑
          if (
            url === '/aigc/api/image_modify/project/save.json' ||
            url === '/aigc/api/image/get_sign.json'
          ) {
            // 获取个人信息接口不应当抛出错误，改为返回 `undefined`
            // eslint-disable-next-line no-throw-literal
            throw axiosError;
          }
          if (
            window.location.href.toLowerCase().includes('zcoolmaterial') &&
            url === '/aigc/api/task/download_image.json'
          ) {
            // 获取个人信息接口不应当抛出错误，改为返回 `undefined`
            // eslint-disable-next-line no-throw-literal
            throw axiosError;
          }

          removeAccountAccessToken();
          if (!window.location.href.toLowerCase().includes('zcoolmaterial')) {
            account.logout({ relogin: true });
          }
          throw axiosError;
        }

        // 禁止访问
        if (
          (status === 403 || (status === 400 && code === BizCode.Forbidden)) &&
          !window.location.href.toLowerCase().includes('zcoolmaterial')
        ) {
          // 跳转到创作者指南文档
          window.location.href = process.env.REACT_APP_CREATION_GUIDE_LINK;

          throw axiosError;
        }

        //  权限不足，重定向到首页
        if (code === BizCode.NoPermission) {
          message.error(response?.data?.message);

          setTimeout(() => {
            window.location.href = process.env.REACT_APP_LINK;
          }, 300);
        }

        throw axiosError;
      }
    }
  );
}

// zcool 接口请求
export function zcoolcreateInstance(config?: CreateAxiosDefaults) {
  const [channelParams] = getTraceChannelParams();

  return createPCBAxiosInstance(
    {
      ...config,
      baseURL: `${process.env.PUBLIC_URL}${config?.baseURL}`
    },
    {
      async beforeRequest(config) {
        config.headers['X-App-Origin'] = appOrigin;

        let abInfo = await abTest.getABInfo();
        config.headers['ab_info'] = JSON.stringify(abInfo);

        // 设计室的应用源下不应该在 headers 中注入 `Access-Token`
        if (appOrigin !== AppOrigin.Designer) {
          config.headers['Access-Token'] = getAccountAccessToken();
        }
        config.headers['App-Id'] = '209';
        config.headers['client_id'] = '**********';
        // config.headers['zcool_session'] = `zcool_mid=${getSessionStorageItem(
        //   'meituUid'
        // )}; zcool_logon_new=${getSessionStorageItem('zcoolLogonNew')}`;
        // config.headers['Access-Token'] = getAccountAccessToken();

        // 渠道参数透传
        if (channelParams && channelParams?.channel) {
          Object.assign(config.headers, channelParams);
        }

        return zcoolshimWheeRequestGeneralParams(config);
      },

      throwException(axiosError, response) {
        const url = `${axiosError.config!.baseURL}${axiosError.config!.url}`;
        const status = response?.status;
        const code = response?.data?.code;

        // 未登录
        if (
          status === 401 ||
          (status === 400 && code === BizCode.Unauthorized)
        ) {
          if (
            url === '/aigc/api/sdk/ai_material/generate.json' ||
            url === '/aigc/api/sdk/task/user_task_params.json' ||
            // url === '/aigc/api/sdk/task/user_task_params.json' ||
            url === '/aigc/api/sdk/ai_material/generate_task.json' ||
            url === '/aigc/api/sdk/ai_material/delete.json'
          ) {
            // 获取个人信息接口不应当抛出错误，改为返回 `undefined`
            // eslint-disable-next-line no-throw-literal
            throw axiosError;
          }
          removeAccountAccessToken();
          // account.logout({ relogin: true });
          throw undefined;
        }

        // 禁止访问
        if (status === 403 || (status === 400 && code === BizCode.Forbidden)) {
          // 跳转到创作者指南文档
          // window.location.href = process.env.REACT_APP_CREATION_GUIDE_LINK;

          throw axiosError;
        }

        //  权限不足，重定向到首页
        if (code === BizCode.NoPermission) {
          message.error(response?.data?.message);

          setTimeout(() => {
            window.location.href = process.env.REACT_APP_LINK;
          }, 300);
        }

        throw axiosError;
      }
    }
  );
}

// 公共SDK接口请求
export function publicSdkInstance(config?: CreateAxiosDefaults) {
  const [channelParams] = getTraceChannelParams();

  return createPCBAxiosInstance(
    {
      ...config,
      baseURL: `${process.env.PUBLIC_URL}/api${config?.baseURL}`
    },
    {
      async beforeRequest(config) {
        config.headers['X-App-Origin'] = appOrigin;

        let abInfo = await abTest.getABInfo();
        config.headers['ab_info'] = JSON.stringify(abInfo);

        // 设计室的应用源下不应该在 headers 中注入 `Access-Token`
        if (appOrigin !== AppOrigin.Designer) {
          config.headers['Access-Token'] = getAccountAccessToken();
        }

        // 渠道参数透传
        if (channelParams && channelParams?.channel) {
          Object.assign(config.headers, channelParams);
        }

        return sdkShimRequestGeneralParams(config);
      },

      throwException(axiosError, response) {
        const url = `${axiosError.config!.baseURL}${axiosError.config!.url}`;
        const status = response?.status;
        const code = response?.data?.code;

        // 未登录
        if (
          status === 401 ||
          (status === 400 && code === BizCode.Unauthorized)
        ) {
          // HACK: 不应当在此执行登录逻辑
          // 获取用户信息的接口不需要执行登录逻辑
          if (url === '/aigc/api/user/me.json') {
            // 获取个人信息接口不应当抛出错误，改为返回 `undefined`
            // eslint-disable-next-line no-throw-literal
            throw undefined;
          }
          // HACK: 不应当在此执行登录逻辑
          // 编辑器的接口不需要执行登录逻辑
          if (
            url === '/aigc/api/image_modify/project/save.json' ||
            url === '/aigc/api/image/get_sign.json'
          ) {
            // 获取个人信息接口不应当抛出错误，改为返回 `undefined`
            // eslint-disable-next-line no-throw-literal
            throw axiosError;
          }
          if (
            window.location.href.toLowerCase().includes('zcoolmaterial') &&
            url === '/aigc/api/task/download_image.json'
          ) {
            // 获取个人信息接口不应当抛出错误，改为返回 `undefined`
            // eslint-disable-next-line no-throw-literal
            throw axiosError;
          }

          removeAccountAccessToken();
          if (!window.location.href.toLowerCase().includes('zcoolmaterial')) {
            account.logout({ relogin: true });
          }
          throw axiosError;
        }

        // 禁止访问
        if (
          (status === 403 || (status === 400 && code === BizCode.Forbidden)) &&
          !window.location.href.toLowerCase().includes('zcoolmaterial')
        ) {
          // 跳转到创作者指南文档
          window.location.href = process.env.REACT_APP_CREATION_GUIDE_LINK;

          throw axiosError;
        }

        //  权限不足，重定向到首页
        if (code === BizCode.NoPermission) {
          message.error(response?.data?.message);

          setTimeout(() => {
            window.location.href = process.env.REACT_APP_LINK;
          }, 300);
        }

        throw axiosError;
      }
    }
  );
}

/**
 * 创建模拟接口的 axios 实例
 * @param pathname 路径
 */
export function createMockInstance(pathname: string) {
  return createPCBAxiosInstance({
    baseURL: `https://api-mock.meitu-int.com/mock/2243${pathname}`
  });
}

/**
 * 创建画廊（秀秀社区的）Axios 实例
 */
export function createGalleryAxiosInstance(config?: CreateAxiosDefaults) {
  return createCommunityAxiosInstance(
    {
      ...config,
      baseURL: `${process.env.PUBLIC_URL}/api${config?.baseURL}`
    },
    {
      beforeRequest: (config) => {
        config.headers['Access-Token'] = getAccountAccessToken();

        if (config?.method?.toUpperCase() === 'POST') {
          // 秀秀社区提供的接口走的是 FormData
          config.headers['Content-Type'] = 'multipart/form-data';
        }

        return shimRequestGeneralParams(config, {
          /** 坑爹的服务端，我们是不要校验版本的，但是一个必填参数 */
          version: '1.0.0'
        });
      }
    }
  );
}
