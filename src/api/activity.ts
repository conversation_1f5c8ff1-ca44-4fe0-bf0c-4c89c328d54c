import { toSnakeCase } from '@meitu/util';
import {
  CheckInResponse,
  ClaimMissionRewardRequestParams,
  ClaimMissionRewardResponse,
  MissionInfoResponse
} from './types/activity';
import { createInstance } from './utils';

const instance = createInstance({ baseURL: '/activity' });

/**
 * 获取任务中心信息
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/152811 接口文档}
 */
export function fetchMissionInfo() {
  return instance.get<void, MissionInfoResponse>('/list.json');
}

/**
 * 签到接口
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/152831 接口文档}
 */
export function requestCheckIn() {
  return instance.post<void, CheckInResponse>('/check_in.json');
}

/**
 * 领取任务奖励
 * @param id 任务id
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/152832 接口文档}
 */
export function claimMissionReward(id: number) {
  return instance.post<
    void,
    ClaimMissionRewardResponse,
    ClaimMissionRewardRequestParams
  >(
    '/receive_explore_reward.json',
    toSnakeCase({
      exploreActivityId: id
    })
  );
}
