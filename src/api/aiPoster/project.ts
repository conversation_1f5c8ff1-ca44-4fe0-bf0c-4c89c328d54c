import { toSnakeCase } from '@meitu/util';
import { createInstance } from '../utils';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/ai_poster/project' });

/**
 * 删除项目
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172525 接口文档}
 */

export function deleteProject(params: { projectId: number }) {
  return instance.post<void, void>('/delete.json', toSnakeCase(params));
}
