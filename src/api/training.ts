import { createMockInstance, createInstance } from './utils';
import {
  TrainingConfigQuery,
  TrainingConfigResponse,
  TrainingCopiedResponse,
  TrainingDeletedResponse,
  TrainingDoBody,
  TrainingDoResponse,
  TrainingFileManageBody,
  TrainingFileManageResponse,
  TrainingParamsSaveResponse,
  TrainingProgressResponse,
  TrainingResultResponse,
  TrainingPublishParams,
  PublishConfigParams,
  PublishConfigResponse,
  PublishResponse,
  IPCharacterLoraTrainingProgressParams,
  CreateIPCharacterLoraParams,
  IPCharacterLoraTrainingProgressResponse
} from './types';
import { toSnakeCase } from '@meitu/util';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/training');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/training' });

/**
 * 训练-配置
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/123460 接口文档}
 */
export function fetchTrainingConfig(params: TrainingConfigQuery) {
  return instance.get<void, TrainingConfigResponse>('/config.json', {
    params
  });
}

/**
 * 训练-文件管理
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/123970 接口文档}
 */
export function trainingFileManage(params: TrainingFileManageBody) {
  return instance.post<void, TrainingFileManageResponse>(
    '/file_manage.json',
    params
  );
}

/**
 * 训练-开始训练
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/124237 接口文档}
 */
export function trainingDo(params: TrainingDoBody) {
  return instance.post<void, TrainingDoResponse>('/do.json', params);
}

/**
 * 训练-训练结果轮询
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/124682 接口文档}
 */
export function fetchTrainingStatus() {
  return instance.get<void, TrainingResultResponse>('/query.json');
}

/**
 * 训练-重新训练
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/125104 接口文档}
 */
export function reTraining(params: { id: number }) {
  return instance.get<void, { id: number }>('/retry.json', {
    params
  });
}

/**
 * 训练-训练进度
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/127219 接口文档}
 */
export function fetchTrainingProgress(params: { id: number }) {
  return instance.get<void, TrainingProgressResponse>('/progress.json', {
    params
  });
}

/**
 * 训练-删除训练模型
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/128563 接口文档}
 */
export function deleteTrainingModel(params: { id: number }) {
  return instance.post<void, TrainingDeletedResponse>('/delete.json', params);
}

/**
 * 训练-复制草稿
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/128560 接口文档}
 */
export function copyTrainingModel(params: { id: number }) {
  return instance.post<void, TrainingCopiedResponse>('/copy.json', params);
}

/**
 * 训练-参数设置
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/128553 接口文档}
 */
export function saveTrainingParams(params: TrainingDoBody) {
  return instance.post<void, TrainingParamsSaveResponse>(
    '/param_set.json',
    params
  );
}

/**
 * 训练-模型发布
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/129706 接口文档}
 */
export function publishTrainingModel(params: TrainingPublishParams) {
  return instance.post<void, PublishResponse>(
    '/publish.json',
    toSnakeCase(params)
  );
}

/**
 * 训练 - 模型发布更新
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/135127 接口文档}
 */
export function updateTrainingModel(params: TrainingPublishParams) {
  return instance.post<void, PublishResponse>(
    '/publish_update.json',
    toSnakeCase(params)
  );
}

/**
 * 训练-模型发布配置
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/135124 接口文档}
 */
export function fetchPublishConfig(params: PublishConfigParams) {
  return instance.get<void, PublishConfigResponse>('/publish_config.json', {
    params
  });
}

/**
 * 训练 - 训练任务量查询
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/149651 接口文档}
 */
export function fetchTrainingTotal() {
  return instance.get<void, { doing: number }>('/volume.json');
}

/**
 * 创建ip形象lora
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/168035 接口文档}
 */
export function createIpCharacterLora(data: CreateIPCharacterLoraParams) {
  return instance.post('ip_generate.json', toSnakeCase(data));
}

/**
 * 查询ip形象lora的训练进度
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/168127 接口文档}
 */
export function fetchIPCharacterTrainingProgress(
  params: IPCharacterLoraTrainingProgressParams
) {
  return instance.get<void, IPCharacterLoraTrainingProgressResponse>(
    '/progress_batch.json',
    {
      params
    }
  );
}

/**
 * ip形象lora训练-重新训练
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/125104 接口文档}
 */
export function ipCharacterLoraTrainingRetry(params: { id: number }) {
  return instance.post<void, { id: number }>('/ip_generate_retry.json', {
    ...params
  });
}
