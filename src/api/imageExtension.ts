import { toSnakeCase } from '@meitu/util';
import {
  ImageExtensionConfigResponse,
  ImageExtensionDoRequest,
  ImageExtensionDoResponse,
  ImageExtensionQueryRequest,
  ImageExtensionQueryResponse,
  ImageExtensionHistoryListResponse
} from './types';
import { createMockInstance, createInstance } from './utils';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/image_extension');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/image_extension' });

/**
 * AI扩图-开始扩展
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/128940 接口文档}
 */
export function startImageExtension(data: ImageExtensionDoRequest) {
  return instance.post<void, ImageExtensionDoResponse>(
    '/do.json',
    toSnakeCase(data)
  );
}

/**
 * AI扩图-查询详情
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/128951 接口文档}
 */
export function queryImageExtension(data: ImageExtensionQueryRequest) {
  return instance
    .get<void, ImageExtensionQueryResponse>('/query.json', {
      params: data
    })
    .then<ImageExtensionQueryResponse>(
      ({ resultImages, status, id, failMsg, params }) => {
        return {
          status,
          id,
          failMsg,
          params,
          resultImages: resultImages?.map((item) =>
            Object.assign(item, {
              hadSatisfied: !!item.hadSatisfied
            })
          )
        };
      }
    );
}

/**
 * AI扩图-配置
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/128926 接口文档}
 */
export function fetchImageExtensionConfig() {
  return instance.get<void, ImageExtensionConfigResponse>('/config.json');
}

/**
 * AI扩图-历史记录
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/128958 接口文档}
 */
export function fetchExtensionHistoryList(params: {
  count: number;
  cursor: string;
}) {
  return instance.get<void, ImageExtensionHistoryListResponse>('/list.json', {
    params
  });
}

/**
 * AI扩图-删除历史记录
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/128962 接口文档}
 */
export function extensionHistoryDelete(data: { id: string; imageUrl: string }) {
  return instance.post<void, { result: boolean }>(
    '/delete.json',
    toSnakeCase(data)
  );
}
