import { createMockInstance, createInstance } from './utils';

import { AxiosRequestConfig } from 'axios';
import type {
  CorpusCategoryResponse,
  CorpusListResponse,
  CorpusTranslateResponse,
  PromptSearchRequest,
  PromptSearchResponse,
  PromptSuggestRequest,
  PromptSuggestResponse,
  RandomPromptResponse,
  DeepSeekOptimizeResponse
} from './types';
import type { Corpus } from '@/types';
import { hasChinese } from '@/utils/corpus';
import { toSnakeCase } from '@meitu/util';

import { CorpusFilter } from '@/types';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/corpus');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/corpus' });

/**
 * 语料库-智能联想
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/124982 接口文档}
 */
export function promptSuggest(
  data: PromptSuggestRequest,
  config?: AxiosRequestConfig
) {
  return instance.post<void, PromptSuggestResponse>(
    '/prompt_suggest.json',
    data,
    config
  );
}

/**
 * 语料库-智能补全
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/124988 接口文档}
 */
export function promptSearch(
  data: PromptSearchRequest,
  config?: AxiosRequestConfig
) {
  return instance.post<void, PromptSearchResponse>(
    '/prompt_search.json',
    data,
    config
  );
}
/**
 * 获取语料库分类列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/123250 接口文档}
 */
export function fetchCorpusCategory(from: CorpusFilter = CorpusFilter.ALL) {
  return instance.get<void, CorpusCategoryResponse>('/category_nav.json', {
    params: { from }
  });
}

function generateCorpusList({ list }: CorpusListResponse): Corpus[] {
  return (
    list?.map(({ id, name, nameEnglish }) => ({
      id: id.toString(),
      text: name,
      translation: nameEnglish
    })) ?? []
  );
}

/**
 * 获取语料库列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/123259 接口文档}
 */
export function fetchCorpusList(id: number) {
  return instance
    .get<void, CorpusListResponse>('/category_list.json', { params: { id } })
    .then<Corpus[]>(generateCorpusList);
}

/**
 * 搜索语料库列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/123271 接口文档}
 */
export function searchCorpusList(
  word: string,
  from: CorpusFilter = CorpusFilter.ALL
) {
  return instance
    .get<void, CorpusListResponse>('/search.json', { params: { word, from } })
    .then<Corpus[]>(generateCorpusList);
}

/**
 * 语料翻译
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/123280 接口文档}
 */
export function translateCorpusText(words: string[]) {
  return instance
    .post<void, CorpusTranslateResponse>(
      '/translate.json',
      toSnakeCase({ wordList: words })
    )
    .then<Pick<Corpus, 'text' | 'translation'>[]>(({ list }) =>
      words.map((word) => {
        const indexInResult =
          list?.findIndex(
            ({ wordZh, wordEn }) => wordZh === word || wordEn === word
          ) ?? -1;

        if (indexInResult > -1) {
          const translated = list[indexInResult];
          list.splice(indexInResult, 1);

          return {
            text: translated.wordZh,
            translation: translated.wordEn
          };
        }

        return hasChinese(word)
          ? {
              text: word,
              translation: ''
            }
          : {
              text: '',
              translation: word
            };
      })
    );
}

/**
 * 搜索联想
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/125227 接口文档}
 */
export function fetchSuggestKeywords(
  word: string,
  from: CorpusFilter = CorpusFilter.ALL
) {
  return instance
    .get<void, CorpusListResponse>('/search_suggest.json', {
      params: { word, from }
    })
    .then<{ value: string }[]>(({ list }) =>
      list.map(({ name }) => ({
        value: name
      }))
    );
}

/**
 * 获取随机 prompt
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/ 接口文档}
 */
export function fetchRandomPrompt() {
  return instance
    .get<void, RandomPromptResponse>('/prompt.json')
    .then<Array<string>>(({ list }) => {
      return list.map(({ content }) => content);
    });
}

/**
 *  根据提示词获取deepseek 的优化
 *  @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/176370 接口文档}
 */
export function fetchDeepSeekOptimize(prompt: string) {
  return instance.post<void, DeepSeekOptimizeResponse>(
    '/prompt_optimize.json',
    {
      text: prompt
    }
  );
}

/**
 *  根据token 获取优化后的提示词
 *  @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/176373 接口文档}
 */
export function fetchDeepSeekOptimizeByToken(token: string) {
  return instance.post<void, any>('/prompt_optimize_result.json', {
    token
  });
}
