import { createFeedbackInstance } from './createFeedbackInstance';
import {
  getSessionStorageItem,
  setSessionStorageItem,
  toSnakeCase,
  getLocalStorageItem,
  removeLocalStorageItem
} from '@meitu/util';
import type {
  // ZcoolOriginalFetchAccountProfileResponse,
  OriginalFetchAccountProfileResponse,
  CommonPostResponse,
  ApplyAccountBody,
  ModelListRequest,
  ModelListResponse,
  AccountProfileWithConfig,
  UnReadMessageCountParams,
  UnReadMessageCountResponse,
  ChangePersonalSettingRequest,
  ChangePersonalSettingResponse
} from './types';

import {
  createMockInstance,
  createInstance,
  zcoolcreateInstance,
  publicSdkInstance
} from './utils';
import { AppOrigin, appOrigin } from '@/constants';
import { mgmShareCodeKey } from '@/App/MGMShareModal';
import { MGM_ACCEPT_SHARE_CODE } from '@/App/MGMAcceptModal';
import { isWebSDK } from '@/utils';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/user');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/user' });

const instanceZcool = zcoolcreateInstance({ baseURL: '/api/sdk/user' });

const sdkInstance = publicSdkInstance({ baseURL: '/sdk/user' });

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const imInstance = createFeedbackInstance({ baseURL: '/im_v2' });

/**
 * TODO: 获取账号信息
 * 获取用户信息
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120297}
 */
export function fetchAccountProfile() {
  // 站酷跳转过来的送美豆临时需求，头部需要验证签名和渠道
  const getChannelVerifyParams = () => {
    if (appOrigin !== AppOrigin.Whee) return {};

    const searchParams = new URLSearchParams(window.location.search);
    const timestamp =
      searchParams.get('timestamp') ??
      getSessionStorageItem('X-Channel-Timestamp');
    const sign =
      searchParams.get('sign') ?? getSessionStorageItem('X-Channel-Sign');

    if (timestamp && sign) {
      setSessionStorageItem('X-Channel-Timestamp', timestamp);
      setSessionStorageItem('X-Channel-Sign', sign);
    }

    return { 'X-Channel-Timestamp': timestamp, 'X-Channel-Sign': sign };
  };
  const queryParams = new URLSearchParams(window.location.search);

  // 邀请code
  const shareCode =
    queryParams.get(mgmShareCodeKey) ||
    getLocalStorageItem(MGM_ACCEPT_SHARE_CODE);
  let _instance = isWebSDK() ? sdkInstance : instance;
  return _instance
    .get<void, OriginalFetchAccountProfileResponse | undefined>('/me.json', {
      headers: getChannelVerifyParams(),
      params: {
        code: shareCode
      }
    })
    .then<AccountProfileWithConfig>((response) => {
      if (!response) {
        return {};
      }
      const {
        user: originalAccountProfile,
        config: { hasEditorAccess: editorAccessibility, ...restProps },
        membership,
        tip: accountTipConfig,
        // eslint-disable-next-line
        personalSetting: personalSetting
      } = response;

      if (shareCode) removeLocalStorageItem(MGM_ACCEPT_SHARE_CODE);
      return {
        accountProfile: {
          id: originalAccountProfile.id,
          name: originalAccountProfile.screenName,
          avatar: originalAccountProfile.avatar
        },
        editorAccessibility,
        editorModuleAccessibility: {
          ...restProps
        },
        membership,
        accountTipConfig,
        personalSetting
      } as const;
    });
}

/**
 * TODO: 获取zcool账号信息
 * 获取用户信息
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120297}
 */
export function fetchZcoolAccountProfile() {
  // 站酷跳转过来的送美豆临时需求，头部需要验证签名和渠道
  const getChannelVerifyParams = () => {
    if (appOrigin !== AppOrigin.Whee) return {};

    const searchParams = new URLSearchParams(window.location.search);
    const timestamp =
      searchParams.get('timestamp') ??
      getSessionStorageItem('X-Channel-Timestamp');
    const sign =
      searchParams.get('sign') ?? getSessionStorageItem('X-Channel-Sign');

    if (timestamp && sign) {
      setSessionStorageItem('X-Channel-Timestamp', timestamp);
      setSessionStorageItem('X-Channel-Sign', sign);
    }

    return { 'X-Channel-Timestamp': timestamp, 'X-Channel-Sign': sign };
  };
  const queryParams = new URLSearchParams(window.location.search);

  // 邀请code
  const shareCode =
    queryParams.get(mgmShareCodeKey) ||
    getLocalStorageItem(MGM_ACCEPT_SHARE_CODE);
  return instanceZcool
    .get<void, OriginalFetchAccountProfileResponse | undefined>('/me.json', {
      headers: getChannelVerifyParams(),
      params: {
        code: shareCode
      }
    })
    .then<any>((response) => {
      if (!response) {
        return {};
      }
      const {
        user: originalAccountProfile
        // config: { hasEditorAccess: editorAccessibility, ...restProps },
        // membership,
        // tip: accountTipConfig,
        // // eslint-disable-next-line
        // personalSetting: personalSetting
      } = response;
      if (shareCode) removeLocalStorageItem(MGM_ACCEPT_SHARE_CODE);
      return {
        accountProfile: {
          id: originalAccountProfile.id,
          name: originalAccountProfile.screenName,
          avatar: originalAccountProfile.avatar
        },
        editorAccessibility: {},
        editorModuleAccessibility: {
          // ...restProps
        },
        membership: {},
        accountTipConfig: {},
        personalSetting: {}
      } as const;
    });
}

/**
 * 申请编辑器使用权限
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120414 接口文档}
 */
export function applyEditorAccount(data: ApplyAccountBody) {
  return mockInstance.post<void, CommonPostResponse>('/apply.json', data);
}

/**
 * 获取用户模型
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/124681 接口文档}
 */
export function fetchAccountModels(params: ModelListRequest) {
  return instance.get<void, ModelListResponse>('/model_list.json', {
    params
  });
}

/**
 * 未读消息数量
 */
export function unReadMessageCount(params: UnReadMessageCountParams) {
  return imInstance.get<void, UnReadMessageCountResponse>(
    `/unread_count.json`,
    {
      params
    }
  );
}

/**
 * 用户-修改个人开关配置
 */
export function updatePersonalSetting(data: ChangePersonalSettingRequest) {
  return instance.post<void, ChangePersonalSettingResponse>(
    '/change_personal_setting.json',
    toSnakeCase(data)
  );
}
