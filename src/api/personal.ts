import { UserInfo } from '@/types';
import { createInstance, publicSdkInstance } from './utils';
import { toSnakeCase } from '@meitu/util';
import {
  FeedListResponse,
  FeedListResponseProfile,
  PosterProjectListResponse,
  PostJobInfoResponse,
  ProjectListResponse
} from './types';
import type { Gallery, GalleryProfile, UserInfoResponse } from '@/types';
import { isWebSDK } from '@/utils';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/user' });
const sdkInstance = publicSdkInstance({ baseURL: '/sdk/user' });

// 类别 1 全部 2 已发布 3 未发布 默认 1
export enum OptionsType {
  All = '1',
  Published = '2',
  UnPublish = '3'
}
//1 作品 2 模型 默认 1
export enum CollectionsType {
  Work = '1',
  Model = '2'
}
export interface ListQuery {
  type?: OptionsType | CollectionsType;
  /**
   * 个数，默认20
   */
  count?: number;
  /**
   * 翻页用的标识，将返回值里存在的非空cursor进行透传翻页
   */
  cursor?: string;
}

/**
 * 个人主页用户信息
 * @param params.uid 作品id
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120297 接口文档}
 */
export function fetchUserInfo() {
  let _instance = isWebSDK() ? sdkInstance : instance;
  // console.log('fetchUserInfo', _instance);
  return _instance
    .get<void, UserInfoResponse>('me.json')
    .then<UserInfo>((res) => {
      const {
        user: { id, screenName, avatar },
        task
      } = res;
      return {
        uid: id,
        screenName,
        avatarUrl: avatar,
        ...task
      };
    });
}

export function transformFeedList(
  params: FeedListResponseProfile
): GalleryProfile {
  const { width, height, user, isFavor, canUseSame } = params;

  return {
    ...params,
    size: [width, height],
    canUseSame: !!canUseSame,
    user: {
      id: user.id,
      userName: user.screenName,
      avatar: user.avatar
    },
    isFavor: isFavor === 1
  };
}

/**
 * 个人主页作品列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/137906 接口文档}
 */
export function fetchPersonalWorkList(params: ListQuery) {
  return instance
    .get<void, FeedListResponse>('feed.json', {
      params: toSnakeCase(params)
    })
    .then<Gallery>((response) => {
      const { cursor, list = [] } = response;
      const result = list.map(transformFeedList);

      return { total: result.length, cursor, list: result };
    });
}

/**
 * 用户收藏的作品列表
 * @param params.uid 作品id
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/137902 接口文档}
 */
export function fetchCollectionList(params: ListQuery) {
  return instance
    .get<void, FeedListResponse>('favorite_feed.json', {
      params: toSnakeCase(params)
    })
    .then<Gallery>((response) => {
      const { cursor, list = [] } = response;
      const result = list.map(transformFeedList);

      return { total: result.length, cursor, list: result };
    });
}

/**
 * 用户的项目列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/148939 接口文档}
 */
export function fetchProjectsList(params: Pick<ListQuery, 'cursor'>) {
  return instance
    .get<void, ProjectListResponse>('project_feed.json', {
      params: toSnakeCase(params)
    })
    .then((response) => {
      const { cursor, list = [] } = response;
      return { total: list.length, cursor, list };
    });
}

/**
 * ai海报项目列表
 * @description {@link https://api-mock.meitu-int.com/project/2485/interface/api/172474 接口文档}
 */
export function fetchPosterProjectsList(params: Pick<ListQuery, 'cursor'>) {
  return instance
    .get<void, PosterProjectListResponse>('ai_poster_feed.json', {
      params: toSnakeCase(params)
    })
    .then((response) => {
      const { cursor, list = [] } = response;
      return { total: list.length, cursor, list };
    });
}

/**
 * 删除 whee 作品
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/138226 接口文档}
 */
export function deletePersonalWork(feedId: string) {
  return instance.post<void, void>(
    'feed_destroy_xiuxiu.json',
    toSnakeCase({ feedId })
  );
}

/**
 * whee 作者职业信息填写发放美豆
 */
export function fillOccupationalInformation() {
  return instance.post<void, PostJobInfoResponse>('submit_job_info.json');
}
