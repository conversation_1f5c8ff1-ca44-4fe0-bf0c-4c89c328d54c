import { CommonCreate, ResultImage, TaskStatus } from '.';

export type ImageEraserDoRequest = {
  /**
   * 原图链接
   */
  initImage: string;
  /**
   * mask图
   */
  maskImage: string;
  /**
   * 一系列正，负交互点（归一化后的）坐标组成的列表。每个交互点表示为： [正负点标志1 或 0， y坐标/height，x坐标/width]，[[1, 0.5, 0.5], [1, 0.9, 0.5], [1, 0.3, 0.5], ...]
   */
  clickList: [number, number, number][];
  // 从其他模块跳转过来的消息id
  msgId?: string;
} & CommonCreate;

export type ImageEraserDoResponse = {
  /**
   * 任务id
   */
  id: string;
};

export type ImageEraserQueryRequest = {
  /**
   * 任务id列表，多个任务id通过英文逗号隔开
   * */
  ids: string;
};

export type ImageEraserQueryResponse = {
  /**
   * 任务id
   * */
  id: string;
  /**
   * 任务参数
   * */
  args: Pick<ImageEraserDoRequest, 'initImage'>;
  /**
   * 任务状态：1-初始，2-生成中，3-生成成功，4-生成失败
   * */
  status: TaskStatus;
  /**
   * 生成的图片列表
   * */
  resultImages: Pick<ResultImage, 'url' | 'imageStatus' | 'hadSatisfied'>[];
  /**
   * 任务失败信息
   * */
  failMessage?: string;
};

export type ImageEraserHistoryResponse = {
  list: Array<ImageEraserQueryResponse>;
  cursor: string;
};

export interface ImageEraserConfigResponse {
  tip: {
    show: boolean;
    title: string;
  };
}
