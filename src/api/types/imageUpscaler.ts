import { CommonCreate } from './common';
import { ImageStatus } from './imageExtension';
import { TaskParams } from './task';

//  1 - 初始状态，2 - 生成中，3 - 成功，4 - 失败
export enum UpscalerTaskStatus {
  INITIAL = 1,
  GENERATING = 2,
  SUCCESS = 3,
  FAILURE = 4
}

export interface CreateUpscalerImageTaskBody extends CommonCreate {
  // 任务id
  msgId: string;
  //要超分的图片链接
  imageFile: string;
}

export interface UpscalerTaskResponse {
  // 生图任务id
  wheeMsgId: string;
  // 超分id
  id: string;
  // 超分前的图，超分过会有
  hdOriginUrl: string;
  status: UpscalerTaskStatus;
  // AI超清 服务端任务状态
  taskStatus: number;
  failMessage: string;
  resultImage: {
    url: string;
    imageStatus: ImageStatus;
    hadSatisfied: boolean;
    urlWatermark?: string;
  };
  originWidth?: number;
  originHeight?: number;
  // 创建时间戳
  createdAt: number;
  // 生成参数
  params: TaskParams;
}

// AI 超清 - 编辑器配置
export interface ImageUpscaleConfigStyleList {
  id: string;
  name: string;
  icon: string;
  desc: string;
  tag?: string;
  createValue: {
    default: number;
    editable: boolean;
    hidden?: boolean; // 是否隐藏
  };
  hdrValue: {
    default: number;
    editable: boolean;
    hidden?: boolean; // 是否隐藏
  };
  resemblanceValue: {
    default: number;
    editable: boolean;
    hidden?: boolean; // 是否隐藏
  };
}
export interface ImageUpscaleConfigResponse {
  styleList: Array<ImageUpscaleConfigStyleList>;
}

// AI 超清 - 任务提交
export interface CreateImageUpscaleTaskBody extends CommonCreate {
  imageFile: string;
  srNum: number;
  styleId: number;
  createValue: number;
  resemblanceValue: number;
  hdrValue: number;
  initImages?: Array<string>;
  // 从其他模块跳转过来的消息id
  msgId?: string;
}

export interface ImageUpscaleSizeBody extends CreateImageUpscaleTaskBody {
  srNums: string;
  styleIds: string;
}

export interface ImageUpscaleSizeResponse {
  [srNum: number]: {
    [styleId: number]: [number, number];
  };
}

// AI 超清 - 列表
export interface ImageUpscaleTaskListResponse {
  /** 翻页标识 */
  cursor?: string;
  /** 草稿结果列表 */
  list: UpscalerTaskResponse[];
}
