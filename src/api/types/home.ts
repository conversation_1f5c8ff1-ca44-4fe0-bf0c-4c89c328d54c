import type {
  OverviewBannerLinkType,
  OverviewRecommendCardStatus
} from '@/types';

/**
 * 原始首页配置响应
 */
export interface OriginalHomeConfigResponse {
  /** 横幅列表 */
  bannerList: Array<{
    /** 横幅编号 */
    id: number;

    /** 横幅文案 */
    content: string;

    /** 图片地址 */
    picture: string;

    /** 横幅链接 */
    url: string;

    /** 跳转类型 */
    type: OverviewBannerLinkType;
  }>;

  /** 功能栏列表 */
  funcBarList: Array<
    Omit<FuncBaseConfig, 'type'> & {
      /**
       * 功能栏类型
       * @description {@link https://cf.meitu.com/confluence/pages/viewpage.action?pageId=402313315|接口文档}
       */
      type: FuncType;
      funcList: Array<
        Omit<FuncBaseConfig, 'type'> &
          FunCoverConfig & {
            /** 状态 1可用，2不可用(前端只展示 不可用，点击提示开发中)	*/
            status: OverviewRecommendCardStatus;
            /** 标签 角标 url */
            tag: string;
            /** 副标题 */
            title: string;
            /** 跳转类型 */
            type: OverviewBannerLinkType;
            colspan?: number;
          }
      >;
    }
  >;

  /** 右上角运营栏列表 */
  rightUpperOperateList: Array<FuncBaseConfig & FunCoverConfig>;
}

/** 功能栏基础属性 */
export interface FuncBaseConfig {
  /** 功能栏id */
  id: number;
  /** 功能栏名称 */
  name: string;
  /**  链接类型 */
  type: OverviewBannerLinkType;
  /**  任务类别 */
  taskCategory?: string;
}

export interface FunCoverConfig {
  /** 跳转地址 */
  url: string;
  /** 未选中图标 */
  pic: string;
  /** 已选中图标 */
  checkPic: string;
}

/** 功能栏类型 */
export enum FuncType {
  /** 首页-feed流 */
  Feed = 7,
  /** 首页-单行多功能栏 */
  Card = 8
}

/**
 * 全局共用配置的返回结果
 */
export interface CommonConfigResponse {
  /**
   * 加群引导弹窗
   */
  addGroupPopup: {
    /**
     * 弹窗id
     */
    id: number;
    /**
     * 标题
     */
    title: string;
    /**
     * 副标题
     */
    subtitle: string;
    /**
     * 背景图
     */
    backImage: string;
    /**
     * 群二维码
     */
    groupImage: string;
  };

  /**
   * 文生图/图生图页面是否展示跳转站酷发布按钮
   */
  showJumpZcoolBtn: boolean;
  /**
   * 站酷跳转按钮图片
   */
  zcoolBtnPicUrl: string;
  /**
   * 站酷活动来源
   */
  activitySource: string;

  /**
   * 更新公告配置
   */
  noticePopup: Array<{
    /** 公告标题 */
    title: string;
    /** 公告内容 */
    content: string;
    /** 跳转链接 */
    jump_link?: string;
  }>;
}
