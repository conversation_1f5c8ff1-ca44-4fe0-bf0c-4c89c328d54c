import { BenefitsType } from '@/types';

export interface MeidouQueryBalanceResponse {
  /**
   * 可用余额（美豆+美叶）
   * */
  availableAmount: number;
  /**
   * 文案提示(默认输出为：美豆可通过购买获得，购买后即永久有效。您可使用美豆购买并体验WHEE的各种权益～更多美豆玩法即将上线～)
   * */
  tips: string;

  // 会员权益描述(本月已发放美豆 xx 个，m月d日待发放xx个)
  vipRightInfo: string;

  //明细页面标题(剩余美豆)
  detailTitle: string;

  // 明细页面描述(“优先消耗临期美豆”)
  detailDesc: string;

  // 美豆明细列表
  amountList: Array<{
    // 类型：1-永久有效，前端加粗，2-临期美豆
    type: BenefitsType;
    // 过期描述：N天后过期
    expireStr: string;
    //描述：“（付费美豆可用于美图系全部产品）”
    desc: string;
    // 数量
    num: number;
  }>;
}

export interface MeidouFetchPriceDescRequest {
  /**
   * 功能标识，例如img2img,text2img,training
   */
  functionCode: FunctionCode;
  /**
   * 原功能完整body压成json字符串
   */
  functionBody: string;

  /** 是否是新版改图项目 */
  isProject?: boolean;
}

export interface MeiDouFetchPriceDescResponse {
  /**
   * 功能标识
   */

  functionCode: FunctionCode;
  /**
   * 功能名称
   */
  functionName: string;
  /**
   * 总金额
   * */
  amount: number;
  /**
   * 支付类型 1.美豆支付 2.美叶支付 3.美豆+美叶
   * */
  payScene: number;
  /**
   *  是否会员
   *  */
  isVip: boolean;
  /**
   * 消耗美豆的文案，例如：消耗6美豆
   * */
  costPriceText: string;
  /**
   * 消耗美豆的原价，例如：消耗3美豆
   * */
  costPriceTextOrigin: string;
  /**
   * 消耗美豆后面的感叹号说明
   * */
  costPriceTips: string;
  /**
   * 价格明细
   * */
  priceDetail: Array<{
    /**
     * 商品名称，例如：文生图
     * */
    itemName: string;
    /**
     * 数量，例如：*3张
     * */
    itemCount: string;
    /**
     * 原价，例如：6美豆
     *
     * */
    priceOrigin: string;
    /**
     * 当前价格，例如：3美豆
     * */
    priceNow: string;
  }>;
  /**
   * 本次生成使用的限免数量
   */
  useFreeNum: number;
  /**
   * 剩余限免次数
   */
  totalFreeNum: number;
  /**
   * AI海报限免张数
   */
  freeNumTips?: string;
}

export enum FunctionCode {
  img2img = 'img2img',
  text2img = 'text2img',
  training = 'training',
  extend = 'extend',
  inpaint = 'inpaint',
  aiVideo = 'ai_video_mvv',
  aimodelimg = 'aimodelimg',
  upScale = 'magicsr',
  aiEraser = 'ai_eraser',
  compound = 'compound', // ai合成
  // IP形象定制
  concept = 'concept_map_design', // 概念图
  ipimagetraining = 'ip_image_training', // ip形象训练
  ipimagedrawing = 'ip_image_drawing', // ip形象生图
  ipplaneto3d = 'ip_planeto3d', // 平面转3d
  aifreematerial = 'aifreematerial', //AI 免抠素材
  aiPoster = 'ai_poster', // AI 海报
  referInpaint = 'refer_inpaint' // 参考图改图
}
