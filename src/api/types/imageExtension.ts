import { CommonCreate } from './common';
import { UpscalerTaskStatus } from './imageUpscaler';
import { Graph } from '@/types';

/**
 * 扩图的固定比例 original:原始  1:1  2:3  3:4  9:16  等其他比例
 */
export enum CropAspect {
  ORIGINAL = 'original',
  RATIO_1_1 = '1:1',
  RATIO_2_3 = '2:3',
  RATIO_3_4 = '3:4',
  RATIO_9_16 = '9:16',
  RATIO_3_2 = '3:2',
  RATIO_4_3 = '4:3',
  RATIO_16_9 = '16:9'
}

/**
 *  状态1初始 2生成中 3生成成功 4生成失败
 * */
export enum TaskStatus {
  INITIAL = 1,
  GENERATING = 2,
  SUCCESS = 3,
  FAILURE = 4
}
/**
 * 图片状态：1正常，2审核删除
 * */
export enum ImageStatus {
  NORMAL = 1,
  DELETED = 2
}

export type ResultImage = {
  /** 图片地址 */
  url: string;
  /** 图片状态：1正常，2审核删除 */

  status?: 1 | 2;
  /** 图片状态：1正常，2审核删除 */
  imageStatus: ImageStatus;
  /** 同一批次每张生成的 seed 不一致 */
  seed?: number;
  /** 是否提交过满意度 1是 0否 */
  hadSatisfied?: boolean;
  /** 超分信息 */
  upscalerInfo?: {
    //超分任务 id
    id: string;
    // 超分状态
    status: UpscalerTaskStatus;
    resultImage: Graph;
    // 分辨率宽高
    width: number;
    height: number;
  };
};

/** --------------------------------------------------- */

export type ImageExtensionDoRequest = {
  /**
   * 描述
   */
  prompt: string;
  /**
   * 图片地址
   *  */
  imageFile: string;
  imageRatio: CropAspect;
  /**
   * 宽度
   * */
  width: number;
  /**
   *  高度
   *      */
  height: number;
  /**
   * 中心水平偏移量
   * */
  centerX: number;
  /**
   * 中心垂直偏移量
   * */
  centerY: number;
  /**
   * 图片缩放	比例
   * */
  scale: number;
  /**
   * 生成张数
   * */
  generateNum: number;
  // 从其他模块跳转过来的消息id
  msgId?: string;
} & CommonCreate;

export type ImageExtensionDoResponse = {
  /**
   * 任务id
   */
  id: string;
};

export type ImageExtensionQueryRequest = {
  /**
   * 任务id
   * */
  id: string;
};

export type ImageExtensionQueryResponse = {
  /**
   * 任务id
   * */
  id: string;
  status: TaskStatus;
  /**
   * 任务结果，效果图片数据
   * */
  resultImages?: ResultImage[];
  /**
   * 失败原因
   * */
  failMsg: string;
  /**
   * 请求参数
   * */
  params: {
    /**
     * 描述
     * */
    prompt: string;
    /**
     * 图片地址
     * */
    imageFile: string;
    imageRatio: CropAspect;
    /**
     * 宽度
     * */
    width: number;
    /**
     * 高度
     * */
    height: number;
    /**
     * 中心水平偏移量
     * */
    centerX: number;
    /**
     * 中心垂直偏移量
     * */
    centerY: number;
    /**
     * 图片缩放	比例
     * */
    scale: number;
    /**
     * 生成张数
     * */
    generateNum: number;
  };
};

export type ImageExtensionConfigResponse = {
  /**
   * 是否不限制 true 不限制，false 限制
   */
  isUnlimited: boolean;
  /**
   * 今日生成张数总数量
   * */
  allNum: number;
  /**
   * 今日生成张数剩余数量
   * */
  residualNum: number;
};

export interface ImageExtensionHistoryListResponse {
  list: Array<ImageExtensionQueryResponse>;
  cursor: string;
}
