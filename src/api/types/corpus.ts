import type { CorpusCategory } from '@/types';

export interface PromptSuggestRequest {
  /** 当前prompt文本，用于智能联想词 */
  text?: string;
  /** 自动联想时不传值，点击换一换时传1值。	 */
  from?: number;
}

export interface SuggestPromptProps {
  promptId: string;
  promptName: string;
}

export interface PromptSuggestResponse {
  suggestPrompt: SuggestPromptProps[];
}

export interface PromptSearchRequest {
  /** 当前prompt文本，用于智能联想词 */
  text?: string;
}

export interface PromptSearchResponse {
  resultPrompt: {
    promptName: string;
  }[];
}

/**
 * 语料库分类返回值
 */
export interface CorpusCategoryResponse {
  /** 分类列表 */
  list: CorpusCategory[];
}

/**
 * 语料单元返回值
 */
export interface CorpusItem {
  /** 语料库id */
  id: number;
  /** 语料库名称 */
  name: string;
  /** 语料库英文名称 */
  nameEnglish: string;
  /** 封面 */
  coverPic?: string;
}

/**
 * 语料库列表返回值
 */
export interface CorpusListResponse {
  /** 总数 */
  total: number;
  /** 语料库列表 */
  list: CorpusItem[];
}

/**
 * 语料库翻译返回值
 */
export interface CorpusTranslateResponse {
  /** 翻译结果列表 */
  list: {
    wordZh: string;
    wordEn: string;
  }[];
}

export interface RandomPromptResponse {
  list: Array<{
    /** 描述词id */
    id: number;

    /** 描述词内容 */
    content: string;
  }>;
}

export interface DeepSeekOptimizeResponse {
  token: string;
}
