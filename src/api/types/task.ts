import type {
  OriginalFetchAccountProfileResponse,
  EditorConfigModelResponse,
  UpscalerTaskStatus,
  CommonCreate
} from '.';
import type { Graph, GraphBatch, To3DTaskStatus } from '@/types';
import { Proportion } from '@/components/SizeSelector/constants';
import { DraftType } from './draft';
import { Optional } from 'utility-types';

/**
 * 获取任务
 */
export interface TaskQuery {
  /**
   * 任务ids，多个任务id用逗号分隔
   */
  ids: string;
}

/**
 * 获取任务返回结果
 */
export type TaskResponse = TaskInterface[];

/**
 * 文生图任务请求参数
 */
export interface CreateText2ImageTaskBody extends CommonCreate {
  /**
   * APP ID
   */
  appId?: number;
  /**
   * 效果 ID
   */
  effectId?: number;
  /**
   * 用户id（uid\gid\admin_id不能都为空）
   */
  uid?: number;

  /**
   * 编辑器生成图片的任务参数
   */
  params: TaskParams;

  /**
   * 创作模式 推荐模式 / 高级创作
   */
  simplifyVersion?: number;
}

/**
 * 延伸创作任务请求参数
 */
export interface DeeperDesignTaskBody extends CommonCreate {
  /**
   * 任务id
   */
  msgId: string;
  /**
   * 图片地址
   */
  image: string;
  /**
   * 图片对应的seed，如果是超分后的图还是用超分前的结果图seed
   */
  imageSeed: number;
  /**
   * 描述词
   */
  prompt: string;
  /**
   * 延伸创作张数，自定义的只用于埋点上报
   */
  customBatchSize?: string;
}

/**
 * 首页简化文生图任务请求参数
 */
export interface SimplifyCreateText2ImageTaskBody
  extends Omit<CreateText2ImageTaskBody, 'params'> {
  params: Pick<TaskParams, 'prompt'>;
}

/**
 * 文生图任务返回结果
 */
export type CreateText2ImageTaskResponse = Array<{
  /**
   * 草稿(任务)id
   */
  id: string;
  /**
   * 预估时间，单位是秒
   */
  estimatedTime: number;
}>;

export interface createImageProcessingTaskBody {
  /**
   * 基础模型 ID
   */
  baseModelId: number;
  /**
   * 原始图片
   */
  image: string;
  /**
   * 预处理器
   */
  module: string;
  /**
   * 预处理器模型
   */
  model: string;
  /**
   * 反色模式：0不启用，1启用
   */
  scribbleMode?: number;
  /**
   * RGB转BGR：0不启用，1启用
   */
  rgb2bgrMode?: number;
  /**
   * 原始图片宽度
   */
  width: number;
  /**
   * 原始图片高度
   */
  height: number;
}

/********************  公共类型放下面  */

export interface ControlNetUnitsType {
  /**
   * 是否启用
   */
  enabled: boolean;
  /**
   * 图片地址
   */
  inputImage: string;
  /**
   * 遮罩图（controlnet中间上传图片后，涂抹黑色的部分）
   */
  mask?: string;
  /**
   * 预处理器
   */
  module: string;
  /**
   * 预处理器模型
   */
  model: string;
  /**
   * 模型id
   */
  modelId: number;
  /**
   * 权重
   */
  weight: number;
  /**
   * 引导介入时机（Start） Guidance Start (T)
   */
  guidanceStart: number;
  /**
   * 引导退出时机（End） Guidance End (T)
   */
  guidanceEnd: number;
  /**
   * 画面缩放：1拉伸，2自动缩放，3裁剪
   */
  resizeMode: number;
  /**
   * 预处理参数
   */
  imageProcessParams?: {
    image: string;
    module: string;
    model: string;
  };
}

export interface StyleModelConfigType {
  /**
   * 风格模型id
   */
  styleModelId: number;
  /**
   * 风格模型所属分类，多个分类,分隔
   * tips: 主要是埋点使用
   */
  styleModelCategories: string;
  /**
   * 风格模型-权重，数值1-100
   */
  styleModelWeight: number;
  /**
   * 风格模型封面
   */
  styleModelImage?: string;
  /**
   * 风格模型名称
   */
  styleModelName?: string;
  /**
   * 风格模型类型名称
   */
  styleModelType?: string;
}

export interface TaskParams {
  /**
   * 正向提示词/提示词
   */
  prompt: string;
  /**
   * 用户填写的原始值  未经过智能联想
   */
  userPrompt?: string;
  /**
   * 不希望呈现的内容
   */
  negativePrompt: string;
  /**
   * 模型id
   */
  baseModelId: number;
  /**
   * 风格模型配置
   */
  styleModelConfig: StyleModelConfigType[];
  /**
   * 参数设定-画面尺寸-宽度
   */
  width: number;
  /**
   * 参数设定-画面尺寸-高度
   */
  height: number;
  /**
   * 参数设定-图片比例（不用上报，会根据尺寸下发）
   */
  picRatio: Proportion;
  /**
   * 创意相关性 ( prompt相关性权重 CFG)
   */
  cfgScale: number;
  /**
   * 人脸相似度 0-1 默认 1
   */
  faceGenerationScale: number;
  /**
   * 选择生成的图片数量，1-10    默认数量4
   */
  batchSize: number;
  /**
   * 采样器
   */
  samplerIndex: string;
  /**
   * 模型采样步骤/采样迭代步数，默认25
   */
  steps: number;
  /**
   * 随机数种子，默认-1（开启随机就是传值-1）
   */
  seed: number;
  /**
   * 生成批次,默认1次
   */
  nIter: number;
  /**
   * 更多细节 取值[-200, 200]
   */
  moreDetailWeight: number;
  /**
   * 是否面部修复（对于人脸图片生成，是否对脸部进行修复）
   */
  restoreFaces: boolean;
  /**
   * 是否开启面部修复 AD 插件
   */
  adetailerFace: boolean;
  /**
   * 是否高清修复/是否对图片进行超分操作
   */
  enableHr: boolean;
  /**
   * 参考图图片-图片地址数组
   */
  initImages?: string[];
  /**
   * 参考图片-降噪强度
   */
  denoisingStrength: number;
  /**
   * 参考图片-缩放模式
   */
  resizeMode: number;
  /**
   * 扩散控制网络(controlnet) 控制参数
   */
  controlnetUnits: ControlNetUnitsType[];
  /**
   * 图片识别
   */
  facialDetection?: boolean;

  /** 图像超分 */
  mdUpscaler?: boolean;

  /** 开启抠图 */
  cutoutImage?: boolean;

  advanced?: { iterationPeriod: number; trainingFrequency?: number };

  imageFile?: string;
  srNum?: number;
  styleId?: number;
  createValue?: number;
  resemblanceValue?: number;
  hdrValue?: number;
  tgEditorParams?: string;
  templateId?: number;
  formulaMsgId?: string;
}

/**
 * 图片状态：1正常，2审核删除
 * */
enum ImageStatus {
  NORMAL = 1,
  DELETED = 2
}

type ResultImage = {
  /** 图片地址 */
  url: string;
  /** 图片状态：1正常，2审核删除 */

  /**水印图片地址 */
  urlWatermark?: string;

  status?: 1 | 2;
  /** 图片状态：1正常，2审核删除 */
  imageStatus: ImageStatus;
  /** 同一批次每张生成的 seed 不一致 */
  seed?: number;
  /** 是否提交过满意度 1是 0否 */
  hadSatisfied?: boolean;
  /** 超分信息 */
  upscalerInfo?: {
    //超分任务 id
    id: string;
    // 超分状态
    status: UpscalerTaskStatus;
    resultImage: Graph;
    // 分辨率宽高
    width: number;
    height: number;
  };
  // 转3D信息
  planeto3DInfo?: {
    // 平面转 3d 任务 I
    id: string;
    // 超分状态
    status: To3DTaskStatus;
  };
  planeto3DOriginUrl?: string;
  zcoolFileId?: string;
};

export interface TaskInterface {
  /**
   * 任务id
   */
  id: string;
  /**
   * 任务种类：
   * txt2img = 文生图，img2img= 图生图
   * TODO：转为枚举
   */
  taskCategory: DraftType;
  /**
   * 任务结果，效果地址列表
   */
  resultImages: ResultImage[];
  /**
   * 处理进度：数值1-100
   */
  processPercent: number;
  /**
   * 用户信息
   */
  user: OriginalFetchAccountProfileResponse['user'];
  /**
   * 任务参数集合json，提交给开放平台的全量参数
   */
  params: TaskParams;
  /**
   * 模型信息
   */
  model: EditorConfigModelResponse;
  /**
   * 1=创建成功；2=入参已投递安全审核；10=预处理器处理中；20=推理任务处理中；30=后置处理器处理中；40=结果已投递安全审核；90=入参审核失败；91=预处理器处理失败；92=推理任务处理失败；93=后置处理器处理失败；94=结果安全审核处理失败；99=未定义异常；100=处理成功
   */
  status: number;
  /**
   * 任务失败原因描述
   */
  failMsg: string;
  /**
   * 是否收藏
   */
  isCollect: boolean;
  /**
   * 是否发布
   */
  isPublish: boolean;
  /**
   * 开始时间
   */
  createdAt: number;
  /**
   * 创作模式 推荐模式 / 高级创作
   */
  simplifyVersion?: number;
  wheeMsgId?: string;

  /** 禁用重新编辑 */
  disableEdit?: boolean;

  /**  zcool是否是做同款 1：同款任务来源于自己，2、同款任务来源于别人 */
  inherit?: number;
}

export type ImageTasks = Array<
  Pick<GraphBatch, 'id' | 'size'> &
    Optional<Pick<GraphBatch, 'params' | 'disableEdit'>>
>;

export interface CreateTo3DImageTaskBody extends CommonCreate {
  initImage: string;
  width: number;
  height: number;
  originalMsgId: string;
  from: DraftType;
}
