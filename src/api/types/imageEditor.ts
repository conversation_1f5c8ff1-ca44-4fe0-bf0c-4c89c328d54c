import {
  CommonCreate,
  ControlNetUnitsType,
  EditorConfigModuleListResponse,
  ResultImage,
  StyleModelConfigType
} from '.';

export type ImageEditorConfigResponse = {
  projectList: {
    projectId: number;
    picUrl: string;
  }[];
  surplus: number;
};
export enum TaskCategory {
  inpaint = 'inpaint',
  referInpaint = 'refer_inpaint',
  eraser = 'ai_eraser',
  segment = 'segment',
  extend = 'extend',
  change_background = 'change_back',
  graffiti = 'graffiti',
  // AI合成枚举
  compound = 'compound',
  cutout = 'cutout',
  extendLayer = 'extendLayer'
}

export type ImageEditorSaveRequest = {
  /**
   * 项目id 传0则表示新增项目
   */
  projectId: number;

  version: number;
  /**
   * 封面图图
   */
  picUrl: string;
  /**
   * 编辑器参数
   */
  editorParams: string;
};

export type ImageEditorSaveResponse = {
  /**
   * 任务id
   */
  projectId: number;
  version: number;
};
export type ImageEditorProjectInfoRequest = {
  projectId: number;

  // 是否返回编辑信息
  withEditorParams?: boolean;
};
export type ImageEditorProjectInfoResponse = {
  projectId: number;
  picUrl: string;
  version: number;
  editorParams?: string;
  editorParamsMd5?: string;
};

export type ImageEditorInferenceTaskRequest<T extends TaskCategory> = {
  /**
   * 任务id
   */
  projectId: number;
  /**推理任务类型 */
  taskCategory: T;

  /**
   * 图层id
   */
  layerId: string;

  /**任务参数  */
  params: T extends TaskCategory.extend
    ? ExtendImageEditorParamsInferenceTaskRequest
    : T extends TaskCategory.inpaint
    ? InpaintImageEditorParamsInferenceTaskRequest
    : T extends TaskCategory.referInpaint
    ? ReferInpaintImageEditorParamsInferenceTaskRequest
    : T extends TaskCategory.compound
    ? CompositeImageEditorParamsInferenceTaskRequest
    : T extends TaskCategory.eraser
    ? eraserImageEditorParamsInferencetaskRequest
    : T extends TaskCategory.cutout
    ? cutoutImageEditorParamsInferencetaskRequest
    : never;
} & CommonCreate;

export type ExtendImageEditorParamsInferenceTaskRequest = {
  /**提示词 */
  prompt: string;
  /**原图地址 */
  imageFile: string;
  /**扩图画面尺寸 */
  imageRatio: number;
  /**扩图宽度 */
  width: number;
  /**扩图高度 */
  height: number;

  /**中心水平偏移 */
  centerX: number;
  /**中心垂直偏移 */
  centerY: number;
  /**图片缩放比例 */
  scale: number;
  /**生成张数 */
  generateNum: number;
};

export type ImageEditorInferenceTaskResponse = {
  id: string;
  reqId: string;
  surplus: number;
};

export type ImageEditorBatchResponse = {
  list: Array<{
    id: string;
    resultImages?: ResultImage[];
  }>;
};

export type InpaintImageEditorParamsInferenceTaskRequest = {
  /**提示词 */
  prompt: string;
  /**反向提示词 */
  negativePrompt: string;
  /**原图链接 */
  initImage: string;
  /**mask 图 */
  maskImage: string;
  /**生成张数 */
  batchSize: number;
};

export type ReferInpaintImageEditorParamsInferenceTaskRequest = {
  /**原图链接 */
  initImage: string;
  /**mask图链接 */
  maskImage: string;
  /**参考图链接 */
  referImage: string;
};
// 合成的请求参数
export type CompositeImageEditorParamsInferenceTaskRequest = {
  /**提示词 */
  prompt: string;
  /**宽度 */
  width: number;
  /**高度 */
  height: number;
  /**生成张数 */
  batchSize: number;
  /**画面参考 */
  controlnetUnits: ControlNetUnitsType[];
  /**风格模型 */
  styleModelConfig: StyleModelConfigType[];
};

export type SmartSelectionRequestParams = {
  projectId: number;
  /**原图链接 */
  initImage: string;
  /**mask图链接 */
  maskImage?: string | null;

  brushWidth: string;

  clickList: [number, number, number][];
} & CommonCreate;

export type SmartSelectionResponse = {
  resultImage: string;
};

export type interactSelectionRequestParams = {
  projectId: number;
  /**原图链接 */
  initImage: string;
  /**mask图链接 */
  maskImage?: string | null;
  interact_type?: string;
} & CommonCreate;

export type interactSelectionResponse = {
  resultImage: string;
};

export type autoRecognitionSelectionRequestParams = {
  projectId: number;
  /**原图链接 */
  initImage: string;
  /**mask图链接 */
  maskImage?: string | null;
  interact_type?: string;
} & CommonCreate;

export type autoRecognitionSelectionResponse = {
  resultImage: string;
};

export type CancelTaskRequestParams = {
  projectId: number;
  msgId: string;
};

export type eraserImageEditorParamsInferencetaskRequest = {
  /**原图链接 */
  initImage: string;
  /**mask图链接 */
  maskImage?: string | null;
};
export type cutoutImageEditorParamsInferencetaskRequest = {
  /**原图链接 */
  initImage: string;
  /**mask图链接 */
  maskImage?: string | null;
  /**结果图 */
  resultImage: string;
};

export type CancelTaskResponse = {
  result: boolean;
};

export type DeleteProjectRequestParams = {
  projectId: number;
};

export type DeleteProjectResponse = {};
export type ImageEditorTaskConfigResponse = {
  compoundConfig: {
    moduleList: EditorConfigModuleListResponse[];
  };
};

// 根据 taskCategory 创建一个枚举 导出一个map类型
export const TaskMap: any = {
  [TaskCategory.inpaint]: 'modification',
  [TaskCategory.eraser]: 'clear',
  [TaskCategory.segment]: 'segment',
  [TaskCategory.extend]: 'extension',
  [TaskCategory.compound]: 'compound',
  [TaskCategory.cutout]: 'matting',
  [TaskCategory.graffiti]: 'sketch'
};

export enum LocationMode {
  sideBar = 'side_bar',
  floating = 'floating'
}
