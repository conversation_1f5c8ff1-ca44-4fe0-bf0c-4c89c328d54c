import { TaskParams } from './task';
import { CommonCreate } from './common';

export type CreateMaterialGenerateTaskParams = Pick<
  TaskParams,
  'prompt' | 'styleModelConfig' | 'batchSize' | 'width' | 'height'
> & {
  userPrompt?: string;
};

export type CreateMaterialGenerateTaskBody = {
  params: CreateMaterialGenerateTaskParams;
} & CommonCreate;

/** 风格模型列表请求参数 */
export interface AiMaterialTemplateListQuery {
  /** 分类id   不传则返回全部分类第一页数据 */
  categoryId?: number;
  /** 搜索内容，必须带上对应的分类id */
  keyword?: string;
  /** 翻页用的标识 ，必须带上对应的分类id */
  cursor?: string;
}

/** 风格模型列表返回参数 */
export interface AiMaterialTemplateListResponse {
  /** 分类列表 */
  list: AiMaterialTemplateResponse[];
}

export interface AiMaterialTemplateResponse {
  list: AiMaterialTemplateListItem[];
  categoryId: number;
  categoryName: string;
}
export interface AiMaterialTemplateListItem {
  id: number;
  images: string[] | string;
  name: string;
}
