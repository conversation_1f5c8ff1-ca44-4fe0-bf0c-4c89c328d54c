import type {
  EditorAccessibility,
  EditorModuleAccessibility,
  AccountProfile,
  AccountTipConfig
} from '@/types';
import { OptionsType } from '../personal';

/**
 * 原始获取用户信息响应结果
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120297 接口文档}
 */
export interface OriginalFetchAccountProfileResponse {
  user: {
    /** 用户 ID */
    id: number;

    /** 用户昵称 */
    screenName: string;

    /** 用户头像 */
    avatar: string;
  };

  /**
   * 相关配置
   */
  config: {
    /** 是否有编辑器权限 */
    hasEditorAccess: EditorAccessibility;

    /** 是否有模型训练权限 */
    hasTrainingAccess: boolean;

    /** 是否有发布到画廊的控制按钮权限 */
    hasPublishGalleryControlAccess: boolean;

    /** 是否有beta模型实验权限 */
    hasBetaModelAccess: boolean;

    /** 是否提交过用户职业信息收集 */
    hasSubmitJobInfo: boolean;
  };

  membership: MembershipDescResponse;

  /** 提示配置 */
  tip: AccountTipConfig;

  /** AI改图美豆自动消耗配置 */
  personalSetting: {
    imageModifyAutoPay: boolean;
  };
}

export interface ZcoolOriginalFetchAccountProfileResponse {
  user?: {
    /** 用户 ID */
    id: number;

    /** 用户昵称 */
    screenName: string;

    /** 用户头像 */
    avatar: string;
  };

  /**
   * 相关配置
   */
  config?: {
    /** 是否有编辑器权限 */
    hasEditorAccess?: EditorAccessibility;

    /** 是否有模型训练权限 */
    hasTrainingAccess?: boolean;

    /** 是否有发布到画廊的控制按钮权限 */
    hasPublishGalleryControlAccess?: boolean;

    /** 是否有beta模型实验权限 */
    hasBetaModelAccess?: boolean;

    /** 是否提交过用户职业信息收集 */
    hasSubmitJobInfo?: boolean;
  };

  membership?: MembershipDescResponse;

  /** 提示配置 */
  tip?: AccountTipConfig;

  /** AI改图美豆自动消耗配置 */
  personalSetting: {
    imageModifyAutoPay: boolean;
  };
}

/**
 * 用户信息 & 用户配置信息
 */
export interface AccountProfileWithConfig {
  /** 账号信息 */
  accountProfile?: AccountProfile;

  editorAccessibility?: EditorAccessibility;
  editorModuleAccessibility?: EditorModuleAccessibility;
  membership?: MembershipDescResponse;

  /** 账号提示配置 */
  accountTipConfig?: OriginalFetchAccountProfileResponse['tip'];
  personalSetting?: OriginalFetchAccountProfileResponse['personalSetting'];
}

export interface MembershipDescResponse {
  /**
   * 是否是vip会员（过期会员也是true）
   */
  isVip: boolean;
  /**
   * 有效期起始时间（不是会员返回0）单位:毫秒，示例：*************
   * */
  validTime: number;
  /**
   * 有效期时间结束时间（不是会员返回0）单位:毫秒
   * */
  invalidTime: number;
  /**
   * 服务器时间，单位:毫秒
   * */
  serverTime: number;
  /**
   * 会员状态 1-正常 2-过期 3-禁用
   * */
  status: MemberStatus;
  /**
   * 展示文案
   */
  desc: string;
}

export enum MemberStatus {
  /** 非会员 */
  NOT_MEMBER = 0,
  /** 正常 */
  NORMAL = 1,
  /** 过期 */
  EXPIRED = 2,
  /** 禁用 */
  DISABLED = 3
}

/**
 * 申请使用编辑器的请求参数
 */
export interface ApplyAccountBody {
  /**
   * 名称
   */
  name: string;
  /**
   * 邮箱
   */
  email: string;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * 描述
   */
  description?: string;
}

export interface ModelListRequest {
  /**
   * 个数，默认20
   */
  count?: number;
  /**
   * 翻页用的标识，将返回值里存在的非空cursor进行透传翻页
   */
  cursor?: string;
  // 分类
  type?: OptionsType;
}

export enum ModelStatus {
  /** 未训练 */
  NOT_TRAINED = 1,
  /** 训练中 */
  TRAINING = 2,
  /** 训练完成 */
  TRAINED = 3,
  /** 训练失败 */
  TRAIN_FAILED = 4,
  /** 审核中 */
  AUDITING = 5,
  /** 已发布 */
  PUBLISHED = 6
}

export interface ModelListItem {
  /**
   * 训练id
   */
  id: number;
  /**
   * 训练模型id
   */
  trainingModelId?: number;
  /**
   * 请求feed的详情
   */
  trainingId?: number;
  /**
   * 模型名称
   */
  name?: string;
  /**
   * 模型描述
   */
  desc?: string;
  /**
   * 封面
   */
  coverPic?: string;
  /**
   * 类型枚举值：1.Checkpoint Trained; 2.Checkpoint Merge; 3.Textual Inversion; 4.HyperNetwork; 5.Aesthetic Gradient; 6.LoRA; 7.LyCORIS; 8.Controlnet; 9.Poses; 10.Wildcards; 11.Other;
   */
  type?: string;
  /**
   * 类型名称
   */
  typeName?: string;
  /**
   * 状态1 未训练 2训练中 3训练完成 4 训练失败
   */
  status: ModelStatus;
  /**
   * 失败原因 状态为4时下发
   */
  failMessage?: string;
  /**
   * 基础模型id
   */
  baseModelId: number;
  /**
   * 模型封面，可能多个图片，默认使用第一个
   */
  images: string[];
  /**
   * 高级设置
   */
  advanced: {
    /**
     * 高级训练次数 1-16次
     */
    trainingFrequency: number;
    /**
     * 迭代周期 20、40、60、80、100
     */
    iterationPeriod: number;
  };
  /**
   * 预估剩余时间，例如：39分钟   、30~120分钟
   */
  estimatedTime?: string;

  /** 已发布模型返回 秀秀feed_id */
  feedId?: number;

  /** 编辑按钮是否禁用 */
  canUpdate: boolean;
}

export interface ModelListResponse {
  /**
   * 个数，默认20
   */
  total: number;
  /**
   * 翻页标识
   */
  cursor: string;

  list: ModelListItem[];
}

export type ProjectListItem = {
  /**项目id */
  projectId: number;
  /**项目版本 */
  version: number;
  /**预览图 */
  picUrl: string;
};

export type ProjectListResponse = {
  cursor: string;
  list: ProjectListItem[];
};

export type PosterProjectListItem = {
  /**项目id */
  projectId: number;
  /**项目版本 */
  projectName: string;
  /**预览图 */
  picUrl?: string;
  // 最近更新时间
  lastEditedTime: string;
};

export type PosterProjectListResponse = {
  cursor: string;
  list: PosterProjectListItem[];
};

export interface UnReadMessageCountParams {
  fid?: number; //意见反馈ID【在意见反馈详情页时需要传递】
  client_id: string;
}
export interface UnReadMessageCountResponse {
  // 在线咨询未读数
  onlineUnreadNum: number;
  // 意见反馈未读数
  feedbackUnreadNum: number;
}

export interface ChangePersonalSettingRequest {
  imageModifyAutoPay: boolean;
}

export interface ChangePersonalSettingResponse {}
