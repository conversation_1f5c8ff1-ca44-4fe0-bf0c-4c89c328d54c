import {
  EditorConfigBaseModelListResponse,
  EditorConfigBaseModelResponse
} from './editorConfig';
import { CreateText2ImageTaskBody, TaskParams } from './task';

export interface AIModelLabelType {
  /**
   * 标签id
   */
  id: string;
  /**
   * 标签名称
   */
  name: string;
  /**
   * 标签展示icon
   */
  icon: string;
  /**
   * 是否默认值
   */
  isDefault: boolean;
  /**
   * 前置依赖标签，只有该数组中任一标签被选中，当前标签才展示；目前只有基础设定信息中的年龄需要依赖性别展示
   */
  dependOn: string[];

  groupId: string;
}

export interface AIModelTagGroupType {
  /**
   * 模型id
   */
  id: string;
  /**
   * 类别：1-基础设定标签，2-场景设定标签
   */
  kind: number;
  /**
   * 标签组名
   */
  name: string;
  /**
   * 是否允许多选
   */
  enableMultiSelect: boolean;

  labels: AIModelLabelType[];
}

export interface AIModelConfigResponse {
  /**
   * 基础模型列表
   */
  baseModels: EditorConfigBaseModelResponse[];
  /**
   * 基础模型，前期只配置一个基础模型，可以直接使用这个
   */
  baseModel: EditorConfigBaseModelListResponse;
  /**
   * 基础设定-性别
   */
  gender: AIModelTagGroupType;
  /**
   * 基础设定-年龄
   */
  age: AIModelTagGroupType;
  /**
   * 基础设定肤色
   */
  skinColor: AIModelTagGroupType;
  /**
   * 场景列表
   */
  sceneList: AIModelTagGroupType[];
}

export interface AIModelGenerateRequest
  extends Omit<CreateText2ImageTaskBody, 'params'> {
  params: TaskParams & {
    aiModel: {
      /**
       * 性别标签
       */
      gender: string;
      /**
       * 年龄标签
       */
      age: string;
      /**
       *  肤色标签
       */
      skinColor: string;

      /**
       *  场景设定标签
       */
      sceneList: string[];
    };
  };
}
