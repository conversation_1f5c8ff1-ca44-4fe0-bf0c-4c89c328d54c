import { SelectProps } from 'antd';
import { ImageStatus, TaskStatus } from './imageExtension';
import { CommonCreate } from './common';

// 0 无、1 横移、2 竖移、3 平摇、4 直摇、5 推拉、6 旋转，默认 0
export enum CameraMovementDirection {
  None = 0,
  Horizontal = 1,
  Vertical = 2,
  PushPull = 5,
  Rotate = 6
}

export type ImageToVideoDoRequest = {
  /**
   * 正向提示词
   */
  prompt?: string;
  /**
   * 原图链接
   */
  initImage?: string;
  /**
   * 图像宽度
   */
  width: number;
  /**
   * 图像高度
   */
  height: number;
  /**
   * 画面比例
   */
  ratio: string;
  /**
   * 画面缩放模式
   */
  resizeMode: number;
  // 运镜方向
  cameraMovementDirection: CameraMovementDirection;
  // 运镜滑杆参数
  cameraMovementValue?: number;
} & CommonCreate;

export type ImageToVideoDoRequestV2 = {
  /**
   * 正向提示词
   */
  prompt?: string;
  /**
   * 原图链接
   */
  initImage?: string;
  /**
   * 文生视频生成的视频比例
   */
  ratio?: string;
  /**
   * 图像宽度
   */
  width?: number;
  /**
   * 图像高度
   */
  height?: number;
} & CommonCreate;

export type ImageToVideoDoResponse = {
  /**
   * 任务id
   */
  id: string;
};

export interface ImageToVideoQueryResponse {
  /**
   * 任务id
   * */
  id: string;
  /**
   * 任务状态：1-初始，2-生成中，3-生成成功，4-生成失败
   * */
  status: TaskStatus;
  /**
   * 状态附加文案
   * */
  statusMessage: string;
  /**
   * 生成的视频信息
   * */
  resultVideo?: {
    url: string;
    videoStatus: ImageStatus;
    hadSatisfied: boolean;
    cover: string;
  };
  params?: ImageToVideoDoRequest;
}

export type ImageToVideoHistoryResponse = {
  list: Array<ImageToVideoQueryResponse>;
  cursor: string;
};

export interface SizeItem {
  ratio: string;
  width: number;
  height: number;
  sizeName: string;
  textDefaultSelect: boolean;
  imageDefaultSelect: boolean;
}
export interface ImageToVideoConfigResponse {
  sizeList: Array<SizeItem>;
  imageBaseLength: number;
}

export interface ImageToVideoConfig {
  list: SelectProps['options'];
  imageBaseLength: number;
  imageDefaultSelectVal: string;
  textDefaultSelectVal: string;
  hiddenScaleVal: string;
}
