export interface TutorialListQuery {
  //分类模块 id
  categoryId?: string;
  cursor?: string;
  count?: number;
}

export interface TutorialListRes {
  list: Array<ListType>;
}

export interface ListType {
  id: number;
  name: string;
  type: number;
  desc: string;
  coverExpectRatio: number;
  total: number;
  cursor: string;
  contentList: Array<ContentListType>;
}

export interface ContentListType {
  id: number;
  name: string;
  type: number;
  address: string;
  cover: string;
  tag: string;
  duration: number;
  width: number;
  height: number;
  colSpan: number;
  aggregationList: Array<ContentListType>;
}

// 文章类型  1 是 url 地址，2 为 scheme 地址，3 为视频地址，4 表本地文章 (字符串或字符串 id)
export enum TutorialType {
  External = 1,
  Scheme = 2,
  Video = 3,
  Local = 4
}

// 分类模块类型  1 顶部推荐位 2 单行横滑 3 双行平铺 4 全部平铺
export enum ModuleType {
  Recommend = 1,
  Single = 2,
  Double = 3,
  All = 4
}

// 详情接口返回
export interface TutorialDetailRes {
  id: number;
  name: string;
  content: string;
  updatedAt: number;
  views: number;
}
