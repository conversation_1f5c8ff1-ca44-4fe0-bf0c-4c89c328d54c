import { CommonCreate } from '../common';
import { TrainingStatus } from '../trainingConfig';

/**
 * 姿势控制可选项
 */
export type Posture = {
  id: number;
  /**
   * 展示的图片
   */
  coverPic: string;
  /**
   * 创建任务时向接口传递的值
   */
  bonesPic: string;
};

export type PostureControl = {
  picList: Posture[];
};

/**
 * 预设的lora
 */
export type LoraItem = {
  id: number;
  name: string;
  images: string;
  weight: number;
  categoryId: string | number;
  defaultPrompt: string;
};

export type LoraCategory = {
  categoryId: number | string;
  categoryName: string;
  cursor: string;
  list: LoraItem[];
};

export type CustomLoraItem = {
  /**
   * 用来查询训练进度
   */
  id: number;
  /**
   * 封面图
   */
  coverPic: string;
  /**
   * 模型id
   * 创建任务制定风格模型id时 使用这个id
   */
  trainingModelId: number;
  /**
   * 训练状态
   */
  status: TrainingStatus;

  name: string;
};

export type IPCharacterCustomizationConfig = {
  postureCtl: PostureControl;
  loraList: LoraCategory[];
  userLoraList: CustomLoraItem[];
};

export type PostureControlParams = {
  id?: number;
  image: string;
};

export type CreateIPCharacterImageParams = {
  prompt: string;
  postureCtl?: PostureControlParams;
  batchSize: number;
  styleModelConfig: {
    styleModelId: string | number;
    styleModelCategories: string | number;
    styleModelName: string;
    styleModelImage: string;
    styleModelWeight: number;
  }[];
} & CommonCreate;
