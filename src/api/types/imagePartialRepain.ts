/** --------------------------------------------------- */

import { CommonCreate } from './common';
import { ResultImage, TaskStatus } from './imageExtension';

export type ImagePartialRepaintDoRequest = {
  /**
   * 正向提示词
   */
  prompt: string;

  /**
   * 负向提示词
   */
  negativePrompt: string;

  /**
   * 原图链接
   */
  initImage: string;

  /**
   * 涂抹后mask图
   */
  maskImage: string;

  /**
   * 张数
   */
  batchSize: number;
} & CommonCreate;

export type ImagePartialRepaintDoResponse = {
  /**
   * 任务id
   */
  id: string;
};

export type ImagePartialRepaintQueryRequest = {
  /**
   * 任务id列表，多个任务id通过英文逗号隔开
   * */
  ids: string;
};

export type ImagePartialRepaintQueryResponse = {
  /**
   * 任务id
   * */
  id: string;
  /**
   * 任务参数
   * */
  args: ImagePartialRepaintDoRequest;
  /**
   * 任务状态：1-初始，2-生成中，3-生成成功，4-生成失败
   * */
  status: TaskStatus;
  /**
   * 生成的图片列表
   * */
  resultImages: ResultImage[];
  /**
   * 任务失败信息
   * */
  failMsg?: string;
};

export type ImagePartialRepaintHistoryResponse = {
  list: Array<ImagePartialRepaintQueryResponse>;
  cursor: number;
};
