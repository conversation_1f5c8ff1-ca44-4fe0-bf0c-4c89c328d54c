import { TaskParams } from './task';
import { type DraftType } from '@/api/types';
import type { ResultImage, FuncBaseConfig, FunCoverConfig } from '.';

// 排序规则，默认值为 normal
export enum SortOptionsType {
  Normal = 'normal',
  Time = 'time',
  Hot = 'hot'
}

/** 秀秀画廊数据 任务类型 */
export enum BizIdType {
  /** 文生图 */
  TextToImage = 1004,

  /** 图生图 */
  ImageToImage = 1007
}
export interface GalleryQuery {
  /**
   * 个数，默认20
   */
  count?: number;

  /**
   * 翻页用的标识，将返回值里存在的非空cursor进行透传翻页
   */
  cursor?: string;
  // 画廊type筛选，首页默认穿空即可
  tabId?: string;
  sort?: SortOptionsType;
  /** 来源，1 灵感-作品 2 灵感-模型 3 首页 */
  from?: GalleryQueryFrom;
}

/** 画廊Feed流来源 */
export enum GalleryQueryFrom {
  /** 灵感-作品 */
  Creation = 1,
  /** 灵感-模型 */
  Model = 2,
  /** 首页 */
  Overview = 3
}

export interface GalleryResponse {
  /** 此次返回数量 */
  total: number;

  /** 翻页标识 */
  cursor: string;

  list: FeedListResponseProfile[];
}

/** feed流基础信息 */
interface FeedBaseResponse {
  /** 秀秀feed_id */
  feedId: string;

  /** 标题 */
  title: string;

  /** 描述 */
  text: string;
}

export interface GalleryFavoritesQuery {
  feedId: string;
}

/**
 * 创作同款接口返回
 */
export interface SameStyleGalleryResponse {
  effectId: number;

  params: TaskParams;
  /**
   * 是否简化版，是简化版传1
   */
  simplifyVersion: number;
}

export interface styleModel {
  id: string;
  modelId: string;
  name: string;
  picUrl: string;
  isUserModel: boolean;
  hasPublished: boolean;
}
/**
 * 作品详情接口返回
 */
export interface GalleryDetailResponse extends FeedListResponseProfile {
  /** 任务结果图片数据 */
  resultImages: (ResultImage & {
    /** 超分分辨率 */
    resolution: string;
    /** 超分前原图链接 */
    hdOriginUrl: string;
  })[];
  /** 生成参数信息 */
  params: TaskParams;
  /** 基础模型信息 */
  model: {
    id: number;
    name: string;
    images: string[];
    desc: string;
  };
  /** 创建时间戳 */
  createdAt: number;
  /** 描述 */
  desc: string;
  /** 运营资源位列表 */
  operateList?: Array<Operation>;
  /** 作品叠加的风格模型 */
  styleModelList: Array<styleModel>;
  /** 是否为优质内容 */
  isExcellent?: boolean;
}

export interface GalleryImage {
  /** 图片(效果图) 路径 */
  url: string;

  width: number;

  height: number;
  // 是否超分图
  isHd: number;
  // 超分前的原图
  originPic?: string;
  // 超分分辨率
  resolution?: string;
}

// 1 灵感作品 2 灵感模型 3 首页
export enum GalleryType {
  Work = '1',
  Model = '2',
  Home = '3'
}

/** 模型详情 */
export interface ModelDetailResponse extends FeedBaseResponse {
  /** 模型来源 */
  modelSource: string;

  /** 收藏数 */
  favoritesCount: number;

  /** 是否收藏 */
  favorites: boolean;

  /** 套用数 */
  appCount: number;

  /** 浏览数 */
  viewCount: number;

  /** 模型是否有发布的同款作品 */
  isTemplate: 0 | 1;

  /** 创建时间 */
  createTime: number;

  medias: {
    /** 模型封面 */
    url: string;
  }[];

  user: User;

  /** 基础模型 */
  baseModel: {
    /** 基础模型id */
    baseModelId: number;

    /** 模型描述 */
    modelDesc: string;

    /** 模型名称 */
    modelName: string;

    /** 模型封面 */
    modelCover: string;
  };

  /** 训练模型 */
  model: {
    /** 模型状态 0 待审核 1 已发布 */
    modelStatus: 0 | 1;

    /** 模型配置信息 JSON.stringify(ModelConfig) */
    modelConfig: string;

    /** 模型id 多个,隔开 只需要第一个 */
    modelIds: string;
  };
}
export interface GalleryModelListResponse {
  nextCursor: string;
  items: GalleryModelListResponseProfile[];
}

interface User {
  /** 用户 ID */
  uid: number;

  /** 用户昵称 */
  screenName: string;

  /** 用户用户头像 */
  avatarUrl: string;
}

export interface GalleryModelListResponseProfile {
  feedId: string;

  /** 封面图地址 */
  thumb: string;

  /** 图片数量 */
  picCount: number;

  /** 图片宽 */
  width: number;

  /** 图片高 */
  height: number;

  /** 标题 */
  caption: string;

  /** 用户信息 */
  user: User;

  /** 收藏数 */
  favoritesCount: number;

  /** 是否收藏 */
  isFavorited: number;

  /** 是否可以创作同款 */
  canUseSame: number;

  /** 效果id */
  effectId: number;

  /** 模型id */
  modelIds: Array<number>;

  /** 模型状态 0 待审核 1 已发布 */
  modelStatus: 0 | 1;

  model: {
    baseModelId: string;
    modelDesc: string;
    modelName: string;
    modelCover: string;
  };

  bizId: BizIdType;
}

/** 模型配置信息 */
export interface ModelConfig {
  /** 训练次数 */
  trainingFrequency: number;

  /** 迭代周期 */
  iterationPeriod: number;
}

/** 模型下的同款作品列表返回值 */
export interface GalleryByModelIdResponse {
  nextCursor: string;
  items: GalleryByModelIdItem[];
}

interface GalleryByModelIdItem
  extends Omit<
    FeedListResponseProfile,
    'id' | 'user' | 'isFavor' | 'favorCount' | 'picUrl' | 'title'
  > {
  feedId: string;
  user: User;

  /** 是否收藏 0 否 1 是 */
  isFavorited: 0 | 1;

  /** 作品被收藏数量 */
  favoritesCount: number;

  /** 缩略图 */
  thumb: string;

  /** 标题 */
  caption: string;

  /** 秀秀画廊数据 任务类型 */
  bizId: BizIdType;
}

// 灵感分类tab
export interface GalleryTabType {
  tabId: string;
  tabName: string;
  backGroundPic: string;
}

export enum FeedStatus {
  /** 初始 */
  INITIAL = 1,
  /** 处理中 */
  DOING = 2,
  /** 失败 */
  FAILED = 3,
  /** 成功 */
  SUCCEED = 4,
  /** 已发布 */
  PUBLISHED = 5,
  /** 发布审核中 */
  AUDITING = 10
}

// feedItem 接口返回的
export interface FeedListResponseProfile {
  // 内容 id
  id: string;
  //秀秀 feed_id
  feedId: string;
  // 状态
  status: FeedStatus;
  //0 - 无超分任务，1 - 存在进行中超分任务，2 - 超分任务都已经完成
  hdStatus: 0 | 1 | 2;

  /** 图片地址 */
  picUrl: string;

  /** 图片数量 */
  picCount: number;

  /** 图片宽 */
  width: number;

  /** 图片高 */
  height: number;

  /** 标题 */
  title: string;

  /** 描述 */
  desc: string;

  /** 用户信息 */
  user: {
    /** 用户 ID */
    id: number;

    /** 用户昵称 */
    screenName: string;

    /** 用户用户头像 */
    avatar: string;
  };

  /** 收藏数 */
  favorCount: number;

  /** 当前用户是否收藏 1-已被自己收藏 2-未被自己收藏 */
  isFavor: 1 | 2;

  // 创作同款数量
  templateUseCount: number;

  /** 是否可以创作同款 */
  canUseSame: number;

  // 创建时间戳
  createdAt: number;

  /** 效果id */
  effectId: number;

  /** 任务类型（文生图/图生图） */
  taskCategory: DraftType;

  /** 风格模型id */
  styleModelIds: string;

  // 推荐下发的上报值 scm
  scm: string;

  /** 运营资源位列表 */
  operateList?: Array<Operation>;
  // 应用模型id
  modelId?: string;

  /** 图片占用列数 */
  colSpan: number;
}

type Operation = FuncBaseConfig &
  FunCoverConfig & {
    /** 副标题 */
    title: string;
  };

// feedList 接口返回的
export interface FeedListResponse {
  cursor: string;
  list: FeedListResponseProfile[];
}

// 提交用户职业信息返回
export interface PostJobInfoResponse {
  toast: string;
  result: boolean;
  num: number;
}

// 邀请信息详情
export interface InviteInfoResponse {
  url: string;
  pic_arry: string[];
}

// 邀请信息详情
export interface codeExplainResponse {
  isEffect: string;
  type: number;
  url: string;
  text: string;
  picArry: string[];
  actUser: object;
}
