import { CommonCreate } from './common';
import { EditorConfigModelResponse } from './editorConfig';

export enum TrainingStatus {
  Training = 2,
  Completed = 3,
  Failed = 4
}

export interface FileListAjaxView {
  /**
   * 文件id
   */
  id: number;

  /**
   * 文件地址
   */
  url: string;
  width: number;
  height: number;
}

export interface AdvancedAjaxView {
  /**
   * 训练次数
   */
  trainingFrequency: number;
  /**
   * 迭代周期
   */
  iterationPeriod: number;
}

export interface TrainingConfigQuery {
  /**
   * 训练id 为空则为新增
   */
  id?: string;
}

/**
 * 返回结果
 */

export interface TrainingConfigResponse {
  /**
   * 训练id
   */
  id: number;

  /**
   * 训练模型类型 6: lora 风格模型
   */
  type: number;

  /**
   * 模型名称
   */
  name: string;

  /**
   * 基础模型id
   */
  baseModelId: number;

  /**
   * 基础模型
   */
  baseModel: EditorConfigModelResponse[];
  /**
   * 高级设置
   */
  advanced: AdvancedAjaxView;
  /**
   * 已上传文件列表
   */
  fileList: FileListAjaxView[];
  /**
   * 模型外层 tag
   */
  baseTagUrl: string;
}

export interface TrainingFileManageBody {
  /**
   * 训练id
   */
  id: number;
  /**
   * 添加的图片地址
   */
  urls?: string[];
  /**
   * 删除的文件id
   */
  fileId: number;
  /**
   * 类型1添加图片2删除图片
   */
  type: number;
}

/**
 * 返回结果
 */
export interface TrainingFileManageResponse {
  /**
   * true 成功 false 失败
   */
  result: boolean;
  /**
   * 失败提示
   */
  message: string;
  /**
   * 本次训练已上传成功的文件列表
   */
  fileList: FileListAjaxView[];
}

export interface TrainingDoBody {
  /**
   * 训练id
   */
  id: number;
  /**
   * 训练模型类型 6: lora 风格模型
   */
  type: number;
  /**
   * 模型名称
   */
  name: string;
  /**
   * 基础模型id
   */
  baseModelId: number;
  /**
   * 高级配置
   */
  advanced: AdvancedAjaxView;
}

/**
 * 返回结果
 */
export interface TrainingDoResponse {
  /**
   * 任务id
   */
  id: string;
  /**
   * 预估剩余时间，例如：39分钟   、30~120分钟
   */
  estimatedTime: string;
}

export interface TrainingSuccessResult {
  /**
   * 训练id
   */
  id: number;
  /**
   * 训练模型类型 6: lora 风格模型
   */
  type: number;
  /**
   * 训练模型id
   */
  trainingModelId?: number;
  /**
   * 模型名称
   */
  name: string;
  /**
   * 状态3训练完成 状态 4 训练失败
   */
  status: number;
  /**
   * 基础模型id
   */
  baseModelId: number;
  /**
   * 基础模型id
   */
  advanced: AdvancedAjaxView;
}

export interface TrainingFailedResult {
  /**
   * 训练id
   */
  id: number;
  /**
   * 训练模型类型 6: lora 风格模型
   */
  type: number;

  /**
   * 模型名称
   */
  name: string;
  // TODO convert to TrainingStatus enum
  /**
   * 状态3训练完成 状态 4 训练失败
   */
  status: number;
  /**
   * 基础模型id
   */
  baseModelId: number;

  advanced: AdvancedAjaxView;
  /**
   * 失败原因
   */
  failMessage: string;
}

export interface TrainingResultResponse {
  /**
   * 训练成功列表
   */
  successList: TrainingSuccessResult[];
  /**
   * 训练失败列表
   */
  failList: TrainingFailedResult[];
}

export interface TrainingProgressResponse {
  /**
   * 训练id
   **/
  id: number;
  /**
   * 状态 2训练中 3训练完成 4 训练失败
   * */
  status: TrainingStatus;
  /**
   * 训练出来的模型id
   * */
  trainingModelId?: number;
  /**
   * 失败原因
   * */
  failMessage?: string;
  /**
   * 任务进度，例如：0.1 、0.85、1
   * */
  progress: number;
  /**
   * 预估剩余时间，例如：39分钟   、30~120分钟
   * */
  estimatedTime?: string;
}

export interface TrainingDeletedResponse {
  /**
   * 结果true 成功，false 失败
   * */
  result: boolean;
}

export interface TrainingCopiedResponse {
  /**
   * 新的训练id
   * */
  id: number;
}

export interface TrainingParamsSaveResponse {
  /**
   * 设置结果true 成功，false 失败
   * */
  result: boolean;
}

export interface TrainingPublishParams {
  /**
   * 训练id
   */
  id: number;
  /**
   * 模型名称
   */
  name: string;
  /**
   * 模型介绍
   */
  desc: string;
  /**
   * 标签，多个标签array上传
   */
  tags?: string[];
  /**
   * 封面
   */
  coverPic?: string;
}

export interface PublishConfigParams {
  /**
   * 训练id
   */
  id: number;
}

export interface PublishConfigResponse {
  /**
   * 发布标签
   */
  publishTags: {
    /**
     * 标签id
     */
    tagId: string;
    /**
     * 标签名称
     */
    tagName: string;
    // 编辑时是否选中
    checked: boolean;
  }[];
}

export type CreateIPCharacterLoraParams = {
  image: string;
} & CommonCreate;

export type createIpCharacterLoraResponse = {
  id: number | string;
  estimatedTime: string;
};

/**
 * 查询ip形象lora训练进度
 */
export type IPCharacterLoraTrainingProgressParams = {
  /**
   * 逗号分隔的id列表
   */
  ids: string;
};

export type IPCharacterLoraTrainingProgressResponse = {
  list: TrainingProgressResponse[];
};
