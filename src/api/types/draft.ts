import type { TaskInterface } from './task';
import type { CommonPostResponse } from './common';
import { SortFilterType } from '@/types';

/**
 * 请求草稿类型
 * img2img图生图
 * txt2img文生图
 */
export enum DraftType {
  /** 图生图 */
  IMAGE_TO_IMAGE = 'img2img',
  /** 文生图 */
  TEXT_TO_IMAGE = 'txt2img',
  /** 模型训练 */
  MODEL = 'model',
  /** 图像扩展 */
  EXTEND = 'extend',
  /** AI改图 */
  INPAINT = ' inpaint',
  /** 资源位 */
  OPERATE = 'operate',
  /** ai模特 */
  AI_MODEL_IMAGE = 'aimodelimg',
  /** ai超清 */
  IMAGE_UPSCALE = 'magicsr',
  /** ai视频 */
  AI_VIDEO = 'ai_video',
  /** Ai消除 */
  AI_ERASER = 'ai_eraser',
  /** ip 形象定制概念图设计 */
  IP_CONCEPT = 'ipconceptimage',
  /** ip 形象定制 */
  IP_CUSTOMIZATION = 'ipfigureimage',
  /** AI免抠素材 素材生成*/
  MATERIAL_GENERATE = 'aimaterialgen',
  /** AI免抠素材 素材转换*/
  MATERIAL_TRANSFORM = 'aimaterialconv',
  /** ai 海报 */
  AI_POSTER = 'ai_poster'
}
export interface DraftQuery {
  /**
   * 个数，默认20
   */
  count?: number;
  /**
   * 翻页用的标识，将返回值里存在的非空cursor进行透传翻页
   */
  cursor?: string;
  /** 状态 0全部 1 已发布 2已收藏 */
  status?: 0 | 1 | 2;
  /** 排序 */
  orderAsc?: SortFilterType;

  /** 搜索关键字 */
  keyword?: string;
}

/**
 * 获取任务返回结果
 */
export type DraftResponse = {
  /** 总量 */
  total: number;
  /** 翻页标识 */
  cursor?: string;
  /** 草稿结果列表 */
  list: TaskInterface[];
};

/**
 * 是否允许使用（同款、将作品同步至画廊、允许使用画面控制器图片）
 */
export enum CanUse {
  ALLOW = 1,
  FORBIDDEN = 2
}

/**
 * 草稿发布请求参数
 */
export interface DraftPublishBody {
  id: string;
  /**
   * 创意名
   */
  name: string;
  /**
   * 描述
   */
  desc: string;
  /**
   * 标签，多个标签逗号风格
   */
  tags: string[];
  /**
   * 选择发布的图片地址
   */
  images: string[];

  /** 同步到画廊 */
  publishGallery: CanUse;

  /**
   * 允许使用同款
   */
  canUseSame?: CanUse;

  /**
   * 允许使用画面控制器图片
   */
  canUseControlnetImage?: CanUse;
}

/**
 * 草稿发布返回
 */
export interface PublishResponse extends CommonPostResponse {
  /** 奖励提示信息 */
  awardTipTitle?: string;
  /** 是否显示奖励提示 */
  showAwardTip?: boolean;
}

/**
 * 草稿移除请求参数
 */
export interface DraftRemoveBody {
  id: string;
  imageUrl?: string;
}

/**
 * 草稿移除请求参数
 */
export interface DraftCollectBody {
  id: string;
  /** 动作类型 1收藏 2取消收藏 */
  actionType: 1 | 2;
}
