import { AppModuleParam } from '@/types';

/**
 * Post 请求公共返回类型
 */
export interface CommonPostResponse {
  result: boolean;
  message?: string;
}

/**
 * 请求错误信息
 */
export interface CommonRequestError {
  code?: number;
  message?: string;

  /** 秀秀社区的错误额外信息（一般是中文的） */
  msg?: string;

  /** 秀秀社区的错误额外信息（一般是英文的） */
  error?: string;
}

export interface CommonCreate {
  functionName?: string;
  mediaType?: string;
  resMediaType?: string;
  modelName?: string;
}

export enum MtccFuncCode {
  FuncCodeText2Image = 'whee.textgepic.tst.v2', // 文生图
  FuncCodeImage2Image = 'whee.pic.tst.v2', // 图生图
  FuncCodeModelTraining = 'whee.model.tst.v2', // 模型训练
  FuncCodeImageExtend = 'whee.textenlarge.tst.v2', // 图像扩展
  FuncCodeImageInpaint = 'whee.jbxg.tst.v2', // 局部修改
  FuncCodeAIModelImage = 'whee.aimodel.tst.v2', // AI 模特图
  // FuncCodeAiVideo = 'whee.aivideo.tst.v2', // AI 视频
  FuncCodeAiVideo = 'whee.aivideomv5.tst', // AI 视频
  FuncCodeImageEraser = 'whee.aieliminate.tst.v2', // AI 消除
  FuncCodeImageMagicsrHDWithRight = 'whee.chaofen.tst', // AI 超清 -快捷超清
  FuncCodeImageMagicsrHDWithRightV2 = 'whee.aihd.tst.v2', // AI 超清 - 高清档位
  FuncCodeImageMagicsrUHDWithRight = 'whee.aiultrahd.tst.v2', // AI 超清 - 超清档位
  FuncCodeAiSynthesisRight = 'whee.aisynthesis.tst.v2', // AI 合成
  FuncCodeImageModifyInpaintRight = 'whee.aiedit.tst.v2', // AI 改图 - 局部修改 (对应 whee.jbxg.tst.v2)
  FuncCodeImageModifyExtendRight = 'whee.aienlarge.tst.v2', // AI 改图 - 图像扩展 (对应 whee.textenlarge.tst.v2)
  FuncCodeImageModifyEraserRight = 'whee.airemove.tst.v2', // AI 改图 - AI 消除 (对应 whee.aieliminate.tst.v2)
  FuncCodeImageCutout = 'whee.imagecutout', // AI 改图 - 抠图（抠图不用钱，所以没有FuncCode，自定义标识 whee.imagecutout）
  FuncCodeConceptMapDesign = 'whee.conceptmapdesign.tst', //概念图设计
  FuncCodeIpImageTraining = 'whee.ipimagetraining.tst', //IP形象训练
  FuncCodeIpImageDrawing = 'whee.ipimagedrawing.tst', // IP形象生图
  FuncCodePlaneTo3d = 'whee.planeto3d.tst', // 转3d
  FuncCodeMaterial = 'whee.aifreecutoutmaterial.tst', // 免抠素材
  FuncCodePoster = 'whee.aiposter.tst', // AI 海报
  FuncCodeSelect = 'whee.autoselect', // 智能选择
  FuncCodeInteract = 'whee.interact'
}
export const AppModuleToMtccFuncCode: Partial<
  Record<AppModuleParam, MtccFuncCode>
> = {
  [AppModuleParam.TextToImage]: MtccFuncCode.FuncCodeText2Image,
  [AppModuleParam.ImageToImage]: MtccFuncCode.FuncCodeImage2Image,
  [AppModuleParam.StyleModelTraining]: MtccFuncCode.FuncCodeModelTraining,
  [AppModuleParam.ImageExtension]: MtccFuncCode.FuncCodeImageExtend,
  [AppModuleParam.ImagePartialRepaint]: MtccFuncCode.FuncCodeImageInpaint,
  [AppModuleParam.ImageAIModel]: MtccFuncCode.FuncCodeAIModelImage,
  [AppModuleParam.ImageToVideo]: MtccFuncCode.FuncCodeAiVideo,
  [AppModuleParam.ImageEraser]: MtccFuncCode.FuncCodeImageEraser,
  [AppModuleParam.ImageUpscale]: MtccFuncCode.FuncCodeImageMagicsrHDWithRight,
  // [AppModuleParam.ImageUpscale2]: MtccFuncCode.FuncCodeImageMagicsrUHDWithRight,
  [AppModuleParam.ImageEditor]: MtccFuncCode.FuncCodeImageModifyInpaintRight,
  [AppModuleParam.IPCharacterConcept]: MtccFuncCode.FuncCodeConceptMapDesign,
  [AppModuleParam.IPCharacterCustomization]:
    MtccFuncCode.FuncCodeIpImageDrawing,
  [AppModuleParam.Material]: MtccFuncCode.FuncCodeMaterial,
  [AppModuleParam.Poster]: MtccFuncCode.FuncCodePoster
};
/**
 * 获取对应的 MtccFuncCode
 * @param moduleParam AppModuleParam
 * @returns MtccFuncCode | undefined
 */
export const getMtccFuncCode = (
  moduleParam: string
): MtccFuncCode | undefined => {
  return AppModuleToMtccFuncCode[moduleParam as AppModuleParam];
};

export enum MediaType {
  Text = 'text',
  Photo = 'photo',
  Video = 'video',
  Model = 'model'
}
