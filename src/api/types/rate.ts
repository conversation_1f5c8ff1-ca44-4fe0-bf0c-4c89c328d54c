/** 评分分数 */
export enum Score {
  /** 非常不满意 */
  VeryDiscontent = 1,

  /** 不满意 */
  Discontent = 2,

  /** 一般 */
  Common = 3,

  /** 好 */
  Good = 4,

  /** 非常好 */
  VeryGood = 5
}

/** 评分选项标签 */
export interface RateTagsItem {
  id: number;
  name: string;
  active?: boolean;
}

/** 评分分类数据 */
export interface RateListItem {
  title: string;
  score: Score;
  tags: RateTagsItem[];
}

export type RateData = Record<number, RateListItem>;

// 0文生图/图生图  1AI扩图
export enum RateType {
  TextOrImg = '0',
  Extend = '1',
  Inpaint = '3',
  Eraser = '4'
}
export interface RateSubmitParams {
  cateId: string;
  content: string;
  pic: string;
  msgId: string;
  type: RateType;
  score: string;
}
