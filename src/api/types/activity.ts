/**
 * 签到奖励类型
 */
export enum CheckInRewardType {
  /**
   * 无奖励
   */
  NONE = 0,
  /**
   * 奖励美豆
   */
  MT_BEAN = 1
}

/**
 * 签到任务状态
 */
export enum CheckInStatus {
  /**
   * 未签到
   */
  NOT_CHECKED = 0,
  /**
   * 已签到
   */
  CHECKED = 1
}

export type CheckInActivityResponse = {
  /** 签到任务id */
  id: number;
  /** 签到任务标题 */
  title: string;
  /** 签到任务奖励类型 */
  rewardType: CheckInRewardType;
  /** 奖励数量 */
  rewardNum: number;
  /** 签到后描述 */
  checkInDesc: string;
  /** 签到状态 */
  status: CheckInStatus;
};

export type MemberShip = {
  isVip: boolean;
};

export type UserInfo = {
  id: number;
  /** 昵称 */
  screenName: string;
  /** 头像 */
  avatar: string;
  /** 美豆余额 */
  availableAmount: number;
  /** 会员信息 */
  membership: MemberShip;
};

export enum ExploreActivityStatus {
  /** 任务未完成 */
  NOT_FINISH = 0,
  /** 完成但未领取 */
  UNCLAIMED = 1,
  /** 已经领取 */
  CLAIMED = 2
}

export enum ExploreActivityURLType {
  /** 站外打开新tab */
  NEW_TAB = 0,
  /** 站内跳转 */
  INTERNAL = 1,
  /** 点击事件 触发弹窗 */
  CLICK_MODAL = 2
}

export enum ExploreActivityType {
  /** 不可追踪 */
  UNTRACEABLE = 1,
  /** 可追踪 */
  TRACKABLE = 2
}

/**
 * 奖励类型
 */
export enum ExploreActivityRewardType {
  /** 美豆 */
  MT_BEAN = 1
}

export type ExploreActivityResponse = {
  id: number;
  /** 任务标题 */
  title: string;
  /** 奖励类型 */
  rewardType: ExploreActivityRewardType;
  /** 奖励数量 */
  rewardNum: number;
  /** 描述 */
  desc: string;
  /** 任务图片 */
  pic: string;
  /** 任务状态 */
  status: ExploreActivityStatus;
  /** 跳转链接 */
  url: string;
  /** 跳转方式 */
  urlType: ExploreActivityURLType;
  /** 任务类型 */
  type: ExploreActivityType;
};

export type MissionInfoResponse = {
  /** 用户信息 */
  user: UserInfo;
  /** 签到任务列表 */
  checkInActivityList: Array<CheckInActivityResponse>;
  /** 今天是否签到 */
  isCheckInToday: boolean;
  /** 总签到次数 */
  totalCheckIn: number;
  /** 任务 */
  exploreActivityList: Array<ExploreActivityResponse>;
};

export type CheckInResponse = {
  result: boolean;
};

export type ClaimMissionRewardRequestParams = {
  exploreActivityId: number;
};

export type ClaimMissionRewardResponse = {
  result: boolean;
};
