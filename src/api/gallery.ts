import { createMockInstance, createInstance } from './utils';
import type { GalleryDetailResponse, SortOptionsType } from './types';
import { GraphStatus } from '@/types';

import moment from 'moment';
import type {
  GalleryQuery,
  GalleryResponse,
  GalleryTabType,
  GalleryType,
  FeedListResponseProfile,
  SameStyleGalleryResponse,
  FeedListResponse
} from './types';
import { transformFeedList } from './personal';
import type { Gallery, GalleryProfile, GalleryDetail } from '@/types';
import { toSnakeCase, formatMoment } from '@meitu/util';
import { DraftType } from './types';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/gallery');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/gallery' });

/**
 * 转换后端数据格式
 * @param params 接口原始数据
 * @returns
 */
function transformGallery(params: FeedListResponseProfile): GalleryProfile {
  const {
    width,
    height,
    user,
    isFavor,
    operateList,
    canUseSame,
    ...restProps
  } = params;

  return {
    size: [width, height],
    isFavor: isFavor === 1,
    canUseSame: canUseSame === 1,
    user: {
      ...user,
      userName: user.screenName
    },
    operations: operateList?.map((operate) => ({
      id: operate.id.toString(),
      title: operate.name,
      description: operate.title,
      cover: operate.pic,
      hoverCover: operate.checkPic,
      link: operate.url
    })),
    ...restProps
  };
}

/**
 * 获取画廊列表
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/125294 接口文档}
 */
export function fetchGallery(params: GalleryQuery) {
  return instance
    .get<void, GalleryResponse>('/feed.json', toSnakeCase({ params }))
    .then<Gallery>((response) => {
      const { total, cursor, list = [] } = response;
      const result = list.map(transformGallery);

      return { total, cursor, list: result };
    });
}

/**
 * 获取创作同款的数据
 * @param id 效果id
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/125303 接口文档}
 */
export function fetchSameStyleGallery(id: string) {
  return instance.get<void, SameStyleGalleryResponse>('/apply_effect.json', {
    params: { id }
  });
}

/**
 * 画廊 - tab 列表
 * @param type 不填，默认全部，1 作品 2 模型 3 首页 tab
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/137895 接口文档}
 */
export function fetchGalleryTypeList(params: { type: GalleryType }) {
  return instance.get<void, { tabs: Array<GalleryTabType> }>(
    '/feed_tabs.json',
    {
      params: toSnakeCase(params)
    }
  );
}

/* 收藏/取消收藏画廊作品
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/137886 接口文档}
 */
export function switchGalleryFavorites(id: string, collect: boolean) {
  return instance.post<void, void>(
    '/feed_favorites.json',
    toSnakeCase({ id, action: collect ? 1 : 2 })
  );
}

/**
 * 画廊 - 搜索
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/138095 接口文档}
 */
export function fetchSearchResultList(params: {
  keyword: string;
  sort: SortOptionsType;
  cursor: string;
}) {
  return instance
    .get<void, FeedListResponse>('/search.json', {
      params: toSnakeCase(params)
    })
    .then<Gallery>((response) => {
      const { cursor, list = [] } = response;
      const result = list.map(transformFeedList);

      return { total: result.length, cursor, list: result };
    });
}

/* 获取作品详情
 * @param params.feedId 作品id
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/137874 接口文档}
 */
export function fetchGalleryDetail(id: string, taskCategory: DraftType) {
  return instance
    .get<void, GalleryDetailResponse>('/feed_detail.json', {
      params: toSnakeCase({
        id,
        taskCategory
      })
    })
    .then<GalleryDetail>(
      ({
        width,
        height,
        resultImages,
        feedId,
        favorCount,
        isFavor,
        canUseSame,
        user,
        desc,
        title,
        effectId,
        createdAt,
        taskCategory,
        // viewCount,
        templateUseCount,
        model,
        params,
        picUrl,
        modelId,
        styleModelList,
        status,
        id,
        isExcellent
      }) => {
        return {
          id,
          feedId,
          isExcellent,
          size: [width, height],
          isFavor: isFavor === 1,
          canUseSame: canUseSame === 1,
          favorCount,
          user: {
            id: user.id,
            avatar: user.avatar,
            userName: user.screenName
          },
          originalPic: params?.initImages?.[0] ?? '',
          model: {
            title: model?.name,
            src: model?.images[0] ?? '',
            desc: model?.desc,
            modelId: model?.id
          },
          prompt: params?.prompt,
          text: desc,
          title,
          effectId,
          publishTime: formatMoment(
            moment(createdAt * 1000),
            'YYYY-MM-DD HH:mm'
          ),
          images: resultImages?.map((image) => ({
            status: GraphStatus.SUCCESS,
            src: image.url,
            isUpscaler: !!image.upscalerInfo?.id,
            hdOriginUrl: image.hdOriginUrl,
            resolution: `${image.upscalerInfo?.width}px * ${image.upscalerInfo?.height}px`
          })),
          zoomMode: params?.resizeMode,
          taskCategory,
          viewCount: 0,
          applyCount: templateUseCount,
          iterationPeriod: params?.advanced?.iterationPeriod,
          trainingFrequency: params?.advanced?.trainingFrequency,
          styleModelIds:
            styleModelList?.map(({ modelId }) => modelId).join(',') ?? '',
          picUrl,
          modelId,
          styleModelList,
          status
        };
      }
    );
}

/* 模型下的同款作品列表
 * @param params.feedId 模型的 feed_id
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/137899 接口文档}
 */
export function fetchGalleryByModelId(feedId: string, params: GalleryQuery) {
  return instance
    .get<void, FeedListResponse>('template_feeds.json', {
      params: toSnakeCase({
        ...params,
        feedId
      })
    })
    .then<Gallery>((response) => {
      const { cursor, list = [] } = response;
      const result = list.map(transformFeedList);

      return { total: result.length, cursor, list: result };
    });
}
