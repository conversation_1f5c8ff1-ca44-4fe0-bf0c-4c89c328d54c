import type { ComplaintQuery } from './types';

import { createInstance, createMockInstance } from './utils';
import { toSnakeCase } from '@meitu/util';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/' });

/**
 * 用户举报投诉作品
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/132925 |接口文档}
 */
export function complaintWork(data: ComplaintQuery) {
  return instance.post<void>('/complaint/submit.json', toSnakeCase(data));
}
