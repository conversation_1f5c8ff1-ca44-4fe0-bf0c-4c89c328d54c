import { createMockInstance, createInstance, publicSdkInstance } from './utils';
import {
  EditorConfigMattResponse,
  ImageMonitorBody,
  ImageUploadSignBody,
  ImageUploadSignResponse
} from './types';
import { CommonPostResponse } from './types';

import { isWebSDK } from '@/utils';

// DEBUG: 调试用的模拟接口
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockInstance = createMockInstance('/image');

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/image' });

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const sdkInstance = publicSdkInstance({ baseURL: '/sdk/image' });

/**
 * 图片合规监测
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120384 接口文档}
 */
export function imageMonitor(data: ImageMonitorBody) {
  let _instance = instance;
  if (isWebSDK()) {
    _instance = sdkInstance;
  }
  return _instance.post<void, CommonPostResponse>('/monitor.json', data);
}

/**
 * 获取图片上传签名
 * @description {@link https://api-mock.meitu-int.com/project/2243/interface/api/120393 接口文档}
 */
export function fetchImageUploadSign(params: ImageUploadSignBody) {
  return instance
    .get<void, EditorConfigMattResponse>('/get_sign.json', {
      params
    })
    .then<ImageUploadSignResponse>((response) => {
      const { app, sig, sigTime, sigVersion, suffix, type, count } = response;

      return {
        app,
        sig,
        sigTime,
        sigVersion,
        suffix,
        type,
        count
      };
    });
}
