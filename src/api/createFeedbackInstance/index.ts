import { createPCBAxiosInstance } from '@meitu/util';
import type { CreateAxiosDefaults } from 'axios';
import { shimRequestGeneralParams } from '../utils';
import { getAccountAccessToken } from '@/services';

export function createFeedbackInstance(config?: CreateAxiosDefaults) {
  return createPCBAxiosInstance(
    {
      ...config,
      baseURL: `${process.env.REACT_APP_FEEDBACK_API}${config?.baseURL}`
    },
    {
      beforeRequest(config) {
        config.headers['Access-Token'] = getAccountAccessToken();
        return shimRequestGeneralParams(config);
      }
    }
  );
}
