import { createInstance } from './utils';
import { InviteInfoResponse, codeExplainResponse } from './types';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const instance = createInstance({ baseURL: '/' });
/**
 * 邀请信息详情
 */
export function inviteInfo() {
  return instance.get<void, InviteInfoResponse>('invite/info.json');
}

/**
 * 口令解析
 */
// https://api-mock.meitu-int.com/project/2485/interface/api/153798
export function codeExplain(params: any) {
  return instance.get<void, codeExplainResponse>('code/explain.json', {
    params
  });
}
