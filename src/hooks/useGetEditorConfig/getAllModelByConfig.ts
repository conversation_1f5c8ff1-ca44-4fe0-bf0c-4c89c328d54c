import {
  BaseModelDefaultParams,
  EditorConfigModelResponse
} from '@/api/types/editorConfig';
import { toAtlasImageMogr2URL } from '@meitu/util';
import { ModelCardProps } from '@/components';
import { BaseModelCardProps } from '@/components/Model/ModelCard/BaseModelCard';

export interface BaseModelInfo
  extends Pick<
    BaseModelCardProps,
    'desc' | 'src' | 'tag' | 'title' | 'cornerLabelUrl'
  > {
  id: number;
  baseModelDefaultParams: BaseModelDefaultParams;
  mvType: number;
  tips?: string | undefined;
  modelFlag?: number;
}

export const getAllModelByConfig = (
  modelConfig: EditorConfigModelResponse[]
): Required<BaseModelInfo>[] => {
  return modelConfig.reduce<ModelCardProps<number>['list']>((acc, cur) => {
    (cur.list ?? []).reduce<ModelCardProps<number>['list']>(
      (listAcc, listCur) => {
        if (listAcc.some((l) => l.id === listCur.id)) return listAcc;
        const image = Array.isArray(listCur.images)
          ? listCur.images[0]
          : listCur.images;

        listAcc.push({
          id: listCur.id,

          src:
            image &&
            toAtlasImageMogr2URL(image, {
              thumbnail: { type: 'size', width: 116, height: 116 }
            }),
          title: listCur.name,
          desc: listCur.desc,
          tag: listCur.typeName,
          tips: listCur.tips || '',
          modelFlag: listCur.modelFlag,
          cornerLabelUrl: listCur.tag?.url ?? '',
          baseModelDefaultParams: listCur.defaultParams,
          mvType: listCur.mvType
        });
        return listAcc;
      },
      acc
    );
    return acc;
  }, []);
};
