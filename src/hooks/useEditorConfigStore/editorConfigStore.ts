import { fetchEditorConfig, fetchImageToImageEditorConfig } from '@/api';
import { EditorConfigResponse } from '@/api/types';
import { BaseModelType } from '@/constants/model';
import { makeAutoObservable, observable, runInAction } from 'mobx';
const fetchConfigFuncMap = {
  'text-to-image': fetchEditorConfig,
  'image-to-image': fetchImageToImageEditorConfig
};

export class EditorConfigStore {
  mvModel: EditorConfigResponse | null = null;
  fluxModel: EditorConfigResponse | null = null;
  externalModel: EditorConfigResponse | null = null;

  constructor() {
    makeAutoObservable(this, {
      mvModel: observable.ref,
      fluxModel: observable.ref,
      externalModel: observable.ref
    });
  }

  async refreshConfig(type: keyof typeof fetchConfigFuncMap) {
    const fetchFunc = fetchConfigFuncMap[type];
    try {
      const mvConfig = await fetchFunc({ baseModelType: BaseModelType.MV });
      const fluxConfig = await fetchFunc({ baseModelType: BaseModelType.Flux });
      const externalConfig = await fetchFunc({
        baseModelType: BaseModelType.External
      });
      runInAction(() => {
        this.mvModel = mvConfig;
        this.fluxModel = fluxConfig;
        this.externalModel = externalConfig;
      });
    } catch (e) {}
  }

  getControlNets(baseModelType: BaseModelType) {
    const pickControlNets = (config: EditorConfigResponse | null) => {
      if (!config) {
        return null;
      }

      return config.moduleList;
    };

    switch (baseModelType) {
      case BaseModelType.Flux:
        return pickControlNets(this.fluxModel);
      case BaseModelType.MV:
        return pickControlNets(this.mvModel);
      case BaseModelType.External:
        return pickControlNets(this.externalModel);
    }
  }

  getFlatMvControlNets() {
    return this.getControlNets(BaseModelType.MV)?.flatMap((category) => {
      return category.list;
    });
  }

  getFlatFluxControlNets() {
    return this.getControlNets(BaseModelType.Flux)?.flatMap((category) => {
      return category.list;
    });
  }

  getFlatAllControlNets() {
    return this.getFlatMvControlNets()?.concat(
      this.getFlatFluxControlNets() ?? []
    );
  }
}
