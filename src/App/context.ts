import type { BusinessCooperationModalHandle } from './BusinessCooperationModal';
import type { BetaInvitationModalHandle } from './BetaInvitationModal';
import type { FeedbackModalHandle } from './FeedbackModal';
import type { OnlineConsultationModalHandle } from './OnlineConsultationModal';
import type { ReportModalHandle } from './ReportModal';
import type { ContactModalHandle } from './ContactModal';

import { createContext } from 'react';
import { GoWheeModalRef } from './GoWheeModal';
import { RefreshModalRef } from './RefreshModal';

import { MGMShareModalRef } from './MGMShareModal';
import { MGMAcceptModalRef } from './MGMAcceptModal';
import { GuideModalRef } from './GuideModal';
import { NoticePopupRef } from './NoticePopup';
import { SdkLoadingRef } from './SdkLoading';

export interface AppContextValues {
  /** 打开内测邀请对话框 */
  openBetaInvitationModal: BetaInvitationModalHandle['open'];

  /** 打开商务合作对话框 */
  openBusinessCooperationModal: BusinessCooperationModalHandle['open'];

  /** 打开意见反馈对话框 */
  openFeedbackModal: FeedbackModalHandle['open'];

  /** 打开在线咨询对话框 */
  openOnlineConsultationModal: OnlineConsultationModalHandle['open'];

  /** 打开举报对话框 */
  openReportModal: ReportModalHandle['open'];

  openContactModal: ContactModalHandle['open'];
  /** 打开跳往whee对话框 */
  openGoWheeModal: GoWheeModalRef['open'];
  /** 打开强制刷新对话框 */
  openRefreshModal: RefreshModalRef['open'];
  /* 拉新活动分享弹窗 */
  openMGMShareModal: MGMShareModalRef['open'];
  /* 拉新活动被邀请 */
  openMGMAcceptModal: MGMAcceptModalRef['open'];
  openGuideModal: GuideModalRef['open'];
  /**更新弹框 */
  openNoticeModal: NoticePopupRef['open'];
  /** 打开sdk loading */
  openSdkLoading: SdkLoadingRef['open'];
  /** 关闭sdk loading */
  closeSdkLoading: SdkLoadingRef['close'];
}

function initialContextMethod() {
  console.warn('正在使用 `AppContext` 的初始方法');
}

export const AppContext = createContext<AppContextValues>({
  openBusinessCooperationModal: initialContextMethod,
  openBetaInvitationModal: initialContextMethod,
  openFeedbackModal: initialContextMethod,
  openOnlineConsultationModal: initialContextMethod,
  openReportModal: initialContextMethod,
  openContactModal: initialContextMethod,
  openGoWheeModal: initialContextMethod,
  openRefreshModal: initialContextMethod,
  openMGMShareModal: initialContextMethod,
  openMGMAcceptModal: initialContextMethod,
  openGuideModal: initialContextMethod,
  openNoticeModal: initialContextMethod,
  openSdkLoading: initialContextMethod,
  closeSdkLoading: initialContextMethod
});
