@import '~@/styles/variables.less';

.external-gift:global(.ant-modal) {
  :global {
    .ant-modal-content {
      padding: 0;
      padding-bottom: 30px;
      border-radius: 12px;
    }

    .ant-modal-close {
      width: 30px;
      height: 30px;
      background-color: rgba(0, 0, 0, 0.25);
      border-radius: 50%;
      color: #fff;

      &:hover {
        background-color: rgba(0, 0, 0, 0.25);
        color: #fff;
      }
    }
  }

  :global(.ant-image) {
    display: block;
  }

  .mask {
    position: absolute;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #fff 100%),
      linear-gradient(180deg, rgba(255, 255, 255, 0.7) 0%, #fff 100%);
    backdrop-filter: blur(25px);
    bottom: 139px;
    left: 0;
    right: 0;
    right: 0;
    z-index: 1;
    height: 64px;
    width: 100%;
    border-top: 1px solid rgba(255, 255, 255, 0.5);
  }

  .banner {
    width: 100%;
    height: 280px !important;
    border-radius: 10px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    object-fit: cover;
    margin-bottom: 24px;
    position: relative;
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .icon {
      font-size: 76px;
      z-index: 2;
    }

    h3 {
      color: @content-system-primary;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      margin-bottom: 28px;
      z-index: 2;
    }

    .button {
      width: 220px;
      height: 44px;
    }
  }
}
