import { useAccountTipConfig } from '@/hooks';
import { CrossBlack } from '@meitu/candy-icons';
import { Image, Modal } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';
import styles from './index.module.less';
import { MeidouWithStars } from '@/icons';
import { Button } from '@/components';

export interface ExternalGiftTipRef {
  open: () => void;
}

export interface ExternalGiftTipProps {}

export const ExternalGiftTip = forwardRef<ExternalGiftTipRef>((props, ref) => {
  const [open, setOpen] = useState(false);
  const accountTipConfig = useAccountTipConfig();

  useImperativeHandle(ref, () => ({
    open: () => setOpen(true)
  }));

  const close = () => setOpen(false);

  return (
    <Modal
      open={open}
      centered
      destroyOnClose
      maskClosable
      closeIcon={<CrossBlack />}
      width={520}
      footer={null}
      className={styles.externalGift}
      onCancel={close}
    >
      <Image
        src={accountTipConfig?.picUrl}
        preview={false}
        className={styles.banner}
        alt="banner"
      />
      <div className={styles.mask} />
      <div className={styles.content}>
        <MeidouWithStars className={styles.icon} />
        <h3>{accountTipConfig?.newUserAwardTipTitle ?? ''}</h3>
        <Button type="primary" className={styles.button} onClick={close}>
          好的
        </Button>
      </div>
    </Modal>
  );
});
