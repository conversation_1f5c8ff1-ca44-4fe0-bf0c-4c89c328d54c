import { Modal, App } from 'antd';
import {
  useState,
  forwardRef,
  useImperativeHandle,
  useEffect,
  useRef
} from 'react';
import { CrossBlack } from '@meitu/candy-icons';
import styles from './index.module.less';
import { useCopyToClipboard } from '@/hooks';
import { MGMPosterModal, MGMPosterModalRef } from './components/Poster';
import { useMGMModal } from '@/hooks/useMGMModal';
import { useAccount } from '@/hooks';
import { trackEvent } from '@/services';
import { useLocation } from 'react-router-dom';
import { removeLocalStorageItem, getLocalStorageItem } from '@meitu/util';
export const MGM_SHARE_VIEWED = 'aigc-editor:mgm-share-viewed';

export interface MGMShareModalRef {
  open: () => void;
}

export const MGMClickingKey = 'MGMClicking';
export const mgmUnLoginCode = '0'; //点击banner未登入  状态下中间态code
export const mgmShareCodeKey = 'MGMshareCode';
// const shareUrlBase = `${window.location.origin}/art`;
///art?MGMshareCode=**********
export const MGMShareModal = forwardRef<MGMShareModalRef>((props, ref) => {
  //拉新活动
  const { initShareDate, shareData, getCode, MGMRemoveStorageCode } =
    useMGMModal();
  const [open, setOpen] = useState(false);
  const [openRule, setOpenRule] = useState(false);
  const [shareUrl, setShareUrl] = useState('');
  const [shareText, setShareText] = useState('');
  const { message } = App.useApp();
  const MGMPosterModalRef = useRef<MGMPosterModalRef>(null);
  const { isLogin } = useAccount();
  const location = useLocation();
  const timerClickBannerNoLogin = useRef<any>(null);
  // const { accountProfile } = useAccountProfile();
  useEffect(() => {
    return () => {
      timerClickBannerNoLogin.current &&
        clearTimeout(timerClickBannerNoLogin.current);
    };
  }, []);
  useEffect(() => {
    if (isLogin) {
      initShareDate();
    }
    if (!isLogin && !getCode()) {
      MGMRemoveStorageCode();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLogin]);

  useEffect(() => {
    getBaseDate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLogin, location.search]);

  const getBaseDate = async () => {
    const queryParams = new URLSearchParams(window.location.search);
    const sdk = queryParams.get('client_from_sdk');
    if (sdk) return;
    if (isLogin && getLocalStorageItem(MGMClickingKey) === mgmUnLoginCode) {
      setOpen(true);
      removeLocalStorageItem(MGMClickingKey);
      return;
    }
    timerClickBannerNoLogin.current = setTimeout(() => {
      clearTimeout(timerClickBannerNoLogin.current);
      timerClickBannerNoLogin.current = null;
      if (!isLogin && getLocalStorageItem(MGMClickingKey) === mgmUnLoginCode) {
        removeLocalStorageItem(MGMClickingKey);
      }
      // 两秒内没有登入进去则判断为没有登入此时则移除弹窗
    }, 1500);
  };
  const copyToClipboard = useCopyToClipboard({
    onSuccess: () => {
      message.success('链接复制成功！快去邀请你的好友一起来体验WHEE吧！');
    },
    onFail: () => {}
  });
  useImperativeHandle(ref, () => ({
    open: () => setOpen(true)
  }));
  useEffect(() => {
    if (shareData?.url) {
      const shame = 'http';
      const [text, url] = (shareData.url as string).split(shame);
      setShareText(text);
      setShareUrl(shame + url);
    }
  }, [shareData]);
  const close = () => setOpen(false);
  const closeRule = () => setOpenRule(false);
  const openRuleHandler = () => setOpenRule(true);
  /**
   * 复制链接。
   */
  const copyUrl = (event: React.MouseEvent<HTMLDivElement>) => {
    event.preventDefault();
    trackEvent('invite_popup_click', {
      click_type: 'url'
    });
    copyToClipboard(`${shareText}${shareUrl}`);
  };
  const showPoster = (event: React.MouseEvent<HTMLDivElement>) => {
    event.preventDefault();
    trackEvent('invite_popup_click', {
      click_type: 'poster'
    });
    MGMPosterModalRef.current?.open();
  };
  return (
    <>
      <Modal
        open={open}
        centered
        destroyOnClose
        maskClosable={false}
        closeIcon={false}
        width={420}
        footer={null}
        wrapClassName={styles.mgmShareModalWrap}
        onCancel={close}
      >
        <div className={styles.mgmShareModalTopBackGround}></div>
        <div onClick={openRuleHandler} className={styles.mgmShareModalRuleBtn}>
          活动规则
        </div>
        <div className={styles.mgmShareModalClose} onClick={close}>
          <CrossBlack />
        </div>
        <p className={styles.mgmShareModalContent}>
          邀请新用户注册并登录，
          <br />
          双方立即获得10美豆奖励！
        </p>
        <div className={styles.mgmShareModalFooter}>
          <div onMouseDown={copyUrl}>链接邀请</div>
          <div onMouseDown={showPoster}>海报邀请</div>
        </div>
      </Modal>
      <Modal
        open={openRule}
        centered
        destroyOnClose
        maskClosable
        width={320}
        closeIcon={false}
        footer={null}
        wrapClassName={styles.mgmShareModalRuleWrap}
        onCancel={closeRule}
      >
        <div className={styles.mgmShareModalRuleClose} onClick={closeRule}>
          <CrossBlack />
        </div>
        <div className={styles.mgmShareModalRuleHeader}>活动规则</div>
        <div className={styles.mgmShareModalRuleCenter}>
          <ol>
            <li>每邀请1名新用户注册并登录WHEE，双方立即获得10美豆奖励。</li>
            <li>美豆有效期为30天。</li>
            <li>
              每个人最多可邀请20名，超过20名用户，将无法继续获得奖励，且被邀请人仍然可以获得奖励。
            </li>
            <li>新用户为从未登录过WHEE的用户。</li>
          </ol>
          备注：如检测到您在邀请过程中存在刷量等通过非正当方式获利的行为，平台有权收回己发放的奖励，情节严重者将被封禁账号。活动最终解释权归美图WHEE所有。
        </div>
      </Modal>
      <MGMPosterModal
        shareUrl={shareUrl}
        sharePic={shareData?.picArry || []}
        ref={MGMPosterModalRef}
      />
    </>
  );
});
