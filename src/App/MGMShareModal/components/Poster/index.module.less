@import '~@/styles/variables.less';

.mgmPosterModalWrap {
  :global {
    .ant-modal-root .ant-modal-wrap {
      overflow: hidden;
    }

    .@{ant-prefix}-modal {
      border-radius: 16px;
      width: 100vw;
      height: 100vh;
      display: block;
      overflow: hidden;

      .@{ant-prefix}-modal-content {
        padding: 0;
        background: rgba(0, 0, 0, 0);
        box-shadow: none;
      }

      // .@{ant-prefix}-modal-wrap{

      // }
    }
  }

  .mgmPosterContent {
    width: 100%;
    background-color: #000;
    position: relative;

    .mgmPosterQRCodeBox {
      position: absolute;
      z-index: -20;
    }

    .mgmPosterBackImage {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .mgmPosterBackBox {
      width: 100%;
      height: 100%;
      background: linear-gradient(
        180deg,
        #0a0a0a 0%,
        rgba(10, 10, 10, 0.5) 100%
      );
      // backdrop-filter: blur(50px);
      backdrop-filter: blur(20px);
    }

    .mgmPosterCanvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  .mgmPosterFooter {
    padding-top: 16px;
    display: flex;
    justify-content: space-between;

    div {
      cursor: pointer;
      width: 212px;
      height: 48px;
      text-align: center;
      line-height: 48px;
      border-radius: 8px;
      font-size: 16px;
      background-color: #fff;
      color: #131314;

      &:last-child {
        background-color: #3549ff;
        color: #fff;
        // margin-left: 18px;
      }
    }
  }

  .mgmPosterModalClose {
    position: absolute;
    top: 0;
    right: -52px;
    border: 2px solid rgba(255, 255, 255, 0.25);
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
  }
}

.mgmPosterModalWrapOut {
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;

  .mgmPosterModalWrapOutCenter {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
