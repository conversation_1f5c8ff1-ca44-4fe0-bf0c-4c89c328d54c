import { Modal, QRCode } from 'antd';
import {
  useState,
  forwardRef,
  useImperativeHandle,
  useEffect,
  useRef,
  useLayoutEffect
} from 'react';
import { DownloadBold, CrossBlack } from '@meitu/candy-icons';
import styles from './index.module.less';
import Konva from 'konva';
import { downloadFile } from '@/utils/blob';
import { createImage } from '@/utils/cropImage';
import { useAccountProfile } from '@/hooks';
import { trackEvent } from '@/services';
import { useAutoCloseToast } from '@/containers/ImageEditor/hooks/useToast';
export const MGM_SHARE_VIEWED = 'aigc-editor:mgm-share-viewed';
const logoImageUrl = require('./img/logo.svg').default;
const WHEEImageUrl = require('./img/WHEE.svg').default;
export interface MGMPosterModalRef {
  open: () => void;
}
export interface MGMPosterModalProps {
  shareUrl: string;
  sharePic: string[];
}

// const imageList = [
//   'https://titan-h5.meitu.com/whee/assets/gamePlay/image2image/image1.png',
//   'https://titan-h5.meitu.com/whee/assets/gamePlay/image2image/image2.png',
//   'https://titan-h5.meitu.com/whee/assets/gamePlay/image2image/image3.png',
//   'https://titan-h5.meitu.com/whee/assets/gamePlay/imageExtend/imageExtend1.png'
// ] as string[];
const nodeId = 'canvasContent';
const posterWidth = 440;
const posterHeight = 708;
const stageHeight = 644;
export const MGMPosterModal = forwardRef<
  MGMPosterModalRef,
  MGMPosterModalProps
>((props, ref) => {
  const { shareUrl, sharePic } = props;
  const { accountProfile } = useAccountProfile();
  const userData = useRef<any>(null);
  const [open, setOpen] = useState(false);
  const [imgList, setImgList] = useState<string[]>([]);
  const [showImageIndex, setShowImageIndex] = useState(1);
  const [mainImageUrl, setMainImageUrl] = useState('');
  const QRBoxRef = useRef<HTMLDivElement>(null);
  const stage = useRef<Konva.Stage | null>();
  const layer = useRef<Konva.Layer | null>();
  const backGroundImageRef = useRef<Konva.Image | null>();
  const mainImageRef = useRef<Konva.Image | null>();
  const imageIndex = useRef<number>(0);
  const pixelRatio = useRef<number>(window.devicePixelRatio || 2);
  const { message, clearTost } = useAutoCloseToast();
  const [posterRatio, setPosterRatio] = useState(1);
  useImperativeHandle(ref, () => ({
    open: () => setOpen(true)
  }));
  useEffect(() => {
    if (sharePic.length === 0) return;
    setImgList(sharePic);
    setMainImageUrl(sharePic[imageIndex.current]);
    preloadImage(sharePic);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sharePic]);

  const preloadImage = async (urls: string[]) => {
    try {
      const promises = urls.map((url) => createImage(url));
      await Promise.all(promises);
    } catch (error) {
      console.error('Error processing strings:', error);
    }
  };
  useLayoutEffect(() => {
    updateWindowSize();
  }, []);
  useEffect(() => {
    window.addEventListener('resize', updateWindowSize);
    return () => {
      window.removeEventListener('resize', updateWindowSize);
    };
  }, []);
  const updateWindowSize = () => {
    let ratio = window.innerHeight / 900;
    ratio = Math.max(0.5, ratio);
    // ratio=Math.min(1,ratio)
    setPosterRatio(ratio);
  };
  useEffect(() => {
    if (open) info();
    return () => {
      stage.current && stage.current.destroy();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);
  // 更新 海报
  useLayoutEffect(() => {
    const url = imgList[imageIndex.current];
    if (url) {
      setMainImageUrl(url);
      updateCanvas(url);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [imageIndex.current, showImageIndex]);
  //更新用户信息
  useEffect(() => {
    userData.current = { ...accountProfile };
    drawUserData(userData.current);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accountProfile?.name]);
  const close = () => {
    setOpen(false);
  };
  const info = async () => {
    upDateImage();
    updateWindowSize();
    setTimeout(() => {
      drawPoster();
    }, 400);
  };
  // const updateModal = () => {
  //   const nodeNode = document.getElementById(nodeId);
  //   if (!nodeNode) return;
  //   const modalDom = findParentWithClass(nodeNode, 'ant-modal')
  //   if (modalDom) {
  //     // modalDom.style.width = nodeNode.clientWidth+ 'px';
  //     // modalDom.style.height = nodeNode.clientHeight + 'px';
  //     modalDom.style.transform=`scale(${posterRatio})`;

  //   }
  //   const modalContentDom = findParentWithClass(nodeNode, 'ant-modal-content')
  //   if (modalContentDom) {
  //     // modalDom.style.width = nodeNode.clientWidth+ 'px';
  //     // modalDom.style.height = nodeNode.clientHeight + 'px';
  //     modalContentDom.style.transform=`scale(${1})`;

  //   }
  //   // const clientRect = nodeNode.getBoundingClientRect();
  //   // const width = clientRect.width; // 定义最大宽度
  //   // const height = clientRect.height; // 定义最大高度
  // }
  const drawPoster = async () => {
    stage.current = new Konva.Stage({
      container: nodeId,
      width: posterWidth,
      height: stageHeight,
      pixelRatio: pixelRatio.current / posterRatio
    });
    layer.current = new Konva.Layer();
    stage.current.add(layer.current);
    await drawContent();
  };
  const drawContent = async () => {
    backGroundImageRef.current = await addGS(mainImageUrl);
    await addImageToKonva(logoImageUrl, 154, 24, 40, 40);
    await addImageToKonva(WHEEImageUrl, 204, 35.5, 82, 20);
    addTextToKonva('我发现了一款免费的AIGC平台，功能齐全！一键生成！', 49, 81, {
      fontSize: 14,
      color: 'rgba(255, 255, 255, 0.70)'
    });
    mainImageRef.current = await addImageToKonva(
      mainImageUrl,
      24,
      117,
      392,
      391,
      4
    );
    await drawUserData(userData.current);

    addTextToKonva(
      '这么好用！我不允许你不知道！\n立即扫码免费体验！',
      24,
      582,
      {
        fontSize: 14,
        color: 'rgba(255, 255, 255, 1)',
        lineHeight: 1.5
      }
    );
    addQR();
  };

  const drawUserData = async (useData: any) => {
    if (!(useData && useData.avatar && useData.name)) return;
    addTextToKonva(useData.name, 68, 548, {
      fontSize: 14,
      color: 'rgba(255, 255, 255, 0.70)'
    });
    await addImageToKonva(useData.avatar, 24, 542, 32, 32, 16);
  };

  const addGS = (bgSrc: string) => {
    if (!(stage.current && layer.current)) return;
    const width = stage.current.width();
    const height = stage.current.height();
    return new Promise<any>((resolve, reject) => {
      // 加载背景
      Konva.Image.fromURL(bgSrc, (bgImg) => {
        bgImg.setAttrs({
          x: 0,
          y: 0,
          width: width,
          height: height,
          blurRadius: 50
        });
        bgImg.cache();
        bgImg.filters([Konva.Filters.Blur]);
        layer.current?.add(bgImg);
        // 渐变遮罩
        const linearGradMask = new Konva.Rect({
          x: 0,
          y: 0,
          width: width,
          height: height,
          fillLinearGradientStartPoint: { x: 0, y: 0 },
          fillLinearGradientEndPoint: { x: 0, y: height },
          fillLinearGradientColorStops: [
            0,
            '#0a0a0a',
            1,
            'rgba(10, 10, 10, 0.5)'
          ]
        });
        layer.current?.add(linearGradMask);
        resolve(bgImg);
      });
    });
  };

  const updateBgImg = (newBgSrc: string) => {
    Konva.Image.fromURL(newBgSrc, (newImg) => {
      if (!backGroundImageRef.current) return;
      backGroundImageRef.current.image(newImg.image()); // 更新图像内容
      backGroundImageRef.current.setAttrs({
        blurRadius: 30 // 示例：更新模糊半径
        // 添加其他需要更新的属性
      });
      backGroundImageRef.current.cache();
      backGroundImageRef.current.filters([Konva.Filters.Blur]);
      layer.current?.batchDraw(); // 重绘层
    });
  };
  const addImageToKonva = async (
    url: string,
    x: number,
    y: number,
    width?: number,
    height?: number,
    radius?: number
  ) => {
    if (!(stage.current && layer.current)) return;
    width = width || stage.current.width();
    height = height || stage.current.height();
    const mainDom = await createImage(url);
    const imageNode = new Konva.Image({
      x: x || 0,
      y: y || 0,
      image: mainDom,
      width: width,
      height: height,
      sceneFunc: (context, shape) => {
        if (!radius) {
          context.drawImage(
            shape.getAttr('image'),
            0,
            0,
            shape.width(),
            shape.height()
          );
          return;
        }
        imgGetRadius(context, shape, radius);
      }
    });
    layer.current.add(imageNode);
    return imageNode;
  };

  const addTextToKonva = async (
    text: string,
    x: number,
    y: number,
    style?: {
      fontSize?: number;
      color?: string;
      lineHeight?: number;
    }
  ) => {
    if (!(stage.current && layer.current)) return;
    const { fontSize = 16, color = '#fff', lineHeight = 1 } = style || {};
    const animatedText = new Konva.Text({
      x: x || 0,
      y: y || 0,
      text: text,
      fontSize: fontSize,
      fill: color,
      lineHeight
    });
    layer.current.add(animatedText);
  };
  const imgGetRadius = (context: any, shape: any, radius: number) => {
    const cornerRadius = radius || 0;
    context.beginPath();
    context.moveTo(cornerRadius, 0);
    context.lineTo(shape.width() - cornerRadius, 0);
    context.arcTo(shape.width(), 0, shape.width(), cornerRadius, cornerRadius);
    context.lineTo(shape.width(), shape.height() - cornerRadius);
    context.arcTo(
      shape.width(),
      shape.height(),
      shape.width() - cornerRadius,
      shape.height(),
      cornerRadius
    );
    context.lineTo(cornerRadius, shape.height());
    context.arcTo(
      0,
      shape.height(),
      0,
      shape.height() - cornerRadius,
      cornerRadius
    );
    context.lineTo(0, cornerRadius);
    context.arcTo(0, 0, cornerRadius, 0, cornerRadius);
    context.closePath();
    context.clip();
    context.drawImage(
      shape.getAttr('image'),
      0,
      0,
      shape.width(),
      shape.height()
    );
  };

  const addQR = () => {
    if (!(stage.current && layer.current)) return;
    if (!QRBoxRef.current) return;
    const dom = QRBoxRef.current.getElementsByTagName('canvas');
    if (!(dom && dom.length > 0)) return;
    const padding = 6;
    const roundedRect = new Konva.Rect({
      x: 320,
      y: 524,
      width: 96,
      height: 96,
      fill: '#fff',
      cornerRadius: 8
    });
    layer.current.add(roundedRect);
    const imageNode = new Konva.Image({
      x: 320 + padding,
      y: 524 + padding,
      image: dom[0],
      width: 96 - padding * 2,
      height: 96 - padding * 2
    });
    layer.current.add(imageNode);
  };
  const getRandomNumber = (n: number, x: number) => {
    let randomNum;
    do {
      randomNum = Math.floor(Math.random() * (n + 1));
    } while (randomNum === x);

    return randomNum;
  };
  const updateCanvas = async (url?: string) => {
    if (!(mainImageRef.current && backGroundImageRef.current)) return;
    url = url || mainImageUrl;
    if (!url) return;
    const imageDom = await createImage(url);
    mainImageRef.current.image(imageDom);
    updateBgImg(url);
  };
  const noOnline = () => {
    clearTost();
    message({
      type: 'error',
      content: '网络异常',
      duration: 3000
    });
  };
  const upDateImage = () => {
    if (!navigator.onLine) {
      noOnline();
      return;
    }
    const newIndex = getRandomNumber(imgList.length, imageIndex.current);
    imageIndex.current = newIndex;
    setShowImageIndex(newIndex);
  };
  const downLoad = (event: React.MouseEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (!layer.current) return;
    if (!navigator.onLine) {
      noOnline();
      return;
    }
    trackEvent('invite_share_popup_click', {
      click_type: 'download'
    });
    downloadCanvasImage(
      layer.current.toCanvas({
        pixelRatio: pixelRatio.current
      })
    );
  };
  /**
   * 下载图片。
   *
   * @param canvas
   */
  const downloadCanvasImage = (canvas: HTMLCanvasElement) => {
    canvas.toBlob((data) => {
      if (data) {
        downloadFile(data, 'shareCard.jpg');
      }
    }, 'image/jpeg');
  };
  const modalStyles = {
    content: {
      transform: `scale(${posterRatio})`
    }
  };
  // const findParentWithClass = (element: HTMLElement, className: string) => {
  //   let parent = element.parentElement;
  //   while (parent) {
  //     if (parent.classList.contains(className)) {
  //       return parent;
  //     }
  //     parent = parent.parentElement;
  //   }
  //   return null;
  // };
  return (
    <>
      <Modal
        open={open}
        centered
        destroyOnClose
        maskClosable={false}
        closeIcon={false}
        width={'100vw'}
        footer={null}
        wrapClassName={styles.mgmPosterModalWrap}
        onCancel={close}
        styles={modalStyles}
      >
        <div className={` ${styles.mgmPosterModalWrapOut}`}>
          <div
            className={` ${styles.mgmPosterModalWrapOutCenter}`}
            style={{
              width: posterWidth,
              height: posterHeight
            }}
          >
            <div
              style={{
                width: '100%',
                height: '100%'
              }}
            >
              <div className={styles.mgmPosterModalClose} onClick={close}>
                <CrossBlack />
              </div>
              <div className={styles.mgmPosterContent} style={{ height: 644 }}>
                <div className={styles.mgmPosterQRCodeBox} ref={QRBoxRef}>
                  <QRCode
                    size={96}
                    value={`${shareUrl}`}
                    bgColor={'#fff'}
                    bordered
                  />
                </div>
                {/* <img src={mainImageUrl} alt="" className={styles.mgmPosterBackImage} /> */}
                <div className={styles.mgmPosterBackBox}>
                  <div id={nodeId} className={styles.mgmPosterCanvas}></div>
                </div>
              </div>
              <div className={styles.mgmPosterFooter}>
                <div
                  onMouseDown={(event: React.MouseEvent<HTMLDivElement>) => {
                    event.preventDefault();
                    upDateImage();
                    trackEvent('invite_share_popup_click', {
                      click_type: 'switch'
                    });
                  }}
                >
                  换一张试试
                </div>
                <div onMouseDown={downLoad}>
                  <DownloadBold style={{ marginRight: 4 }} />
                  保存图片
                </div>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
});
