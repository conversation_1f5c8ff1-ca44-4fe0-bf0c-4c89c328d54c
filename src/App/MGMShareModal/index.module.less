@import '~@/styles/variables.less';

.mgm-share-modal-wrap {
  :global {
    .@{ant-prefix}-modal {
      border-radius: 16px;
      clip-path: inset(0 round 16px);
      .@{ant-prefix}-modal-content {
        padding: 0;
      }
    }
  }

  .mgm-share-modal-top-backGround {
    width: 100%;
    height: 240px;
    background: rgba(64, 85, 128, 0.06);
    background-image: url('https://titan-h5.meitu.com/whee/assets/gamePlay/MGMActivity/MGMModal.jpg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .mgmShareModalRuleBtn {
    position: absolute;
    top: 16px;
    left: 16px;
    height: 28px;
    line-height: 28px;
    border-radius: 14px;
    background: rgba(0, 0, 0, 0.25);
    padding: 0px 10px;
    color: #fff;

    text-align: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    cursor: pointer;
  }

  .mgmShareModalClose {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 28px;
    height: 28px;
    background-color: rgba(0, 0, 0, 0.25);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
  }

  .mgmShareModalContent {
    color: #131314;
    text-align: center;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    padding-top: 24px;
    padding-bottom: 24px;
    margin-bottom: 0;
  }

  .mgmShareModalFooter {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 24px;

    div {
      margin: 0 8px;
      border-radius: 8px;
      height: 40px;
      width: 132px;
      color: #131314;
      text-align: center;
      line-height: 40px;
      background-color: rgba(20, 31, 51, 0.12);
      cursor: pointer;

      &:last-child {
        color: #fff;
        background-color: #3549ff;
      }
    }
  }
}

.mgmShareModalRuleWrap {
  :global {
    .@{ant-prefix}-modal {
      .@{ant-prefix}-modal-content {
        padding: 20px;
        border-radius: 16px;
      }
    }
  }

  .mgmShareModalRuleHeader {
    padding-top: 8px;
    color: #131314;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 16px;
  }

  .mgmShareModalRuleClose {
    position: absolute;
    top: 12px;
    right: 16px;
    color: rgba(177, 182, 191, 1);
    font-size: 16px;
    cursor: pointer;
  }

  .mgmShareModalRuleCenter {
    color: #7f828a;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    ol {
      padding-left: 20px;
      margin-bottom: 16px;
    }
  }
}
