import { Divider, Modal } from 'antd';
import { CrossBlack } from '@meitu/candy-icons';
import { useState, forwardRef, useImperativeHandle } from 'react';
import { Phone, Email } from '@/icons';

import styles from './index.module.less';

export interface ContactModalHandle {
  open: () => void;
}

export const ContactModal = forwardRef<ContactModalHandle>((props, ref) => {
  const [open, setOpen] = useState(false);

  useImperativeHandle(ref, () => ({
    open: () => setOpen(true)
  }));

  const close = () => setOpen(false);

  return (
    <Modal
      open={open}
      centered
      destroyOnClose
      closeIcon={<CrossBlack />}
      width={600}
      className={styles.contact}
      onCancel={close}
      footer={null}
    >
      <h2>您的体验次数已用完</h2>
      <Divider />
      <p>
        感谢您对 MiracleVision
        的关注！我们欢迎新的合作伙伴，若您有任何合作意向或想要了解我们请致电或发送电子邮件至以下地址。我们会尽快回复，期待与您的合作！
      </p>
      <section className={styles.content}>
        <div className={styles.contentContainer}>
          <Phone className={styles.icon} />
          <div className={styles.detail}>
            <div className={styles.text}>联系电话</div>
            <a className={styles.info} href="tel:0592-5320520">
              0592-5320520
            </a>
          </div>
        </div>
        <div className={styles.contentContainer}>
          <Email className={styles.icon} />
          <div className={styles.detail}>
            <div className={styles.text}>联系邮箱</div>
            <a className={styles.info} href="mailto:<EMAIL>">
              <EMAIL>
            </a>
          </div>
        </div>
      </section>
    </Modal>
  );
});
