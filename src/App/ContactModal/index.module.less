@import '~@/styles/variables.less';

.contact:global(.@{ant-prefix}-modal) {
  :global {
    .@{ant-prefix}-modal-content {
      padding: @size-xl;

      .@{ant-prefix}-modal-body {
        display: flex;
        align-items: center;
        flex-direction: column;

        h2 {
          width: 100%;
          height: 34px;
          align-items: center;
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 0;
        }

        .@{ant-prefix}-divider-horizontal {
          margin: 20px 0;
        }

        p {
          color: #616366;
          line-height: 20px;
          font-size: @size;
          line-height: 150%;
          font-weight: 400;
          margin-bottom: 24px;
        }
      }
    }
  }

  .content {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    width: 100%;
    margin-bottom: 16px;

    &-container {
      height: 96px;
      padding: 23px 20px;
      display: flex;
      align-items: center;
      border-radius: 8px;
      border: 2px solid #eff0f2;
      flex-grow: 1;

      .icon {
        font-size: 28px;
        color: white;
        margin-right: 16px;
      }

      .detail {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .text {
          color: #939599;
          font-size: 14px;
          font-weight: 500;
        }

        .info {
          color: #4d4dff;
          font-size: 14px;
          font-weight: 590;
        }
      }
    }
  }
}
