import { useCallback, useEffect, useState } from 'react';
import Designer from '@/assets/images/designer.png';
import Coder from '@/assets/images/coder.png';
import MarketingStaff from '@/assets/images/marketing-staff.png';
import Teacher from '@/assets/images/teacher.png';
import Bot from '@/assets/images/bot.png';
import Shopping from '@/assets/images/shoping.png';
import SlefMedia from '@/assets/images/self-media.png';

import GraphicDesign from '@/assets/images/graphic-design.png';
import BrandDesign from '@/assets/images/brand-design.png';
import ShootDesign from '@/assets/images/shoot-design.png';
import Inset from '@/assets/images/inset-design.png';
import ProductDesign from '@/assets/images/product-design.png';
import Game from '@/assets/images/game-design.png';
import CommerceDesign from '@/assets/images/commerce-design.png';
import { trackEvent } from '@/services/tracer';
import { fillOccupationalInformation } from '@/api/personal';
import { App } from 'antd';
import { useMeiDouBalance } from '@/hooks/useMeiDou';

export const firstList = [
  {
    img: Designer,
    desc: '设计师/艺术家',
    code: 'artist'
  },
  {
    img: SlefMedia,
    desc: '自媒体',
    code: 'self_media'
  },
  {
    img: MarketingStaff,
    desc: '市场营销人员',
    code: 'marketing'
  },
  {
    img: Teacher,
    desc: '教育培训者',
    code: 'educator'
  },
  {
    img: Bot,
    desc: 'AI绘画爱好者',
    code: 'aigc_pic_fan'
  },
  {
    img: Shopping,
    desc: '电商从业者',
    code: 'ecommerce'
  },
  {
    img: Coder,
    desc: 'AI软件从业者',
    code: 'aigc_practitioner'
  },
  {
    desc: '其他',
    showInput: true,
    code: 'other'
  }
];

export const secondList = [
  {
    img: GraphicDesign,
    title: '平面设计',
    desc: '海报、主视觉、封面图、包装等',
    code: 'graphic_design'
  },
  {
    img: BrandDesign,
    title: '品牌设计',
    desc: 'IP、Logo、商标等',
    code: 'brand_design'
  },
  {
    img: CommerceDesign,
    title: '电商设计',
    desc: '商品图、商详图',
    code: 'commerce_design'
  },
  {
    img: Inset,
    title: '插画',
    desc: '头像、壁纸、漫画、绘本等',
    code: 'illustration'
  },
  {
    img: ProductDesign,
    title: '产品设计',
    desc: '产品、服饰、家具、汽车等',
    code: 'product_design'
  },
  {
    img: Game,
    title: '游戏',
    desc: '角色、道具、场景等',
    code: 'game'
  },
  {
    img: ShootDesign,
    title: '摄影',
    desc: '模特、商品、写真后期等',
    code: 'photography'
  },
  {
    title: '其他',
    desc: '',
    code: 'other',
    showInput: true
  }
];

type PageConfig = {
  title: string;
  buttonContent: string;
  stepHandler: () => void;
  clickItem: (key: string) => void;
};
export function useCustomStepForm() {
  const { updateMeiDouBalance } = useMeiDouBalance();
  const { message } = App.useApp();

  const [open, setOpen] = useState(false);

  // 控制当前步骤
  const [step, setStep] = useState<1 | 2>(1);
  // 存储选中的工作
  const [job, setJob] = useState<string>('');

  // 存储选中的工作场景
  const [secondPageParams, setSecondPageParams] = useState<string[]>([]);
  // 存储其他工作岗位输入内容
  const [otherJob, setOtherJob] = useState<string>('');
  // 存储其他工作内容输入内容
  const [otherJobContext, setOtherJobContext] = useState<string>('');
  // 选中状态切换
  const stateEffect = useCallback((prevState: string[], key: string) => {
    const i = prevState.indexOf(key);
    if (i !== -1) {
      prevState.splice(i, 1);
      return [...prevState];
    } else {
      chooseJobContextTrack('ugc_type_choose_click', key);
      return [...prevState, key];
    }
  }, []);

  // 第一步点击事件
  const firstStepClick = useCallback(
    (key: string) => {
      if (key !== job) {
        chooseJobTrack('occupation_choose_click', key);
      }
      setJob(key);
    },
    [job]
  );

  // 选择职业埋点
  const chooseJobTrack = (key: string, code: string) => {
    trackEvent(key, {
      occupation: code === 'other' ? '' : code
    });
  };
  // 选择职业场景埋点
  const chooseJobContextTrack = (key: string, code: string) => {
    trackEvent(key, {
      ugc_type: code === 'other' ? '' : code
    });
  };

  // 职业输入框失焦埋点
  const otherJobInputBlurTrack = (code: string) => {
    if (!code) return;
    trackEvent('occupation_choose_click', {
      occupation: code
    });
  };
  // 职业场景输入框失焦埋点
  const otherJobContextInputBlurTrack = (code: string) => {
    if (!code) return;
    chooseJobContextTrack('ugc_type_choose_click', code);
  };

  // 第二步点击事件
  const secondStepClick = useCallback(
    (key: string) => {
      setSecondPageParams((prevState) => stateEffect(prevState, key));
    },
    [stateEffect]
  );

  // 页面配置
  const [pageConfig, setPageConfig] = useState<PageConfig>({
    title: '为了提供更好的使用体验，请问您的身份是？',
    buttonContent: '下一步',
    stepHandler: () => {
      setStep(2);
    },
    clickItem: firstStepClick
  });

  //  提交埋点
  const postTrackHandler = () => {
    const otherIndex = secondPageParams.indexOf('other');
    let contextValue = '';
    if (otherIndex === -1) {
      contextValue = secondPageParams.join(',');
    } else {
      secondPageParams.splice(otherIndex, 1, otherJobContext);
      contextValue = secondPageParams.join(',');
    }
    chooseJobTrack(
      'occupation_question_submit_click',
      job === 'other' ? otherJob : job
    );
    chooseJobContextTrack('occupation_question_submit_click', contextValue);
  };
  // 提交事件
  const postHandler = async () => {
    postTrackHandler();
    try {
      const { toast } = await fillOccupationalInformation();
      // 更新美豆信息
      updateMeiDouBalance();
      message.success(toast);
    } catch (error) {
      message.error('提交失败请重试');
    }
    setOpen(false);
  };

  useEffect(() => {
    if (step === 2) {
      setPageConfig({
        title: '您希望用WHEE创作什么类型的内容？',
        buttonContent: '上一步',
        stepHandler: () => {
          setStep(1);
        },
        clickItem: secondStepClick
      });
    } else {
      setPageConfig({
        title: '为了提供更好的使用体验，请问您的身份是？',
        buttonContent: '下一步',
        stepHandler: () => {
          setStep(2);
        },
        clickItem: firstStepClick
      });
    }
  }, [firstStepClick, otherJob, otherJobContext, secondStepClick, step]);

  return {
    step,
    ...pageConfig,
    job,
    secondPageParams,
    postHandler,
    open,
    setOpen,
    otherJobInputBlurTrack,
    otherJobContextInputBlurTrack,
    otherJobContext,
    otherJob,
    setOtherJob,
    setOtherJobContext
  };
}
