import { forwardRef, useImperativeHandle, useMemo } from 'react';
import { Modal } from 'antd';
import { ModalFooter } from '@/components/OccupationalInformation/ModalFooter';
import {
  InformationCard,
  InformationForm
} from '@/components/OccupationalInformation/InformationCard';
import {
  FieldCard,
  FieldForm
} from '@/components/OccupationalInformation/FieldCard';
import styles from './index.module.less';

import { firstList, secondList, useCustomStepForm } from './useCustomStepForm';
import { setCanExpireLocal } from '@/utils/storage';

export interface OccupationalInformationTipRef {
  open: () => void;
}
export const OccupationalInformationTip =
  forwardRef<OccupationalInformationTipRef>((props, ref) => {
    const {
      step,
      stepHandler,
      buttonContent,
      title,
      clickItem,
      job,
      secondPageParams,
      open,
      otherJob,
      otherJobContext,
      setOtherJob,
      setOtherJobContext,
      setOpen,
      postHand<PERSON>,
      otherJobContextInputBlurTrack,
      otherJobInputBlurTrack
    } = useCustomStepForm();
    useImperativeHandle(ref, () => ({
      open: () => setOpen(true)
    }));
    const close = () => {
      setCanExpireLocal('hasSubmitJobInfo', 'true', 7);
      setOpen(false);
    };
    // 工作视图
    const StepFirst = useMemo(() => {
      return firstList.map(({ showInput, ...reset }, index) => {
        if (showInput) {
          return (
            <InformationForm
              key={index}
              {...reset}
              clickItem={clickItem}
              params={job}
              inputBlurTrack={otherJobInputBlurTrack}
              inputVal={otherJob}
              inputHandler={setOtherJob}
            />
          );
        }
        return (
          <InformationCard
            key={index}
            {...reset}
            clickItem={clickItem}
            params={job}
          />
        );
      });
    }, [clickItem, job, otherJob, otherJobInputBlurTrack, setOtherJob]);

    // 工作内容视图
    const StepSecond = useMemo(() => {
      return secondList.map(({ showInput, ...reset }, index) => {
        if (showInput) {
          return (
            <FieldForm
              key={index}
              clickItem={clickItem}
              params={secondPageParams}
              title={reset.title || ''}
              desc={reset.desc}
              code={reset.code}
              inputBlurTrack={otherJobContextInputBlurTrack}
              inputVal={otherJobContext}
              inputHandler={setOtherJobContext}
            />
          );
        }
        return (
          <FieldCard
            key={index}
            clickItem={clickItem}
            params={secondPageParams}
            title={reset.title || ''}
            desc={reset.desc}
            code={reset.code}
            img={reset.img || ''}
          />
        );
      });
    }, [
      clickItem,
      secondPageParams,
      otherJobContextInputBlurTrack,
      otherJobContext,
      setOtherJobContext
    ]);

    return (
      <Modal
        open={open}
        title={title}
        wrapClassName={styles.containerWithModal}
        centered
        destroyOnClose
        maskClosable
        closeIcon={true}
        footer={
          <ModalFooter
            buttonContext={buttonContent}
            stepHandler={stepHandler}
            postHandler={postHandler}
            step={step}
            job={job}
            secondPageParams={secondPageParams}
          />
        }
        onCancel={close}
      >
        <div className={styles.cardContainer}>
          {step === 1 ? StepFirst : StepSecond}
        </div>
      </Modal>
    );
  });
