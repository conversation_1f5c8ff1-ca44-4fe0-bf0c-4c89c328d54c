import { CrossBoldOutlined } from '@meitu/candy-icons';
import { Modal, Form, message } from 'antd';
import { TextArea } from '@/components';
import type { OnFormFinish } from '@/types';
import { complaintWork } from '@/api';

import { useImperativeHandle, forwardRef, useState } from 'react';
import styles from './index.module.less';
import { Button } from '@/components';
import { defaultErrorHandler } from '@/utils/defaultErrorHandler';

const basicRules = [{ required: true, message: '' }];

export interface ReportModalHandle {
  open: (id: string) => void;
}

interface ReportParams {
  /** 投诉内容 */
  content: string;

  /** 联系方式 */
  concat: string;
}

/**
 * 意见反馈对话框
 */
export const ReportModal = forwardRef<ReportModalHandle>((props, ref) => {
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [taskId, setTaskId] = useState('');

  useImperativeHandle(
    ref,
    () => ({
      open(id: string) {
        setOpen(true);
        setTaskId(id);
      }
    }),
    []
  );

  const onFinish: OnFormFinish<ReportParams> = async (values) => {
    const { content: description, concat: concatInfo } = values;
    const reportQuery = {
      id: taskId,
      description,
      concatInfo
    };

    complaintWork(reportQuery)
      .then((response) => {
        message.success('举报成功');
        form.resetFields();
        setOpen(false);
      })
      .catch(defaultErrorHandler);
  };

  return (
    <Modal
      className={styles.modal}
      rootClassName={styles.root}
      title="举报"
      open={open}
      footer={null}
      maskClosable={false}
      closeIcon={<CrossBoldOutlined />}
      destroyOnClose
      width={640}
      onCancel={setOpen.bind(null, false)}
    >
      <Form
        className={styles.container}
        form={form}
        layout="vertical"
        onFinish={onFinish}
      >
        <Form.Item label="投诉内容" required name="content" rules={basicRules}>
          <TextArea.SmartTextarea
            hasComplementAction={false}
            rows={6}
            placeholder="请输入10字以上的内容描述以便我们更好为您提供更好帮助。"
            maxLength={200}
            showCount
            allowClear
          />
        </Form.Item>
        <Form.Item label="联系方式" required name="concat" rules={basicRules}>
          <TextArea.SmartTextarea
            hasComplementAction={false}
            rows={4}
            placeholder="请输入您的手机号/QQ号/邮箱/微信号便于联系"
            maxLength={200}
            showCount
            allowClear
          />
        </Form.Item>
        <section className={styles.submit}>
          <Button htmlType="submit">提交</Button>
        </section>
      </Form>
    </Modal>
  );
});
