@import '@/styles/variables.less';

.root :global .@{ant-prefix}-modal {
  &-mask {
    backdrop-filter: blur(@size-xs);
  }

  &-wrap {
    display: flex;
    align-items: center;
  }
}

.modal:global(.@{ant-prefix}-modal) {
  top: auto;
  padding-bottom: 0;

  :global .@{ant-prefix}-modal {
    &-header {
      padding: 8px 0;
    }

    &-title {
      text-align: center;
    }

    &-content {
      padding: 0;
      height: 520px;
      border-radius: 8px;
      overflow: hidden;
    }

    &-body {
      height: calc(100% - 48px);
      padding: @size;
    }
  }

  .container {
    label {
      font-weight: 600;
    }
  }

  .submit {
    display: flex;
    justify-content: flex-end;
  }
}
