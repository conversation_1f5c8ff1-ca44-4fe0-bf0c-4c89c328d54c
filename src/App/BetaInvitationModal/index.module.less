@import '@/styles/variables.less';

.root :global .@{ant-prefix}-modal {
  &-mask {
    backdrop-filter: blur(@size-xs);
  }

  &-wrap {
    display: flex;
    align-items: center;
  }
}

.modal:global(.@{ant-prefix}-modal) {
  top: auto;
  padding-bottom: 0;

  :global .@{ant-prefix}-modal {
    &-content {
      padding: 0;
      height: 444px;
      background: transparent;
      filter: drop-shadow(@base-level-2-light) drop-shadow(@base-level-2-dark);
    }

    &-body {
      height: 100%;
    }

    &-close {
      color: @color-white;
      top: 12px;
      right: 12px;

      &:hover {
        color: @color-white;
      }
    }
  }

  .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    overflow: hidden;
    color: @base-white-opacity-100;
    padding-top: 40px;
    background-size: cover;

    h2 {
      max-width: 364px;
      font-size: @size-xl;
      font-weight: 900;
      margin-bottom: @size-sm;
      line-height: 34px;
      text-align: center;
    }

    p {
      max-width: 364px;
      font-size: @font-size;
      font-weight: 600;
      margin-bottom: 38px;
      text-align: center;
    }

    img {
      width: 204px;
      height: 204px;
      border-radius: @font-size-lg;
    }
  }
}
