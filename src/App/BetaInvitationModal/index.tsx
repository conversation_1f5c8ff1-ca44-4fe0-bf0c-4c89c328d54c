import { CrossBoldOutlined } from '@meitu/candy-icons';
import { Modal } from 'antd';

import { useImperativeHandle, forwardRef, useState } from 'react';
import { useGroupPopupConfig } from '@/hooks';
import styles from './index.module.less';

export interface BetaInvitationModalHandle {
  open: () => void;
}

/**
 * 内测邀请对话框
 */
export const BetaInvitationModal = forwardRef<BetaInvitationModalHandle>(
  (props, ref) => {
    const [open, setOpen] = useState(false);
    const [groupPopupConfig] = useGroupPopupConfig();
    const { title, description, backgroundImage, qrcode } =
      groupPopupConfig || {};

    useImperativeHandle(
      ref,
      () => ({
        open() {
          console.log('open beta invitation modal');
          setOpen(true);
        }
      }),
      []
    );

    return (
      <Modal
        className={styles.modal}
        wrapClassName="wrapClassName"
        rootClassName={styles.root}
        open={open}
        footer={null}
        maskClosable={false}
        closeIcon={<CrossBoldOutlined />}
        destroyOnClose
        width={520}
        onCancel={setOpen.bind(null, false)}
      >
        <section
          className={styles.container}
          style={{
            backgroundImage: `url(${backgroundImage})`
          }}
        >
          <h2>{title}</h2>
          <p>{description}</p>
          <img src={qrcode} alt="内测邀请" />
        </section>
      </Modal>
    );
  }
);
