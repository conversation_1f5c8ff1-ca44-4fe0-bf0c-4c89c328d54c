import { AppModule, getAppModulePath } from '@/services';
import { ConfigProvider, Flex, notification } from 'antd';
import { matchPath } from 'react-router-dom';
import styles from './index.module.less';
import { defaultLevel4 } from '@meitu/candy-theme';
import { OverviewBanner } from '@/types';
import { Carousel } from '@/containers/Overview/Gallery/Carousel';
import { fetchVersionMaterial } from '@/api/versionMaterial';
import { getLocalStorageItem, setLocalStorageItem } from '@meitu/util';
import { ComponentProps, useEffect, useRef, useState } from 'react';
import { Button } from '@/components';
import { v4 as uuid } from 'uuid';

const VERSION_PREFIX = 'whee_version_changelog';

/**
 * 与提醒用户版本更新，刷新页面获取最新bundle不同
 * 该版本更新弹窗的需求只是进入页面后比对编译中注入的版本号和缓存的版本是否有差异
 * 并且拉取远程更新物料json比对来决定是否需要弹窗
 *
 * 简单来说，并不是提醒用户更新 而是一个当前用户版本的更新日志
 */

export interface VersionLogModalProps {}

export const VersionLogModal = (props: VersionLogModalProps) => {
  const isInOverView = !!matchPath(
    { path: getAppModulePath(AppModule.Overview), end: true },
    window.location.pathname
  );

  const isOpened = useRef(false);

  const [api, contextHolder] = notification.useNotification({
    bottom: 0
  });

  useEffect(() => {
    if (!isInOverView || isOpened.current) return;
    api.destroy();

    fetchVersionMaterial()
      .then((res) => {
        const fixturesBanner = res?.images.map((banner) => ({
          ...banner,
          id: uuid()
        }));
        const localStorageVersion = getLocalStorageItem(VERSION_PREFIX) ?? 0;
        const injectVersion = process.env.REACT_APP_VERSION;
        const manifestVersion = res?.version;

        setLocalStorageItem(VERSION_PREFIX, injectVersion);

        // 如果本地存储中没有版本号，或者服务器版本号大于本地存储的版本号，则显示更新日志
        const isShowLog =
          !localStorageVersion || manifestVersion > localStorageVersion;

        if (isShowLog && !!res?.contentList.length) {
          api.open({
            key: '123',
            closeIcon: null,
            icon: null,

            message: null,
            description: (
              <Content
                onClose={() => api.destroy()}
                contentList={res.contentList}
                banners={fixturesBanner}
              />
            ),
            placement: 'bottomRight',
            duration: 0,
            className: styles.version
          });
        }
      })
      .catch((err) => console.log(err))
      .finally(() => {
        isOpened.current = true;
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInOverView]);

  if (!isInOverView) return null;

  return (
    <ConfigProvider
      theme={{
        components: {
          Notification: {
            borderRadiusLG: 12,
            boxShadow: defaultLevel4
          }
        }
      }}
    >
      {contextHolder}
    </ConfigProvider>
  );
};

type ContentType = Pick<ComponentProps<typeof Carousel>, 'banners'> & {
  contentList: string[];
  onClose: () => void;
};

const Content = ({ contentList, onClose, banners }: ContentType) => {
  //useState 中转， 防止闪烁
  const [fixturesBanners, setFixturesBanners] = useState<OverviewBanner[]>([]);

  useEffect(() => {
    setFixturesBanners(banners);
  }, [banners]);

  return (
    <Flex vertical gap={18}>
      {!!fixturesBanners.length && (
        <div className={styles.carousel}>
          <Carousel banners={fixturesBanners} />
        </div>
      )}

      <Flex vertical gap={16}>
        <h3>WHEE更新啦</h3>
        <Flex vertical gap={10}>
          {contentList.map((content) => (
            <div
              className={styles.li}
              dangerouslySetInnerHTML={{ __html: content }}
            />
          ))}
        </Flex>
        <Flex justify="end">
          <Button size="large" onClick={onClose}>
            知道了
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
};
