@import '~@/styles/variables.less';

:global(.ant-notification-notice).version {
  padding: 20px !important;
  width: 360px !important;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100px;
    border-radius: 12px;
    background-image: url('~@/assets/images/version-log-bg.jpg');
    background-size: cover;
    background-position: center center;
  }

  h3 {
    z-index: 3;
    color: @content-system-primary;
    font-size: 20px;
    font-weight: 600;
    line-height: normal;
    margin-bottom: 0;
  }

  .li {
    font-size: 14px;
    line-height: 20px;
    padding-left: 16px;
    color: @content-system-secondary;
    position: relative;

    strong {
      color: @content-system-primary;
      font-weight: 600;
    }

    &::before {
      content: '';
      position: absolute;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #ff70ce;
      left: 0;
      top: 10px;
      transform: translateY(-50%);
    }
  }
  :global(.ant-notification-notice-message) {
    margin-bottom: 0 !important;
  }
}

.carousel {
  width: 100%;
  height: 172px;
}
