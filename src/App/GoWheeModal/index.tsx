import { Flex, Modal } from 'antd';
import { CrossBlack } from '@meitu/candy-icons';
import { useState, forwardRef, useImperativeHandle } from 'react';
import styles from './index.module.less';
import { Button } from '@/components';
import { simulateLinkClick } from '@/utils/simulateLinkClick';

export interface GoWheeModalRef {
  open: () => void;
}

export const GoWheeModal = forwardRef<GoWheeModalRef>((props, ref) => {
  const [open, setOpen] = useState(false);

  useImperativeHandle(ref, () => ({
    open: () => setOpen(true)
  }));

  const close = () => setOpen(false);

  return (
    <Modal
      open={open}
      centered
      destroyOnClose
      closeIcon={<CrossBlack />}
      width={480}
      className={styles.goWhee}
      onCancel={close}
      footer={null}
    >
      <Flex vertical gap={16} align="center">
        <img src={`${process.env.PUBLIC_URL}/wx.cover.png`} alt="whee-logo" />
        <span>
          您的免费体验次数已用完，可前往“WHEE” 继续
          使用“MiracleVision”进行创作。
        </span>
        <Button
          block={false}
          onClick={() => {
            simulateLinkClick(
              process.env.REACT_APP_LINK + '?channel=miraclevision',
              { target: '_self' }
            );
          }}
        >
          去WHEE探索更多玩法
        </Button>
      </Flex>
    </Modal>
  );
});
