import { Modal } from 'antd';
import { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import styles from './index.module.less';
import { Button } from '@/components/Button';
import { useCommonConfigState } from '@/hooks/useCommonConfig';
import { CrossBlack } from '@meitu/candy-icons';
import md5 from 'md5';
import { trackEvent } from '@/services';

export interface NoticePopupRef {
  open: () => void;
}
export const NoticePopup = forwardRef<NoticePopupRef>((props, ref) => {
  const [open, setOpen] = useState(false);
  const [commonConfig] = useCommonConfigState();
  // 添加开始时间状态
  const [startTime, setStartTime] = useState<number | null>(null);
  const close = () => {
    setOpen(false);
    // 计算并保存停留时间
    if (startTime) {
      const duration = Date.now() - startTime;
      // console.log(duration.toString()); //
      trackEvent('operation_popup_duration', {
        duration: duration.toString()
      });
    }
    let noticeString = md5(JSON.stringify(commonConfig?.noticePopup || {}));
    localStorage.setItem('__notice__', noticeString);
  };

  useImperativeHandle(ref, () => ({
    open: () => {
      setOpen(true);
      setStartTime(Date.now()); // 记录开始时间
    }
  }));
  useEffect(() => {}, []);
  return (
    <>
      <Modal
        open={open}
        centered
        destroyOnClose
        maskClosable={false}
        closeIcon={false}
        width={420}
        footer={null}
        rootClassName={styles.noticeModalWrap}
        onCancel={close}
      >
        <div className={styles.noticeModalTopBackground}></div>
        <div className={styles.noticeModalClose} onClick={close}>
          <CrossBlack color="#fff" />
        </div>
        <div className={styles.noticeModalTitle}>WHEE 更新公告</div>
        <div className={styles.noticeModalContent}>
          {commonConfig?.noticePopup.map((item, index) => {
            return (
              <div className={styles.noticeModalContentItem} key={index}>
                <div className={styles.noticeModalContentItemTag}>NEW</div>
                <div className={styles.noticeModalContentItemTc}>
                  <div className={styles.noticeModalContentItemTitle}>
                    {item.title}
                  </div>
                  <div
                    className={styles.noticeModalContentItemContent}
                    dangerouslySetInnerHTML={{
                      __html: item?.content
                    }}
                  >
                    {/* {item.content} */}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        <div className={styles.noticeModalTips}>
          以上，期待与您一起见证WHEE的每一次进步~
        </div>
        <div className={styles.noticeModalFooter} onClick={close}>
          <Button
            style={{
              width: 132,
              height: 40
            }}
          >
            我知道啦
          </Button>
        </div>
      </Modal>
    </>
  );
});
