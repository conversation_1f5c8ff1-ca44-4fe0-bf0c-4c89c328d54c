@import '~@/styles/variables.less';

.notice-modal-wrap {
  :global {
    .@{ant-prefix}-modal {
      border-radius: 16px;
      clip-path: inset(0 round 16px);
      .@{ant-prefix}-modal-content {
        padding: 0;
        padding-bottom: 24px;
      }
    }
  }
  .notice-modal-top-background {
    width: 420px;
    height: 116.667px;
    flex-shrink: 0;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #fff 100%),
      linear-gradient(90deg, #fff2cb 0%, #ffd2f0 100%);
  }
  .notice-modal-close {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 28px;
    height: 28px;
    background-color: rgba(0, 0, 0, 0.25);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
  }
  .notice-modal-title {
    position: absolute;
    top: 36px;
    width: 420px;
    left: 0;
    color: var(--content-systemPrimary, #1c1d1f);
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 22px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    text-align: center;
  }
  .notice-modal-content {
    width: 384px;
    margin: -30px auto 0;
    &-item {
      display: flex;
      flex-direction: row;
      &-tag {
        width: 40px;
        height: 26px;
        display: flex;
        padding: var(--spacing-4, 4px) var(--spacing-6, 6px);
        justify-content: center;
        align-items: center;
        border-radius: var(--radius-6, 6px);
        background: linear-gradient(90deg, #fb23d3 0%, #ff9b44 100%);
        color: #fff;
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        margin-left: 23px;
      }
      &-tc {
        margin-left: 12px;
      }
      &-title {
        color: var(--content-systemPrimary, #1c1d1f);
        font-family: 'PingFang SC';
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
      }
      &-content {
        color: var(--content-editorSecondary, #81838c);
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        width: 286px;
        word-break: keep-all;
      }
    }
  }
  .notice-modal-tips {
    color: var(--content-editorSecondary, #81838c);
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: 24px;
  }
  .notice-modal-footer {
    width: 132px;
    height: 40px;
    margin: 36px auto 0;
    // padding-bottom: 24px;
  }
}
