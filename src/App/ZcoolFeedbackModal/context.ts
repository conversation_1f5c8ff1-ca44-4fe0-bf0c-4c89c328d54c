import { createContext } from 'react';
import { ZcoolFeedbackModalHandle } from '.';

export interface ZcoolContextValues {
  /** 小素材打开意见反馈对话框 */
  openZcoolFeedbackModal: ZcoolFeedbackModalHandle['open'];
}

function initialContextMethod() {
  console.warn('正在使用 `AppContext` 的初始方法');
}

export const ZcoolContext = createContext<ZcoolContextValues>({
  openZcoolFeedbackModal: initialContextMethod
});
