import { CrossBoldOutlined } from '@meitu/candy-icons';
import { Modal } from 'antd';

import { useImperativeHandle, forwardRef, useState } from 'react';
import { accountClientId } from '@/services';
import { appName } from '@/constants';
import styles from './index.module.less';
import { getAccountAccessToken } from '@/services';
import qs from 'qs';
import mtstat from '@meitu/mtstat-sdk';
import {
  setMessengerOrigin,
  bindHandlers,
  businessMessenger
} from '@meitu/feedback-web-bridge/es/business';
import type { SetNavigationBarTitleParams } from '@meitu/feedback-web-bridge/es/business';

export interface ZcoolFeedbackModalHandle {
  open: () => void;
}

/**
 * 意见反馈对话框
 */
export const ZcoolFeedbackModal = forwardRef<ZcoolFeedbackModalHandle>(
  (props, ref) => {
    const [open, setOpen] = useState(false);
    const [modalTitle, setModalTitle] = useState('意见反馈');
    const accessToken = getAccountAccessToken();
    const search = qs.stringify(
      {
        // client_id:  xianlong zhi dao
        // client_id: accountClientId,
        client_id: **********,
        access_token: accessToken,
        appName: '反馈记录',
        gid: mtstat.getDeviceId()
      },
      { addQueryPrefix: true, encode: false }
    );
    const iframeSrc = `${process.env.REACT_APP_FEEDBACK_LINK}${search}`;

    businessMessenger.setTarget(iframeSrc);

    bindHandlers({
      getAppInfo() {
        return {
          gid: mtstat.getDeviceId(),
          language: 'zh_CN'
        };
      },
      // 跨域时必须提供该方法
      getMeituAccountEncryptedToken() {
        return {
          encryptedToken: accessToken
        };
      },
      getUserInfo() {
        return {};
      },
      setNavigationBarTitle({ title }: SetNavigationBarTitleParams) {
        if (typeof title === 'string') {
          setModalTitle(title);
        } else {
          // eslint-disable-next-line no-throw-literal
          throw 'title 参数错误';
        }
      }
    });

    useImperativeHandle(
      ref,
      () => ({
        open() {
          setOpen(true);
        }
      }),
      []
    );

    return (
      <Modal
        className={styles.modal}
        rootClassName={styles.root}
        title={modalTitle}
        open={open}
        footer={null}
        maskClosable={false}
        closeIcon={<CrossBoldOutlined />}
        destroyOnClose
        width={640}
        onCancel={setOpen.bind(null, false)}
      >
        <iframe
          title="意见反馈"
          src={setMessengerOrigin(iframeSrc)}
          frameBorder={0}
        />
      </Modal>
    );
  }
);
