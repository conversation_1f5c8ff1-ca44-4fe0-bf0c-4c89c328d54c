import { forwardRef } from 'react';
import { useAccountTipConfig } from '@/hooks';

import { UserAwardModal, UserAwardModalProps } from '@/components/UserAwardTip';

export interface UserAwardTipRef {
  open: (info?: UserAwardModalProps) => void;
}

export const UserAwardTip = forwardRef<UserAwardTipRef>((props, ref) => {
  const accountTipConfig = useAccountTipConfig();
  return (
    <UserAwardModal
      ref={ref}
      title={accountTipConfig?.newUserAwardTipTitle}
      description={accountTipConfig?.newUserAwardTipDesc}
    />
  );
});
