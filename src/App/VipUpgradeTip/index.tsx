import { Modal } from 'antd';
import { VipButton } from '@/components/VipButton';
import { useState, forwardRef, useImperativeHandle } from 'react';

import styles from './index.module.less';

export const VIP_UPGRADE_TIP_VIEWED = 'aigc-editor:vip-upgrade-tip-viewed';

export interface VipUpgradeTipRef {
  open: () => void;
}

export const VipUpgradeTip = forwardRef<VipUpgradeTipRef>((props, ref) => {
  const [open, setOpen] = useState(false);

  useImperativeHandle(ref, () => ({
    open: () => setOpen(true)
  }));

  const close = () => setOpen(false);

  return (
    <Modal
      open={open}
      centered
      destroyOnClose
      maskClosable
      closeIcon={false}
      footer={
        <VipButton className={styles.vipUpgradeTipConfirm} onClick={close}>
          好的
        </VipButton>
      }
      wrapClassName={styles.vipUpgradeTipWrap}
      onCancel={close}
    />
  );
});
