@import '~@/styles/variables.less';

.vip-upgrade-tip {
  &-wrap {
    :global {
      .@{ant-prefix}-modal {
        width: 441px !important;
        height: 695px;
        margin-top: -92px;

        .@{ant-prefix}-modal-content {
          width: 100%;
          height: 100%;
          background-image: url('~@/assets/images/vip-upgrade.png');
          background-size: contain;
          background-position: center center;
          background-repeat: no-repeat;
          background-color: transparent;
          box-shadow: none;
        }

        .@{ant-prefix}-modal-close {
          display: none;
        }
      }
    }
  }

  &-confirm:global(.@{ant-prefix}-btn) {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    width: calc(100% - 40px);
    height: 40px;
    font-size: 18px;
    font-weight: 500;
  }
}
