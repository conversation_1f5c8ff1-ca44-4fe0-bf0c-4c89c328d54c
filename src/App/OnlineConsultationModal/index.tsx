import { CrossBoldOutlined } from '@meitu/candy-icons';
import { Modal } from 'antd';

import { useImperativeHandle, forwardRef, useState } from 'react';
import { accountClientId, getAccountAccessToken } from '@/services';
import styles from './index.module.less';
import qs from 'qs';
import {
  setMessengerOrigin,
  bindHandlers,
  businessMessenger
} from '@meitu/feedback-web-bridge/es/business';
import type { SetNavigationBarTitleParams } from '@meitu/feedback-web-bridge/es/business';
import mtstat from '@meitu/mtstat-sdk';

export interface OnlineConsultationModalHandle {
  open: () => void;
}

/**
 * 在线咨询对话框
 */
export const OnlineConsultationModal =
  forwardRef<OnlineConsultationModalHandle>((props, ref) => {
    const [open, setOpen] = useState(false);
    const [modalTitle, setModalTitle] = useState('在线咨询');

    const search = qs.stringify(
      {
        client_id: accountClientId
      },
      { addQueryPrefix: true }
    );
    const iframeSrc = `${process.env.REACT_APP_CONSULTATION_LINK}${search}`;

    businessMessenger.setTarget(iframeSrc);

    bindHandlers({
      getAppInfo() {
        return {
          gid: mtstat.getDeviceId(),
          language: 'zh_CN'
        };
      },
      // 跨域时必须提供该方法
      getMeituAccountEncryptedToken() {
        return {
          encryptedToken: getAccountAccessToken()
        };
      },
      getUserInfo() {
        return {};
      },
      setNavigationBarTitle({ title }: SetNavigationBarTitleParams) {
        if (typeof title === 'string') {
          setModalTitle(title);
        } else {
          // eslint-disable-next-line no-throw-literal
          throw 'title 参数错误';
        }
      }
    });

    useImperativeHandle(
      ref,
      () => ({
        open() {
          setOpen(true);
        }
      }),
      []
    );

    return (
      <Modal
        className={styles.modal}
        rootClassName={styles.root}
        title={modalTitle}
        open={open}
        footer={null}
        maskClosable={false}
        closeIcon={<CrossBoldOutlined />}
        destroyOnClose
        width={368}
        mask={false}
        onCancel={setOpen.bind(null, false)}
        getContainer={'#root'}
      >
        <iframe
          title="在线咨询"
          src={setMessengerOrigin(iframeSrc)}
          frameBorder={0}
        />
      </Modal>
    );
  });
