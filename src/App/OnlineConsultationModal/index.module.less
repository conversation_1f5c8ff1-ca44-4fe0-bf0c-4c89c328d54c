@import '@/styles/variables.less';

.root :global .@{ant-prefix}-modal {
  &-mask {
    backdrop-filter: blur(@size-xs);
  }

  &-wrap {
    top: auto !important;
    right: 21px !important;
    bottom: 21px !important;
    left: auto !important;
  }
}

.modal:global(.@{ant-prefix}-modal) {
  top: auto;
  padding-bottom: 0;

  :global .@{ant-prefix}-modal {
    &-header {
      padding-top: 16px;
      padding-left: 25px;
    }

    &-title {
      text-align: left;
    }

    &-content {
      padding: 0;
      height: 572px;
      border-radius: 8px;
      overflow: hidden;
    }

    &-body {
      height: calc(100% - 48px);
    }
  }

  :global {
    .@{ant-prefix}-spin-container,
    .@{ant-prefix}-spin-nested-loading {
      height: 100%;
    }

    iframe {
      width: 100%;
      height: 100%;
    }
  }
}
