@import '@/styles/variables.less';

.root :global .@{ant-prefix}-modal {
  &-mask {
    backdrop-filter: blur(@size-xs);
  }

  &-wrap {
    display: flex;
    align-items: center;
  }
}

.modal:global(.@{ant-prefix}-modal) {
  top: auto;
  padding-bottom: 0;

  :global .@{ant-prefix}-modal {
    &-header {
      padding: 8px 0;
    }

    &-title {
      text-align: center;
    }

    &-content {
      padding: 0;
      height: 575px;
      border-radius: 8px;
      overflow: hidden;
    }

    &-body {
      height: calc(100% - 48px);
    }
  }

  :global {
    .@{ant-prefix}-spin-container,
    .@{ant-prefix}-spin-nested-loading {
      height: 100%;
    }

    iframe {
      width: 100%;
      height: 100%;
    }
  }
}
