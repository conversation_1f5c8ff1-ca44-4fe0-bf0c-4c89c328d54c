@import '~@/styles/variables.less';

.root :global .@{ant-prefix}-modal {
  &-mask {
    backdrop-filter: blur(@size-xs);
  }

  &-wrap {
    display: flex;
    align-items: center;
  }
}

.refresh:global(.@{ant-prefix}-modal) {
  :global {
    .ant-modal-content {
      padding: 36px 16px 16px;

      .ant-modal-body {
        text-align: center;

        .title {
          color: @content-system-primary;
          text-align: center;
          font-size: 16px;
          font-weight: 600;
          line-height: 22px;
        }

        .ant-btn {
          width: 100%;
          height: 36px;
          margin-top: 28px;
          color: @content-btn-primary;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
        }
      }
    }
  }
}
