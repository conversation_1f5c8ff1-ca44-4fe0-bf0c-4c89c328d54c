import { Modal } from 'antd';
import { useState, forwardRef, useImperativeHandle } from 'react';
import styles from './index.module.less';
import { Button } from '@/components';

export interface RefreshModalRef {
  open: () => void;
}

export const RefreshModal = forwardRef<RefreshModalRef>((props, ref) => {
  const [open, setOpen] = useState(false);

  useImperativeHandle(ref, () => ({
    open: () => setOpen(true)
  }));

  const close = () => setOpen(false);

  return (
    <Modal
      open={open}
      centered
      destroyOnClose
      width={300}
      closeIcon={null}
      className={styles.refresh}
      rootClassName={styles.root}
      onCancel={close}
      footer={null}
      maskClosable={false}
    >
      <span className="title">您离开WHEE太久啦，请刷新页面继续创作！</span>
      <Button
        block={false}
        onClick={() => {
          window.location.reload();
        }}
      >
        刷新
      </Button>
    </Modal>
  );
});
