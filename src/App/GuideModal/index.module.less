@import '~@/styles/variables.less';

.guide-modal-wrap {
  :global {
    .@{ant-prefix}-modal {
      border-radius: 16px;

      .@{ant-prefix}-modal-content {
        padding: 0;
      }
    }
  }

  .guide-modal-top-backGround {
    width: 100%;
    height: 320px;
    background: rgba(64, 85, 128, 0.06);
    cursor: pointer;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .guideModalClose {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 28px;
    height: 28px;
    background-color: rgba(0, 0, 0, 0.25);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
  }

  .guideFooter {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 28px; //
    padding-bottom: 28px; //

    div {
      margin: 0 8px;
      border-radius: 8px;
      height: 40px;
      width: 160px; //
      color: #131314;
      text-align: center;
      line-height: 40px;
      background-color: rgba(20, 31, 51, 0.12);
      cursor: pointer;

      &:last-child {
        color: #fff;
        background-color: #3549ff;
      }
    }
  }

  .guideModalLoginAfter {
    padding: 8px;
    padding-top: 28px;

    .guideModalCarousel {
      font-size: 16px;
      background: rgba(64, 85, 128, 0.06);
      border-radius: 8px;
    }

    .guideModalClose {
      color: rgba(177, 182, 191, 1);
      background-color: rgba(0, 0, 0, 0);
      width: 16px;
      height: 16px;
      top: 16px;
      right: 16px;
    }

    .guideModalTopHeader {
      img {
        width: 76px;
        height: auto;
        display: block;
        margin: 0 auto;
        margin-bottom: 16px;
      }

      h3 {
        text-align: center;
        color: #131314;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        margin-bottom: 8px;
      }

      ul {
        list-style: none;
        padding-left: 0;
        margin-bottom: 24;

        li {
          text-align: center;
          font-size: 12px;
          font-weight: 400;
          line-height: 17px;
          color: #7f828a;
        }
      }

      p {
        text-align: center;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 10px;
      }
    }
  }
}
