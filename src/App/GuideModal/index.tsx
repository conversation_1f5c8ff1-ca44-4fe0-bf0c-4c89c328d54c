import { Modal } from 'antd';
import { useState, forwardRef, useImperativeHandle } from 'react';
import { CrossBlack } from '@meitu/candy-icons';
import styles from './index.module.less';
// import { useNavigate, Link } from 'react-router-dom';
import { Link } from 'react-router-dom';
export const MGM_ACCEPT_VIEWED = 'aigc-editor:guide-viewed';
export interface GuideModalRef {
  open: () => void;
}
export const GuideModal = forwardRef<GuideModalRef>((props, ref) => {
  // const navigate = useNavigate();

  const [open, setOpen] = useState(false);
  // const {  } = useGuideModal();
  useImperativeHandle(ref, () => ({
    open: () => setOpen(true)
  }));
  const close = () => setOpen(false);
  const goPage = () => {
    close();
    // navigate('/tutorial');
    // window.location.href='/tutorial'
  };
  const bgImg =
    'https://titan-h5.meitu.com/whee/assets/gamePlay/image2image/image1.png';
  const toURL = '/tutorial';
  return (
    <>
      <Modal
        open={open}
        centered
        destroyOnClose
        maskClosable
        closeIcon={false}
        width={560}
        footer={null}
        wrapClassName={styles.guideModalWrap}
        onCancel={close}
      >
        <>
          <Link to={toURL}>
            <div
              className={styles.guideModalTopBackGround}
              onClick={goPage}
              style={{ backgroundImage: `url(${bgImg}) ` }}
            ></div>
          </Link>
          <div className={styles.guideModalClose} onClick={close}>
            <CrossBlack />
          </div>
          <div className={styles.guideFooter}>
            <Link to={toURL}>
              <div onClick={goPage}>立即体验</div>
            </Link>
          </div>
        </>
      </Modal>
    </>
  );
});
