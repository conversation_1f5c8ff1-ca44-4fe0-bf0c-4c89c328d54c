@import '~@/styles/variables.less';

.mgm-accept-modal-wrap {
  :global {
    .@{ant-prefix}-modal {
      border-radius: 16px;
      clip-path: inset(0 round 16px);
      .@{ant-prefix}-modal-content {
        padding: 0;
      }
    }
  }

  .mgm-accept-modal-top-backGround {
    width: 100%;
    height: 240px;
    background: rgba(64, 85, 128, 0.06);
  }
  .carouselItem {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
  }
  .carouselItemHeader {
    height: 240px;
  }
  .carouselItemFooter {
    height: 230px;
    cursor: pointer;
  }
  .mgmAcceptModalClose {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 28px;
    height: 28px;
    background-color: rgba(0, 0, 0, 0.25);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
  }

  .mgmAcceptModalContent {
    color: #131314;
    text-align: center;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    padding-top: 24px;
    padding-bottom: 24px;
    margin-bottom: 0;
  }

  .mgmAcceptFooter {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 28px; //

    div {
      margin: 0 8px;
      border-radius: 8px;
      height: 40px;
      width: 160px; //
      color: #131314;
      text-align: center;
      line-height: 40px;
      background-color: rgba(20, 31, 51, 0.12);
      cursor: pointer;

      &:last-child {
        color: #fff;
        background-color: #3549ff;
      }
    }
  }

  .mgmAcceptModalLoginAfter {
    padding: 8px;
    padding-top: 28px;

    .mgmAcceptModalCarouselBox {
      .mgmAcceptModalCarousel {
        font-size: 16px;
        background: rgba(64, 85, 128, 0.06);
        clip-path: inset(0 round 8px);
      }
      border-radius: 9px;

      border: 1px solid rgba(19, 19, 20, 0.08);
    }

    .mgmAcceptModalClose {
      color: rgba(177, 182, 191, 1);
      background-color: rgba(0, 0, 0, 0);
      width: 16px;
      height: 16px;
      top: 16px;
      right: 16px;
    }

    .mgmAcceptModalTopHeader {
      img {
        width: 76px;
        height: auto;
        display: block;
        margin: 0 auto;
        margin-bottom: 16px;
      }

      h3 {
        text-align: center;
        color: #131314;
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        margin-bottom: 8px;
      }

      ul {
        list-style: none;
        padding-left: 0;
        margin-bottom: 24;

        li {
          text-align: center;
          font-size: 12px;
          font-weight: 400;
          line-height: 17px;
          color: #7f828a;
        }
      }

      p {
        text-align: center;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 10px;
      }
    }
  }
}
