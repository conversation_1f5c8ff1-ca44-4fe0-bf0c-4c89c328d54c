import { Modal } from 'antd';
import { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { CrossBlack } from '@meitu/candy-icons';
import styles from './index.module.less';
import { MGMCarousel } from './components/Carousel';
import meidouAward from '@/assets/images/mgmMeidou.png';
import { useMGMModal } from '@/hooks/useMGMModal';
import { useAccount, useAccountTipConfig } from '@/hooks';
import { useNavigate } from 'react-router-dom';
import { trackEvent } from '@/services';

export const MGM_ACCEPT_VIEWED = 'aigc-editor:mgm-accept-viewed';
export const MGM_ACCEPT_SHARE_CODE = 'aigc-editor:mgm-accept-share-code';
export interface MGMAcceptModalRef {
  open: () => void;
}
export const MGMAcceptModal = forwardRef<MGMAcceptModalRef>((props, ref) => {
  const [open, setOpen] = useState(false);
  const { login } = useAccount();
  const navigate = useNavigate();
  const {
    unLoginShareAcceptData,
    shareAcceptDate,
    shareAcceptType,
    loginShareAcceptData,
    MGMSetStorageCode,
    MGMRemoveStorageCode
  } = useMGMModal();
  const accountTipConfig = useAccountTipConfig();
  const { token } = useAccount();
  useEffect(() => {
    if (!token) unLoginShareAcceptData(accountTipConfig);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token]);
  useEffect(() => {
    // 登入状态下 若newUserAwardTip 为false 则不是新用户 此时要弹出邀请
    loginShareAcceptData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accountTipConfig?.showNewUserAwardTip]);
  useImperativeHandle(ref, () => ({
    open: () => setOpen(true)
  }));
  const close = () => {
    setOpen(false);
    if (!shareAcceptType) {
      MGMSetStorageCode();
    }
  };

  const loginModal = () => {
    return (
      <>
        <div className={styles.mgmAcceptModalTopBackGround}>
          <MGMCarousel>
            {Array.isArray(shareAcceptDate?.picArry) &&
              shareAcceptDate?.picArry.map(
                (cornerLabelUrl: string, key: number) => {
                  return (
                    <div key={key}>
                      <div
                        className={`${styles.carouselItem} ${styles.carouselItemHeader}`}
                        style={{ backgroundImage: `url(${cornerLabelUrl})` }}
                      >
                        {' '}
                      </div>
                    </div>
                  );
                }
              )}
          </MGMCarousel>
        </div>
        <div className={styles.mgmAcceptModalClose} onClick={close}>
          <CrossBlack />
        </div>
        <p className={styles.mgmAcceptModalContent}>
          {/* xxx邀请你免费体验WHEE啦！！ */}
          {shareAcceptDate?.text}
        </p>
        <div className={styles.mgmAcceptFooter}>
          <div
            onMouseDown={(event) => {
              event.preventDefault();
              MGMRemoveStorageCode();
              trackEvent('user_invited_popup_click', {
                click_type: 'login'
              });
              login();
            }}
          >
            登录领取奖励
          </div>
        </div>
      </>
    );
  };
  const loginModalAfter = () => {
    return (
      <>
        <div className={styles.mgmAcceptModalLoginAfter}>
          <div className={styles.mgmAcceptModalClose} onClick={close}>
            <CrossBlack />
          </div>
          <div className={styles.mgmAcceptModalTopHeader}>
            <img src={meidouAward} alt="meidou-award" />
            <h3>{accountTipConfig?.title}</h3>
            <ul>
              {/* <li>邀请拉新活动：奖励20美豆</li> */}
              {/* <li>{accountTipConfig?.inviteAwardTipTitle}</li> */}
              {/* <li>新用户连册：买励30美豆</li> */}
              <li>{accountTipConfig?.newUserAwardTipTitle}</li>
            </ul>
            <p>{accountTipConfig?.desc}</p>
          </div>
          <div className={styles.mgmAcceptModalCarouselBox}>
            <div className={styles.mgmAcceptModalCarousel}>
              <MGMCarousel autoplaySpeed={5000}>
                {accountTipConfig?.picArry?.map(
                  (linkData: { url: string; pic: string }, key) => {
                    const { url, pic } = linkData;
                    return (
                      <div
                        onClick={() => {
                          close();
                          trackEvent('user_invited_popup_click', {
                            click_type: 'card',
                            url: url
                            // TODO card_type动态配置  只有url和图片
                          });
                          setTimeout(() => {
                            if (
                              url.includes('https://') ||
                              url.includes('http://')
                            ) {
                              window.location.href = url;
                            } else {
                              navigate(url);
                            }
                          }, 0);
                        }}
                      >
                        {/* <Link to={url}> */}
                        <div
                          className={`${styles.carouselItem} ${styles.carouselItemFooter}`}
                          style={{ backgroundImage: `url(${pic})` }}
                        >
                          {' '}
                        </div>
                        {/* </Link> */}
                      </div>
                    );
                  }
                )}
              </MGMCarousel>
            </div>
          </div>
        </div>
      </>
    );
  };
  return (
    <>
      <Modal
        open={open}
        centered
        destroyOnClose
        maskClosable={false}
        closeIcon={false}
        width={420}
        footer={null}
        wrapClassName={styles.mgmAcceptModalWrap}
        onCancel={close}
      >
        {shareAcceptType ? loginModalAfter() : loginModal()}
      </Modal>
    </>
  );
});
