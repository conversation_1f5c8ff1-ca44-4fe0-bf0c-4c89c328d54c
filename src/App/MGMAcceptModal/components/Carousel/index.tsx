import { Carousel } from 'antd';
import { forwardRef, useImperativeHandle, useRef, ReactElement } from 'react';
import { ChevronLeftBold, ChevronRightBold } from '@meitu/candy-icons';
import type { CarouselRef } from 'antd/es/carousel';
import styles from './index.module.less';
export interface MGMCarouselRef {}
export interface MGMCarouselProps {
  children?: ReactElement | ReactElement[];
  autoplaySpeed?: number;
}
export const MGMCarousel = forwardRef<MGMCarouselRef, MGMCarouselProps>(
  (props, ref) => {
    const { autoplaySpeed = 3000 } = props;
    const carouselRef = useRef<CarouselRef>(null);
    useImperativeHandle(ref, () => ({}));

    const prev = (event: React.MouseEvent<HTMLDivElement>) => {
      event.preventDefault();
      carouselRef.current?.prev();
    };
    const next = (event: React.MouseEvent<HTMLDivElement>) => {
      event.preventDefault();
      carouselRef.current?.next();
    };

    return (
      <div className={styles.MGMCarousel}>
        <Carousel
          infinite={true}
          autoplay
          dots={false}
          autoplaySpeed={autoplaySpeed}
          ref={carouselRef}
        >
          {props?.children}
        </Carousel>
        <div
          onMouseDown={prev}
          className={`${styles.MGMCarouselLeftBtn} ${styles.MGMCarouselBtn}`}
        >
          <ChevronLeftBold />
        </div>
        <div
          onMouseDown={next}
          className={`${styles.MGMCarouselRightBtn} ${styles.MGMCarouselBtn}`}
        >
          <ChevronRightBold />
        </div>
      </div>
    );
  }
);
