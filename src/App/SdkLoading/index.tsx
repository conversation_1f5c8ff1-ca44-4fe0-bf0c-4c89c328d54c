import { Spin } from 'antd';
import { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import styles from './index.module.less';
import { Loading } from '@/components';
import { useParams, useNavigate } from 'react-router-dom';
import { isWebSDK } from '@/utils';
import { useSearchParams } from '@/hooks';

import { account } from '@/services/account';

export interface SdkLoadingRef {
  open: () => void;
  close: () => void;
}

export const SdkLoading = forwardRef<SdkLoadingRef>((props, ref) => {
  const [open, setOpen] = useState(false);
  const { accessKey } = useSearchParams();
  const query = new URLSearchParams(window.location.search);
  const navigate = useNavigate();
  useImperativeHandle(ref, () => ({
    open: () => setOpen(true),
    close: () => setOpen(false)
  }));

  //   console.log('accessKey', accessKey);
  useEffect(() => {
    if (accessKey) {
      // account.requestRedirectAccount()
      //   setTimeout(() => {
      //     navigate(`/ai/image-inpaint-sdk?${query.toString()}`, { replace: true });
      //   }, 3000);
    }
  }, []);

  const close = () => {
    setOpen(false);
  };

  return (
    <div
      className={styles.globalLoading}
      style={{ display: open ? 'block' : 'none' }}
    >
      <Spin
        spinning={open}
        indicator={<Loading />}
        className={styles.globalLoading}
        tip="页面加载中..."
      ></Spin>
    </div>
  );
});
