import { CrossBlackOutlined } from '@meitu/candy-icons';
import { Modal } from 'antd';

import { useImperativeHandle, forwardRef, useState } from 'react';
import styles from './index.module.less';

export interface BusinessCooperationModalHandle {
  open: () => void;
}

export const BusinessCooperationModal =
  forwardRef<BusinessCooperationModalHandle>(function BusinessCooperationModal(
    props,
    ref
  ) {
    const [open, setOpen] = useState(false);

    useImperativeHandle(
      ref,
      () => ({
        open() {
          setOpen(true);
        }
      }),
      []
    );

    return (
      <Modal
        rootClassName={styles.root}
        wrapClassName={styles.wrap}
        className={styles.modal}
        title="商务合作"
        width={300}
        open={open}
        closeIcon={<CrossBlackOutlined />}
        footer={
          <>
            邮箱地址：
            <a href="mailto:<EMAIL>" target="_blank" rel="noreferrer">
              <EMAIL>
            </a>
          </>
        }
        destroyOnClose
        maskClosable={false}
        onCancel={setOpen.bind(null, false)}
      >
        <p>
          感谢您对WHEE的关注！我们欢迎新的合作伙伴，若您有任何合作意向或想要了解我们请发送电子邮件至以下地址。我们会尽快回复，期待与您的合作！
        </p>
      </Modal>
    );
  });
