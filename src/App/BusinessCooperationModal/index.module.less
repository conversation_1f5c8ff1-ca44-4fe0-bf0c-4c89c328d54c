@import '~@/styles/variables.less';

.root :global .@{ant-prefix}-modal-mask {
  background: @background-system-overlay-mask;
  backdrop-filter: blur(@size-xxs);
}

.wrap:global(.@{ant-prefix}-modal-wrap) {
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal:global(.@{ant-prefix}-modal) {
  top: 0;

  :global .@{ant-prefix}-modal {
    &-header {
      margin: 0;
      background: transparent;
    }

    &-title {
      font-size: @text-18-bold;
      font-weight: 600;
      text-align: center;
      color: @content-system-primary;
    }

    &-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: @size-md;
      border-radius: @size-sm;
      height: 230px;
      box-shadow: @level-4;

      // prettier-ignore
      background: @background-system-frame-floatpanel url('./bg.svg') no-repeat 50% 0 / contain;
    }

    &-body {
      color: @content-system-primary;

      p {
        margin-bottom: 0;
      }
    }

    &-footer {
      margin: 0;
      padding: @size-xs;
      border-radius: @size-xs;
      width: 100%;
      background: @background-btn-hover;
      text-align: center;

      a {
        color: @content-system-link;

        &:hover {
          color: @content-system-link-hover;
        }
      }
    }
  }
}
