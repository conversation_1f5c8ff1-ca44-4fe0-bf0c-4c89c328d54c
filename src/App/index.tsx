import type { BusinessCooperationModalHandle } from './BusinessCooperationModal';
import type { BetaInvitationModalHandle } from './BetaInvitationModal';
import type { FeedbackModalHandle } from './FeedbackModal';
import type { OnlineConsultationModalHandle } from './OnlineConsultationModal';
import type { VipUpgradeTipRef } from './VipUpgradeTip';
import type { OccupationalInformationTipRef } from './OccupationalInformationTip';
import type { UserAwardTipRef } from './UserAwardTip';
import type { ReportModalHandle } from './ReportModal';
import type { ContactModalHandle } from './ContactModal';
import type { AppContextValues } from './context';

import { TrainingNotification } from '@/components/TrainingNotification';
import { BusinessCooperationModal } from './BusinessCooperationModal';
import { BetaInvitationModal } from './BetaInvitationModal';
import { FeedbackModal } from './FeedbackModal';
import { OnlineConsultationModal } from './OnlineConsultationModal';
import { ReportModal } from './ReportModal';
import { VIP_UPGRADE_TIP_VIEWED, VipUpgradeTip } from './VipUpgradeTip';
import { OccupationalInformationTip } from './OccupationalInformationTip';
import { UserAwardTip } from './UserAwardTip';
import { AppOrigin, appOrigin } from '@/constants';
import { Prompt } from '@/components/Prompt';
import { Outlet, useLocation } from 'react-router-dom';
import { AppContext } from './context';

import { useContext, useLayoutEffect, useMemo, useRef } from 'react';
import { useAccountProfileEffect } from '@/hooks';
import { useMeiDouBalance } from '@/hooks/useMeiDou';
import { initTracer } from '@/services';
import { ContactModal } from './ContactModal';
import { useViewportCheckEffect } from '@/hooks/useViewport';
import { ExternalGiftTip, ExternalGiftTipRef } from './ExternalGiftTip/index';
import { getLocalStorageItem, setLocalStorageItem } from '@meitu/util';
import { GoWheeModal, GoWheeModalRef } from './GoWheeModal';
import { RefreshModal, RefreshModalRef } from './RefreshModal';
import { getCanExpireLocal } from '@/utils/storage';
// import { VersionLogModal } from './VersionLogModal';
// import { MGMShareModal, MGMShareModalRef } from './MGMShareModal';
import { MGMAcceptModal, MGMAcceptModalRef } from './MGMAcceptModal';
import { GuideModal, GuideModalRef } from './GuideModal';
import { SdkLoading, SdkLoadingRef } from './SdkLoading';
import { NoticePopup, NoticePopupRef } from './NoticePopup';
import { shouldShowNoticePopup } from './NoticePopup/utils';
import { IPLoraTrainingNotify } from '@/containers/IPCharacter/components/IPLoraTrainingNotify';
import { useSetCommonConfig } from '@/hooks/useCommonConfig';
import { fetchCommonConfig } from '@/api';
import md5 from 'md5';

export function useApp() {
  return useContext(AppContext);
}

export function App() {
  const betaInvitationModalRef = useRef<BetaInvitationModalHandle>(null);
  const businessCooperModalRef = useRef<BusinessCooperationModalHandle>(null);
  const feedbackModalRef = useRef<FeedbackModalHandle>(null);
  const onlineConsultationModalRef =
    useRef<OnlineConsultationModalHandle>(null);
  const reportModalRef = useRef<ReportModalHandle>(null);
  const vipUpgradeTipRef = useRef<VipUpgradeTipRef>(null);
  const OccupationalInformationTipRef =
    useRef<OccupationalInformationTipRef>(null);
  const userAwardTipRef = useRef<UserAwardTipRef>(null);
  const externalGiftTipRef = useRef<ExternalGiftTipRef>(null);
  const contactModalRef = useRef<ContactModalHandle>(null);
  const goWheeModalRef = useRef<GoWheeModalRef>(null);
  const refreshModalRef = useRef<RefreshModalRef>(null);
  // const MGMShareModalRef = useRef<MGMShareModalRef>(null);
  const MGMAcceptModalRef = useRef<MGMAcceptModalRef>(null);
  const guideModalRef = useRef<GuideModalRef>(null);
  const NoticePopupRef = useRef<NoticePopupRef>(null);
  const SdkLoadingRef = useRef<SdkLoadingRef>(null);

  // 局部修改
  // 获取iframe页面参数是否有access_key

  const { search } = useLocation();
  const accessKey = new URLSearchParams(search).get('accessKey');
  // console.log(accessKey, 'accessKey =>>>>>>>');

  const { updateMeiDouBalance } = useMeiDouBalance();

  const appContextValues = useMemo<AppContextValues>(
    () => ({
      openBetaInvitationModal() {
        betaInvitationModalRef.current?.open();
      },

      openBusinessCooperationModal() {
        businessCooperModalRef.current?.open();
      },

      openFeedbackModal() {
        feedbackModalRef.current?.open();
      },

      openOnlineConsultationModal() {
        onlineConsultationModalRef.current?.open();
      },

      openReportModal(id) {
        reportModalRef.current?.open(id);
      },

      openContactModal() {
        contactModalRef.current?.open();
      },

      openGoWheeModal() {
        goWheeModalRef.current?.open();
      },
      openMGMShareModal() {
        // MGMShareModalRef.current?.open();
      },
      openMGMAcceptModal() {
        MGMAcceptModalRef.current?.open();
      },
      openRefreshModal() {
        refreshModalRef.current?.open();
      },
      openGuideModal() {
        guideModalRef.current?.open();
      },
      openNoticeModal() {
        NoticePopupRef.current?.open();
      },
      openSdkLoading() {
        SdkLoadingRef.current?.open();
      },
      closeSdkLoading() {
        SdkLoadingRef.current?.close();
      }
    }),
    []
  );
  const { pathname } = useLocation();
  const { setCommonConfig } = useSetCommonConfig();
  useLayoutEffect(() => {
    initTracer();
    // if (accessKey) {
    //   SdkLoadingRef.current?.open();
    // }
  }, []);

  // 全局加载公用的配置
  // useFetchCommonConfig();

  // 全局加载一次用户信息即可
  useAccountProfileEffect({
    onFetchSuccess: async (res) => {
      if (appOrigin !== AppOrigin.Whee) return;
      if (window.location.href.toLowerCase().includes('zcoolmaterial')) return;
      if (
        window.location.href.toLowerCase().includes('websdk.whee') ||
        window.location.href.toLowerCase().includes('third-party-callback')
      )
        return;
      let config = await fetchCommonConfig();
      setCommonConfig(config);

      const openNoticePopupHandler = () => {
        if (pathname === '/' && config && shouldShowNoticePopup(config)) {
          NoticePopupRef.current?.open();
          // 存储当前notice的hash，避免重复显示
          const currentNoticeHash = md5(JSON.stringify(config.noticePopup));
          localStorage.setItem('__notice__', currentNoticeHash);
          return true; // 返回true表示显示了通知
        }
        return false;
      };
      //  GuideModalRef.current?.open();
      // 权益变动有限提示

      if (res.accountTipConfig?.showVipUpgradeTip) {
        const vipUpgradeTipViewed = getLocalStorageItem<boolean>(
          VIP_UPGRADE_TIP_VIEWED
        );

        if (!vipUpgradeTipViewed) {
          vipUpgradeTipRef.current?.open();
          setLocalStorageItem(VIP_UPGRADE_TIP_VIEWED, true);
        }
      }

      // 用户职业收集信息弹窗
      const openOccupationalInformationHandler = () => {
        const hasSubmitJobInfo = getCanExpireLocal('hasSubmitJobInfo');
        if (
          !res.editorModuleAccessibility?.hasSubmitJobInfo &&
          pathname === '/' &&
          !hasSubmitJobInfo
        ) {
          OccupationalInformationTipRef.current?.open();
        }
      };
      if (res.accountTipConfig?.showNewUserAwardTip) {
        // 更新美豆信息
        updateMeiDouBalance();
        //  弃用 使用 MGMAcceptModal 替代
        // userAwardTipRef.current?.open({
        //   callback: openOccupationalInformationHandler
        // });
      } else {
        if (openNoticePopupHandler()) {
        } else {
          openOccupationalInformationHandler();
        }
      }

      if (res.accountTipConfig?.showZcoolNewUserAwardTip) {
        // 更新美豆信息
        updateMeiDouBalance();
        externalGiftTipRef.current?.open();
      }
    },
    onFetchFail: async (error) => {
      // console.log(error, 'error =>>>>>>>');
      if (appOrigin !== AppOrigin.Whee) return;
      if (window.location.href.toLowerCase().includes('zcoolmaterial')) return;
      if (
        window.location.href.toLowerCase().includes('websdk.whee') ||
        window.location.href.toLowerCase().includes('third-party-callback')
      )
        return;
      let config = await fetchCommonConfig();
      setCommonConfig(config);

      const openNoticePopupHandler = () => {
        if (pathname === '/' && config && shouldShowNoticePopup(config)) {
          NoticePopupRef.current?.open();
          // 存储当前notice的hash，避免重复显示
          const currentNoticeHash = md5(JSON.stringify(config.noticePopup));
          localStorage.setItem('__notice__', currentNoticeHash);
          return true; // 返回true表示显示了通知
        }
        return false;
      };
      openNoticePopupHandler();
    }
  });

  // 视口检测包含-设备尺寸检查
  useViewportCheckEffect();

  return (
    <AppContext.Provider value={appContextValues}>
      {/* 训练模型消息管理 */}
      {appOrigin !== AppOrigin.Designer &&
        !window.location.href.toLowerCase().includes('zcoolmaterial') && (
          <TrainingNotification />
        )}

      {/* ip形象定制模型训练站内通知 */}
      {appOrigin !== AppOrigin.Designer &&
        !window.location.href.toLowerCase().includes('zcoolmaterial') && (
          <IPLoraTrainingNotify />
        )}

      {/* 路由跳转提示 */}
      <Prompt />

      <Outlet />

      {/* 内测邀请对话框 */}
      <BetaInvitationModal ref={betaInvitationModalRef} />

      {/* 商务合作对话框 */}
      <BusinessCooperationModal ref={businessCooperModalRef} />

      {/** 意见反馈弹窗 */}
      <FeedbackModal ref={feedbackModalRef} />

      {/** 在线咨询弹窗 */}
      <OnlineConsultationModal ref={onlineConsultationModalRef} />

      {/** 举报弹窗 */}
      <ReportModal ref={reportModalRef} />

      {/* 美豆到账弹窗 */}
      <UserAwardTip ref={userAwardTipRef} />
      <ExternalGiftTip ref={externalGiftTipRef} />

      {/* 权益变动弹窗 */}
      <VipUpgradeTip ref={vipUpgradeTipRef} />
      {/*用户职业收集信息弹窗*/}
      <OccupationalInformationTip
        ref={OccupationalInformationTipRef}
      ></OccupationalInformationTip>

      {/* 大模型官网 联系方式弹窗 */}
      {appOrigin === AppOrigin.MiracleVision && (
        <>
          <ContactModal ref={contactModalRef} />
          <GoWheeModal ref={goWheeModalRef} />
        </>
      )}

      {/* 强制刷新弹窗 */}
      <RefreshModal ref={refreshModalRef} />

      {/* 拉新活动分享弹窗 */}
      {/* <MGMShareModal ref={MGMShareModalRef} /> */}
      {/* 拉新活动被邀请 */}
      <MGMAcceptModal ref={MGMAcceptModalRef} />
      {/* 运营引导弹窗 */}
      <GuideModal ref={guideModalRef} />
      {/* sdk loading */}
      <SdkLoading ref={SdkLoadingRef} />
      {/** 版本更新弹窗 */}
      {appOrigin === AppOrigin.Whee && <NoticePopup ref={NoticePopupRef} />}
      {/* {appOrigin === AppOrigin.Whee && <VersionLogModal />} */}
    </AppContext.Provider>
  );
}
