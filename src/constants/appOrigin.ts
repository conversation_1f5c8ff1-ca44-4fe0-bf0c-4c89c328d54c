import { getLocalStorageItem } from '@meitu/util';

/** 应用源 */
export enum AppOrigin {
  /** Whee */
  Whee = 'whee',

  /** 设计室 */
  Designer = 'designer',

  /** 美图大模型 */
  MiracleVision = 'miracle-vision'
}

/**
 * 获取应用源
 * @param appOriginViaEnv 应用源 (从环境变量中获取)
 * @param hostname
 */
function getAppOrigin(
  appOriginViaEnv: typeof process.env.REACT_APP_ORIGIN,
  hostname = window.location.hostname
) {
  if (appOriginViaEnv !== 'auto') {
    switch (appOriginViaEnv) {
      case 'designer': {
        return AppOrigin.Designer;
      }

      case 'miracle-vision': {
        return AppOrigin.MiracleVision;
      }

      case 'whee':
      default: {
        return AppOrigin.Whee;
      }
    }
  }

  if (
    hostname.endsWith('design.meitu.com') ||
    hostname.endsWith('x-design.com')
  ) {
    return AppOrigin.Designer;
  }

  if (hostname.endsWith('miraclevision.com')) {
    return AppOrigin.MiracleVision;
  }

  // 默认即为 Whee
  // if (hostname.endsWith('whee.com')) {
  //   return AppOrigin.Whee;
  // }

  // DEBUG
  if (getLocalStorageItem('debug:miraclevision')) {
    console.log('DEBUG 开发大模型应用来源');
    return AppOrigin.MiracleVision;
  }

  return AppOrigin.Whee;
}

/**
 * 应用源
 * @description 仅可作为 shim / polyfill / glue 层源码的一部分
 */
export const appOrigin = getAppOrigin(process.env.REACT_APP_ORIGIN);
