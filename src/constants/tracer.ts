import { AppModuleParam } from '@/types';
import { AppOrigin, appOrigin } from './appOrigin';
import { AppModule, getAppModulePath } from '@/services';

/**
 * 获取统计埋点 app_key
 * @param appOrigin 应用源
 */
export function getTracerAppKey(appOrigin: AppOrigin) {
  switch (appOrigin) {
    case AppOrigin.Designer: {
      return process.env.REACT_APP_DESIGNER_TRACER_APP_KEY;
    }

    case AppOrigin.MiracleVision: {
      return process.env.REACT_APP_MIRACLE_VISION_TRACER_APP_KEY;
    }

    case AppOrigin.Whee:
    default: {
      return process.env.REACT_APP_TRACER_APP_KEY;
    }
  }
}

/**
 * 页面类型
 * @description {@link https://dayu.tatstm.com/requirement/requirement?id=1945&appId=209&platformAgg=WEB&fullScreen=true&tableName=sdk_sdz_web&databaseName=stat_sdk}
 * 参数变更
 * @description {@link https://dayu.tatstm.com/requirement/requirement?id=2496&appId=209&platformAgg=WEB&fullScreen=true}
 */
export const moduleDictionary = new Map<string, AppModuleParam>([
  [getAppModulePath(AppModule.Overview), AppModuleParam.Overview],
  [getAppModulePath(AppModule.Art), AppModuleParam.Art],
  [getAppModulePath(AppModule.TextToImage), AppModuleParam.TextToImage],
  [getAppModulePath(AppModule.ImageToImage), AppModuleParam.ImageToImage],
  [getAppModulePath(AppModule.TextGenerator), AppModuleParam.TextGenerator],
  [
    getAppModulePath(AppModule.StyleModelTraining),
    AppModuleParam.StyleModelTraining
  ],
  [getAppModulePath(AppModule.Tutorial), AppModuleParam.Tutorial],
  [getAppModulePath(AppModule.ImageExtension), AppModuleParam.ImageExtension],
  [getAppModulePath(AppModule.DownloadPage), AppModuleParam.DownloadPage],
  [
    getAppModulePath(AppModule.ImagePartialRepaint),
    AppModuleParam.ImagePartialRepaint
  ],
  [
    getAppModulePath(AppModule.ImagePartialRepaintSdk),
    AppModuleParam.ImagePartialRepaint
  ],
  [getAppModulePath(AppModule.Personal), AppModuleParam.Personal],
  [getAppModulePath(AppModule.SearchResult), AppModuleParam.SearchResult],
  [getAppModulePath(AppModule.ImageAIModel), AppModuleParam.ImageAIModel],
  [getAppModulePath(AppModule.ImageToVideo), AppModuleParam.ImageToVideo],
  [getAppModulePath(AppModule.ImageUpscale), AppModuleParam.ImageUpscale],
  [getAppModulePath(AppModule.ImageEraser), AppModuleParam.ImageEraser],
  [getAppModulePath(AppModule.ImageEditor), AppModuleParam.ImageEditor],
  [getAppModulePath(AppModule.IPCharacterConcept), AppModuleParam.IPDesign],
  [getAppModulePath(AppModule.Poster), AppModuleParam.Poster],
  [getAppModulePath(AppModule.Material), AppModuleParam.Material]
]);

export const tracerAppKey = getTracerAppKey(appOrigin);
