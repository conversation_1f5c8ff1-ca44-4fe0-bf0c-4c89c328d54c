import { AppOrigin, appOrigin } from './appOrigin';

/**
 * 获取路由基本名
 */
function getRouteBasename() {
  switch (appOrigin) {
    case AppOrigin.Designer: {
      return process.env.REACT_APP_DESIGNER_ROUTE_BASENAME;
    }

    case AppOrigin.Whee:
    default: {
      return process.env.REACT_APP_ROUTE_BASENAME;
    }
  }
}

/**
 * 路由基本名
 * @description 一般情况下，我们仅用于创建路由时的 `basename` 参数
 */
export const routeBasename = getRouteBasename();
