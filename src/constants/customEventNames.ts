import { CustomMessager } from '@/hooks/useCustomEvent';

export enum CustomEventNames {
  // AI扩图结果页点击重试按钮
  ClickRetry = 'clickRetry',
  ClickRetryByImageRepaint = 'ClickRetryByImageRepaint',
  // 支付成功 重新计算价格
  ReGetPrice = 'reGetPrice'
}

const getMessager = <T extends {}>(name: CustomEventNames) =>
  new CustomMessager<T>(name).send.bind(new CustomMessager(name));

export const sendClickRetryEvent = getMessager<{}>(CustomEventNames.ClickRetry);
export const sendReGetPriceEvent = getMessager<{}>(CustomEventNames.ReGetPrice);
export const sendClickRetryByImageRepaintEvent = getMessager<{}>(
  CustomEventNames.ClickRetryByImageRepaint
);
