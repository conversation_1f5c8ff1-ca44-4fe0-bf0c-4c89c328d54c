/** 权限控制相关的 key Component 开头为组件级别 ，Page 开头为页面级别  TODO: 后续权限策略放在后端*/

// -------------------------------编辑器页面---------------------------------

// 创作模式组件 （推荐/高级）
export const PermissionComponentEditorMode = 'PermissionComponentEditorMode';

// 提示词组件
export const PermissionComponentPrompt = 'PermissionComponentPrompt';

// 不希望呈现的内容组件
export const PermissionComponentNegativePrompt =
  'PermissionComponentNegativePrompt';

// 基础模型组件
export const PermissionComponentBaseModel = 'PermissionComponentBaseModel';

// 风格模型组件
export const PermissionComponentStyleModel = 'PermissionComponentStyleModel';

// 参数设定组件
export const PermissionComponentParams = 'PermissionComponentParams';

// 画面尺寸组件
export const PermissionComponentSize = 'PermissionComponentSize';

// 创意相关性组件
export const PermissionComponentPromptWeight =
  'PermissionComponentPromptWeight';

// 生成张数组件
export const PermissionComponentQuantity = 'PermissionComponentQuantity';

// 采样器类型组件
export const PermissionComponentSamplerType = 'PermissionComponentSamplerType';

// 采样器步骤组件
export const PermissionComponentSamplerStep = 'PermissionComponentSamplerStep';

// seed组件 （包含Input、随机 、生成批次）
export const PermissionComponentSeed = 'PermissionComponentSeed';

// seed Input组件
export const PermissionComponentSeedInput = 'PermissionComponentSeedInput';

// seed 生成批次 组件
export const PermissionComponentSeedBatches = 'PermissionComponentSeedBatches';

// 额外的生成参数 组件
export const PermissionComponentExtraFunctions =
  'PermissionComponentExtraFunctions';

// 面部修复 组件
export const PermissionComponentFacialRestoration =
  'PermissionComponentFacialRestoration';

// 高清修复 组件
export const PermissionComponentHdRestoration =
  'PermissionComponentHdRestoration';

// 图像超分
export const PermissionComponentSuperResolution =
  'PermissionComponentSuperResolution';
// ----------------------------TODO: 以下组件在暂时先不定义子组件的 key 等需要用到再定义 太多了------------------------------------

// 上传参考图 组件
export const PermissionComponentUploadImage = 'PermissionComponentUploadImage';

// controlNet 组件
export const PermissionComponentControlNet = 'PermissionComponentControlNet';
