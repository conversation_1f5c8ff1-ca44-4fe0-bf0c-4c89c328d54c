import { <PERSON>Mode, appOrigin, AppOrigin } from '@/constants';
import {
  PermissionComponentEditorMode,
  PermissionComponentPrompt,
  PermissionComponentNegativePrompt,
  PermissionComponentBaseModel,
  PermissionComponentStyleModel,
  PermissionComponentParams,
  PermissionComponentPromptWeight,
  PermissionComponentSize,
  PermissionComponentQuantity,
  PermissionComponentSamplerType,
  PermissionComponentSamplerStep,
  PermissionComponentSeed,
  PermissionComponentSeedInput,
  PermissionComponentSeedBatches,
  PermissionComponentExtraFunctions,
  PermissionComponentFacialRestoration,
  PermissionComponentHdRestoration,
  PermissionComponentUploadImage,
  PermissionComponentControlNet,
  PermissionComponentSuperResolution
} from './index';

import _ from 'lodash';

/**
 * 根据创作模式 获取对应的权限列表
 * @param
 */
export function getPermissionListByEditorMode(mode: EditorMode) {
  switch (mode) {
    case EditorMode.Advanced: {
      return [
        PermissionComponentEditorMode,
        PermissionComponentPrompt,
        PermissionComponentBaseModel,
        PermissionComponentStyleModel,
        PermissionComponentParams,
        PermissionComponentPromptWeight,
        PermissionComponentSize,
        PermissionComponentQuantity,
        PermissionComponentSamplerType,
        PermissionComponentSamplerStep,
        PermissionComponentSeed,
        PermissionComponentSeedInput,
        PermissionComponentSeedBatches,
        PermissionComponentExtraFunctions,
        PermissionComponentFacialRestoration,
        PermissionComponentHdRestoration,
        PermissionComponentUploadImage,
        PermissionComponentSuperResolution,
        PermissionComponentNegativePrompt,
        PermissionComponentControlNet
      ];
    }

    case EditorMode.Recommend: {
      const permissionList = [
        PermissionComponentEditorMode,
        PermissionComponentPrompt,
        PermissionComponentBaseModel,
        PermissionComponentSize,
        PermissionComponentQuantity
      ];

      if (appOrigin === AppOrigin.Designer) {
        // 设计室只有推荐模式没有高级模式 去除模式选择组件的权限
        return _.reject(
          permissionList,
          (PermissionComponentKey) =>
            PermissionComponentKey === PermissionComponentEditorMode
        );
      }

      if (appOrigin === AppOrigin.MiracleVision) {
        // 大模型只有推荐模式没有高级模式 去除模式选择组件的权限以及基础模型
        return _.reject(
          permissionList,
          (PermissionComponentKey) =>
            PermissionComponentKey === PermissionComponentEditorMode ||
            PermissionComponentKey === PermissionComponentBaseModel
        );
      }

      return permissionList;
    }

    default: {
      return [];
    }
  }
}
