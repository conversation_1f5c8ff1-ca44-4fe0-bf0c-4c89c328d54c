#!/bin/bash

# 消息标题
title=$1

if [ "$title" = "" ]
    then
        echo "错误：消息标题不能为空"
        exit
fi

# 最后一个提交用户
COMMIT_USER=$(git log -1 --pretty=format:'%an %ae')

# Git 提交短 Hash 
CI_COMMIT_SHA_SHORT=$(echo $CI_COMMIT_SHA | cut -c 1-8)

# Matrix 环境标识
MATRIX_ENV=$([ "$REACT_APP_ENV" == "pre" ] && echo "-pre" || echo "")

# Matrix 部署版本页面链接
MATRIX_URL="http://matrix${MATRIX_ENV}.meitu-int.com/#/deployment/project-setting?namespace=aigc-fex&project=aigc-editor&stage=${REACT_APP_ENV}&currentTab=deploy"

# 构建版本号
VERSION=$([ "$CI_JOB_STAGE" == "notify" ] && echo "$IMAGE_DEFAULT_VERSION_NUMBER" || echo $(node --eval "console.log(require('./package.json').version.split('-')[0])") )

# Docker 镜像信息
DOCKER_IMAGE_INFO=$([ -n "$IMAGE_DEFAULT_VERSION_NUMBER" ] && echo "\n构建容器镜像版本：[${REACT_APP_ENV}-${IMAGE_DEFAULT_VERSION_NUMBER}-${CI_PIPELINE_IID}](${MATRIX_URL})\n" || echo "构建版本：$VERSION")

# 当前时间
DATETIME=$(date '+%Y-%m-%d %H:%M:%S')

# Markdown 消息内容
content='## '$title'

> '$DATETIME'

执行任务：['$CI_JOB_NAME' '$CI_JOB_ID']('$CI_JOB_URL') [@'$CI_COMMIT_SHA_SHORT']('$CI_PROJECT_URL'/commit/'$CI_COMMIT_SHA')
'$DOCKER_IMAGE_INFO'
操作人员：'$GITLAB_USER_NAME' '$GITLAB_USER_EMAIL'
'

# # 发送 Markdown 通知消息到企业微信群
# curl 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c45265be-63c1-40a0-8136-5f66d60a9213' \
#     -X POST \
#     -H 'Content-Type: application/json' \
#     -d '{
#         "msgtype": "markdown",
#         "markdown": {
#             "content": "'"${content}"'"
#         }
#     }'
# 发送通知到企业微信
# curl 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c45265be-63c1-40a0-8136-5f66d60a9213' \
#     -X POST \
#     -H 'Content-Type: application/json' \
#     -d '{
#         "msgtype": "markdown",
#         "markdown": {
#             "content": "'"${content}"'"
#         }
#     }'