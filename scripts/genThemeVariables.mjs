import { formatCSSText, shouldUpdateFile } from './utils.mjs';
import path from 'node:path';
import { chalk } from 'zx';
import fs from 'node:fs';
import _ from 'lodash';

const lessVarsTarFile = './src/styles/theme/less-variables.less';
const cssVarsTarFile = './src/styles/theme/css-variables.css';
const variablesFile = './src/styles/theme/variables.js';
const cwd = process.cwd();

function toCSSText(elem, themeValues) {
  return `${elem} {
    ${themeValues.reduce(
      (acc, [key, value]) => `${acc}\n${key}: ${value};`,
      ''
    )}
  }`;
}

/**
 *
 * @param {Record<string, any>} lessVarNames
 * @return {string}
 */
function toLessText(lessVarNames) {
  return lessVarNames.reduce(
    (acc, [key, value]) => `${acc}\n${key}: var(${value});`,
    ''
  );
}

/**
 * @return {Array<[[string, string][], [string, string][], [string, string][]]>}
 */
function toThemeVariables() {
  const defaultThemeVariables = new Map();
  const darkThemeVariables = new Map();
  const lessVarNames = new Map();

  delete require.cache[require.resolve(variablesFile)];

  require(variablesFile).forEach(
    ([name, defaultThemeValue, darkThemeValue]) => {
      if (!defaultThemeValue && !darkThemeValue) {
        return;
      }

      const cssVarName = `--${_.kebabCase(name)}`;
      const lessVarName = `@${_.kebabCase(name)}`;

      lessVarNames.set(lessVarName, cssVarName);

      if (defaultThemeValue === darkThemeValue) {
        defaultThemeVariables.set(cssVarName, defaultThemeValue);
        return;
      }

      if (defaultThemeValue) {
        defaultThemeVariables.set(cssVarName, defaultThemeValue);
      }

      if (darkThemeValue) {
        darkThemeVariables.set(cssVarName, darkThemeValue);
      }
    }
  );

  return [
    Array.from(defaultThemeVariables),
    Array.from(darkThemeVariables),
    Array.from(lessVarNames)
  ];
}

/**
 *
 * @param {string} filePath
 * @param {string} fileContent
 */
async function writeFile(filePath, fileContent) {
  if (shouldUpdateFile(filePath, fileContent)) {
    fs.writeFileSync(filePath, fileContent);
    return;
  }

  echo`文件 ${chalk.yellow(path.basename(filePath))} 已是最新版本`;
}

async function genTheme() {
  // prettier-ignore
  const [
    defaultThemeVariables,
    darkThemeVariables,
    lessVarNames
  ] = toThemeVariables();

  await writeFile(
    cssVarsTarFile,
    formatCSSText(`
    ${toCSSText(':root[data-theme="dark"]', darkThemeVariables)}
    
    ${toCSSText(':root', defaultThemeVariables)}
  `)
  );

  await writeFile(lessVarsTarFile, formatCSSText(toLessText(lessVarNames)));
}

export async function genThemeVariables(isBuildMode) {
  await genTheme();

  if (!isBuildMode) {
    echo`正在监听 ${chalk.yellow(path.basename(variablesFile))} 变化`;

    fs.watchFile(path.join(cwd, variablesFile), (curr, prev) => {
      genTheme();
    });
  }
}
