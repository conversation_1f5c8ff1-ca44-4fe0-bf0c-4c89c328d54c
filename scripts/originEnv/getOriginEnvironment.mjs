import dotenv from 'dotenv';
import fs from 'node:fs';

// 在使用 `react-scripts/config/env` 之前需要显式
// 指定 `NODE_ENV` 否则会出现如下的错误
//  Error: The NODE_EN environment variable is required but was not specified.
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

/** @type {Record<string, string>} */
const env = require('react-scripts/config/env')().raw;

/** @type {Record<string, string>} */
const paths = require('react-scripts/config/paths');

// Grab NODE_ENV and REACT_APP_* environment variables and prepare them to be
// injected into the application via DefinePlugin in webpack configuration.
const REACT_APP = /^REACT_APP_/i;

/**
 * @param {string} origin
 * @param {string} appEnv
 */
export function getOriginEnvironment(origin, appEnv) {
  const envFiles = [
    `${paths.dotenv}.origin.${origin}`,
    `${paths.dotenv}.origin.${origin}.${appEnv}`,
    `${paths.dotenv}.origin.${origin}.${appEnv}.local`
  ];

  /** @type {Record<string, string>} */
  const envs = envFiles.reduce((acc, envFile) => {
    if (fs.existsSync(envFile)) {
      Object.assign(acc, dotenv.config({ path: envFile }).parsed);
    }

    return acc;
  }, {});

  /** @type {Record<string, string>} */
  const raw = Object.keys(envs)
    .filter((key) => REACT_APP.test(key))
    .reduce((acc, key) => Object.assign(acc, { [key]: envs[key] }), {
      ...env,
      REACT_APP_ENV: appEnv,
      PUBLIC_URL: paths.publicUrlOrPath
    });

  /** @type {{ 'process.env': Record<string, string> }} */
  const stringified = {
    'process.env': Object.keys(raw).reduce(
      (acc, key) =>
        Object.assign(acc, {
          [key]: JSON.stringify(raw[key])
        }),
      {}
    )
  };

  return { raw, stringified };
}
