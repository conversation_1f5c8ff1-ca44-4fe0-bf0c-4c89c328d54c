import { getOriginEnvironment } from './getOriginEnvironment.mjs';
import { readFile, writeFile } from 'node:fs/promises';
import cheerio from 'cheerio';
import path from 'node:path';
import fs from 'node:fs';

/** @type {Record<string, string>} */
const paths = require('react-scripts/config/paths');

const indexHtmlPath = path.join(paths.appBuild, 'index.html');

/**
 * 生成特定源的 `index.html`
 * @param {string} origin
 * @param {string} appEnv
 */
export async function generateOriginIndexHtml(origin, appEnv) {
  const env = getOriginEnvironment(origin, appEnv);
  const $ = cheerio.load(await readFile(indexHtmlPath, 'utf-8'));

  $('title')
    .attr('data-title', env.raw.REACT_APP_TITLE)
    .text(env.raw.REACT_APP_TITLE);

  $('meta[name="description"]').attr('content', env.raw.REACT_APP_DESCRIPTION);

  if (env.raw.REACT_APP_KEYWORDS) {
    $('meta[name="keywords"]').attr('content', env.raw.REACT_APP_KEYWORDS);
  } else {
    $('meta[name="keywords"]').remove();
  }

  const icoName = `favicon.${origin}.ico`;

  if (fs.existsSync(path.join(paths.appBuild, icoName))) {
    $('link[rel="icon"]').attr(
      'href',
      path.join(env.raw.PUBLIC_URL, `favicon.${origin}.ico`)
    );
  } else {
    $('link[rel="icon"]').remove();
  }

  const wxCoverName = `wx.cover.${origin}.png`;

  if (fs.existsSync(path.join(paths.appBuild, wxCoverName))) {
    $('#wx-cover').attr('src', path.join(env.raw.PUBLIC_URL, wxCoverName));
  } else {
    $('#wx-cover').remove();
  }

  return writeFile(
    path.join(paths.appBuild, `index.${origin}.html`),
    $.html(),
    'utf-8'
  );
}
