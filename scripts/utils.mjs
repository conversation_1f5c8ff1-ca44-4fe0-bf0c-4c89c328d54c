import crypto from 'node:crypto';
import prettier from 'prettier';
import path from 'node:path';
import fs from 'node:fs';
import _ from 'lodash';

const cwd = process.cwd();

/**
 * 计算 SHA-1
 * @param {string} data
 * @return {string}
 */
export function sha1(data) {
  return crypto.createHash('sha1').update(data, 'binary').digest('hex');
}

/**
 * 格式化 CSS
 * @param {string} cssText
 * @param {import('prettier').Options} [override]
 * @return {string}
 */
export function formatCSSText(cssText, override) {
  return prettier.format(
    cssText,
    Object.assign(
      {},
      require(path.join(cwd, '.prettierrc.json')),
      { parser: 'less' },
      override
    )
  );
}

/**
 * 格式化 JavaScript
 * @param {string} jsText
 * @param {import('prettier').Options} [override]
 * @return {string}
 */
export function formatJsText(jsText, override) {
  return prettier.format(
    jsText,
    Object.assign(
      {},
      require(path.join(cwd, '.prettierrc.json')),
      { parser: 'babel' },
      override
    )
  );
}

/**
 *
 * @param {string} filePath
 * @param {string} fileContent
 * @return {boolean}
 */
export function shouldUpdateFile(filePath, fileContent) {
  // prettier-ignore
  const absFilePath = (
    path.isAbsolute(filePath) ?
      filePath :
      path.join(cwd, filePath)
  );

  return !(
    fs.existsSync(absFilePath) &&
    sha1(fs.readFileSync(absFilePath, 'utf-8').trim()) ===
      sha1(fileContent.trim())
  );
}

/**
 * 转换为 Less 变量文本
 * @param {Map<string, string>} lessVarNames
 */
export function toLessVarNamesText(lessVarNames) {
  return Array.from(lessVarNames).reduce(
    (acc, [key, value]) => `${acc}\n${key}: var(${value});`,
    ''
  );
}

/**
 * 转换为 CSS Vars 文本
 * @param {Record<string, string>} cssVars
 * @return {string}
 */
export function toCSSVarsText(cssVars) {
  return _.reduce(
    cssVars,
    (acc, value, key) => `${acc}\n${key}: ${value};`,
    ''
  );
}

/**
 * 创建主题 CSS Vars 文本
 * @param {Record<string, string>} defaultCSSVars
 * @param {Record<string, string>} darkCSSVars
 * return {string}
 */
export function toThemeCSSVarsText(defaultCSSVars, darkCSSVars) {
  return formatCSSText(
    `
    :root[data-theme="dark"] { ${toCSSVarsText(darkCSSVars)} }\n
    :root { ${toCSSVarsText(defaultCSSVars)} }
  `,
    { printWidth: 2333 }
  );
}

/**
 * 创建主题 Less Vars 文本
 * @param {Record<string, string>} defaultCSSVars
 * @param {Record<string, string>} darkCSSVars
 * return {string}
 */
export function toThemeLessVarsText(defaultCSSVars, darkCSSVars) {
  const cssVarNames = Array.from(
    new Set(Object.keys(defaultCSSVars).concat(Object.keys(darkCSSVars)))
  );

  const lessVars = cssVarNames.reduce((acc, cssVarName) => {
    acc.set(`@${_.kebabCase(cssVarName)}`, cssVarName);
    return acc;
  }, new Map());

  return formatCSSText(toLessVarNamesText(lessVars), { printWidth: 2333 });
}

/**
 * 获取应用环境
 * @param {string} appEnv
 * @param {string[]} validEnvs 可用的应用换
 * @return {string}
 */
export function toAppEnv(
  appEnv = process.env.REACT_APP_ENV,
  validEnvs = ['pre', 'test', 'beta', 'release']
) {
  return (validEnvs.includes(appEnv) ? appEnv : 'development') ?? 'development';
}
