import { pxUnitVarNames } from './constants.mjs';
import { writeFileSync } from 'node:fs';
import { ensureDir } from 'fs-extra';
import { chalk, echo } from 'zx';
import { theme } from 'antd';
import path from 'node:path';
import _ from 'lodash';

import {
  toThemeLessVarsText,
  toThemeCSSVarsText,
  shouldUpdateFile
} from '../../utils.mjs';

const tarDir = './src/styles/theme/antd';
const tarCSSVarsFile = path.join(tarDir, './css-variables.less');
const tarLessVarsFile = path.join(tarDir, './less-variables.less');
const seedTokenFile = './src/styles/seedToken.mjs';
const cwd = process.cwd();

async function fetchAntdThemeCSSVariables(duplicate = false) {
  const { toThemeTokens } = await import(path.join(cwd, seedTokenFile));
  const defaultCSSVars = {};
  const darkCSSVars = {};

  const themes = [
    [
      'default',
      theme.defaultAlgorithm,
      Object.assign({}, theme.defaultSeed, toThemeTokens(false))
    ],
    [
      'dark',
      theme.darkAlgorithm,
      Object.assign({}, theme.defaultSeed, toThemeTokens(true))
    ]
  ];

  for (const [theme, algorithm, seeds] of themes) {
    for (const [key, value] of _.toPairs(algorithm(seeds))) {
      const varName = `--${_.kebabCase(key)}`;
      const varValue = pxUnitVarNames.includes(varName) ? `${value}px` : value;

      Object.assign(theme === 'default' ? defaultCSSVars : darkCSSVars, {
        [varName]: varValue
      });
    }
  }

  // 移除 dark 主题中与 default 值一样的键值对
  if (!duplicate) {
    Object.keys(darkCSSVars).forEach((key) => {
      if (defaultCSSVars[key] === darkCSSVars[key]) {
        delete darkCSSVars[key];
      }
    });
  }

  return [defaultCSSVars, darkCSSVars];
}

/**
 * 生成 antd 主题 CSS 变量文件
 * @param {Record<string, string>} defaultCSSVars
 * @param {Record<string, string>} darkCSSVars
 * @return {Promise<void>}
 */
async function genAntdCSSVariables(defaultCSSVars, darkCSSVars) {
  const cssText = toThemeCSSVarsText(defaultCSSVars, darkCSSVars);

  if (shouldUpdateFile(tarCSSVarsFile, cssText)) {
    // Gitlab CI/CD 环境下，系统镜像是 Alpine Linux，cssText 的内容有引号，带来引号转义问题，即要改成 "${cssText}"，而在 macOS 系统下开发，又不需要加引号
    // https://github.com/google/zx/blob/main/docs/quotes.md
    // return $`echo ${cssText} > ${tarCSSVarsFile}`;
    return writeFileSync(tarCSSVarsFile, cssText);
  }

  echo`文件 ${chalk.yellow(tarCSSVarsFile)} 已是最新版本`;
}

/**
 * 生成 antd 主题 Less 变量文件
 * @param {Record<string, string>} defaultCSSVars
 * @param {Record<string, string>} darkCSSVars
 * @return {Promise<void>}
 */
async function genAntdLessVars(defaultCSSVars, darkCSSVars) {
  const lessText = toThemeLessVarsText(defaultCSSVars, darkCSSVars);

  if (shouldUpdateFile(tarLessVarsFile, lessText)) {
    return writeFileSync(tarLessVarsFile, lessText);
  }

  echo`文件 ${chalk.yellow(tarLessVarsFile)} 已是最新版本`;
}

/**
 * 生成 antd 主题文件
 * @return {Promise<void>}
 */
async function genAntdThemeFiles() {
  const [defaultCSSVars, darkCSSVars] = await fetchAntdThemeCSSVariables();

  await genAntdCSSVariables(defaultCSSVars, darkCSSVars);
  await genAntdLessVars(defaultCSSVars, darkCSSVars);
}

export async function genAntdTheme() {
  await ensureDir(tarDir);
  await genAntdThemeFiles();
}
